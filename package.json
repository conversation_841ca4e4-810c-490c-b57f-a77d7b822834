{"name": "vibico-education-web", "version": "0.1.0", "private": true, "scripts": {"dev": "vite --port 8080 --open", "build": "vite build", "serve": "vite preview", "lint": "eslint --ext .ts,.js,.vue src", "format": "prettier --check src/", "format-fix": "prettier --check src/ --write", "type-check": "tsc --noEmit --project './tsconfig.json'", "type-watch": "tsc --watch --noEmit --project './tsconfig.json'"}, "dependencies": {"@bachdx/b-vuse": "^1.6.2", "@popperjs/core": "^2.11.8", "@vueform/multiselect": "^2.6.2", "@vueuse/core": "^12.7.0", "apexcharts": "^4.4.0", "axios": "^1.7.9", "bootstrap": "^5.3.3", "bootstrap-vue-next": "^0.26.22", "canvas-confetti": "^1.9.3", "click-outside-vue3": "^4.0.1", "core-js": "^3.8.3", "flag-icons": "^7.5.0", "graphql": "^16.10.0", "graphql-tag": "^2.12.6", "hls.js": "^1.6.2", "lodash": "^4.17.21", "metismenujs": "^1.4.0", "moment": "^2.30.1", "motion-v": "^1.1.0-alpha.1", "pinia": "^2.3.1", "plyr": "^3.7.8", "quill": "^2.0.3", "sass": "1.77.6", "simple-notify": "^1.0.6", "simplebar-vue": "^2.4.0", "sweetalert2": "^11.17.2", "swiper": "^11.2.10", "unplugin-auto-import": "^19.0.0", "uuid": "^11.1.0", "video.js": "^8.22.0", "videojs-youtube": "^3.0.1", "vidstack": "^1.12.13", "vue": "^3.5.13", "vue-advanced-cropper": "^2.8.9", "vue-datepicker-next": "^1.0.3", "vue-i18n": "^11.1.1", "vue-router": "^4.0.3", "vue3-apexcharts": "^1.8.0", "vue3-otp-input": "^0.5.30", "vuedraggable": "^4.1.0"}, "devDependencies": {"@types/webpack-env": "^1.18.8", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-typescript": "^9.1.0", "eslint": "8", "eslint-plugin-vue": "8", "prettier": "^3.6.2", "typescript": "5.1.6", "vite": "^6.1.0", "vue-template-compiler": "^2.7.16"}}