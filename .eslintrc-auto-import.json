{"globals": {"acceptHMRUpdate": true, "cloneDeep": true, "Component": true, "ComponentPublicInstance": true, "computed": true, "ComputedRef": true, "createApp": true, "createPinia": true, "customRef": true, "defineAsyncComponent": true, "defineComponent": true, "defineEmits": true, "defineExpose": true, "defineModel": true, "defineProps": true, "defineStore": true, "DirectiveBinding": true, "effectScope": true, "EffectScope": true, "ExtractDefaultPropTypes": true, "ExtractPropTypes": true, "ExtractPublicPropTypes": true, "get": true, "getActivePinia": true, "getCurrentInstance": true, "getCurrentScope": true, "h": true, "inject": true, "InjectionKey": true, "isEmpty": true, "isProxy": true, "isReactive": true, "isReadonly": true, "isRef": true, "mapActions": true, "mapGetters": true, "mapState": true, "mapStores": true, "mapWritableState": true, "markRaw": true, "MaybeRef": true, "MaybeRefOrGetter": true, "nextTick": true, "omit": true, "onActivated": true, "onBeforeMount": true, "onBeforeRouteLeave": true, "onBeforeRouteUpdate": true, "onBeforeUnmount": true, "onBeforeUpdate": true, "onDeactivated": true, "onErrorCaptured": true, "onMounted": true, "onRenderTracked": true, "onRenderTriggered": true, "onScopeDispose": true, "onServerPrefetch": true, "onUnmounted": true, "onUpdated": true, "onWatcherCleanup": true, "PropType": true, "provide": true, "reactive": true, "readonly": true, "ref": true, "Ref": true, "resolveComponent": true, "setActivePinia": true, "setMapStoreSuffix": true, "shallowReactive": true, "shallowReadonly": true, "shallowRef": true, "storeToRefs": true, "toRaw": true, "toRef": true, "toRefs": true, "toValue": true, "triggerRef": true, "unref": true, "useAttrs": true, "useCssModule": true, "useCssVars": true, "useId": true, "useLink": true, "useModel": true, "useRoute": true, "useRouter": true, "useSlots": true, "useTemplateRef": true, "VNode": true, "watch": true, "watchEffect": true, "watchPostEffect": true, "watchSyncEffect": true, "withDefaults": true, "WritableComputedRef": true}}