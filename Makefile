# ------------ Docker Build & Deploy ----------------------------------------------------
REGISTRY=us-west1-docker.pkg.dev/behemothvn/library/vibico
IMAGE_NAME=academy-web
COMMIT_SHA=$(shell git rev-parse HEAD)
NAMESPACE_DEV=vibico-dev
NAMESPACE_SANDBOX=vibico-sandbox
CONTEXT=gke_behemothvn_us-west1_insight-prod

.PHONY: docker-build docker-push k8s-release-dev k8s-release-sandbox release-dev release-sandbox

docker-build:
	docker build --platform=linux/amd64 -f docker/Dockerfile -t $(IMAGE_NAME):latest .
	docker tag $(IMAGE_NAME):latest $(REGISTRY)/$(IMAGE_NAME):latest
	docker tag $(IMAGE_NAME):latest $(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA)

docker-push:
	docker push $(REGISTRY)/$(IMAGE_NAME):latest
	docker push $(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA)

k8s-release-dev:
	kubectl --context=$(CONTEXT) set image deployment/$(IMAGE_NAME) $(IMAGE_NAME)=$(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA) -n $(NAMESPACE_DEV)

k8s-release-sandbox:
	@echo "About to update $(IMAGE_NAME) deployment in $(NAMESPACE_SANDBOX) environment with image $(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA)"
	@read -p "Are you sure you want to continue? (Y/n): " -n 1 -r; \
	echo; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		kubectl --context=$(CONTEXT) set image deployment/$(IMAGE_NAME) $(IMAGE_NAME)=$(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA) -n $(NAMESPACE_SANDBOX); \
		echo "Checking deployment status..."; \
		kubectl --context=$(CONTEXT) rollout status deployment/$(IMAGE_NAME) -n $(NAMESPACE_SANDBOX); \
	else \
		echo "Deployment cancelled."; \
		exit 1; \
	fi

release-dev: docker-build docker-push k8s-release-dev

release-sandbox: docker-build docker-push k8s-release-sandbox

# ------------ Docker Build & Deploy for LP ---------------------------------------------
LP_IMAGE_NAME=vibico-lp

.PHONY: docker-lp-build docker-lp-push k8s-lp-release-sandbox release-lp-sandbox

docker-lp-build:
	docker build --platform=linux/amd64 -f docker/lp/Dockerfile -t $(LP_IMAGE_NAME):latest .
	docker tag $(LP_IMAGE_NAME):latest $(REGISTRY)/$(LP_IMAGE_NAME):latest
	docker tag $(LP_IMAGE_NAME):latest $(REGISTRY)/$(LP_IMAGE_NAME):$(COMMIT_SHA)

docker-lp-push:
	docker push $(REGISTRY)/$(LP_IMAGE_NAME):latest
	docker push $(REGISTRY)/$(LP_IMAGE_NAME):$(COMMIT_SHA)

k8s-lp-release-sandbox:
	@echo "About to update $(LP_IMAGE_NAME) deployment in $(NAMESPACE_SANDBOX) environment with image $(REGISTRY)/$(LP_IMAGE_NAME):$(COMMIT_SHA)"
	@read -p "Are you sure you want to continue? (Y/n): " -n 1 -r; \
	echo; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		kubectl --context=$(CONTEXT) set image deployment/$(LP_IMAGE_NAME) $(LP_IMAGE_NAME)=$(REGISTRY)/$(LP_IMAGE_NAME):$(COMMIT_SHA) -n $(NAMESPACE_SANDBOX); \
		echo "Checking deployment status..."; \
		kubectl --context=$(CONTEXT) rollout status deployment/$(LP_IMAGE_NAME) -n $(NAMESPACE_SANDBOX); \
	else \
		echo "Deployment cancelled."; \
		exit 1; \
	fi

release-lp-sandbox: docker-lp-build docker-lp-push k8s-lp-release-sandbox
