/* eslint-disable */
declare module '*.vue' {
  import type { DefineComponent } from 'vue';
  const component: DefineComponent<{}, {}, any>;
  export default component;
}
declare module 'bootstrap-vue-next';
declare module 'click-outside-vue3';

declare module 'lodash';

// TODO add type for lib
declare module '@bachdx/b-vuse' {
  export function useGoList({}): any;
  export function useBreadcrumb({}): any;
}
