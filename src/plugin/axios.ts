import axios from 'axios';
import Toast from '@/utils/toast';
import { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { CustomInternalAxiosRequestConfig, CustomAxiosRequestConfig } from '@/utils/interface/axios';
import { get } from 'lodash';
import { AXIOS_METHOD_ENUMS, CREATE_AXIOS_DEFAULT_CONFIG, GQL_API_ENDPOINT_ENUMS } from '@/utils/constant';

import { useGlobalStore } from '@/store/global';
import { useAuthPublicStore } from '@/store/public/auth';
import { useAuthAdminStore } from '@/store/admin/auth';

export class ApiClient {
  private client: AxiosInstance;
  private isRefreshing = false;
  private refreshSubscribers: (() => void)[] = [];

  public request(
    method: string,
    url: string,
    data: any = null,
    options: CustomAxiosRequestConfig = {}
  ): Promise<AxiosResponse> {
    const config: AxiosRequestConfig = {
      method,
      url,
      ...options,
      [method === AXIOS_METHOD_ENUMS.GET ? 'params' : 'data']: data
    };
    return this.client(config);
  }

  constructor(config: AxiosRequestConfig = {}) {
    this.client = axios.create({
      ...CREATE_AXIOS_DEFAULT_CONFIG,
      ...config,
      withCredentials: true // TODO: move language to local storage
    });
    this.setupInterceptors();
  }

  private setupInterceptors() {
    this.client.interceptors.request.use(
      request => this.handleRequest(request),
      error => this.handleRequestError(error)
    );
    this.client.interceptors.response.use(
      response => this.handleResponse(response),
      error => this.handleResponseError(error)
    );
  }

  private handleRequest(request: CustomInternalAxiosRequestConfig) {
    useGlobalStore().setErrors([]);

    const site = request.site?.toLowerCase() || GQL_API_ENDPOINT_ENUMS.TEACHER;
    let token = '';

    if (site === GQL_API_ENDPOINT_ENUMS.PUBLIC) {
      const tokenSource = request.tokenSource;
      if (tokenSource) {
        token = tokenSource === 'admin' ? useAuthAdminStore().adminAccessToken : useAuthPublicStore().accessToken;
      }
    } else {
      token =
        site === GQL_API_ENDPOINT_ENUMS.ADMIN ? useAuthAdminStore().adminAccessToken : useAuthPublicStore().accessToken;
    }

    request.headers['Education-Authorization'] = `Bearer ${token}`;

    return request;
  }

  private handleRequestError(error: any) {
    const globalStore = useGlobalStore();
    globalStore.setErrors([]);
    return Promise.reject(error);
  }

  private handleResponse(response: AxiosResponse) {
    const errors = get(response, 'data.errors', []);
    if (errors.length) {
      return this.handleErrors(errors, response);
    }

    const data = get(response, 'data.data');
    this.showToast(response.config, data);
    return response.data;
  }

  private handleResponseError(error: any) {
    const status = get(error, 'response.status');
    if (status === 401) {
      return this.handleAuth({ config: error.config } as AxiosResponse);
    }

    const errors = get(error, 'response.data.errors', []);
    return errors.length ? this.handleErrors(errors) : Promise.reject(error);
  }

  private handleErrors(errors: any[], response?: AxiosResponse) {
    const error = errors[0];
    const httpCode = get(error, 'extensions.code');
    const errorMessage = get(error, 'extensions.message') || get(error, 'message') || 'Đã xảy ra lỗi.';

    if (httpCode === 401 && response) {
      return this.handleAuth(response);
    }

    if (httpCode === 403) {
      const errCode = get(error, 'extensions.errorCode', '');

      if (errCode == 'ACCOUNT_DEACTIVATED') {
        const authStore = useAuthPublicStore();
        authStore.clearTokens();
        this.redirectToLogin();
      }

      return Promise.reject({
        errors: error.extensions?.errors || error.message,
        message: errorMessage
      });
    }

    const globalStore = useGlobalStore();
    const errorData = get(error, 'extensions.errors');
    globalStore.setErrors(errorData);

    if (!response?.config || !get(response.config, 'data', '').includes('mutation RefreshToken')) {
      Toast.error({ title: errorMessage });
    }

    return Promise.reject({
      errors: error.extensions?.errors || error.message,
      message: errorMessage
    });
  }

  private handleAuth(response: AxiosResponse) {
    const originalRequest = response.config as CustomInternalAxiosRequestConfig;
    if (get(originalRequest, 'data', '').includes('mutation RefreshToken')) {
      this.refreshSubscribers = [];
      return;
    }

    if (this.isRefreshing) {
      return new Promise(resolve => {
        this.refreshSubscribers.push(() => resolve(this.client(originalRequest)));
      });
    }

    this.isRefreshing = true;
    return this.refreshToken(originalRequest).finally(() => (this.isRefreshing = false));
  }

  private async refreshToken(request: CustomInternalAxiosRequestConfig) {
    const site = request.site || GQL_API_ENDPOINT_ENUMS.TEACHER;

    const authStore =
      site.toLocaleLowerCase() === GQL_API_ENDPOINT_ENUMS.ADMIN ? useAuthAdminStore() : useAuthPublicStore();

    try {
      await authStore.handleRefreshToken();

      this.refreshSubscribers.forEach(callback => callback());
      this.refreshSubscribers = [];
      return this.client(request);
    } catch (error) {
      authStore.clearTokens();
      this.refreshSubscribers = [];
      this.redirectToLogin(site);

      return Promise.reject({ error: 'error when refresh token' });
    }
  }

  private redirectToLogin(site?: string) {
    const path = site?.toLocaleLowerCase() === GQL_API_ENDPOINT_ENUMS.ADMIN ? '/admin/login' : '/login';
    window.location.href = path;
  }

  private showToast(config: CustomInternalAxiosRequestConfig, data: any) {
    if (config.toast && data && typeof data === 'object') {
      Object.values(data).forEach(value => {
        if (value && typeof value === 'object' && 'message' in value) {
          Toast.success({ title: value.message as string });
        }
      });
    }
  }
}
