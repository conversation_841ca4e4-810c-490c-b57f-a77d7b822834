import { createI18n } from 'vue-i18n';
import locales from '@/locales';
import { LANGUAGES } from '@/utils/constant';

const langCookie = document.cookie.split('; ').find(row => row.startsWith('language='));
const defaultLocale = localStorage.getItem('language') || LANGUAGES.VI;

if (!langCookie) {
  document.cookie = `language=${defaultLocale}; path=/; max-age=31536000`;
}

const i18n = createI18n({
  locale: defaultLocale,
  legacy: false,
  globalInjection: true,
  fallbackLocale: LANGUAGES.VI,
  messages: locales
});

export default i18n;
