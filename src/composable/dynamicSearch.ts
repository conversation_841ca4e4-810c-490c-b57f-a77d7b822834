import { ref, shallowRef } from 'vue';

import TextInputField from '@/components/base/TextInputField.vue';
import SingleSelectField from '@/components/base/SingleSelectField.vue';
import MultipleSelectField from '@/components/base/MultipleSelectField.vue';

import SearchField from '@/utils/search-fields';

export default function useDynamicSearch() {
  const searchFieldsList = ref<SearchField[]>([]);

  const searchComponents = {
    TextInputField: shallowRef(TextInputField),
    SingleSelectField: shallowRef(SingleSelectField),
    MultipleSelectField: shallowRef(MultipleSelectField)
  };

  return {
    searchFieldsList,
    searchComponents
  };
}
