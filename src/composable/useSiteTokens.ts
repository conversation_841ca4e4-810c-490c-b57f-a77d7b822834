import { SiteType } from '@/utils/interface/common';
import { useAuthAdminStore } from '@/store/admin/auth';
import { useAuthPublicStore } from '@/store/public/auth';

export function useSiteToken(site: SiteType) {
  const siteToken = computed(() => {
    if (site === 'admin') {
      return useAuthAdminStore().adminAccessToken;
    }
    return useAuthPublicStore().accessToken;
  });

  return {
    siteToken: siteToken.value
  };
}
