import { Ref } from 'vue';
import { CoursePackageInterface } from '@/utils/interface/user/userCourse';
import { PACKAGES_NAMES } from '@/utils/constant';

export function useCoursePackage(coursePackageID: Ref<number | string>, packageDetails: any) {
  const getPackageName = (ck: CoursePackageInterface) => {
    return ck.packageDeal?.name?.toCamelCase();
  };

  const getPackageDetail = (ck: CoursePackageInterface) => {
    switch (ck.packageDeal?.name) {
      case PACKAGES_NAMES.BASIC:
        return packageDetails.basic;
      case PACKAGES_NAMES.ADVANCE:
        return packageDetails.advance;
      case PACKAGES_NAMES.OFFLINE:
        return packageDetails.offline;
      default:
        return packageDetails.basic;
    }
  };

  const isSelected = (ck: CoursePackageInterface) => {
    return ck.id == coursePackageID.value;
  };

  const selectPackage = (item: any) => {
    coursePackageID.value = item;
  };

  const getResponsiveColumnClass = (packageCount: number) => {
    if (packageCount === 1) {
      return 'col-12';
    } else if (packageCount === 2) {
      return 'col-md-6 col-12';
    } else {
      return 'col-lg-4 col-md-6 col-12';
    }
  };

  return {
    getPackageName,
    getPackageDetail,
    isSelected,
    selectPackage,
    getResponsiveColumnClass
  };
}
