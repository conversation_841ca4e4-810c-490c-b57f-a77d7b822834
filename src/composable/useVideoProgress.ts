// composables/useVideoProgress.ts
import { ref } from 'vue';
import type { IVideoProgress } from '@/utils/interface/video';
import { UserCourseInterface } from '@/utils/interface/user/userCourse';

const progressMap = ref(new Map<string, IVideoProgress>());

const currentVideoProgress = ref<{
  lessonId: string;
  videoId: string;
  progress: number;
} | null>(null);

export function useVideoProgress() {
  const setProgress = (lessonId: string, progress: IVideoProgress) => {
    const newMap = new Map(progressMap.value);
    newMap.set(lessonId, progress);
    progressMap.value = newMap;
  };

  const getProgress = (lessonId: string) => {
    return progressMap.value.get(lessonId);
  };

  const updateVideoProgress = (lessonId: string, videoId: string, progress: number, duration: number) => {
    if (duration <= 0) return;

    const pct = (progress / duration) * 100;
    if (pct < 0 || pct > 100) return;

    const existingMap = getProgress(lessonId) ?? new Map<string, number>();

    const newMap = new Map(existingMap);
    newMap.set(videoId, pct);

    setProgress(lessonId, newMap);
  };

  const setCurrentVideoProgress = (lessonId: string, videoId: string, progress: number) => {
    currentVideoProgress.value = {
      lessonId,
      videoId,
      progress
    };
  };

  const getCurrentVideoProgress = () => {
    return currentVideoProgress.value;
  };

  function initializeCourseProgress(course: UserCourseInterface) {
    course.courseSections.forEach(section => {
      if (!Array.isArray(section.courseSectionItems) || section.courseSectionItems.length === 0) return;

      section.courseSectionItems.forEach(item => {
        if (!Array.isArray(item.videos) || item.videos.length === 0) return;

        const progress = new Map<string, number>();

        item.videos.forEach(video => {
          if (video.duration > 0) {
            const progressPct = (video.currentPosition / video.duration) * 100;
            progress.set(String(video.id), Math.min(progressPct, 100));
          }
        });

        if (progress.size > 0) {
          setProgress(String(item.id), progress);
        }
      });
    });
  }
  return {
    progressMap,
    setProgress,
    getProgress,
    updateVideoProgress,
    setCurrentVideoProgress,
    getCurrentVideoProgress,
    initializeCourseProgress
  };
}
