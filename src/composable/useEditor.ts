import { SiteType } from '@/utils/interface/common';

export type CourseDrillStepsKey = 'DIAGRAMS' | 'STEPS' | 'VIDEOS';
export type DrillStepsKey = 'BASIC_INFO' | 'DIAGRAMS' | 'STEPS' | 'VIDEOS' | 'PREVIEW';
export type StepSets = typeof DRILL_STEPS | typeof COURSE_DRILL_STEPS | typeof COURSE_STEPS;
export type CourseStepsKey = 'PLANNING_TIP' | 'SECTIONS' | 'CERTIFICATE' | 'LANDING_PAGE' | 'PRICING' | 'PREVIEW';

export interface StepConfigInterface {
  id: string;
  key: string;
  icon?: string;
  lazyLoad?: boolean;
  label: (site: SiteType) => string;
  description: (site: SiteType) => string;
  title: (site: SiteType) => string;
}

export const DRILL_STEPS: Record<DrillStepsKey, StepConfigInterface> = {
  BASIC_INFO: {
    id: 'basic_info',
    key: 'basic-info',
    lazyLoad: true,
    label: (site: SiteType) => `${site}.pool_table.tabs.basic_info.label`,
    description: (site: SiteType) => `${site}.pool_table.tabs.basic_info.description`,
    title: (site: SiteType) => `${site}.pool_table.tabs.basic_info.title`
  },
  DIAGRAMS: {
    id: 'diagrams',
    key: 'diagrams',
    label: (site: SiteType) => `${site}.pool_table.tabs.diagrams.label`,
    description: (site: SiteType) => `${site}.pool_table.tabs.diagrams.description`,
    title: (site: SiteType) => `${site}.pool_table.tabs.diagrams.title`
  },
  STEPS: {
    id: 'steps',
    key: 'steps',
    label: (site: SiteType) => `${site}.pool_table.tabs.steps.label`,
    description: (site: SiteType) => `${site}.pool_table.tabs.steps.description`,
    title: (site: SiteType) => `${site}.pool_table.tabs.steps.title`
  },
  VIDEOS: {
    id: 'videos',
    key: 'videos',
    lazyLoad: true,
    label: (site: SiteType) => `${site}.pool_table.tabs.videos.label`,
    description: (site: SiteType) => `${site}.pool_table.tabs.videos.description`,
    title: (site: SiteType) => `${site}.pool_table.tabs.videos.title`
  },
  PREVIEW: {
    id: 'preview',
    key: 'preview',
    lazyLoad: true,
    label: (site: SiteType) => `${site}.pool_table.tabs.preview.label`,
    description: (site: SiteType) => `${site}.pool_table.tabs.preview.description`,
    title: (site: SiteType) => `${site}.pool_table.tabs.preview.title`
  }
} as const;

export const COURSE_DRILL_STEPS: Record<CourseDrillStepsKey, StepConfigInterface> = {
  DIAGRAMS: {
    id: 'diagrams',
    key: 'diagrams',
    label: (site: SiteType) => `${site}.pool_table.tabs.diagrams.label`,
    description: (site: SiteType) => `${site}.pool_table.tabs.diagrams.description`,
    title: (site: SiteType) => `${site}.pool_table.tabs.diagrams.title`
  },
  STEPS: {
    id: 'steps',
    key: 'steps',
    label: (site: SiteType) => `${site}.pool_table.tabs.steps.label`,
    description: (site: SiteType) => `${site}.pool_table.tabs.steps.description`,
    title: (site: SiteType) => `${site}.pool_table.tabs.steps.title`
  },
  VIDEOS: {
    id: 'videos',
    key: 'videos',
    lazyLoad: true,
    label: (site: SiteType) => `${site}.pool_table.tabs.videos.label`,
    description: (site: SiteType) => `${site}.pool_table.tabs.videos.description`,
    title: (site: SiteType) => `${site}.pool_table.tabs.videos.title`
  }
} as const;

export const COURSE_STEPS: Record<CourseStepsKey, StepConfigInterface> = {
  PLANNING_TIP: {
    id: 'planning_tip',
    key: 'planning-tip',
    icon: 'bx-info-circle',
    lazyLoad: true,
    label: (site: SiteType) => `${site}.editor.course.steps.planning_tip.label`,
    description: (site: SiteType) => `${site}.editor.course.steps.planning_tip.description`,
    title: (site: SiteType) => `${site}.editor.course.steps.planning_tip.title`
  },
  SECTIONS: {
    id: 'sections',
    key: 'sections',
    label: (site: SiteType) => `${site}.editor.course.steps.sections.label`,
    description: (site: SiteType) => `${site}.editor.course.steps.sections.description`,
    title: (site: SiteType) => `${site}.editor.course.steps.sections.title`
  },
  CERTIFICATE: {
    id: 'certificate',
    key: 'certificate',
    label: (site: SiteType) => `${site}.editor.course.steps.certificate.label`,
    description: (site: SiteType) => `${site}.editor.course.steps.certificate.description`,
    title: (site: SiteType) => `${site}.editor.course.steps.certificate.title`
  },
  LANDING_PAGE: {
    id: 'landing_page',
    key: 'landing-page',
    lazyLoad: true,
    label: (site: SiteType) => `${site}.editor.course.steps.landing_page.label`,
    description: (site: SiteType) => `${site}.editor.course.steps.landing_page.description`,
    title: (site: SiteType) => `${site}.editor.course.steps.landing_page.title`
  },
  PRICING: {
    id: 'pricing',
    key: 'pricing',
    lazyLoad: true,
    label: (site: SiteType) => `${site}.editor.course.steps.pricing.label`,
    description: (site: SiteType) => `${site}.editor.course.steps.pricing.description`,
    title: (site: SiteType) => `${site}.editor.course.steps.pricing.title`
  },
  PREVIEW: {
    id: 'preview',
    key: 'preview',
    lazyLoad: true,
    label: (site: SiteType) => `${site}.editor.course.steps.preview.label`,
    description: (site: SiteType) => `${site}.editor.course.steps.preview.description`,
    title: (site: SiteType) => `${site}.editor.course.steps.preview.title`
  }
} as const;

export function getStepByIndex<K extends string, T extends Record<K, StepConfigInterface>>(
  target: T,
  index: number
): StepConfigInterface {
  const steps = Object.values(target);

  return steps[index] as StepConfigInterface;
}

export function getNextStepByIndex<K extends string, T extends Record<K, StepConfigInterface>>(
  target: T,
  index: number
): StepConfigInterface {
  return getStepByIndex(target, index + 1);
}

export function getPreviousStepByIndex<K extends string, T extends Record<K, StepConfigInterface>>(
  target: T,
  index: number
): StepConfigInterface {
  return getStepByIndex(target, index - 1);
}

export function getMaxStepPosition<K extends string, T extends Record<K, StepConfigInterface>>(target: T): number {
  return Object.keys(target).length;
}

export function getMaxStepIndex<K extends string, T extends Record<K, StepConfigInterface>>(target: T): number {
  return getMaxStepPosition(target) - 1;
}

export function getStepIndexById<K extends string, T extends Record<K, StepConfigInterface>>(
  target: T,
  id: string
): number {
  const keys = Object.keys(target);
  return keys.findIndex(key => target[key as keyof typeof target].id === id);
}

export function getStepPositionById<K extends string, T extends Record<K, StepConfigInterface>>(
  target: T,
  id: string
): number {
  return getStepIndexById(target, id) + 1;
}

export function getStepByPosition<K extends string, T extends Record<K, StepConfigInterface>>(
  target: T,
  position: number
): StepConfigInterface {
  return getStepByIndex(target, position - 1);
}

export function getMinStepId<K extends string, T extends Record<K, StepConfigInterface>>(target: T): string {
  return getStepByIndex(target, 0).id;
}

export function getArraySteps<K extends string, T extends Record<K, StepConfigInterface>>(
  target: T
): StepConfigInterface[] {
  return Object.values(target) as StepConfigInterface[];
}
