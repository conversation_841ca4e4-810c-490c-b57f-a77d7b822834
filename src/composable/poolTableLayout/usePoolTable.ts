import { FILE_TYPE_ENUMS } from '@/utils/constant';

import { useFlipLayout } from './useFlipLayout';
import { useInTableActions } from './useInTableActions';
import { useInTableDraw } from './useInTableDraw';
import { useLayoutStorage } from './useLayoutStorage';
import { useRack } from './useRack';
import { useTableLayoutDraw } from './useTableLayoutDraw';
import { useToolboxDraw } from './useToolboxDraw';

import {
  BallConfig,
  TableConfig,
  type ActivePoint,
  type Ball,
  type DiagramItem,
  type SpeedBar,
  type TableLayoutData,
  type TargetingBall,
  type TargetingZone,
  type TextBox
} from './config/poolTableConfig';

export const usePoolTable = () => {
  const canvas = ref<HTMLCanvasElement | null>(null);
  const canvasId = ref<string>('canvas-container');
  const ctx = ref<CanvasRenderingContext2D | null>(null);
  const balls = ref<Ball[]>([]);
  const availableBalls = ref<Ball[]>(
    TableConfig.BALL_LIST.createInitialBalls().map(ball => ({
      ...ball,
      paths: []
    }))
  );
  const targetingBalls = ref<TargetingBall[]>([]);
  const activePoint = ref<ActivePoint | null>(null);
  const targetingZones = ref<TargetingZone[]>([]);
  const textBoxes = ref<TextBox[]>([]);
  const speedBars = ref<SpeedBar[]>([]);

  const tableBalls: ComputedRef<Ball[]> = computed(() => balls.value);
  const tableData: ComputedRef<DiagramItem[]> = computed(() => [
    {
      balls: tableBalls.value,
      targetingBalls: targetingBalls.value,
      targetingZones: targetingZones.value,
      textBoxes: textBoxes.value,
      speedBars: speedBars.value
    }
  ]);

  // Initialize composables
  const tableLayoutDraw = useTableLayoutDraw(ctx);
  const toolboxDraw = useToolboxDraw(ctx, availableBalls);
  const inTableDraw = useInTableDraw(ctx, balls, targetingBalls, activePoint, targetingZones, textBoxes, speedBars);

  const { savedLayouts, saveLayout } = useLayoutStorage();
  const { getRackedBallPosition } = useRack();
  const { flipLayout } = useFlipLayout(balls, speedBars, targetingBalls, targetingZones, textBoxes);

  let resizeObserver: ResizeObserver | null = null;

  const drawCanvas = (id = 'canvas-container') => {
    if (!ctx.value || !canvas.value) return;

    // Calculate the base canvas dimensions
    const canvasWidth =
      TableConfig.TABLE.LEFT +
      TableConfig.TABLE.WIDTH +
      TableConfig.TABLE.RAIL_THICKNESS +
      (TableConfig.TABLE.LEFT - TableConfig.TABLE.RAIL_THICKNESS);
    const canvasHeight = TableConfig.BALL_LIST.TOP + BallConfig.ROW_SPACING + 50;

    // Get the canvas container width
    const canvasContainer = document.getElementById(id);
    if (!canvasContainer) return;

    const containerWidth = canvasContainer.clientWidth;
    const scale = containerWidth / canvasWidth;

    // Apply scale if container is smaller than canvas
    if (scale < 1) {
      canvas.value.width = canvasWidth * scale;
      canvas.value.height = canvasHeight * scale;
      ctx.value.scale(scale, scale);
    } else {
      canvas.value.width = canvasWidth;
      canvas.value.height = canvasHeight;
    }

    ctx.value.clearRect(0, 0, canvas.value.width, canvas.value.height);

    // Draw table layout
    tableLayoutDraw.draw();

    // Draw toolbox and available balls
    toolboxDraw.draw();

    // Draw table objects (balls, targeting balls, paths)
    inTableDraw.draw();
  };

  const {
    handleDragging,
    handleMouseMove,
    handleMouseUp,
    handleKeyUp,
    handleDeletePoint,
    handleClick,
    handlePathCreation
  } = useInTableActions(
    canvas,
    canvasId,
    balls,
    availableBalls,
    targetingBalls,
    targetingZones,
    textBoxes,
    speedBars,
    activePoint,
    drawCanvas,
    inTableDraw.updateGhostBallPosition,
    inTableDraw.deleteGhostBall
  );

  const initializeCanvas = (id = 'canvas-container') => {
    if (canvas.value) {
      ctx.value = canvas.value.getContext('2d');

      const canvasContainer = document.getElementById(id);

      if (canvasContainer) {
        canvasId.value = canvasContainer.id;
        resizeObserver = new ResizeObserver(() => {
          drawCanvas(canvasId.value);
        });
        resizeObserver.observe(canvasContainer);
      }

      drawCanvas(canvasId.value);
    }
  };

  const exportTableImage = () => {
    if (!canvas.value || !ctx.value) return;

    activePoint.value = null;

    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d');
    if (!tempCtx) return;

    const { TABLE } = TableConfig;
    const originalWidth = TABLE.WIDTH + TABLE.RAIL_THICKNESS * 2;
    const originalHeight = TABLE.HEIGHT + TABLE.RAIL_THICKNESS * 2;
    const padding = 1;

    // Set temp canvas to original size
    tempCanvas.width = originalWidth;
    tempCanvas.height = originalHeight;

    // Save current canvas state
    const originalCanvasWidth = canvas.value.width;
    const originalCanvasHeight = canvas.value.height;
    const originalScale = ctx.value.getTransform().a;

    // Temporarily reset canvas to full size
    canvas.value.width = originalWidth;
    canvas.value.height = originalHeight;

    // Draw at original size
    ctx.value.save();
    ctx.value.setTransform(
      1,
      0,
      0,
      1,
      -TABLE.CLOTH_COVER_THICKNESS * 2 - padding,
      -TABLE.CLOTH_COVER_THICKNESS * 2 - padding
    );

    tableLayoutDraw.draw();
    inTableDraw.draw();

    // Copy only the table portion
    tempCtx.drawImage(
      canvas.value,
      TABLE.LEFT - TABLE.RAIL_THICKNESS - TABLE.CLOTH_COVER_THICKNESS * 2 - padding,
      TABLE.TOP - TABLE.RAIL_THICKNESS - TABLE.CLOTH_COVER_THICKNESS * 2 - padding,
      originalWidth,
      originalHeight,
      0,
      0,
      originalWidth,
      originalHeight
    );

    // Restore canvas to original state
    canvas.value.width = originalCanvasWidth;
    canvas.value.height = originalCanvasHeight;
    ctx.value.restore();
    if (originalScale < 1) {
      ctx.value.scale(originalScale, originalScale);
    }
    drawCanvas(canvasId.value);

    const link = document.createElement('a');
    link.download = 'pool-table.png';
    link.href = tempCanvas.toDataURL(FILE_TYPE_ENUMS.PNG);
    link.click();
  };

  const generateTableImage = async () => {
    if (!canvas.value || !ctx.value) return;

    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d');
    if (!tempCtx) return;

    const { TABLE } = TableConfig;
    const originalWidth = TABLE.WIDTH + TABLE.RAIL_THICKNESS * 2;
    const originalHeight = TABLE.HEIGHT + TABLE.RAIL_THICKNESS * 2;
    const padding = 1;

    // Set temp canvas to original size
    tempCanvas.width = originalWidth;
    tempCanvas.height = originalHeight;

    // Save current canvas state
    const originalCanvasWidth = canvas.value.width;
    const originalCanvasHeight = canvas.value.height;
    const originalScale = ctx.value.getTransform().a;

    // Temporarily reset canvas to full size
    canvas.value.width = originalWidth;
    canvas.value.height = originalHeight;

    // Draw at original size
    ctx.value.save();
    ctx.value.setTransform(
      1,
      0,
      0,
      1,
      -TABLE.CLOTH_COVER_THICKNESS * 2 - padding,
      -TABLE.CLOTH_COVER_THICKNESS * 2 - padding
    );

    tableLayoutDraw.draw();
    inTableDraw.draw();

    // Copy only the table portion
    tempCtx.drawImage(
      canvas.value,
      TABLE.LEFT - TABLE.RAIL_THICKNESS - TABLE.CLOTH_COVER_THICKNESS * 2 - padding,
      TABLE.TOP - TABLE.RAIL_THICKNESS - TABLE.CLOTH_COVER_THICKNESS * 2 - padding,
      originalWidth,
      originalHeight,
      0,
      0,
      originalWidth,
      originalHeight
    );

    // Restore canvas to original state
    canvas.value.width = originalCanvasWidth;
    canvas.value.height = originalCanvasHeight;
    ctx.value.restore();
    if (originalScale < 1) {
      ctx.value.scale(originalScale, originalScale);
    }
    drawCanvas(canvasId.value);

    const imageBlob = await new Promise<Blob | null>(resolve =>
      tempCanvas.toBlob(blob => resolve(blob), FILE_TYPE_ENUMS.PNG)
    );
    if (!imageBlob) return;

    const imageFile = new File([imageBlob], 'diagram.png');

    return imageFile;
  };

  const getLayoutData = () => {
    return {
      balls: balls.value,
      targetingBalls: targetingBalls.value,
      targetingZones: targetingZones.value,
      textBoxes: textBoxes.value,
      speedBars: speedBars.value
    };
  };

  const saveCurrentLayout = () => {
    const currentLayout = getLayoutData();
    saveLayout(
      currentLayout.balls,
      currentLayout.targetingBalls,
      currentLayout.targetingZones,
      currentLayout.textBoxes,
      currentLayout.speedBars
    );
  };

  const loadLayout = (layoutData: TableLayoutData) => {
    balls.value = JSON.parse(JSON.stringify(layoutData.balls));
    targetingBalls.value = JSON.parse(JSON.stringify(layoutData.targetingBalls));
    targetingZones.value = JSON.parse(JSON.stringify(layoutData.targetingZones));
    textBoxes.value = JSON.parse(JSON.stringify(layoutData.textBoxes));
    speedBars.value = JSON.parse(JSON.stringify(layoutData.speedBars));
    activePoint.value = null;
    drawCanvas(canvasId.value);
  };

  const loadRackLayout = (type: string) => {
    balls.value = getRackedBallPosition(type);
    targetingBalls.value = [];
    targetingZones.value = [];
    textBoxes.value = [];
    speedBars.value = [];

    activePoint.value = null;
    drawCanvas(canvasId.value);
  };

  const loadFlippedLayout = (type: string) => {
    flipLayout(type);
    activePoint.value = null;
    drawCanvas(canvasId.value);
  };

  const cleanBallsData = (): Ball[] => {
    return balls.value
      .filter(ball => !ball.isGhost)
      .map(ball => {
        const cleanedBall = { ...ball };

        if (cleanedBall.paths.length > 0 && cleanedBall.paths[0].points.length > 0) {
          const lastPoint = cleanedBall.paths[0].points[cleanedBall.paths[0].points.length - 1];
          cleanedBall.x = lastPoint.x;
          cleanedBall.y = lastPoint.y;
        }

        cleanedBall.paths = [];

        return cleanedBall;
      });
  };

  const cleanTableDataForStepping = () => {
    balls.value = cleanBallsData();
    targetingBalls.value = [];
    targetingZones.value = [];
    textBoxes.value = [];
    speedBars.value = [];
  };

  onUnmounted(() => {
    if (resizeObserver) {
      resizeObserver.disconnect();
    }
  });

  return {
    activePoint,
    balls,
    canvas,
    savedLayouts,
    speedBars,
    tableBalls,
    tableData,
    targetingBalls,
    targetingZones,
    textBoxes,
    cleanTableDataForStepping,
    exportTableImage,
    fetchSavedLayouts: useLayoutStorage().fetchSavedLayouts,
    generateTableImage,
    getLayoutData,
    handleClick,
    handleDeletePoint,
    handleDragging,
    handleKeyUp,
    handleMouseMove,
    handleMouseUp,
    handlePathCreation,
    initializeCanvas,
    loadFlippedLayout,
    loadLayout,
    loadRackLayout,
    saveCurrentLayout
  };
};
