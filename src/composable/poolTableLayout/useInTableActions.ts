import { ref, type Ref } from 'vue';

import {
  type Ball,
  type TargetingBall,
  type ActivePoint,
  type DraggedItem,
  type TargetingZone,
  type TextBox,
  type SpeedBar
} from './config/poolTableConfig';

import { useMouseHandlers } from './actions/useMouseHandlers';
import { useKeyboardHandlers } from './actions/useKeyboardHandlers';
import { usePathActions } from './actions/usePathActions';

export const useInTableActions = (
  canvas: Ref<HTMLCanvasElement | null>,
  canvasId: Ref<string>,
  balls: Ref<Ball[]>,
  availableBalls: Ref<Ball[]>,
  targetingBalls: Ref<TargetingBall[]>,
  targetingZones: Ref<TargetingZone[]>,
  textBoxes: Ref<TextBox[]>,
  speedBars: Ref<SpeedBar[]>,
  activePoint: Ref<ActivePoint | null>,
  drawCanvas: (id: string) => void,
  updateGhostBallPosition: (x: number, y: number) => void,
  deleteGhostBall: () => void
) => {
  const isDragging = ref(false);
  const draggedObj = ref<DraggedItem | null>(null);

  const { handleDragging, handleMouseMove, handleMouseUp, handleClick } = useMouseHandlers(
    canvas,
    canvasId,
    balls,
    availableBalls,
    targetingBalls,
    targetingZones,
    textBoxes,
    speedBars,
    isDragging,
    draggedObj,
    activePoint,
    drawCanvas,
    updateGhostBallPosition,
    deleteGhostBall
  );

  const { handleDeletePoint, handleKeyUp } = useKeyboardHandlers(
    canvasId,
    balls,
    targetingBalls,
    targetingZones,
    textBoxes,
    speedBars,
    activePoint,
    drawCanvas
  );

  const { handlePathCreation } = usePathActions(canvasId, balls, activePoint, targetingBalls, drawCanvas);

  return {
    handleDragging,
    handleMouseMove,
    handleMouseUp,
    handleClick,
    handleKeyUp,
    handlePathCreation,
    handleDeletePoint
  };
};
