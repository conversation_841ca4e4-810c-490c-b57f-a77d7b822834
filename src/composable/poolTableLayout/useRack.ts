import { TableConfig } from './config/poolTableConfig';
import { BallConfig } from './config/ballConfig';

import { type BallPosition } from './config/types';

export const useRack = () => {
  const eightBallConfig = [1, 2, 3, 9, 8, 10, 11, 4, 5, 12, 6, 13, 14, 7, 15];
  const nineBallConfig = [1, 2, 3, 4, 9, 5, 6, 7, 8];
  const tenBallConfig = [1, 2, 3, 4, 10, 5, 6, 7, 8, 9];

  const getRackBallNumber = (rackType: string): number => {
    switch (rackType) {
      case '8-ball':
        return 15;
      case '9-ball-low':
      case '9-ball-high':
        return 9;
      case '10-ball':
        return 10;
      default:
        return 15;
    }
  };

  const getApexBallPosition = (rackType: string): BallPosition => {
    // 3 balls that are next to each other will form an equilateral triangle
    // The height of the equilateral triangle is given by the formula:
    // h = ½(√3a), where h is the height of equilateral triangle and a is the side length
    // a = 2 * radius = diameter of the ball
    // https://www.cuemath.com/geometry/height-of-equilateral-triangle/
    const triangleHeight = Math.sqrt(3) * BallConfig.DIAMETER;

    const basePosition = {
      x: TableConfig.TABLE.LEFT + (TableConfig.TABLE.WIDTH * 3) / 4 - (rackType === '9-ball-high' ? triangleHeight : 0),
      y: TableConfig.TABLE.TOP + TableConfig.TABLE.HEIGHT / 2
    };

    return basePosition;
  };

  const getTrianglePositions = (apexBallPosition: BallPosition, rowNumber: number): BallPosition[] => {
    const positions: BallPosition[] = [];
    const ballDiameter = BallConfig.DIAMETER;
    const rowSpacing = (Math.sqrt(3) / 2) * ballDiameter;

    for (let row = 0; row < rowNumber; row++) {
      const ballsInRow = row + 1;

      const x = apexBallPosition.x + row * rowSpacing;
      const totalRowHeight = (ballsInRow - 1) * ballDiameter;
      const topY = apexBallPosition.y - totalRowHeight / 2;

      for (let col = 0; col < ballsInRow; col++) {
        const y = topY + col * ballDiameter;
        positions.push({ x, y });
      }
    }

    return positions;
  };

  const getDiamondPositions = (apexBallPosition: BallPosition): BallPosition[] => {
    const positions: BallPosition[] = [];
    const ballDiameter = BallConfig.DIAMETER;
    const rowSpacing = (Math.sqrt(3) / 2) * ballDiameter;

    const rowCounts = [1, 2, 3, 2, 1];

    for (let r = 0; r < rowCounts.length; r++) {
      const ballsInRow = rowCounts[r];
      const x = apexBallPosition.x + r * rowSpacing;

      const rowHeight = (ballsInRow - 1) * ballDiameter;
      const topY = apexBallPosition.y - rowHeight / 2;

      for (let c = 0; c < ballsInRow; c++) {
        const y = topY + c * ballDiameter;
        positions.push({ x, y });
      }
    }

    return positions;
  };

  const getRackedBallPosition = (rackType: string) => {
    const rackedBalls = BallConfig.createRackedBalls(rackType);
    const apexBallPosition = getApexBallPosition(rackType);
    let positions: BallPosition[] = [];
    let config: number[] = [];

    switch (rackType) {
      case '8-ball':
        // triangle shape with 5 rows
        positions = getTrianglePositions(apexBallPosition, 5);
        config = eightBallConfig;
        break;
      case '10-ball':
        // triangle shape with 4 rows
        positions = getTrianglePositions(apexBallPosition, 4);
        config = tenBallConfig;
        break;
      case '9-ball-low':
      case '9-ball-high':
        // diamond shape
        positions = getDiamondPositions(apexBallPosition);
        config = nineBallConfig;
        break;
      default:
        positions = getTrianglePositions(apexBallPosition, 5);
        config = eightBallConfig;
        break;
    }

    config.forEach((ballNumber, i) => {
      const ball = rackedBalls.find(b => b.number === ballNumber);
      if (ball && positions[i]) {
        ball.x = positions[i].x;
        ball.y = positions[i].y;
      }
    });

    return rackedBalls;
  };

  return {
    getRackBallNumber,
    getRackedBallPosition
  };
};
