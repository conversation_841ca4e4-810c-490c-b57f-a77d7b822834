import { useRack } from '../useRack';

export const BallConfig = {
  DIAMETER: 21,
  get RADIUS() {
    return this.DIAMETER / 2;
  },
  get SPACING() {
    return this.DIAMETER * 1.3;
  },
  get ROW_SPACING() {
    return this.DIAMETER * 1.5;
  },
  COLORS: ['#FDB022', '#3B82F6', '#EF4444', '#FE68AE', '#F97316', '#006515', '#953F02'],

  TOOLBOX: {
    SCALE: 1.5,
    SPACING_MULTIPLIER: 1.1,
    PADDING_MULTIPLIER: 0.5
  },

  GHOST: {
    COLOR: 'rgba(255, 255, 255, 0.3)',
    BORDER_COLOR: 'rgba(255, 255, 255, 0.8)',
    LINE_WIDTH: 1.5,
    DASH: [3, 3]
  },

  HIGHLIGHT: {
    COLOR: '#22E93D'
  },

  SHADOW: {
    COLOR: 'rgba(0, 0, 0, 0.15)',
    BLUR: 4,
    OFFSET: 2
  },

  NUMBER: {
    FONT_SCALE: 0.75,
    BACKGROUND_SCALE: 0.65,
    CUE_DOT_RADIUS: 2.5
  },

  createInitialBalls(tableLeft: number, tableWidth: number, listTop: number) {
    return [
      {
        id: 'ghost_ball',
        number: null,
        color: '#FFFFFF',
        x: tableLeft + tableWidth / 2 - 8.5 * this.SPACING,
        y: listTop,
        radius: this.RADIUS,
        isInTable: false,
        isGhost: true,
        isCue: false
      },
      {
        id: 'cue_ball',
        number: null,
        color: '#FFFFFF',
        x: tableLeft + tableWidth / 2 - 7.5 * this.SPACING,
        y: listTop,
        radius: this.RADIUS,
        isInTable: false,
        isCue: true
      },
      ...Array.from({ length: 15 }, (_, i) => ({
        id: `ball_${i + 1}`,
        number: i + 1,
        color:
          i === 7
            ? '#000000' // 8 ball is black
            : i >= 8
              ? this.COLORS[i - 8] // balls 9-15 use colors starting from index 0
              : this.COLORS[i], // balls 1-7 use colors as is
        x: tableLeft + tableWidth / 2 - 6.5 * this.SPACING + i * this.SPACING,
        y: listTop,
        radius: this.RADIUS,
        isInTable: false,
        isCue: false
      }))
    ];
  },

  createRackedBalls(rackType: string) {
    const { getRackBallNumber } = useRack();

    return [
      ...Array.from({ length: getRackBallNumber(rackType) }, (_, i) => ({
        id: `ball_${Math.random().toString(36).substring(2, 15)}`,
        number: i + 1,
        color:
          i === 7
            ? '#000000' // 8 ball is black
            : i >= 8
              ? this.COLORS[i - 8] // balls 9-15 use colors starting from index 0
              : this.COLORS[i], // balls 1-7 use colors as is
        x: 0,
        y: 0,
        radius: this.RADIUS,
        isInTable: true,
        isGhost: false,
        isCue: false,
        paths: []
      }))
    ];
  }
} as const;
