import { type TargetingZone } from './types';

export const TargetingZoneConfig = {
  DEFAULT: {
    size: 60,
    cornerRadius: 7
  },

  STYLE: {
    color: 'rgba(255, 255, 255, 0.2)',
    borderColor: '#FFFFFF',
    borderWidth: 1,
    cornerColor: '#FFFFFF',
    cornerBorderColor: '#000000'
  },

  createTargetingZone(x: number, y: number): TargetingZone {
    const size = this.DEFAULT.size;
    const halfSize = size / 2;

    return {
      id: `zone_${Math.random().toString(36).substring(2, 15)}`,
      x,
      y,
      size: this.DEFAULT.size,
      cornerRadius: this.DEFAULT.cornerRadius,
      isInTable: true,
      corners: {
        topLeft: { x: x - halfSize, y: y - halfSize },
        topRight: { x: x + halfSize, y: y - halfSize },
        bottomRight: { x: x + halfSize, y: y + halfSize },
        bottomLeft: { x: x - halfSize, y: y + halfSize }
      }
    };
  }
} as const;
