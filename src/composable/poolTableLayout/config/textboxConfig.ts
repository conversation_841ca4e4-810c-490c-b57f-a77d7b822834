import { type TextBox } from './types';

export const TextBoxConfig = {
  DEFAULT: {
    color: '#000000',
    textValue: 'Text',
    rotation: 0,
    width: 32,
    height: 16,
    fontSize: 16,
    fontFamily: 'Arial',
    toolboxText: 'Text',
    rotationStep: 5
  },

  ICON: {
    width: 20,
    height: 20
  },

  createTextBox(x: number, y: number): TextBox {
    return {
      id: `text_${Math.random().toString(36).substring(2, 15)}`,
      x,
      y,
      isInTable: true,
      color: this.DEFAULT.color,
      value: this.DEFAULT.textValue,
      isActive: false,
      rotation: this.DEFAULT.rotation
    };
  }
} as const;
