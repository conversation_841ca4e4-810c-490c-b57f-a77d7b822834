export interface BallPosition {
  x: number;
  y: number;
}

export interface ActivePoint {
  ball: Ball | TargetingBall;
  isPoint: boolean;
  isTargetingBall?: boolean;
  pathIndex: number;
  pointIndex: number;
  isSpeedBar?: boolean;
  isZone?: boolean;
  isZoneCorner?: boolean;
  isTextBox?: boolean;
  cornerIndex?: number;
  zone?: TargetingZone;
  textBox?: TextBox;
  speedBar?: SpeedBar;
}

export interface BallPath {
  ballId: string;
  points: BallPosition[];
  color: string;
}

export interface Ball {
  id: string;
  number: number | null;
  color: string;
  x: number;
  y: number;
  radius: number;
  isInTable: boolean;
  isGhost?: boolean;
  isCue?: boolean;
  paths: BallPath[];
}

export interface Point {
  x: number;
  y: number;
}

export interface TargetingBall {
  id: string;
  x: number;
  y: number;
  radius: number;
  isInTable: boolean;
  color: string;
  crosshair: {
    color: string;
    width: number;
  };
  centerDot: {
    color: string;
    radius: number;
    x: number;
    y: number;
  };
}

export interface TableLayoutData {
  id: string;
  timestamp: number;
  balls: Ball[];
  targetingBalls: TargetingBall[] | null;
  targetingZones: TargetingZone[] | null;
  textBoxes: TextBox[] | null;
  speedBars: SpeedBar[] | null;
}

export interface WrapperConfig {
  top: number;
  padding: number;
  width: number;
  height: number;
}

export interface WrapperPosition {
  left: number;
  top: number;
  padding: number;
  spacing: number;
  width: number;
}

export interface TargetingZone {
  id: string;
  x: number;
  y: number;
  size: number;
  cornerRadius: number;
  isInTable: boolean;
  corners: {
    topLeft: BallPosition;
    topRight: BallPosition;
    bottomRight: BallPosition;
    bottomLeft: BallPosition;
  };
}

export interface SpeedBar {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  isInTable: boolean;
  isActive: boolean;
  value: number;
  color: string;
  backgroundColor: string;
  borderColor: string;
}

export interface TextBox {
  id: string;
  x: number;
  y: number;
  isInTable: boolean;
  color: string; // TODO: able to change text color
  value: string;
  isActive: boolean;
  rotation: number;
}

export interface DiagramItem {
  balls: Ball[];
  targetingBalls: TargetingBall[];
  targetingZones: TargetingZone[];
  textBoxes: TextBox[];
  speedBars: SpeedBar[];
}

interface DraggedBallItem {
  type: 'ball';
  item: Ball;
}

interface DraggedTargetingItem {
  type: 'targeting';
  item: TargetingBall;
}

interface DraggedCenterDotItem {
  type: 'centerDot';
  item: TargetingBall;
}

interface DraggedPathPointItem {
  type: 'pathPoint';
  item: Ball;
  pathIndex: number;
  pointIndex: number;
}

interface DraggedTargetingZoneItem {
  type: 'targetingZone';
  item: TargetingZone;
}

interface DraggedTargetingZoneCornerItem {
  type: 'targetingZoneCorner';
  item: TargetingZone;
  cornerIndex: number;
}

interface DraggedSpeedBarItem {
  type: 'speedBar';
  item: SpeedBar;
}

interface DraggedSpeedBarTriangleMarkItem {
  type: 'speedBarTriangleMark';
  item: SpeedBar;
}

interface DraggedTextBoxItem {
  type: 'textBox';
  item: TextBox;
}

export type DraggedItem =
  | DraggedBallItem
  | DraggedTargetingItem
  | DraggedCenterDotItem
  | DraggedPathPointItem
  | DraggedTargetingZoneItem
  | DraggedTargetingZoneCornerItem
  | DraggedSpeedBarItem
  | DraggedTextBoxItem
  | DraggedSpeedBarTriangleMarkItem;
