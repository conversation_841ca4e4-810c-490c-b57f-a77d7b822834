export const SpeedBarConfig = {
  DEFAULT: {
    width: 150,
    height: 40,
    color: '#FF4444',
    backgroundColor: '#374151',
    borderColor: '#FFFFFF',
    activeColor: '#22E93D',
    padding: 4,
    borderWidth: 2,
    segmentSpacing: 1,
    segmentCount: 10,
    value: 8
  },

  TRIANGLE_MARK: {
    backgroundColor: '#FFF',
    hitBoxBuffer: 2,
    size: 0.9
  },

  createSpeedBar(x: number, y: number) {
    return {
      id: `speedbar_${Math.random().toString(36).substring(2, 15)}`,
      x,
      y,
      width: this.DEFAULT.width,
      height: this.DEFAULT.height,
      isInTable: true,
      isActive: false,
      value: this.DEFAULT.value,
      color: this.DEFAULT.color,
      backgroundColor: this.DEFAULT.backgroundColor,
      borderColor: this.DEFAULT.borderColor
    };
  }
} as const;
