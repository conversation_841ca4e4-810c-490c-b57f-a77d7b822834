import { BallConfig } from './ballConfig';

export const ToolboxConfig = {
  TOOLS: {
    TARGET: {
      id: 'target_tool',
      color: '#FFFFFF',
      radius: 30,
      icon: {
        color: '#FF4444',
        size: 4
      }
    }
  },

  CONSTANTS: {
    WRAPPER_OFFSET: 100,
    TARGETING_TOOL_OFFSET: 50,
    TARGETING_ZONE_OFFSET: 180,
    BALL_SCALE: BallConfig.TOOLBOX.SCALE,
    BALL_SPACING_MULTIPLIER: BallConfig.TOOLBOX.SPACING_MULTIPLIER,
    PADDING_MULTIPLIER: BallConfig.TOOLBOX.PADDING_MULTIPLIER
  },

  COLORS: {
    WRAPPER: '#374151',
    BALL_WRAPPER: '#2A3443'
  },

  DIMENSIONS: {
    WRAPPER_HEIGHT: 120,
    WRAPPER_RADIUS: 10,
    BALL_LIST_TOP_OFFSET: 140,
    TARGETING_TOOL_Y_OFFSET: 160
  }
} as const;
