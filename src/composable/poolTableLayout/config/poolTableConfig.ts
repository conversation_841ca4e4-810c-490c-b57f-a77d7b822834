import { BallConfig } from './ballConfig';

export const TableConfig = {
  TABLE: {
    LEFT: 80,
    TOP: 80,
    WIDTH: 944,
    HEIGHT: 944 / 2,
    RAIL_THICKNESS: 55,
    CLOTH_COVER_THICKNESS: 12
  },

  WATERMARK: {
    FONT: 'bold 60px Arial',
    COLOR: 'rgba(255, 255, 255, 0.1)',
    TEXT: 'vibico.co'
  },

  COLORS: {
    CLOTH: '#0AAEE1',
    CLOTH_COVER: '#085670',
    RAIL_PRIMARY: '#505050',
    RAIL_SECONDARY: '#535353',
    POCKET: '#222',
    POCKET_GRADIENT: {
      START: '#28282B',
      END: '#000'
    }
  },

  POCKET: {
    SPACE: 3,
    get RADIUS() {
      return BallConfig.RADIUS * 2.4;
    }
  },

  GRID: {
    COLOR: 'rgba(255, 255, 255, 0.3)',
    LINE_WIDTH: 1.4,
    DASH_PATTERN: [4, 4]
  },

  BALL_LIST: {
    get TOP() {
      return TableConfig.TABLE.TOP + TableConfig.TABLE.HEIGHT + 140;
    },
    createInitialBalls() {
      return BallConfig.createInitialBalls(TableConfig.TABLE.LEFT, TableConfig.TABLE.WIDTH, this.TOP);
    }
  }
} as const;

export {
  type ActivePoint,
  type Ball,
  type BallPath,
  type BallPosition,
  type DiagramItem,
  type DraggedItem,
  type Point,
  type SpeedBar,
  type TableLayoutData,
  type TargetingBall,
  type TargetingZone,
  type TextBox,
  type WrapperConfig,
  type WrapperPosition
} from './types';

export { BallConfig } from './ballConfig';
export { SpeedBarConfig } from './speedBarConfig';
export { TargetingBallConfig } from './targetingBallConfig';
export { TargetingZoneConfig } from './targetingZoneConfig';
export { TextBoxConfig } from './textboxConfig';
export { ToolboxConfig } from './toolboxConfig';
