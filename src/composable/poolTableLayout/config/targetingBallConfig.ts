import { type TargetingBall } from './types';

export const TargetingBallConfig = {
  DEFAULT: {
    color: '#FFFFFF',
    radius: 30,
    crosshair: {
      color: '#000000',
      width: 1
    },
    centerDot: {
      color: '#FF4444',
      radius: 5
    }
  },

  createTargetingBall(x: number, y: number): TargetingBall {
    return {
      id: `target_${Math.random().toString(36).substring(2, 15)}`,
      x,
      y,
      radius: this.DEFAULT.radius,
      isInTable: true,
      color: this.DEFAULT.color,
      crosshair: { ...this.DEFAULT.crosshair },
      centerDot: { ...this.DEFAULT.centerDot, x: 0, y: 0 }
    };
  }
} as const;
