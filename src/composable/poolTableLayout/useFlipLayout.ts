import { type Ref } from 'vue';
import {
  TableConfig,
  type Ball,
  type SpeedBar,
  type TargetingBall,
  type TargetingZone,
  type TextBox
} from './config/poolTableConfig';

export const useFlipLayout = (
  balls: Ref<Ball[]>,
  speedBars: Ref<SpeedBar[]>,
  targetingBalls: Ref<TargetingBall[]>,
  targetingZones: Ref<TargetingZone[]>,
  textBoxes: Ref<TextBox[]>
) => {
  const { TABLE } = TableConfig;

  const tableWidth = TABLE.WIDTH;
  const tableHeight = TABLE.HEIGHT;
  const tableCenterX = TABLE.LEFT + tableWidth / 2;
  const tableCenterY = TABLE.TOP + tableHeight / 2;

  const flipPoint = (x: number, y: number, isHorizontal: boolean): { x: number; y: number } => {
    if (isHorizontal) {
      const distanceFromCenter = x - tableCenterX;
      return { x: tableCenterX - distanceFromCenter, y };
    } else {
      const distanceFromCenter = y - tableCenterY;
      return { x, y: tableCenterY - distanceFromCenter };
    }
  };

  const flipTargetingBallCenterPoint = (x: number, y: number): { x: number; y: number } => {
    return { x: -x, y };
  };

  const flipBall = (ball: Ball, isHorizontal: boolean): Ball => {
    const flippedPosition = flipPoint(ball.x, ball.y, isHorizontal);

    return {
      ...ball,
      x: flippedPosition.x,
      y: flippedPosition.y,
      paths: ball.paths.map(path => ({
        ...path,
        points: path.points.map(point => flipPoint(point.x, point.y, isHorizontal))
      }))
    };
  };

  const flipTargetingBall = (ball: TargetingBall, isHorizontal: boolean) => {
    const flippedPosition = flipPoint(ball.x, ball.y, isHorizontal);
    const centerDotFlipped = flipTargetingBallCenterPoint(ball.centerDot.x, ball.centerDot.y);

    return {
      ...ball,
      x: flippedPosition.x,
      y: flippedPosition.y,
      centerDot: {
        ...ball.centerDot,
        x: centerDotFlipped.x,
        y: centerDotFlipped.y
      }
    };
  };

  const flipTargetingZone = (zone: TargetingZone, isHorizontal: boolean) => {
    const flippedPosition = flipPoint(zone.x, zone.y, isHorizontal);
    const flippedCorners = {
      topLeft: flipPoint(zone.corners.topLeft.x, zone.corners.topLeft.y, isHorizontal),
      topRight: flipPoint(zone.corners.topRight.x, zone.corners.topRight.y, isHorizontal),
      bottomRight: flipPoint(zone.corners.bottomRight.x, zone.corners.bottomRight.y, isHorizontal),
      bottomLeft: flipPoint(zone.corners.bottomLeft.x, zone.corners.bottomLeft.y, isHorizontal)
    };

    return {
      ...zone,
      x: flippedPosition.x,
      y: flippedPosition.y,
      corners: flippedCorners
    };
  };

  const flipTextBox = (textBox: TextBox, isHorizontal: boolean) => {
    const flippedPosition = flipPoint(textBox.x, textBox.y, isHorizontal);

    return {
      ...textBox,
      x: flippedPosition.x,
      y: flippedPosition.y,
      rotation: isHorizontal ? -textBox.rotation : textBox.rotation
    };
  };

  const flipSpeedBar = (speedBar: SpeedBar, isHorizontal: boolean) => {
    const flippedPosition = flipPoint(speedBar.x, speedBar.y, isHorizontal);

    return {
      ...speedBar,
      x: flippedPosition.x,
      y: flippedPosition.y
    };
  };

  const flipLayout = (type: string) => {
    const isHorizontal = type === 'horizontally';

    balls.value = balls.value.map(ball => flipBall(ball, isHorizontal));
    targetingBalls.value = targetingBalls.value.map(ball => flipTargetingBall(ball, isHorizontal));
    targetingZones.value = targetingZones.value.map(zone => flipTargetingZone(zone, isHorizontal));
    textBoxes.value = textBoxes.value.map(textBox => flipTextBox(textBox, isHorizontal));
    speedBars.value = speedBars.value.map(speedBar => flipSpeedBar(speedBar, isHorizontal));
  };

  return { flipLayout };
};
