import { type Ref } from 'vue';
import {
  type ActivePoint,
  type Ball,
  type TargetingBall,
  type TargetingZone,
  type TextBox,
  type SpeedBar
} from './config/poolTableConfig';

import { useBallDraw } from './draw/useBallDraw';
import { useTargetingBallDraw } from './draw/useTargetingBallDraw';
import { usePathDraw } from './draw/usePathDraw';
import { useGhostBallDraw } from './draw/useGhostBallDraw';
import { useTargetingZoneDraw } from './draw/useTargetingZoneDraw';
import { useTextBoxDraw } from './draw/useTextBoxDraw';
import { useSpeedBarDraw } from './draw/useSpeedBarDraw';

export const useInTableDraw = (
  ctx: Ref<CanvasRenderingContext2D | null>,
  balls: Ref<Ball[]>,
  targetingBalls: Ref<TargetingBall[]>,
  activePoint: Ref<ActivePoint | null>,
  targetingZones: Ref<TargetingZone[]>,
  textBoxes: Ref<TextBox[]>,
  speedBars: Ref<SpeedBar[]>
) => {
  const { renderBall } = useBallDraw(ctx, activePoint);
  const { drawTargetingBall } = useTargetingBallDraw(ctx, activePoint);
  const { drawBallPaths } = usePathDraw(ctx, activePoint);
  const { drawGhostBall, updateGhostBallPosition, deleteGhostBall } = useGhostBallDraw(
    ctx,
    activePoint,
    balls,
    targetingBalls,
    textBoxes,
    speedBars
  );
  const { drawTargetingZones } = useTargetingZoneDraw(ctx, targetingZones, activePoint);
  const { drawTextBox } = useTextBoxDraw(ctx, activePoint);
  const { drawSpeedBar } = useSpeedBarDraw(ctx, activePoint);

  const draw = () => {
    drawTargetingZones();
    speedBars.value.forEach(speedBar => drawSpeedBar(speedBar));
    balls.value.forEach(renderBall);
    targetingBalls.value.filter(ball => ball.isInTable).forEach(drawTargetingBall);
    drawBallPaths(balls.value);
    textBoxes.value.forEach(textBox => drawTextBox(textBox));
    drawGhostBall();
  };

  return {
    deleteGhostBall,
    draw,
    updateGhostBallPosition
  };
};
