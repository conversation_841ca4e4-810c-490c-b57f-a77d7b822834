import { type Ref } from 'vue';
import { TableConfig, BallConfig, type Point } from './config/poolTableConfig';

export const useTableLayoutDraw = (ctx: Ref<CanvasRenderingContext2D | null>) => {
  const drawPath = (points: Point[]) => {
    if (!ctx.value) return;
    ctx.value.beginPath();
    points.forEach(({ x, y }, index) => {
      index === 0 ? ctx.value!.moveTo(x, y) : ctx.value!.lineTo(x, y);
    });
    ctx.value.closePath();
    ctx.value.fill();
  };

  const createRailGradient = (startY: number, endY: number) => {
    if (!ctx.value) return null;
    const { TABLE, COLORS } = TableConfig;
    const gradient = ctx.value.createLinearGradient(TABLE.LEFT, startY, TABLE.LEFT, endY);
    gradient.addColorStop(0, COLORS.RAIL_PRIMARY);
    gradient.addColorStop(0.5, COLORS.RAIL_SECONDARY);
    gradient.addColorStop(1, COLORS.RAIL_PRIMARY);
    return gradient;
  };

  const drawRails = () => {
    if (!ctx.value) return;
    const { TABLE, COLORS } = TableConfig;
    const chamferSize = 25;

    // Draw top rail
    const topGradient = createRailGradient(TABLE.TOP - TABLE.RAIL_THICKNESS, TABLE.TOP);
    if (topGradient) {
      ctx.value.fillStyle = topGradient;
      drawPath([
        { x: TABLE.LEFT - TABLE.RAIL_THICKNESS + chamferSize, y: TABLE.TOP - TABLE.RAIL_THICKNESS },
        { x: TABLE.LEFT + TABLE.WIDTH + TABLE.RAIL_THICKNESS - chamferSize, y: TABLE.TOP - TABLE.RAIL_THICKNESS },
        { x: TABLE.LEFT + TABLE.WIDTH + TABLE.RAIL_THICKNESS, y: TABLE.TOP - TABLE.RAIL_THICKNESS + chamferSize },
        { x: TABLE.LEFT + TABLE.WIDTH + TABLE.RAIL_THICKNESS, y: TABLE.TOP },
        { x: TABLE.LEFT - TABLE.RAIL_THICKNESS, y: TABLE.TOP },
        { x: TABLE.LEFT - TABLE.RAIL_THICKNESS, y: TABLE.TOP - TABLE.RAIL_THICKNESS + chamferSize }
      ]);
    }

    // Draw bottom rail
    const bottomGradient = createRailGradient(
      TABLE.TOP + TABLE.HEIGHT,
      TABLE.TOP + TABLE.HEIGHT + TABLE.RAIL_THICKNESS
    );
    if (bottomGradient) {
      ctx.value.fillStyle = bottomGradient;
      drawPath([
        { x: TABLE.LEFT - TABLE.RAIL_THICKNESS + chamferSize, y: TABLE.TOP + TABLE.HEIGHT + TABLE.RAIL_THICKNESS },
        {
          x: TABLE.LEFT + TABLE.WIDTH + TABLE.RAIL_THICKNESS - chamferSize,
          y: TABLE.TOP + TABLE.HEIGHT + TABLE.RAIL_THICKNESS
        },
        {
          x: TABLE.LEFT + TABLE.WIDTH + TABLE.RAIL_THICKNESS,
          y: TABLE.TOP + TABLE.HEIGHT + TABLE.RAIL_THICKNESS - chamferSize
        },
        { x: TABLE.LEFT + TABLE.WIDTH + TABLE.RAIL_THICKNESS, y: TABLE.TOP + TABLE.HEIGHT },
        { x: TABLE.LEFT - TABLE.RAIL_THICKNESS, y: TABLE.TOP + TABLE.HEIGHT },
        { x: TABLE.LEFT - TABLE.RAIL_THICKNESS, y: TABLE.TOP + TABLE.HEIGHT + TABLE.RAIL_THICKNESS - chamferSize }
      ]);
    }

    // Draw side rails
    ctx.value.fillStyle = COLORS.RAIL_PRIMARY;
    ctx.value.fillRect(TABLE.LEFT - TABLE.RAIL_THICKNESS, TABLE.TOP, TABLE.RAIL_THICKNESS, TABLE.HEIGHT);
    ctx.value.fillRect(TABLE.LEFT + TABLE.WIDTH, TABLE.TOP, TABLE.RAIL_THICKNESS, TABLE.HEIGHT);
  };

  const drawCloth = () => {
    if (!ctx.value) return;
    const { TABLE, COLORS } = TableConfig;
    const chamferSize = 22;

    ctx.value.fillStyle = COLORS.CLOTH;
    drawPath([
      { x: TABLE.LEFT + chamferSize, y: TABLE.TOP },
      { x: TABLE.LEFT + TABLE.WIDTH - chamferSize, y: TABLE.TOP },
      { x: TABLE.LEFT + TABLE.WIDTH, y: TABLE.TOP + chamferSize },
      { x: TABLE.LEFT + TABLE.WIDTH, y: TABLE.TOP + TABLE.HEIGHT - chamferSize },
      { x: TABLE.LEFT + TABLE.WIDTH - chamferSize, y: TABLE.TOP + TABLE.HEIGHT },
      { x: TABLE.LEFT + chamferSize, y: TABLE.TOP + TABLE.HEIGHT },
      { x: TABLE.LEFT, y: TABLE.TOP + TABLE.HEIGHT - chamferSize },
      { x: TABLE.LEFT, y: TABLE.TOP + chamferSize }
    ]);
  };

  const drawDiamond = (x: number, y: number, size: number) => {
    if (!ctx.value) return;
    drawPath([
      { x, y: y - size },
      { x: x + size, y },
      { x, y: y + size },
      { x: x - size, y }
    ]);
  };

  const drawDiamonds = () => {
    if (!ctx.value) return;
    const { TABLE } = TableConfig;
    const diamondSize = 8;

    ctx.value.fillStyle = '#ffffff';

    // Draw top and bottom rail diamonds
    const topBottomSpacing = TABLE.WIDTH / 8;
    [-3, -2, -1, 1, 2, 3].forEach(i => {
      // Top rail diamonds
      drawDiamond(
        TABLE.LEFT + TABLE.WIDTH / 2 + i * topBottomSpacing,
        TABLE.TOP - TABLE.RAIL_THICKNESS / 2,
        diamondSize
      );
      // Bottom rail diamonds
      drawDiamond(
        TABLE.LEFT + TABLE.WIDTH / 2 + i * topBottomSpacing,
        TABLE.TOP + TABLE.HEIGHT + TABLE.RAIL_THICKNESS / 2,
        diamondSize
      );
    });

    // Draw side diamonds
    const sideDiamonds = 3;
    const sideSpacing = TABLE.HEIGHT / (sideDiamonds + 1);
    Array.from({ length: sideDiamonds }, (_, i) => i + 1).forEach(i => {
      // Left rail
      drawDiamond(TABLE.LEFT - TABLE.RAIL_THICKNESS / 2, TABLE.TOP + i * sideSpacing, diamondSize);
      // Right rail
      drawDiamond(TABLE.LEFT + TABLE.WIDTH + TABLE.RAIL_THICKNESS / 2, TABLE.TOP + i * sideSpacing, diamondSize);
    });
  };

  const getPocketPositions = () => {
    const { TABLE } = TableConfig;
    return [
      { x: TABLE.LEFT, y: TABLE.TOP },
      { x: TABLE.LEFT + TABLE.WIDTH / 2, y: TABLE.TOP },
      { x: TABLE.LEFT + TABLE.WIDTH, y: TABLE.TOP },
      { x: TABLE.LEFT, y: TABLE.TOP + TABLE.HEIGHT },
      { x: TABLE.LEFT + TABLE.WIDTH / 2, y: TABLE.TOP + TABLE.HEIGHT },
      { x: TABLE.LEFT + TABLE.WIDTH, y: TABLE.TOP + TABLE.HEIGHT }
    ];
  };

  const isInsidePocket = (x: number, y: number, pocketRadius: number) => {
    const pocketRadiusSq = pocketRadius * pocketRadius;
    return getPocketPositions().some(pocket => {
      const dx = x - pocket.x;
      const dy = y - pocket.y;
      return dx * dx + dy * dy < pocketRadiusSq;
    });
  };

  const drawGridLine = (start: Point, end: Point) => {
    if (!ctx.value) return;
    ctx.value.beginPath();
    ctx.value.moveTo(start.x, start.y);
    ctx.value.lineTo(end.x, end.y);
    ctx.value.stroke();
  };

  const drawGrid = () => {
    if (!ctx.value) return;
    const { TABLE, GRID } = TableConfig;
    const pocketRadius = BallConfig.RADIUS * 2.2;

    ctx.value.strokeStyle = GRID.COLOR;
    ctx.value.lineWidth = GRID.LINE_WIDTH;
    ctx.value.setLineDash(GRID.DASH_PATTERN);

    // Draw vertical lines
    const topBottomSpacing = TABLE.WIDTH / 8;
    [-3, -2, -1, 0, 1, 2, 3].forEach(i => {
      const x = TABLE.LEFT + TABLE.WIDTH / 2 + i * topBottomSpacing;
      const startY =
        i === 0 ? TABLE.TOP : isInsidePocket(x, TABLE.TOP, pocketRadius) ? TABLE.TOP + pocketRadius : TABLE.TOP;
      const endY =
        i === 0
          ? TABLE.TOP + TABLE.HEIGHT
          : isInsidePocket(x, TABLE.TOP + TABLE.HEIGHT, pocketRadius)
            ? TABLE.TOP + TABLE.HEIGHT - pocketRadius
            : TABLE.TOP + TABLE.HEIGHT;

      drawGridLine({ x, y: startY }, { x, y: endY });
    });

    // Draw horizontal lines
    const sideSpacing = TABLE.HEIGHT / 4;
    Array.from({ length: 3 }, (_, i) => i + 1).forEach(i => {
      const y = TABLE.TOP + i * sideSpacing;
      const startX = isInsidePocket(TABLE.LEFT, y, pocketRadius) ? TABLE.LEFT + pocketRadius : TABLE.LEFT;
      const endX = isInsidePocket(TABLE.LEFT + TABLE.WIDTH, y, pocketRadius)
        ? TABLE.LEFT + TABLE.WIDTH - pocketRadius
        : TABLE.LEFT + TABLE.WIDTH;

      drawGridLine({ x: startX, y }, { x: endX, y });
    });

    ctx.value.setLineDash([]);
  };

  const drawPocket = (position: Point) => {
    if (!ctx.value) return;
    const { POCKET, COLORS } = TableConfig;

    // Base pocket
    ctx.value.beginPath();
    ctx.value.arc(position.x, position.y, POCKET.RADIUS, 0, Math.PI * 2);
    ctx.value.fillStyle = COLORS.POCKET;
    ctx.value.fill();

    // Pocket gradient
    const gradient = ctx.value.createRadialGradient(
      position.x,
      position.y,
      0,
      position.x,
      position.y,
      POCKET.RADIUS * 1.2
    );
    gradient.addColorStop(0, COLORS.POCKET_GRADIENT.START);
    gradient.addColorStop(1, COLORS.POCKET_GRADIENT.END);
    ctx.value.fillStyle = gradient;
    ctx.value.fill();
  };

  const drawPockets = () => {
    if (!ctx.value) return;
    const { TABLE, POCKET } = TableConfig;

    const pocketPositions = [
      { x: TABLE.LEFT - POCKET.SPACE, y: TABLE.TOP - POCKET.SPACE },
      { x: TABLE.LEFT + TABLE.WIDTH / 2, y: TABLE.TOP - POCKET.SPACE * 2.5 - 5 },
      { x: TABLE.LEFT + TABLE.WIDTH + POCKET.SPACE, y: TABLE.TOP - POCKET.SPACE },
      { x: TABLE.LEFT - POCKET.SPACE, y: TABLE.TOP + TABLE.HEIGHT + POCKET.SPACE },
      { x: TABLE.LEFT + TABLE.WIDTH / 2, y: TABLE.TOP + TABLE.HEIGHT + POCKET.SPACE * 2.5 + 5 },
      { x: TABLE.LEFT + TABLE.WIDTH + POCKET.SPACE, y: TABLE.TOP + TABLE.HEIGHT + POCKET.SPACE }
    ];

    pocketPositions.forEach(drawPocket);
  };

  const drawPocketCover = (params: {
    centerX: number;
    centerY: number;
    width: number;
    height: number;
    isTop: boolean;
  }) => {
    if (!ctx.value) return;
    const { centerX, centerY, width, height, isTop } = params;
    const offset = 9;

    ctx.value.beginPath();
    ctx.value.moveTo(centerX - width / 2, centerY + (isTop ? -offset : offset));
    ctx.value.lineTo(centerX + width / 2, centerY + (isTop ? -offset : offset));
    ctx.value.quadraticCurveTo(centerX, centerY + (isTop ? height / 2 : -height / 2), centerX - width / 2, centerY);
    ctx.value.closePath();
    ctx.value.fill();
  };

  const drawPocketCovering = () => {
    if (!ctx.value) return;
    const { TABLE, COLORS } = TableConfig;

    ctx.value.fillStyle = COLORS.CLOTH;
    const coverParams = {
      width: BallConfig.RADIUS * 5,
      height: BallConfig.RADIUS * 5
    };

    // Top middle pocket
    drawPocketCover({
      ...coverParams,
      centerX: TABLE.LEFT + TABLE.WIDTH / 2,
      centerY: TABLE.TOP + BallConfig.RADIUS * 0.8,
      isTop: true
    });

    // Bottom middle pocket
    drawPocketCover({
      ...coverParams,
      centerX: TABLE.LEFT + TABLE.WIDTH / 2,
      centerY: TABLE.TOP + TABLE.HEIGHT - BallConfig.RADIUS * 0.8,
      isTop: false
    });
  };

  const drawClothCoveredRails = () => {
    if (!ctx.value) return;
    const { TABLE, COLORS } = TableConfig;

    ctx.value.fillStyle = COLORS.CLOTH_COVER;
    const thickness = TABLE.CLOTH_COVER_THICKNESS;

    [
      // Top
      { x: TABLE.LEFT, y: TABLE.TOP - thickness, w: TABLE.WIDTH, h: thickness },
      // Bottom
      { x: TABLE.LEFT, y: TABLE.TOP + TABLE.HEIGHT, w: TABLE.WIDTH, h: thickness },
      // Left
      { x: TABLE.LEFT - thickness, y: TABLE.TOP, w: thickness, h: TABLE.HEIGHT },
      // Right
      { x: TABLE.LEFT + TABLE.WIDTH, y: TABLE.TOP, w: thickness, h: TABLE.HEIGHT }
    ].forEach(({ x, y, w, h }) => ctx.value?.fillRect(x, y, w, h));
  };

  const drawWatermark = (ctx: CanvasRenderingContext2D) => {
    ctx.save();
    ctx.fillStyle = TableConfig.WATERMARK.COLOR;
    ctx.font = TableConfig.WATERMARK.FONT;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    const x = TableConfig.TABLE.LEFT + TableConfig.TABLE.WIDTH / 2;
    const y = TableConfig.TABLE.TOP + TableConfig.TABLE.HEIGHT / 2;

    ctx.fillText(TableConfig.WATERMARK.TEXT, x, y);
    ctx.restore();
  };

  const draw = () => {
    drawRails();
    drawDiamonds();
    drawClothCoveredRails();
    drawPockets();
    drawCloth();
    drawPocketCovering();
    drawGrid();
    drawWatermark(ctx.value!);
  };

  return {
    draw
  };
};
