import { type Ref } from 'vue';
import {
  TableConfig,
  BallConfig,
  ToolboxConfig,
  TargetingBallConfig,
  TargetingZoneConfig,
  SpeedBarConfig,
  TextBoxConfig,
  type Ball,
  type WrapperConfig,
  type WrapperPosition
} from './config/poolTableConfig';

export const useToolboxDraw = (ctx: Ref<CanvasRenderingContext2D | null>, availableBalls: Ref<Ball[]>) => {
  const { TABLE } = TableConfig;

  const calculateWrapperDimensions = (): WrapperConfig => {
    const toolboxBallDiameter = BallConfig.DIAMETER * BallConfig.TOOLBOX.SCALE;
    const toolboxBallSpacing = toolboxBallDiameter * BallConfig.TOOLBOX.SPACING_MULTIPLIER;
    const padding = toolboxBallDiameter * BallConfig.TOOLBOX.PADDING_MULTIPLIER;

    return {
      top: TABLE.TOP + TABLE.HEIGHT + ToolboxConfig.DIMENSIONS.BALL_LIST_TOP_OFFSET,
      padding,
      width: 17 * toolboxBallSpacing + padding * 2,
      height: toolboxBallDiameter * 1.5
    };
  };

  const drawToolboxWrapper = () => {
    if (!ctx.value) return;

    const wrapperConfig = {
      top: TABLE.TOP + TABLE.HEIGHT + 100,
      height: ToolboxConfig.DIMENSIONS.WRAPPER_HEIGHT,
      width: TABLE.WIDTH + TABLE.RAIL_THICKNESS * 2
    };

    const position = {
      left: TABLE.LEFT - TABLE.RAIL_THICKNESS,
      top: wrapperConfig.top
    };

    ctx.value.fillStyle = ToolboxConfig.COLORS.WRAPPER;
    ctx.value.beginPath();
    ctx.value.roundRect(
      position.left,
      position.top,
      wrapperConfig.width,
      wrapperConfig.height,
      ToolboxConfig.DIMENSIONS.WRAPPER_RADIUS
    );
    ctx.value.fill();
  };

  const drawTargetingTool = () => {
    if (!ctx.value) return;

    const x = TABLE.LEFT - TABLE.RAIL_THICKNESS + ToolboxConfig.CONSTANTS.TARGETING_TOOL_OFFSET;
    const y = TABLE.TOP + TABLE.HEIGHT + 160;

    const targetingBall = TargetingBallConfig.createTargetingBall(x, y);
    targetingBall.isInTable = false;

    // Draw ball background
    ctx.value.beginPath();
    ctx.value.arc(x, y, targetingBall.radius, 0, Math.PI * 2);
    ctx.value.fillStyle = targetingBall.color;
    ctx.value.fill();

    // Draw the two red circles first
    ctx.value.beginPath();
    ctx.value.arc(x, y, targetingBall.radius / 3, 0, Math.PI * 2);
    ctx.value.strokeStyle = '#FF0000';
    ctx.value.lineWidth = 1;
    ctx.value.stroke();

    ctx.value.beginPath();
    ctx.value.arc(x, y, (targetingBall.radius / 3) * 2, 0, Math.PI * 2);
    ctx.value.strokeStyle = '#FF0000';
    ctx.value.lineWidth = 1;
    ctx.value.stroke();

    // Draw crosshair
    ctx.value.beginPath();
    ctx.value.strokeStyle = targetingBall.crosshair.color;
    ctx.value.lineWidth = targetingBall.crosshair.width;

    ctx.value.moveTo(x, y - targetingBall.radius);
    ctx.value.lineTo(x, y + targetingBall.radius);
    ctx.value.moveTo(x - targetingBall.radius, y);
    ctx.value.lineTo(x + targetingBall.radius, y);
    ctx.value.stroke();

    // Draw center dot
    ctx.value.beginPath();
    ctx.value.arc(x, y, targetingBall.centerDot.radius, 0, Math.PI * 2);
    ctx.value.fillStyle = targetingBall.centerDot.color;
    ctx.value.fill();
  };

  const drawTextBoxTool = () => {
    if (!ctx.value) return;

    const wrapperConfig = calculateWrapperDimensions();

    const toolBoxOffset = TABLE.LEFT - TABLE.RAIL_THICKNESS;
    const targetingToolMarginX = ToolboxConfig.CONSTANTS.TARGETING_TOOL_OFFSET - TargetingBallConfig.DEFAULT.radius;
    const ballWrapperMarginRight = targetingToolMarginX * 1.5;
    const ballWrapperOffsetWidth =
      ToolboxConfig.CONSTANTS.WRAPPER_OFFSET + wrapperConfig.width + ballWrapperMarginRight;

    const x = toolBoxOffset + ballWrapperOffsetWidth;
    const y = wrapperConfig.top + wrapperConfig.height / 2;

    ctx.value.font = `bold ${TextBoxConfig.DEFAULT.fontSize}px ${TextBoxConfig.DEFAULT.fontFamily}`;
    ctx.value.fillStyle = 'white';
    ctx.value.fillText(TextBoxConfig.DEFAULT.toolboxText, x, y);
  };

  const drawBallWrapper = (): WrapperPosition | null => {
    if (!ctx.value) return null;

    const wrapperConfig = calculateWrapperDimensions();
    const position = {
      left: TABLE.LEFT - TABLE.RAIL_THICKNESS + ToolboxConfig.CONSTANTS.WRAPPER_OFFSET,
      top: wrapperConfig.top
    };

    ctx.value.fillStyle = '#2A3443';
    ctx.value.beginPath();
    ctx.value.roundRect(position.left, position.top, wrapperConfig.width, wrapperConfig.height, 10);
    ctx.value.fill();

    return {
      left: position.left,
      top: position.top + wrapperConfig.height / 2,
      padding: wrapperConfig.padding,
      spacing: BallConfig.DIAMETER * BallConfig.TOOLBOX.SCALE * BallConfig.TOOLBOX.SPACING_MULTIPLIER,
      width: wrapperConfig.width
    };
  };

  const drawBallSolid = (ball: Ball, toolboxBallRadius: number) => {
    if (!ctx.value) return;

    ctx.value.save();

    if (ball.isGhost) {
      ctx.value.fillStyle = 'rgba(255, 255, 255, 0.3)';
      ctx.value.beginPath();
      ctx.value.arc(ball.x, ball.y, ball.radius, 0, Math.PI * 2);
      ctx.value.fill();

      // Dashed border
      ctx.value.strokeStyle = 'rgba(255, 255, 255, 0.8)';
      ctx.value.lineWidth = 1.5;
      ctx.value.setLineDash([3, 3]);
      ctx.value.stroke();
      ctx.value.restore();
      return;
    }

    const gradient = ctx.value.createRadialGradient(
      ball.x - toolboxBallRadius * 0.3,
      ball.y - toolboxBallRadius * 0.3,
      0,
      ball.x,
      ball.y,
      toolboxBallRadius * 1.1
    );

    const baseColor = ball.number && ball.number >= 9 ? '#FFFFFF' : ball.color;
    const darkColor = shadeColor(baseColor, -15);

    gradient.addColorStop(0, baseColor);
    gradient.addColorStop(0.6, baseColor);
    gradient.addColorStop(1, darkColor);

    ctx.value.fillStyle = gradient;
    ctx.value.beginPath();
    ctx.value.arc(ball.x, ball.y, toolboxBallRadius, 0, Math.PI * 2);
    ctx.value.fill();
    ctx.value.restore();
  };

  const drawBallStripe = (ball: Ball, toolboxBallRadius: number) => {
    if (!ctx.value || !ball.number || ball.number < 9) return;

    ctx.value.save();
    ctx.value.beginPath();
    ctx.value.arc(ball.x, ball.y, toolboxBallRadius, 0, Math.PI * 2);
    ctx.value.clip();

    ctx.value.fillStyle = ball.color;
    const stripeHeight = toolboxBallRadius * 1.1;
    ctx.value.fillRect(ball.x - toolboxBallRadius, ball.y - stripeHeight / 2, toolboxBallRadius * 2, stripeHeight);
    ctx.value.restore();
  };

  const drawBallNumber = (ball: Ball, toolboxBallRadius: number) => {
    if (!ctx.value || ball.isGhost) return;

    const fontSize = toolboxBallRadius * 0.75;
    ctx.value.font = `bold ${fontSize}px Arial`;
    ctx.value.textAlign = 'center';
    ctx.value.textBaseline = 'middle';

    if (ball.isCue) {
      ctx.value.fillStyle = '#cc0000';
      ctx.value.beginPath();
      ctx.value.arc(ball.x, ball.y, 2.5, 0, Math.PI * 2);
      ctx.value.fill();
    } else {
      ctx.value.fillStyle = '#ffffff';
      ctx.value.beginPath();
      ctx.value.arc(ball.x, ball.y, fontSize * 0.65, 0, Math.PI * 2);
      ctx.value.fill();

      ctx.value.fillStyle = '#000000';
      ctx.value.fillText(ball.number!.toString(), ball.x, ball.y + 1);
    }
  };

  const renderToolboxBall = (ball: Ball, toolboxBallRadius: number) => {
    if (ball.isInTable) return;

    drawBallSolid(ball, toolboxBallRadius);
    drawBallStripe(ball, toolboxBallRadius);
    drawBallNumber(ball, toolboxBallRadius);
  };

  const positionBallsInToolbox = (
    wrapperPosition: WrapperPosition,
    toolboxBallRadius: number,
    toolboxBallSpacing: number
  ) => {
    const totalBallsWidth = availableBalls.value.length * toolboxBallSpacing;
    const startX = wrapperPosition.left + (wrapperPosition.width - totalBallsWidth) / 2 + 17;

    availableBalls.value.forEach((ball, index) => {
      if (!ball.isInTable) {
        ball.x = startX + index * toolboxBallSpacing;
        ball.y = wrapperPosition.top;
        ball.radius = toolboxBallRadius;
      }
    });
  };

  const drawTargetingZone = () => {
    if (!ctx.value) return;

    // Position targeting zone next to ball lists
    const wrapperConfig = calculateWrapperDimensions();
    const zoneX = TABLE.LEFT + TABLE.WIDTH - ToolboxConfig.CONSTANTS.TARGETING_ZONE_OFFSET;
    const zoneY = wrapperConfig.top + wrapperConfig.height / 2;
    const zoneSize = TargetingZoneConfig.DEFAULT.size;

    ctx.value.fillStyle = TargetingZoneConfig.STYLE.color;
    ctx.value.strokeStyle = TargetingZoneConfig.STYLE.borderColor;
    ctx.value.lineWidth = TargetingZoneConfig.STYLE.borderWidth;

    // Draw zone rectangle
    ctx.value.fillRect(zoneX - zoneSize / 2, zoneY - zoneSize / 2, zoneSize, zoneSize);
    ctx.value.strokeRect(zoneX - zoneSize / 2, zoneY - zoneSize / 2, zoneSize, zoneSize);

    // Draw corners
    ctx.value.fillStyle = TargetingZoneConfig.STYLE.cornerColor;
    ctx.value.strokeStyle = TargetingZoneConfig.STYLE.cornerBorderColor;
    const cornerRadius = TargetingZoneConfig.DEFAULT.cornerRadius;

    [
      { x: -zoneSize / 2, y: -zoneSize / 2 },
      { x: zoneSize / 2, y: -zoneSize / 2 },
      { x: zoneSize / 2, y: zoneSize / 2 },
      { x: -zoneSize / 2, y: zoneSize / 2 }
    ].forEach(corner => {
      ctx.value!.beginPath();
      ctx.value!.arc(zoneX + corner.x, zoneY + corner.y, cornerRadius, 0, Math.PI * 2);
      ctx.value!.fill();
      ctx.value!.stroke();
    });
  };

  const drawBallsAndWrapper = () => {
    const wrapperPosition = drawBallWrapper();
    if (!wrapperPosition) return;

    const toolboxBallRadius = BallConfig.RADIUS * BallConfig.TOOLBOX.SCALE;
    const toolboxBallSpacing = BallConfig.DIAMETER * BallConfig.TOOLBOX.SCALE * BallConfig.TOOLBOX.SPACING_MULTIPLIER;

    positionBallsInToolbox(wrapperPosition, toolboxBallRadius, toolboxBallSpacing);
    availableBalls.value.forEach(ball => renderToolboxBall(ball, toolboxBallRadius));
  };

  const drawSpeedBar = () => {
    if (!ctx.value) return;

    const {
      backgroundColor,
      borderColor,
      borderWidth,
      color,
      height,
      padding,
      segmentCount,
      segmentSpacing,
      value,
      width
    } = SpeedBarConfig.DEFAULT;
    const { TARGETING_TOOL_OFFSET, WRAPPER_OFFSET, TARGETING_ZONE_OFFSET } = ToolboxConfig.CONSTANTS;

    // Position speed bar next to targeting zone
    const wrapperConfig = calculateWrapperDimensions();
    const toolBoxOffset = TABLE.LEFT - TABLE.RAIL_THICKNESS;
    const targetingToolMarginX = TARGETING_TOOL_OFFSET - TargetingBallConfig.DEFAULT.radius;
    const ballWrapperMarginRight = targetingToolMarginX * 1.5;
    const ballWrapperOffsetWidth = WRAPPER_OFFSET + wrapperConfig.width + ballWrapperMarginRight;
    const targetingZoneOffset = TABLE.LEFT + TABLE.WIDTH - TARGETING_ZONE_OFFSET;

    const barX =
      toolBoxOffset +
      ballWrapperOffsetWidth +
      (targetingZoneOffset - (toolBoxOffset + ballWrapperOffsetWidth)) +
      TargetingZoneConfig.DEFAULT.size +
      width / 2;
    const barY = wrapperConfig.top + wrapperConfig.height / 2;

    // Draw background
    ctx.value.fillStyle = backgroundColor;
    ctx.value.strokeStyle = borderColor;
    ctx.value.lineWidth = borderWidth;

    ctx.value.beginPath();
    ctx.value.roundRect(barX - width / 2, barY - height / 2, width, height, 5);
    ctx.value.fill();
    ctx.value.stroke();

    // Draw the segments inside
    const filledSegments = Math.round(value);
    const innerWidth = width - padding * 2;
    const innerHeight = height - padding * 2;
    const totalSpacing = segmentSpacing * (segmentCount - 1);
    const segmentWidth = (innerWidth - totalSpacing) / segmentCount;
    // First segment
    const startX = barX - width / 2 + padding;
    const startY = barY - height / 2 + padding;

    for (let i = 0; i < segmentCount; i++) {
      const segmentX = startX + i * (segmentWidth + segmentSpacing);

      ctx.value.fillStyle = i < filledSegments ? color : '#FFFFFF';
      ctx.value.fillRect(segmentX, startY, segmentWidth, innerHeight);
    }
  };

  const draw = () => {
    drawToolboxWrapper();
    drawBallsAndWrapper();
    drawTargetingTool();
    drawTargetingZone();
    drawSpeedBar();
    drawTextBoxTool();
  };

  return {
    draw
  };
};

const shadeColor = (color: string, percent: number): string => {
  const num = parseInt(color.replace('#', ''), 16);
  const amt = Math.round(2.55 * percent);
  const R = Math.max(Math.min((num >> 16) + amt, 255), 0);
  const G = Math.max(Math.min(((num >> 8) & 0x00ff) + amt, 255), 0);
  const B = Math.max(Math.min((num & 0x0000ff) + amt, 255), 0);
  return '#' + (0x1000000 + (R << 16) + (G << 8) + B).toString(16).slice(1);
};
