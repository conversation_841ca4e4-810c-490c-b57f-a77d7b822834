import { ref } from 'vue';
import type { Ball, TargetingBall, TableLayoutData, TargetingZone, TextBox, SpeedBar } from './config/poolTableConfig';

export const useLayoutStorage = () => {
  const savedLayouts = ref<TableLayoutData[]>([]);

  const fetchSavedLayouts = async () => {
    // implement api
    savedLayouts.value = [];
  };

  const saveLayout = (
    balls: Ball[],
    targetingBalls: TargetingBall[],
    targetingZones: TargetingZone[],
    textBoxes: TextBox[],
    speedBars: SpeedBar[]
  ) => {
    const timestamp = Date.now();
    const layoutData: TableLayoutData = {
      id: `layout_${timestamp}`,
      timestamp,
      balls: parseRawData(balls),
      targetingBalls: parseRawData(targetingBalls),
      targetingZones: parseRawData(targetingZones),
      textBoxes: parseRawData(textBoxes),
      speedBars: parseRawData(speedBars)
    };

    savedLayouts.value.push(layoutData);
  };

  const parseRawData = (rawData: Ball[] | TargetingBall[] | TargetingZone[] | TextBox[] | SpeedBar[] | null) => {
    if (!rawData) return null;

    return JSON.parse(JSON.stringify(rawData));
  };

  return {
    savedLayouts,
    saveLayout,
    fetchSavedLayouts
  };
};
