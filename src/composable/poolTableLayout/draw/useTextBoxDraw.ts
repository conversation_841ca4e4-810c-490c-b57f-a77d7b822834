import { type Ref } from 'vue';
import { BallConfig, TextBoxConfig, type ActivePoint, type TextBox } from '../config/poolTableConfig';

export const useTextBoxDraw = (ctx: Ref<CanvasRenderingContext2D | null>, activePoint: Ref<ActivePoint | null>) => {
  // Preload icons
  const editIcon = new Image();
  const rotateRightIcon = new Image();
  const rotateLeftIcon = new Image();
  editIcon.src = '/edit.svg';
  rotateRightIcon.src = '/rotate-right.svg';
  rotateLeftIcon.src = '/rotate-left.svg';

  const drawTextBox = (textBox: TextBox) => {
    if (!ctx.value) return;

    const textValue = textBox.value;
    const color = TextBoxConfig.DEFAULT.color;
    const fontSize = TextBoxConfig.DEFAULT.fontSize;
    const fontFamily = TextBoxConfig.DEFAULT.fontFamily;
    const rotation = textBox.rotation;

    ctx.value.save();
    ctx.value.font = `bold ${fontSize}px ${fontFamily}`;

    const metrics = ctx.value.measureText(textValue);
    const textBoxWidth = metrics.width;
    const textBoxHeight = metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent;

    const pivotX = textBox.x - textBoxWidth / 2;
    const pivotY = textBox.y;

    ctx.value.translate(pivotX, pivotY);
    if (textBox.rotation) {
      const rotationAngle = (rotation * Math.PI) / 180;
      ctx.value.rotate(rotationAngle);
    }

    // Draw active text box
    if (activePoint.value && activePoint.value.isTextBox && activePoint.value.textBox?.id === textBox.id) {
      ctx.value.fillStyle = BallConfig.HIGHLIGHT.COLOR;
      ctx.value.fillRect(0, -textBoxHeight / 2, textBoxWidth, textBoxHeight);
      ctx.value.stroke();

      textBox.isActive = true;
    } else {
      textBox.isActive = false;
    }

    ctx.value.textAlign = 'left';
    ctx.value.textBaseline = 'middle';
    ctx.value.fillStyle = color;
    ctx.value.fillText(textValue, 0, 0);

    ctx.value.restore();

    if (textBox.isActive) {
      drawTextboxActions(textBox, textBoxWidth, textBoxHeight);
    }
  };

  const drawTextboxActions = (textBox: TextBox, textBoxWidth: number, textBoxHeight: number) => {
    if (!ctx.value) return;

    const drawIcon = (icon: HTMLImageElement, offset: number) => {
      const draw = () => {
        if (!ctx.value) return;
        ctx.value.drawImage(icon, textBox.x - textBoxWidth / 2 - offset, textBox.y - textBoxHeight);
      };

      if (icon.complete) {
        draw();
      } else {
        icon.onload = draw;
      }
    };

    drawIcon(editIcon, 30);
    drawIcon(rotateRightIcon, 55);
    drawIcon(rotateLeftIcon, 75);
  };

  return {
    drawTextBox
  };
};
