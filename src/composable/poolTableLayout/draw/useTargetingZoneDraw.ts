import { type Ref } from 'vue';
import { BallConfig, TargetingZoneConfig, type TargetingZone, type ActivePoint } from '../config/poolTableConfig';

export const useTargetingZoneDraw = (
  ctx: Ref<CanvasRenderingContext2D | null>,
  targetingZones: Ref<TargetingZone[]>,
  activePoint: Ref<ActivePoint | null>
) => {
  const drawTargetingZone = (zone: TargetingZone) => {
    if (!ctx.value) return;

    // Draw highlight for active targeting zone
    if (activePoint?.value?.isZone && activePoint.value.zone?.id === zone.id) {
      // Draw highlight border around the active zone
      ctx.value.strokeStyle = BallConfig.HIGHLIGHT.COLOR;
      ctx.value.lineWidth = 3;

      // Draw highlight as a path following the corners
      ctx.value.beginPath();
      ctx.value.moveTo(zone.corners.topLeft.x - 2, zone.corners.topLeft.y - 2);
      ctx.value.lineTo(zone.corners.topRight.x + 2, zone.corners.topRight.y - 2);
      ctx.value.lineTo(zone.corners.bottomRight.x + 2, zone.corners.bottomRight.y + 2);
      ctx.value.lineTo(zone.corners.bottomLeft.x - 2, zone.corners.bottomLeft.y + 2);
      ctx.value.closePath();
      ctx.value.stroke();

      // Reset to default styles for the normal zone drawing
      ctx.value.strokeStyle = TargetingZoneConfig.STYLE.borderColor;
      ctx.value.lineWidth = TargetingZoneConfig.STYLE.borderWidth;
    }

    // Draw zone as a quadrilateral using the corner coordinates
    ctx.value.fillStyle = TargetingZoneConfig.STYLE.color;
    ctx.value.strokeStyle = TargetingZoneConfig.STYLE.borderColor;
    ctx.value.lineWidth = TargetingZoneConfig.STYLE.borderWidth;

    ctx.value.beginPath();
    ctx.value.moveTo(zone.corners.topLeft.x, zone.corners.topLeft.y);
    ctx.value.lineTo(zone.corners.topRight.x, zone.corners.topRight.y);
    ctx.value.lineTo(zone.corners.bottomRight.x, zone.corners.bottomRight.y);
    ctx.value.lineTo(zone.corners.bottomLeft.x, zone.corners.bottomLeft.y);
    ctx.value.closePath();
    ctx.value.fill();
    ctx.value.stroke();

    // Draw corner handles
    ctx.value.fillStyle = TargetingZoneConfig.STYLE.cornerColor;
    ctx.value.strokeStyle = TargetingZoneConfig.STYLE.cornerBorderColor;

    const corners = [zone.corners.topLeft, zone.corners.topRight, zone.corners.bottomRight, zone.corners.bottomLeft];

    corners.forEach((corner, index) => {
      const cornerRadius = TargetingZoneConfig.DEFAULT.cornerRadius;
      const isActiveCorner =
        activePoint?.value?.isZoneCorner &&
        activePoint.value.zone?.id === zone.id &&
        activePoint.value.cornerIndex === index;

      ctx.value!.beginPath();
      ctx.value!.arc(corner.x, corner.y, cornerRadius, 0, Math.PI * 2);

      // Highlight the active corner
      if (isActiveCorner) {
        ctx.value!.fillStyle = '#ff0000'; // Red for active corner
      } else {
        ctx.value!.fillStyle = TargetingZoneConfig.STYLE.cornerColor;
      }

      ctx.value!.fill();
      ctx.value!.stroke();
    });
  };

  const drawTargetingZones = () => {
    targetingZones.value.forEach(zone => drawTargetingZone(zone));
  };

  return {
    drawTargetingZones
  };
};
