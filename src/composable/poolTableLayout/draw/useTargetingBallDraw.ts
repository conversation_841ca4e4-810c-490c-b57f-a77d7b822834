import { type Ref } from 'vue';
import { type TargetingBall, type ActivePoint, BallConfig } from '../config/poolTableConfig';

export const useTargetingBallDraw = (
  ctx: Ref<CanvasRenderingContext2D | null>,
  activePoint: Ref<ActivePoint | null>
) => {
  const drawTargetingBall = (ball: TargetingBall) => {
    if (!ctx.value) return;

    // Draw active targeting ball
    if (activePoint?.value && activePoint.value.isTargetingBall && activePoint.value.ball.id === ball.id) {
      ctx.value!.beginPath();
      ctx.value!.arc(
        activePoint.value.ball.x,
        activePoint.value.ball.y,
        activePoint.value.ball.radius + 2,
        0,
        Math.PI * 2
      );
      ctx.value!.fillStyle = BallConfig.HIGHLIGHT.COLOR;
      ctx.value!.fill();
    }

    // Draw ball background
    ctx.value.beginPath();
    ctx.value.arc(ball.x, ball.y, ball.radius, 0, Math.PI * 2);
    ctx.value.fillStyle = ball.color;
    ctx.value.fill();

    // Draw crosshair
    ctx.value.beginPath();
    ctx.value.strokeStyle = ball.crosshair.color;
    ctx.value.lineWidth = ball.crosshair.width;

    ctx.value.moveTo(ball.x, ball.y - ball.radius);
    ctx.value.lineTo(ball.x, ball.y + ball.radius);
    ctx.value.moveTo(ball.x - ball.radius, ball.y);
    ctx.value.lineTo(ball.x + ball.radius, ball.y);
    ctx.value.stroke();

    // Draw center dot and circles
    // Draw the two red circles first (relative to ball center)
    ctx.value.beginPath();
    ctx.value.arc(ball.x, ball.y, ball.radius / 3, 0, Math.PI * 2);
    ctx.value.strokeStyle = '#FF0000';
    ctx.value.lineWidth = 1;
    ctx.value.stroke();

    ctx.value.beginPath();
    ctx.value.arc(ball.x, ball.y, (ball.radius / 3) * 2, 0, Math.PI * 2);
    ctx.value.strokeStyle = '#FF0000';
    ctx.value.lineWidth = 1;
    ctx.value.stroke();

    // Draw the draggable center dot last (with its offset position)
    const centerX = ball.x + ball.centerDot.x;
    const centerY = ball.y + ball.centerDot.y;

    ctx.value.beginPath();
    ctx.value.arc(centerX, centerY, ball.centerDot.radius, 0, Math.PI * 2);
    ctx.value.fillStyle = ball.centerDot.color;
    ctx.value.fill();
  };

  return {
    drawTargetingBall
  };
};
