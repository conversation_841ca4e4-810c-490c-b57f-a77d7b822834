import { type Ref } from 'vue';
import { BallConfig, type Ball, type BallPosition, type ActivePoint } from '../config/poolTableConfig';

export const usePathDraw = (ctx: Ref<CanvasRenderingContext2D | null>, activePoint: Ref<ActivePoint | null>) => {
  const drawBallPaths = (balls: Ball[]) => {
    if (!ctx.value) return;

    balls.forEach(ball => {
      if (!ball.paths) return;

      ball.paths.forEach((path, pathIndex) => {
        if (path.points.length < 2) return;

        // For cue ball, draw curved paths
        if (ball.isCue) {
          drawCueBallPath(ball, path, pathIndex);
        } else {
          drawRegularBallPath(ball, path, pathIndex);
        }
      });
    });
  };

  const drawCueBallPath = (ball: Ball, path: any, pathIndex: number) => {
    if (!ctx.value) return;

    // Draw the main path line
    ctx.value.lineWidth = 2;
    ctx.value.strokeStyle = path.color;

    // For each segment (start point, control point, end point)
    for (let i = 0; i < path.points.length - 1; i++) {
      const start = path.points[i];
      const end = path.points[i + 1];

      // If we have a control point between these points
      if (i + 2 < path.points.length && i % 2 === 0) {
        const controlPoint = path.points[i + 1];
        const endPoint = path.points[i + 2];

        ctx.value.beginPath();
        ctx.value.moveTo(start.x, start.y);
        ctx.value.quadraticCurveTo(controlPoint.x, controlPoint.y, endPoint.x, endPoint.y);
        ctx.value.stroke();

        // Skip the next two points as we've already used them
        i += 1;
      } else {
        // Draw straight line if no control point
        ctx.value.beginPath();
        ctx.value.moveTo(start.x, start.y);
        ctx.value.lineTo(end.x, end.y);
        ctx.value.stroke();
      }
    }

    // Draw the points
    drawCueBallPathPoints(ball, path, pathIndex);

    // Draw arrow at the end of the path
    if (path.points.length >= 2) {
      drawCueBallPathArrow(path);
    }
  };

  const drawCueBallPathPoints = (ball: Ball, path: any, pathIndex: number) => {
    path.points.forEach((point: BallPosition, pointIndex: number) => {
      if (!ctx.value || pointIndex === 0) return; // Skip the starting point (ball position)

      // Draw highlight for active point
      if (
        activePoint?.value &&
        !activePoint.value.isZone &&
        activePoint.value.ball.id === ball.id &&
        activePoint.value.pathIndex === pathIndex &&
        activePoint.value.pointIndex === pointIndex
      ) {
        ctx.value.beginPath();
        ctx.value.arc(point.x, point.y, BallConfig.RADIUS + 2, 0, Math.PI * 2);
        ctx.value.strokeStyle = BallConfig.HIGHLIGHT.COLOR;
        ctx.value.lineWidth = 2;
        ctx.value.stroke();
      }

      // Check if we should show control points
      const showControlPoints =
        activePoint?.value &&
        !activePoint.value.isZone &&
        (('isCue' in activePoint.value.ball && activePoint.value.ball.isCue) ||
          (activePoint.value.ball.id === ball.id && ball.isCue));

      // Draw control points differently
      ctx.value.beginPath();

      // Control points are at odd indices (1, 3, 5...)
      if (pointIndex % 2 === 1) {
        // Only draw control points if the active point is related to the cue ball
        if (showControlPoints) {
          ctx.value.arc(point.x, point.y, BallConfig.RADIUS, 0, Math.PI * 2);
          ctx.value.fillStyle = 'rgba(111, 144, 154, 0.6)'; // #6F909A with 60% opacity
          ctx.value.strokeStyle = '#6F909A';
          ctx.value.lineWidth = 2;
          ctx.value.fill();
          ctx.value.stroke();
        }
      } else {
        // End point (normal size, path color)
        ctx.value.arc(point.x, point.y, BallConfig.RADIUS, 0, Math.PI * 2);
        ctx.value.fillStyle = 'rgba(255, 255, 255, 0)';
        ctx.value.strokeStyle = path.color;
        ctx.value.lineWidth = 2;
        ctx.value.fill();
        ctx.value.stroke();
      }
    });
  };

  const drawCueBallPathArrow = (path: any) => {
    if (!ctx.value || path.points.length < 2) return;

    const lastPoint = path.points[path.points.length - 1];
    const prevPoint = path.points[path.points.length - 2];

    // If the last point is a control point, use the point before it
    if ((path.points.length - 1) % 2 === 1) {
      const endPoint = path.points[path.points.length - 1];
      const controlPoint = path.points[path.points.length - 2];

      // Calculate the tangent at the end point
      const tangentX = 2 * (endPoint.x - controlPoint.x);
      const tangentY = 2 * (endPoint.y - controlPoint.y);

      // Create a virtual point to draw the arrow in the right direction
      const virtualPoint = {
        x: endPoint.x + tangentX * 0.01,
        y: endPoint.y + tangentY * 0.01
      };

      drawPathArrow(endPoint, virtualPoint, path.color);
    } else {
      drawPathArrow(prevPoint, lastPoint, path.color);
    }
  };

  const drawRegularBallPath = (ball: Ball, path: any, pathIndex: number) => {
    if (!ctx.value) return;

    // Draw the path background first
    drawPathBackground(path);

    // Draw the main path line
    ctx.value.beginPath();
    ctx.value.strokeStyle = path.color;
    ctx.value.lineWidth = 2;
    drawPathPoints(path.points);
    ctx.value.stroke();

    // Draw path points as circles with borders
    drawRegularBallPathPoints(ball, path, pathIndex);

    // Draw arrow at the end of the path
    if (path.points.length >= 2) {
      const lastPoint = path.points[path.points.length - 1];
      const secondLastPoint = path.points[path.points.length - 2];
      drawPathArrow(secondLastPoint, lastPoint, path.color);
    }
  };

  const drawPathBackground = (path: any) => {
    if (!ctx.value) return;

    // Draw individual segments with better styling
    for (let i = 0; i < path.points.length - 1; i++) {
      const start = path.points[i];
      const end = path.points[i + 1];

      // Calculate direction vector between points
      const dx = end.x - start.x;
      const dy = end.y - start.y;

      // Calculate distance between points
      const distance = Math.sqrt(dx * dx + dy * dy);

      // Skip if points are too close
      if (distance < BallConfig.RADIUS * 2) continue;

      // Normalize direction vector
      const nx = dx / distance;
      const ny = dy / distance;

      // Calculate start and end positions at the edges of the balls
      const startX = start.x + nx * BallConfig.RADIUS;
      const startY = start.y + ny * BallConfig.RADIUS;
      const endX = end.x - nx * BallConfig.RADIUS;
      const endY = end.y - ny * BallConfig.RADIUS;

      // Create gradient for more natural looking path
      const gradient = ctx.value.createLinearGradient(startX, startY, endX, endY);
      gradient.addColorStop(0, `${path.color}10`);
      gradient.addColorStop(0.5, `${path.color}25`);
      gradient.addColorStop(1, `${path.color}10`);

      ctx.value.beginPath();
      ctx.value.lineCap = 'round';
      ctx.value.lineWidth = 12;
      ctx.value.strokeStyle = gradient;
      ctx.value.moveTo(startX, startY);
      ctx.value.lineTo(endX, endY);
      ctx.value.stroke();
    }

    // Reset line cap for other drawing operations
    ctx.value.lineCap = 'butt';
  };

  const drawRegularBallPathPoints = (ball: Ball, path: any, pathIndex: number) => {
    path.points.forEach((point: BallPosition, pointIndex: number) => {
      if (!ctx.value || pointIndex === 0) return; // Skip the starting point (ball position)

      // Draw highlight for active point
      if (
        activePoint?.value &&
        !activePoint.value.isZone &&
        activePoint.value.ball.id === ball.id &&
        activePoint.value.pathIndex === pathIndex &&
        activePoint.value.pointIndex === pointIndex
      ) {
        ctx.value.beginPath();
        ctx.value.arc(point.x, point.y, BallConfig.RADIUS + 2, 0, Math.PI * 2);
        ctx.value.strokeStyle = BallConfig.HIGHLIGHT.COLOR;
        ctx.value.lineWidth = 2;
        ctx.value.stroke();
      }

      // Draw path points
      ctx.value.beginPath();
      ctx.value.arc(point.x, point.y, BallConfig.RADIUS, 0, Math.PI * 2);
      ctx.value.fillStyle = 'rgba(255, 255, 255, 0)';
      ctx.value.strokeStyle = path.color;
      ctx.value.lineWidth = 2;
      ctx.value.fill();
      ctx.value.stroke();
    });
  };

  const drawPathPoints = (points: BallPosition[]) => {
    points.forEach((point, index) => {
      if (!ctx.value) return;

      if (index === 0) {
        ctx.value.moveTo(point.x, point.y);
      } else {
        ctx.value.lineTo(point.x, point.y);
      }
    });
  };

  const drawPathArrow = (start: BallPosition, end: BallPosition, color: string) => {
    if (!ctx.value) return;

    const angle = Math.atan2(end.y - start.y, end.x - start.x);
    const arrowSize = 10;

    ctx.value.save();
    ctx.value.translate(end.x, end.y);
    ctx.value.rotate(angle);

    ctx.value.beginPath();
    ctx.value.moveTo(0, 0);
    ctx.value.lineTo(-arrowSize, -arrowSize / 2);
    ctx.value.lineTo(-arrowSize, arrowSize / 2);
    ctx.value.closePath();
    ctx.value.fillStyle = color;
    ctx.value.fill();

    ctx.value.restore();
  };

  return {
    drawBallPaths
  };
};
