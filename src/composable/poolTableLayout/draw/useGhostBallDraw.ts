import { type Ref, ref } from 'vue';
import {
  BallConfig,
  TableConfig,
  type BallPosition,
  type ActivePoint,
  type Ball,
  type TargetingBall,
  type TextBox,
  type SpeedBar
} from '../config/poolTableConfig';
import { usePositions } from '../actions/usePositions';

export const useGhostBallDraw = (
  ctx: Ref<CanvasRenderingContext2D | null>,
  activePoint: Ref<ActivePoint | null>,
  balls: Ref<Ball[]>,
  targetingBalls: Ref<TargetingBall[]>,
  textBoxes: Ref<TextBox[]>,
  speedBars: Ref<SpeedBar[]>
) => {
  const ghostBallPosition = ref<BallPosition | null>(null);
  const showGhostBall = ref(false);

  const { isPointInTextBox, isPointInSpeedBar } = usePositions();

  const isPositionInTableBounds = (position: BallPosition): boolean => {
    const { TABLE } = TableConfig;
    return (
      position.x >= TABLE.LEFT + BallConfig.RADIUS &&
      position.x <= TABLE.LEFT + TABLE.WIDTH - BallConfig.RADIUS &&
      position.y >= TABLE.TOP + BallConfig.RADIUS &&
      position.y <= TABLE.TOP + TABLE.HEIGHT - BallConfig.RADIUS
    );
  };

  const isPositionInTargetingBall = (position: BallPosition): boolean => {
    for (const ball of targetingBalls.value) {
      const dx = position.x - ball.x;
      const dy = position.y - ball.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance <= ball.radius) {
        return true;
      }
    }
    return false;
  };

  const isPositionInBall = (position: BallPosition): boolean => {
    for (const ball of balls.value) {
      const dx = position.x - ball.x;
      const dy = position.y - ball.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance <= ball.radius) {
        return true;
      }
    }
    return false;
  };

  const isPositionInControlPoint = (position: BallPosition): boolean => {
    for (const ball of balls.value) {
      if (!ball.paths) continue;

      for (let pathIndex = 0; pathIndex < ball.paths.length; pathIndex++) {
        const path = ball.paths[pathIndex];
        for (let pointIndex = 1; pointIndex < path.points.length; pointIndex++) {
          // Check only control points (odd indices)
          if (pointIndex % 2 === 1) {
            const point = path.points[pointIndex];
            const dx = position.x - point.x;
            const dy = position.y - point.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance <= BallConfig.RADIUS) {
              return true;
            }
          }
        }
      }
    }
    return false;
  };

  const isPositionInPathPoint = (position: BallPosition): boolean => {
    for (const ball of balls.value) {
      if (!ball.paths) continue;

      for (const path of ball.paths) {
        for (let i = 0; i < path.points.length; i++) {
          const point = path.points[i];
          const dx = position.x - point.x;
          const dy = position.y - point.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance <= BallConfig.RADIUS) {
            return true;
          }
        }
      }
    }
    return false;
  };

  const isPositionInTextBox = (position: BallPosition): boolean => {
    if (!ctx.value) return false;

    for (const textBox of textBoxes.value) {
      if (isPointInTextBox(ctx.value.canvas, position, textBox)) {
        return true;
      }
    }

    return false;
  };

  const isPositionInSpeedBar = (position: BallPosition): boolean => {
    if (!ctx.value) return false;

    for (const speedBar of speedBars.value) {
      if (isPointInSpeedBar(position, speedBar)) {
        return true;
      }
    }

    return false;
  };

  const drawGhostBall = () => {
    if (!ctx.value || !ghostBallPosition.value || !showGhostBall.value) return;

    // Don't show ghost ball and dash line if active point is a targeting ball
    if (activePoint?.value?.isTargetingBall) return;
    if (activePoint?.value?.isZone) return;

    const { x, y } = ghostBallPosition.value;

    // Draw dashed line from active point to ghost ball
    if (activePoint?.value) {
      const ball = activePoint.value.ball;

      if (!activePoint.value.isTargetingBall && !activePoint.value.isZone) {
        const regularBall = ball as Ball;
        const paths = regularBall.paths?.[activePoint.value.pathIndex != -1 ? activePoint.value.pathIndex : 0];
        const startPoint = paths?.points?.[paths.points.length - 1] ?? {
          x: ball.x,
          y: ball.y
        };

        ctx.value.save();
        ctx.value.beginPath();
        ctx.value.setLineDash([5, 5]);
        ctx.value.strokeStyle = 'rgba(255, 255, 255, 0.6)';
        ctx.value.lineWidth = 1;
        ctx.value.moveTo(startPoint.x, startPoint.y);
        ctx.value.lineTo(x, y);
        ctx.value.stroke();
        ctx.value.restore();
      }
    }

    ctx.value.save();

    // Draw semi-transparent ball
    ctx.value.beginPath();
    ctx.value.arc(x, y, BallConfig.RADIUS, 0, Math.PI * 2);
    ctx.value.fillStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.value.fill();

    // Draw dashed outline
    ctx.value.strokeStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.value.lineWidth = 1.5;
    ctx.value.setLineDash([3, 3]);
    ctx.value.stroke();

    ctx.value.restore();
  };

  const updateGhostBallPosition = (x: number, y: number) => {
    // Don't show ghost ball if active point is a targeting ball
    if (activePoint?.value?.isTargetingBall) {
      showGhostBall.value = false;
      return;
    }

    // Don't show ghost ball for ghost balls
    const activeBall = activePoint?.value?.ball as Ball;
    if (activeBall?.isGhost) {
      showGhostBall.value = false;
      return;
    }

    if (
      isPositionInTableBounds({ x, y }) &&
      !isPositionInTargetingBall({ x, y }) &&
      !isPositionInBall({ x, y }) &&
      !isPositionInControlPoint({ x, y }) &&
      !isPositionInPathPoint({ x, y }) &&
      !isPositionInTextBox({ x, y }) &&
      !isPositionInSpeedBar({ x, y })
    ) {
      ghostBallPosition.value = { x, y };
      showGhostBall.value = true;
    } else {
      showGhostBall.value = false;
    }
  };

  const deleteGhostBall = () => {
    showGhostBall.value = false;
    ghostBallPosition.value = null;
  };

  return {
    deleteGhostBall,
    drawGhostBall,
    updateGhostBallPosition,
    ghostBallPosition,
    showGhostBall
  };
};
