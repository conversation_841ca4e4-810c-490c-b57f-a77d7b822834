import { type Ref } from 'vue';
import { SpeedBarConfig, type SpeedBar, type ActivePoint } from '../config/poolTableConfig';

export const useSpeedBarDraw = (ctx: Ref<CanvasRenderingContext2D | null>, activePoint: Ref<ActivePoint | null>) => {
  // Preload icons
  const editIcon = new Image();
  const checkMarkIcon = new Image();
  editIcon.src = '/edit.svg';
  checkMarkIcon.src = '/check-mark.svg';

  const drawSpeedBar = (speedBar: SpeedBar) => {
    if (!ctx.value) return;

    const { borderWidth, padding, segmentCount, segmentSpacing } = SpeedBarConfig.DEFAULT;
    const { x, y, width, height, value, color, backgroundColor, borderColor } = speedBar;

    // Draw background
    ctx.value.fillStyle = backgroundColor;
    ctx.value.strokeStyle = borderColor;
    ctx.value.lineWidth = borderWidth;

    // Draw active speed bar
    if (activePoint.value && activePoint.value.isSpeedBar && activePoint.value.speedBar?.id === speedBar.id) {
      ctx.value.strokeStyle = SpeedBarConfig.DEFAULT.activeColor;
      speedBar.isActive = true;
    } else {
      speedBar.isActive = false;
    }

    ctx.value.beginPath();
    ctx.value.roundRect(x - width / 2, y - height / 2, width, height, 5);
    ctx.value.fill();
    ctx.value.stroke();

    // Draw the segments inside
    const filledSegments = Math.round(value);
    const innerWidth = width - padding * 2;
    const innerHeight = height - padding * 2;
    const totalSpacing = segmentSpacing * (segmentCount - 1);
    const segmentWidth = (innerWidth - totalSpacing) / segmentCount;
    // First segment
    const startX = x - width / 2 + padding;
    const startY = y - height / 2 + padding;

    for (let i = 0; i < segmentCount; i++) {
      const segmentX = startX + i * (segmentWidth + segmentSpacing);

      ctx.value.fillStyle = i < filledSegments ? color : '#FFFFFF';
      ctx.value.fillRect(segmentX, startY, segmentWidth, innerHeight);
    }

    if (speedBar.isActive) {
      drawSpeedBarActions(speedBar, padding, segmentSpacing, segmentCount);
    }
  };

  const drawSpeedBarActions = (speedBar: SpeedBar, padding: number, segmentSpacing: number, segmentCount: number) => {
    if (!ctx.value) return;

    const drawTriangleMark = () => {
      if (!ctx.value) return;

      const innerWidth = speedBar.width - padding * 2;
      const totalSpacing = segmentSpacing * (segmentCount - 1);
      const segmentWidth = (innerWidth - totalSpacing) / segmentCount;

      const activeIndex = Math.round(speedBar.value) - 1;

      // Starting X position of the first segment
      const startX = speedBar.x - speedBar.width / 2 + padding;
      const activeCenterX = startX + activeIndex * (segmentWidth + segmentSpacing) + segmentWidth / 2;

      const triangleSide = segmentWidth * SpeedBarConfig.TRIANGLE_MARK.size;
      // The height of the equilateral triangle is given by the formula:
      // h = ½(√3a), where h is the height of equilateral triangle and a is the side length
      // https://www.cuemath.com/geometry/height-of-equilateral-triangle/
      const triangleHeight = (triangleSide * Math.sqrt(3)) / 2;

      // Triangle's tip (equilateral triangle pointing downward).
      const tipX = activeCenterX;
      const tipY = speedBar.y - speedBar.height / 2 - padding / 2;
      const leftX = tipX - triangleSide / 2;
      const rightX = tipX + triangleSide / 2;
      const y = tipY - triangleHeight;

      // Draw the triangle
      ctx.value.beginPath();
      ctx.value.moveTo(leftX, y);
      ctx.value.lineTo(rightX, y);
      ctx.value.lineTo(tipX, tipY);
      ctx.value.closePath();

      ctx.value.fillStyle = SpeedBarConfig.TRIANGLE_MARK.backgroundColor;
      ctx.value.fill();
    };

    drawTriangleMark();
  };

  return {
    drawSpeedBar
  };
};
