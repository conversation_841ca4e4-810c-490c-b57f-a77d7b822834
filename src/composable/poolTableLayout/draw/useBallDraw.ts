import { type Ref } from 'vue';
import { BallConfig, type Ball, type ActivePoint } from '../config/poolTableConfig';
import { shadeColor } from '../utils/colorUtils';

export const useBallDraw = (ctx: Ref<CanvasRenderingContext2D | null>, activePoint: Ref<ActivePoint | null>) => {
  const drawBallHighlight = (ball: Ball) => {
    if (!ctx.value || !activePoint.value) return;

    // Check if activePoint is for a zone, not a ball
    if (activePoint.value.isZone) return;

    if (activePoint.value.ball.id !== ball.id || !ball.isInTable) return;
    if (activePoint.value.isPoint) return;

    ctx.value.fillStyle = BallConfig.HIGHLIGHT.COLOR;
    ctx.value.beginPath();
    ctx.value.arc(ball.x, ball.y, ball.radius + 3, 0, Math.PI * 2);
    ctx.value.fill();
  };

  const drawBallSolid = (ball: Ball) => {
    if (!ctx.value) return;

    ctx.value.save();

    if (ball.isGhost) {
      ctx.value.fillStyle = 'rgba(255, 255, 255, 0.3)';
      ctx.value.beginPath();
      ctx.value.arc(ball.x, ball.y, ball.radius, 0, Math.PI * 2);
      ctx.value.fill();

      ctx.value.strokeStyle = 'rgba(255, 255, 255, 0.8)';
      ctx.value.lineWidth = 1.5;
      ctx.value.setLineDash([3, 3]);
      ctx.value.stroke();
      ctx.value.restore();
      return;
    }

    ctx.value.shadowColor = 'rgba(0, 0, 0, 0.15)';
    ctx.value.shadowBlur = 4;
    ctx.value.shadowOffsetX = 2;
    ctx.value.shadowOffsetY = 2;

    const gradient = ctx.value.createRadialGradient(
      ball.x - ball.radius * 0.3,
      ball.y - ball.radius * 0.3,
      0,
      ball.x,
      ball.y,
      ball.radius * 1.1
    );

    const baseColor = ball.number && ball.number >= 9 ? '#FFFFFF' : ball.color;
    const darkColor = shadeColor(baseColor, -15);

    gradient.addColorStop(0, baseColor);
    gradient.addColorStop(0.6, baseColor);
    gradient.addColorStop(1, darkColor);

    ctx.value.fillStyle = gradient;
    ctx.value.beginPath();
    ctx.value.arc(ball.x, ball.y, ball.radius, 0, Math.PI * 2);
    ctx.value.fill();
    ctx.value.restore();

    const highlightGradient = ctx.value.createRadialGradient(
      ball.x - ball.radius * 0.4,
      ball.y - ball.radius * 0.4,
      0,
      ball.x - ball.radius * 0.4,
      ball.y - ball.radius * 0.4,
      ball.radius * 0.8
    );
    highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.4)');
    highlightGradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.1)');
    highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

    ctx.value.fillStyle = highlightGradient;
    ctx.value.beginPath();
    ctx.value.arc(ball.x, ball.y, ball.radius, 0, Math.PI * 2);
    ctx.value.fill();
  };

  const drawBallStripe = (ball: Ball) => {
    if (!ctx.value || !ball.number || ball.number < 9) return;

    ctx.value.save();
    ctx.value.beginPath();
    ctx.value.arc(ball.x, ball.y, ball.radius, 0, Math.PI * 2);
    ctx.value.clip();

    ctx.value.fillStyle = ball.color;
    const stripeHeight = ball.radius * 1.1;
    ctx.value.fillRect(ball.x - ball.radius, ball.y - stripeHeight / 2, ball.radius * 2, stripeHeight);
    ctx.value.restore();
  };

  const drawBallNumber = (ball: Ball) => {
    if (!ctx.value || ball.isGhost) return;

    const fontSize = ball.radius * 0.75;
    ctx.value.font = `bold ${fontSize}px Arial`;
    ctx.value.textAlign = 'center';
    ctx.value.textBaseline = 'middle';

    if (!ball.isCue) {
      ctx.value.fillStyle = '#ffffff';
      ctx.value.beginPath();
      ctx.value.arc(ball.x, ball.y, fontSize * 0.65, 0, Math.PI * 2);
      ctx.value.fill();

      ctx.value.fillStyle = '#000000';
      ctx.value.fillText(ball.number!.toString(), ball.x, ball.y + 1);
    } else {
      ctx.value.fillStyle = '#cc0000';
      ctx.value.beginPath();
      ctx.value.arc(ball.x, ball.y, 2.5, 0, Math.PI * 2);
      ctx.value.fill();
    }
  };

  const renderBall = (ball: Ball) => {
    if (!ball.isInTable) return;

    drawBallHighlight(ball);
    drawBallSolid(ball);
    drawBallStripe(ball);
    drawBallNumber(ball);
  };

  return {
    renderBall
  };
};
