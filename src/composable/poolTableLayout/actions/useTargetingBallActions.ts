import { type Ref } from 'vue';
import { type TargetingBall, type BallPosition } from '../config/poolTableConfig';

export const useTargetingBallActions = (targetingBalls: Ref<TargetingBall[]>) => {
  const updateTargetingBallPosition = (ball: TargetingBall, position: BallPosition) => {
    const existingBall = targetingBalls.value.find(b => b.id === ball.id);
    if (existingBall) {
      existingBall.x = position.x;
      existingBall.y = position.y;
    }
  };

  const deleteTargetingBall = (ballId: string) => {
    targetingBalls.value = targetingBalls.value.filter(b => b.id !== ballId);
  };

  return {
    updateTargetingBallPosition,
    deleteTargetingBall
  };
};
