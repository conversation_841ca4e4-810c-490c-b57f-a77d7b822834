import { type Ref } from 'vue';
import {
  TableConfig,
  BallConfig,
  TargetingBallConfig,
  TargetingZoneConfig,
  TextBoxConfig,
  SpeedBarConfig,
  type Ball,
  type ActivePoint,
  type TargetingBall,
  type DraggedItem,
  type BallPosition,
  type TargetingZone,
  type TextBox,
  type SpeedBar,
  type Point
} from '../config/poolTableConfig';

import { useBallActions } from './useBallActions';
import { useTargetingBallActions } from './useTargetingBallActions';
import { usePositions } from './usePositions';
import { useSpeedBarActions } from './useSpeedBarActions';

export const useMouseHandlers = (
  canvas: Ref<HTMLCanvasElement | null>,
  canvasId: Ref<string>,
  balls: Ref<Ball[]>,
  availableBalls: Ref<Ball[]>,
  targetingBalls: Ref<TargetingBall[]>,
  targetingZones: Ref<TargetingZone[]>,
  textBoxes: Ref<TextBox[]>,
  speedBars: Ref<SpeedBar[]>,
  isDragging: Ref<boolean>,
  draggedObj: Ref<DraggedItem | null>,
  activePoint: Ref<ActivePoint | null>,
  drawCanvas: (id: string) => void,
  updateGhostBallPosition: (x: number, y: number) => void,
  deleteGhostBall: () => void
) => {
  const { createBallCopy, updateBallPosition, deleteBall, updateBallPathsDuringDrag } = useBallActions(balls);
  const { updateTargetingBallPosition } = useTargetingBallActions(targetingBalls);
  const { updateSpeedBarValue } = useSpeedBarActions(speedBars);
  const {
    isPointInTable,
    isPositionInTableBounds,
    constrainQuadrilateralPositionToTable,
    isPointInSpeedBar,
    isPointInSpeedBarTool,
    isPointOutsideTable,
    isPointInBall,
    isPointInTargetingTool,
    isPointInSpeedBarTriangleMark,
    isPointInCenterDot,
    isPointInTargetingBall,
    isPointInTargetingZoneTool,
    isPointInTargetingZoneOrCorner,
    isPointInTargetingZoneCorner,
    isPointInTextBox,
    isPointInTextBoxEditValue,
    isPointInTextBoxRotateLeft,
    isPointInTextBoxRotateRight,
    isPointInTextBoxTool,
    constrainCirclePositionToTable,
    constrainCenterDotPosition,
    constrainRectanglePositionToTable
  } = usePositions();

  const getCanvasScale = (canvas: HTMLCanvasElement): number => {
    const canvasWidth =
      TableConfig.TABLE.LEFT +
      TableConfig.TABLE.WIDTH +
      TableConfig.TABLE.RAIL_THICKNESS +
      (TableConfig.TABLE.LEFT - TableConfig.TABLE.RAIL_THICKNESS);
    const containerWidth = canvas.parentElement?.clientWidth || canvasWidth;

    return containerWidth < canvasWidth ? containerWidth / canvasWidth : 1;
  };

  const getScaledMousePosition = (event: MouseEvent, canvas: HTMLCanvasElement) => {
    const scale = getCanvasScale(canvas);
    const rect = canvas.getBoundingClientRect();
    return {
      x: (event.clientX - rect.left) / scale,
      y: (event.clientY - rect.top) / scale
    };
  };

  const findClickedBall = (position: BallPosition): Ball | null => {
    return (
      availableBalls.value.find(ball => isPointInBall(position, ball)) ||
      balls.value.find(ball => isPointInBall(position, ball)) ||
      null
    );
  };

  const findClickedPathPoint = (position: BallPosition): ActivePoint | null => {
    for (const ball of balls.value) {
      if (!ball.paths) continue;

      for (let pathIndex = 0; pathIndex < ball.paths.length; pathIndex++) {
        const path = ball.paths[pathIndex];
        for (let pointIndex = 1; pointIndex < path.points.length; pointIndex++) {
          const point = path.points[pointIndex];
          const dx = position.x - point.x;
          const dy = position.y - point.y;
          if (Math.sqrt(dx * dx + dy * dy) < 12) {
            return { ball, isPoint: true, pathIndex, pointIndex };
          }
        }
      }
    }
    return null;
  };

  const findClickedElements = (position: Point) => {
    const activeTextBox = textBoxes.value.filter(textBox => textBox.isActive);
    const activeSpeedBar = speedBars.value.filter(speedBar => speedBar.isActive);

    return {
      clickedBall: balls.value.find(ball => isPointInBall(position, ball)),
      clickedTargetingBall: targetingBalls.value.find(ball => isPointInTargetingBall(position, ball)),
      clickedTextBox: textBoxes.value.find(textBox => isPointInTextBox(canvas.value!, position, textBox)),
      clickedSpeedBar: speedBars.value.find(speedBar => isPointInSpeedBar(position, speedBar)),
      clickedZone: targetingZones.value.find(zone => isPointInTargetingZoneOrCorner(position, zone)),
      clickedTextBoxEditValueIcon: activeTextBox.find(activeTextBox =>
        isPointInTextBoxEditValue(canvas.value!, position, activeTextBox)
      ),
      clickedTextBoxRotateRightIcon: activeTextBox.find(activeTextBox =>
        isPointInTextBoxRotateRight(canvas.value!, position, activeTextBox)
      ),
      clickedTextBoxRotateLeftIcon: activeTextBox.find(activeTextBox =>
        isPointInTextBoxRotateLeft(canvas.value!, position, activeTextBox)
      ),
      clickSpeedBarAction: activeSpeedBar.find(activeSpeedBar =>
        isPointInSpeedBarTriangleMark(position, activeSpeedBar)
      )
    };
  };

  const handleDragging = (event: MouseEvent) => {
    if (!canvas.value) return;

    const position = getScaledMousePosition(event, canvas.value);

    if (isPointOutsideTable(position.x, position.y)) {
      activePoint!.value = null;
      drawCanvas(canvasId.value);
    }

    // Check if clicking on targeting tool
    if (isPointInTargetingTool(position)) {
      const newBall = TargetingBallConfig.createTargetingBall(position.x, position.y);
      // Set initial smaller radius for toolbox
      newBall.radius = newBall.radius * BallConfig.TOOLBOX.SCALE;
      targetingBalls.value.push(newBall);
      draggedObj.value = { type: 'targeting', item: newBall };
      isDragging.value = true;
      return;
    }

    // Check for path point dragging
    const clickedPoint = findClickedPathPoint(position);

    if (clickedPoint) {
      activePoint.value = clickedPoint;

      draggedObj.value = {
        type: 'pathPoint',
        item: clickedPoint.ball as Ball,
        pathIndex: clickedPoint.pathIndex,
        pointIndex: clickedPoint.pointIndex
      };
      isDragging.value = true;
      drawCanvas(canvasId.value);
      return;
    }

    // Check for targeting ball interactions first
    const targetingBallWithDot = targetingBalls.value.find(
      ball => ball.isInTable && isPointInCenterDot(position, ball)
    );
    const clickedTargetingBall = targetingBalls.value.find(ball => isPointInTargetingBall(position, ball));

    if (targetingBallWithDot || clickedTargetingBall || isPointInTargetingTool(position)) {
      // Reset both active states when interacting with targeting balls
      activePoint.value = null;
      isDragging.value = true;

      if (targetingBallWithDot) {
        draggedObj.value = { type: 'centerDot', item: targetingBallWithDot };
      } else if (isPointInTargetingTool(position)) {
        const newBall = TargetingBallConfig.createTargetingBall(position.x, position.y);
        targetingBalls.value.push(newBall);
        draggedObj.value = { type: 'targeting', item: newBall };
      } else if (clickedTargetingBall) {
        draggedObj.value = { type: 'targeting', item: clickedTargetingBall };
      }
      drawCanvas(canvasId.value);
      return;
    }

    // Handle regular balls
    const clickedBall = findClickedBall(position);
    if (clickedBall) {
      isDragging.value = true;
      if (!clickedBall.isInTable) {
        const newBall = createBallCopy(clickedBall);
        newBall.x = position.x;
        newBall.y = position.y;
        draggedObj.value = { type: 'ball', item: newBall };
        balls.value.push(newBall);
      } else {
        draggedObj.value = { type: 'ball', item: clickedBall };
      }
    }

    // Check if clicking on text box tool
    if (isPointInTextBoxTool(position)) {
      const newTextbox = TextBoxConfig.createTextBox(position.x, position.y);

      textBoxes.value.push(newTextbox);
      draggedObj.value = { type: 'textBox', item: newTextbox };
      isDragging.value = true;

      return;
    }

    const {
      clickedSpeedBar,
      clickedTextBox,
      clickedTextBoxEditValueIcon,
      clickedTextBoxRotateLeftIcon,
      clickedTextBoxRotateRightIcon,
      clickedZone,
      clickSpeedBarAction
    } = findClickedElements(position);

    if (clickedBall || clickedTextBoxEditValueIcon || clickedTextBoxRotateLeftIcon || clickedTextBoxRotateRightIcon) {
      return;
    }

    if (clickedTextBox) {
      isDragging.value = true;
      draggedObj.value = { type: 'textBox', item: clickedTextBox };

      drawCanvas(canvasId.value);
      return;
    }

    // Check for targeting zone interaction
    if (isPointInTargetingZoneTool(position)) {
      const newZone = TargetingZoneConfig.createTargetingZone(position.x, position.y);
      targetingZones.value.push(newZone);
      draggedObj.value = { type: 'targetingZone', item: newZone };
      isDragging.value = true;
      return;
    }

    // Check if clicking on speed bar tool
    if (isPointInSpeedBarTool(position)) {
      const newSpeedBar = SpeedBarConfig.createSpeedBar(position.x, position.y);

      speedBars.value.push(newSpeedBar);
      draggedObj.value = { type: 'speedBar', item: newSpeedBar };
      isDragging.value = true;

      return;
    }

    if (clickedSpeedBar) {
      isDragging.value = true;
      draggedObj.value = { type: 'speedBar', item: clickedSpeedBar };

      drawCanvas(canvasId.value);
      return;
    }

    if (clickSpeedBarAction) {
      isDragging.value = true;
      draggedObj.value = { type: 'speedBarTriangleMark', item: clickSpeedBarAction };

      drawCanvas(canvasId.value);
      return;
    }

    // Check for existing targeting zone
    if (clickedZone) {
      const ball = activePoint.value?.ball as Ball;
      if (activePoint.value && ball && (ball.isCue || ball.number)) {
        return;
      }

      // Check if clicking on a corner
      for (let i = 0; i < 4; i++) {
        if (isPointInTargetingZoneCorner(position, clickedZone, i)) {
          activePoint.value = {
            ball: {} as Ball, // Placeholder, not used for zones
            isPoint: false,
            isTargetingBall: false,
            pathIndex: -1,
            pointIndex: -1,
            isZone: true,
            isZoneCorner: true,
            cornerIndex: i,
            zone: clickedZone
          };

          draggedObj.value = {
            type: 'targetingZoneCorner',
            item: clickedZone,
            cornerIndex: i
          };

          isDragging.value = true;
          drawCanvas(canvasId.value);
          return;
        }
      }

      activePoint.value = {
        isZone: true,
        zone: clickedZone,
        ball: null as any,
        isPoint: false,
        pathIndex: -1,
        pointIndex: -1
      };
      // Start dragging immediately
      isDragging.value = true;
      draggedObj.value = { type: 'targetingZone', item: clickedZone };
      drawCanvas(canvasId.value);
      return;
    }
  };

  const handleMouseMove = (event: MouseEvent) => {
    if (!canvas.value) return;
    const position = getScaledMousePosition(event, canvas.value);

    // Update ghost ball if not have active point
    if (!activePoint.value && deleteGhostBall && !isDragging.value) {
      deleteGhostBall();
    }

    // Update ghost ball position if not dragging
    if (
      activePoint.value &&
      updateGhostBallPosition &&
      !isDragging.value &&
      !activePoint.value.isZone &&
      !activePoint.value.isTextBox &&
      !activePoint.value.isSpeedBar
    ) {
      updateGhostBallPosition(position.x, position.y);
      drawCanvas(canvasId.value);
    }

    if (isDragging.value && draggedObj.value) {
      if (draggedObj.value.type === 'targetingZoneCorner') {
        const zone = draggedObj.value.item;
        const cornerIndex = draggedObj.value.cornerIndex;

        // Constrain position to table bounds
        const constrainedPosition = constrainCirclePositionToTable(position, 0);

        // Update the specific corner that's being dragged
        switch (cornerIndex) {
          case 0: // Top-left
            zone.corners.topLeft = constrainedPosition;
            break;
          case 1: // Top-right
            zone.corners.topRight = constrainedPosition;
            break;
          case 2: // Bottom-right
            zone.corners.bottomRight = constrainedPosition;
            break;
          case 3: // Bottom-left
            zone.corners.bottomLeft = constrainedPosition;
            break;
        }

        // Recalculate center and size for rendering purposes
        const minX = Math.min(
          zone.corners.topLeft.x,
          zone.corners.topRight.x,
          zone.corners.bottomRight.x,
          zone.corners.bottomLeft.x
        );
        const maxX = Math.max(
          zone.corners.topLeft.x,
          zone.corners.topRight.x,
          zone.corners.bottomRight.x,
          zone.corners.bottomLeft.x
        );
        const minY = Math.min(
          zone.corners.topLeft.y,
          zone.corners.topRight.y,
          zone.corners.bottomRight.y,
          zone.corners.bottomLeft.y
        );
        const maxY = Math.max(
          zone.corners.topLeft.y,
          zone.corners.topRight.y,
          zone.corners.bottomRight.y,
          zone.corners.bottomLeft.y
        );

        zone.x = (minX + maxX) / 2;
        zone.y = (minY + maxY) / 2;
        zone.size = Math.max(maxX - minX, maxY - minY);
      } else if (draggedObj.value.type === 'targetingZone') {
        const zone = draggedObj.value.item;
        const constrainedPosition = constrainQuadrilateralPositionToTable(position, zone);

        // Calculate the movement delta
        const dx = constrainedPosition.x - zone.x;
        const dy = constrainedPosition.y - zone.y;

        // Update all corners
        zone.corners.topLeft.x += dx;
        zone.corners.topLeft.y += dy;
        zone.corners.topRight.x += dx;
        zone.corners.topRight.y += dy;
        zone.corners.bottomRight.x += dx;
        zone.corners.bottomRight.y += dy;
        zone.corners.bottomLeft.x += dx;
        zone.corners.bottomLeft.y += dy;

        // Update center
        zone.x = constrainedPosition.x;
        zone.y = constrainedPosition.y;
      } else if (draggedObj.value.type === 'pathPoint') {
        const { item: ball, pathIndex, pointIndex } = draggedObj.value;
        if (ball.paths?.[pathIndex]?.points[pointIndex]) {
          const newPosition = {
            x: position.x,
            y: position.y
          };

          if (isPointInTable(newPosition)) {
            ball.paths[pathIndex].points[pointIndex] = newPosition;
          }
        }
      } else if (draggedObj.value.type === 'centerDot') {
        const ball = draggedObj.value.item;
        const newPosition = constrainCenterDotPosition(position, ball);
        ball.centerDot.x = newPosition.x - ball.x;
        ball.centerDot.y = newPosition.y - ball.y;
      } else if (draggedObj.value.type === 'targeting') {
        const ball = draggedObj.value.item;

        // Only apply constraints if the ball is in the table or being moved into the table
        if (ball.isInTable || isPositionInTableBounds(position, ball.radius)) {
          const constrainedPosition = constrainCirclePositionToTable(position, ball.radius);
          ball.x = constrainedPosition.x;
          ball.y = constrainedPosition.y;
        } else {
          // Allow free movement outside the table (for initial placement or deletion)
          ball.x = position.x;
          ball.y = position.y;
        }
      } else if (draggedObj.value.type == 'ball') {
        // For regular balls and targeting balls, constrain movement to table bounds
        const ball = draggedObj.value.item;
        const radius = draggedObj.value.type === 'ball' ? BallConfig.RADIUS : ball.radius;

        // Only constrain if the ball is already in the table or being dragged from toolbox
        if (ball.isInTable || isPositionInTableBounds(position, radius)) {
          const constrainedPosition = constrainCirclePositionToTable(position, radius);
          ball.x = constrainedPosition.x;
          ball.y = constrainedPosition.y;

          // Update the path's starting point in real-time during dragging
          updateBallPathsDuringDrag(ball);
        }
      } else if (draggedObj.value.type == 'textBox') {
        const textBox = draggedObj.value.item;
        const constrainedPosition = constrainRectanglePositionToTable(
          position,
          TextBoxConfig.DEFAULT.width,
          TextBoxConfig.DEFAULT.height
        );

        textBox.x = constrainedPosition.x;
        textBox.y = constrainedPosition.y;
      } else if (draggedObj.value.type == 'speedBar') {
        const speedBar = draggedObj.value.item;
        const constrainedPosition = constrainRectanglePositionToTable(
          position,
          SpeedBarConfig.DEFAULT.width,
          SpeedBarConfig.DEFAULT.height
        );

        speedBar.x = constrainedPosition.x;
        speedBar.y = constrainedPosition.y;
      } else if (draggedObj.value.type == 'speedBarTriangleMark') {
        const speedBar = draggedObj.value.item;
        updateSpeedBarValue(speedBar, position.x);
      }
    } else {
      if (
        activePoint.value &&
        !activePoint.value.isZone &&
        !activePoint.value.isTargetingBall &&
        !activePoint.value.isTextBox &&
        !activePoint.value.isSpeedBar
      ) {
        updateGhostBallPosition(position.x, position.y);
      }
    }

    drawCanvas(canvasId.value);
  };

  const handleMouseUp = (event: MouseEvent) => {
    if (!isDragging.value || !draggedObj.value) return;

    const position = getScaledMousePosition(event, canvas.value!);
    const { type, item } = draggedObj.value;

    if (type === 'targetingZone' || type === 'targetingZoneCorner') {
      // Check if the zone is dropped outside the table or in the toolbox area
      if (
        isPointOutsideTable(position.x, position.y) ||
        position.y > TableConfig.TABLE.TOP + TableConfig.TABLE.HEIGHT + 20
      ) {
        // Remove the targeting zone if dropped outside the table
        targetingZones.value = targetingZones.value.filter(zone => zone.id !== item.id);

        // Clear active point if it was this zone
        if (activePoint.value?.isZone && activePoint.value.zone?.id === item.id) {
          activePoint.value = null;
        }
      }
    } else if (type === 'targeting') {
      if (position.y > TableConfig.TABLE.TOP + TableConfig.TABLE.HEIGHT + 20) {
        targetingBalls.value = targetingBalls.value.filter(b => b.id !== item.id);
      } else if (isPositionInTableBounds(position, item.radius)) {
        const constrainedPosition = constrainCirclePositionToTable(position, item.radius);
        updateTargetingBallPosition(item, constrainedPosition);
        item.isInTable = true;
      }
    } else if (type === 'ball') {
      if (item.isInTable) {
        if (position.y > TableConfig.TABLE.TOP + TableConfig.TABLE.HEIGHT + 20) {
          deleteBall(item.id);
        } else if (isPositionInTableBounds(position, BallConfig.RADIUS)) {
          const constrainedPosition = constrainCirclePositionToTable(position, BallConfig.RADIUS);
          updateBallPosition(item, constrainedPosition);
        }
      }
    } else if (type === 'textBox') {
      if (position.y > TableConfig.TABLE.TOP + TableConfig.TABLE.HEIGHT + 20) {
        textBoxes.value = textBoxes.value.filter(b => b.id !== item.id);
      }
    } else if (type === 'speedBar') {
      if (position.y > TableConfig.TABLE.TOP + TableConfig.TABLE.HEIGHT + 20) {
        speedBars.value = speedBars.value.filter(b => b.id !== item.id);
      }
    }

    isDragging.value = false;
    draggedObj.value = null;
    drawCanvas(canvasId.value);
  };

  const handleClick = (event: MouseEvent) => {
    if (!canvas.value) return;

    const position = getScaledMousePosition(event, canvas.value);

    // Check if click is outside the table bounds
    if (isPointOutsideTable(position.x, position.y)) {
      activePoint!.value = null;

      drawCanvas(canvasId.value);
      return;
    }

    const {
      clickedBall,
      clickedSpeedBar,
      clickedTargetingBall,
      clickedTextBox,
      clickedTextBoxEditValueIcon,
      clickedTextBoxRotateLeftIcon,
      clickedTextBoxRotateRightIcon
    } = findClickedElements(position);

    if (clickedBall) {
      activePoint.value = {
        ball: clickedBall,
        isPoint: false,
        isTargetingBall: false,
        pathIndex: -1,
        pointIndex: -1
      };
    } else if (clickedTargetingBall) {
      activePoint.value = {
        ball: clickedTargetingBall,
        isTargetingBall: true,
        isPoint: false,
        pathIndex: -1,
        pointIndex: -1
      };
    } else if (clickedTextBox) {
      activePoint.value = {
        ball: {} as Ball, // Placeholder, not used for text boxes
        isPoint: false,
        pathIndex: -1,
        pointIndex: -1,
        isTextBox: true,
        textBox: clickedTextBox
      };
    } else if (clickedTextBoxEditValueIcon) {
      const input = prompt('Edit Text', clickedTextBoxEditValueIcon.value);
      if (input !== null && input.trim() !== '') {
        clickedTextBoxEditValueIcon.value = input.trim();
      }
    } else if (clickedTextBoxRotateRightIcon) {
      clickedTextBoxRotateRightIcon.rotation += TextBoxConfig.DEFAULT.rotationStep;
    } else if (clickedTextBoxRotateLeftIcon) {
      clickedTextBoxRotateLeftIcon.rotation -= TextBoxConfig.DEFAULT.rotationStep;
    } else if (clickedSpeedBar) {
      activePoint.value = {
        ball: {} as Ball, // Placeholder, not used for speed bars
        isPoint: false,
        pathIndex: -1,
        pointIndex: -1,
        isSpeedBar: true,
        speedBar: clickedSpeedBar
      };
    }

    drawCanvas(canvasId.value);
  };

  return {
    handleDragging,
    handleMouseMove,
    handleMouseUp,
    handleClick
  };
};
