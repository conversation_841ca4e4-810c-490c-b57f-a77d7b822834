import { type Ref } from 'vue';
import { type ActivePoint, type Ball, type BallPosition, type TargetingBall } from '../config/poolTableConfig';

import { usePositions } from './usePositions';

export const usePathActions = (
  canvasId: Ref<string>,
  balls: Ref<Ball[]>,
  activePoint: Ref<ActivePoint | null>,
  targetingBalls: Ref<TargetingBall[]>,
  drawCanvas: (id: string) => void
) => {
  const { isPointInTable, isPointInTargetingBall } = usePositions();

  const setNewActivePoint = (ball: Ball, isPoint: boolean, pathIndex: number, pointIndex: number) => {
    activePoint.value = {
      ball,
      isPoint,
      isTargetingBall: false,
      pathIndex,
      pointIndex
    };
  };

  // Path deletion functions
  const deletePathPointForCue = (ballId: string, pointIndex: number) => {
    const ball = balls.value.find(b => b.id === ballId);
    if (!ball?.paths) return;

    ball.paths.forEach(path => {
      if (path.ballId === ballId && pointIndex % 2 === 0) {
        path.points.splice(pointIndex - 1, path.points.length);
      }
    });

    // If the last point is deleted, set ball as active
    if (pointIndex === 2) {
      setNewActivePoint(ball, false, -1, -1);
      return;
    }

    // For cue balls, update active point when the index is even
    if (pointIndex % 2 === 0) {
      setNewActivePoint(ball, true, ball.paths!.length - 1, pointIndex - 2);
    }
  };

  const deletePathPointForRegularBall = (ballId: string, pointIndex: number) => {
    const ball = balls.value.find(b => b.id === ballId);
    if (!ball?.paths) return;

    ball.paths.forEach(path => {
      if (path.ballId === ballId) {
        path.points.splice(pointIndex, path.points.length);
      }
    });

    // If the last point is deleted, set ball as active
    if (pointIndex === 1) {
      setNewActivePoint(ball, false, -1, -1);
      return;
    }

    // Update active point for a regular ball
    setNewActivePoint(ball, true, ball.paths!.length - 1, pointIndex - 1);
  };

  const deletePathPoint = (ballId: string, pointIndex: number) => {
    const ball = balls.value.find(b => b.id === ballId);
    if (!ball?.paths) return;

    if (ball.isCue) {
      deletePathPointForCue(ballId, pointIndex);
    } else {
      deletePathPointForRegularBall(ballId, pointIndex);
    }
  };

  // Helper functions for path creation
  const isNearExistingElements = (newPoint: BallPosition): boolean => {
    // Check if clicking near any ball
    const isNearBall = balls.value.some(ball => {
      const dx = newPoint.x - ball.x;
      const dy = newPoint.y - ball.y;
      return Math.sqrt(dx * dx + dy * dy) < ball.radius * 1.5;
    });

    // Check if clicking near any path point
    const isNearPathPoint = balls.value.some(ball => {
      if (!ball.paths) return false;
      return ball.paths.some(path =>
        path.points.some(point => {
          const dx = newPoint.x - point.x;
          const dy = newPoint.y - point.y;
          return Math.sqrt(dx * dx + dy * dy) < 12;
        })
      );
    });

    return isNearBall || isNearPathPoint;
  };

  const initializeBallPath = (ball: Ball): void => {
    if (!ball.paths) {
      ball.paths = [];
    }

    if (ball.paths.length === 0) {
      ball.paths.push({
        ballId: ball.id,
        points: [{ x: ball.x, y: ball.y }],
        color: ball.color
      });
    }
  };

  const createCueBallPathSegment = (
    ball: Ball,
    currentPath: any,
    newPoint: BallPosition,
    isTargetingBall: boolean
  ): void => {
    // If this is the first segment (only ball position exists)
    if (currentPath.points.length === 1) {
      // Add the end point first (straight line initially)
      currentPath.points.push(newPoint);

      // Add a control point exactly in the middle (for a straight line initially)
      const startPoint = currentPath.points[0];
      const endPoint = currentPath.points[1];
      const controlPoint = {
        x: (startPoint.x + endPoint.x) / 2,
        y: (startPoint.y + endPoint.y) / 2
      };

      // Insert the control point between start and end
      currentPath.points.splice(1, 0, controlPoint);

      // Set the end point as active
      activePoint.value = {
        ball,
        isPoint: true,
        isTargetingBall,
        pathIndex: ball.paths!.length - 1,
        pointIndex: 2 // The end point (index 2)
      };
    }
    // If we already have a path segment and want to extend it
    else if (currentPath.points.length >= 3 && currentPath.points.length % 2 === 1) {
      const lastPoint = currentPath.points[currentPath.points.length - 1];

      // Add the new end point (straight line initially)
      currentPath.points.push(newPoint);

      // Add a control point exactly in the middle
      const controlPoint = {
        x: (lastPoint.x + newPoint.x) / 2,
        y: (lastPoint.y + newPoint.y) / 2
      };

      // Insert the control point between the previous end and new end
      currentPath.points.splice(currentPath.points.length - 1, 0, controlPoint);

      // Set the end point as active
      activePoint.value = {
        ball,
        isPoint: true,
        isTargetingBall,
        pathIndex: ball.paths!.length - 1,
        pointIndex: currentPath.points.length - 1
      };
    }
  };

  const createRegularBallPathSegment = (
    ball: Ball,
    currentPath: any,
    newPoint: BallPosition,
    isTargetingBall: boolean
  ): void => {
    // For regular balls, just add the point
    currentPath.points.push(newPoint);

    // Set the new point as active point
    activePoint.value = {
      ball,
      isPoint: true,
      isTargetingBall,
      pathIndex: ball.paths!.length - 1,
      pointIndex: currentPath.points.length - 1
    };
  };

  // Main path creation handler
  const handlePathCreation = (event: MouseEvent) => {
    const canvas = event.target as HTMLCanvasElement;
    const ctx = canvas.getContext('2d');

    // Get the current scale from the canvas transform
    const scale = ctx ? ctx.getTransform().a : 1;

    // Adjust coordinates based on scale
    const newPoint = {
      x: event.offsetX / scale,
      y: event.offsetY / scale
    };

    // Early returns for invalid conditions
    if (!isPointInTable(newPoint)) return;
    if (
      !activePoint.value ||
      activePoint.value.isTargetingBall ||
      activePoint.value.isZoneCorner ||
      activePoint.value.isTextBox ||
      activePoint.value.isSpeedBar
    )
      return;
    if (isNearExistingElements(newPoint)) return;

    // Get the current working ball
    const currentWorkingBall = activePoint.value.ball as Ball;
    if (!currentWorkingBall || currentWorkingBall.isGhost) return;

    // Initialize ball path if needed
    initializeBallPath(currentWorkingBall);

    // Get the current path and check for targeting ball
    const currentPath = currentWorkingBall.paths![currentWorkingBall.paths!.length - 1];
    const clickedTargetingBall = targetingBalls.value.find(ball => isPointInTargetingBall(newPoint, ball));
    const isTargetingBall = !!clickedTargetingBall;

    // Create path segment based on ball type
    if (currentWorkingBall.isCue) {
      createCueBallPathSegment(currentWorkingBall, currentPath, newPoint, isTargetingBall);
    } else {
      createRegularBallPathSegment(currentWorkingBall, currentPath, newPoint, isTargetingBall);
    }

    drawCanvas(canvasId.value);
  };

  return {
    deletePathPoint,
    handlePathCreation
  };
};
