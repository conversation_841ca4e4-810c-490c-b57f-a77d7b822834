import { type Ref } from 'vue';
import { BallConfig, type Ball, type BallPosition } from '../config/poolTableConfig';

export const useBallActions = (balls: Ref<Ball[]>) => {
  const generateBallId = () => `ball_${Math.random().toString(36).substring(2, 15)}`;

  const createBallCopy = (ball: Ball): Ball => {
    const newId = generateBallId();
    return {
      ...ball,
      id: newId,
      isInTable: true,
      radius: BallConfig.RADIUS,
      paths: ball.paths
        ? ball.paths.map(path => ({
            ...path,
            ballId: newId,
            points: [...path.points]
          }))
        : []
    };
  };

  const updateBallPosition = (ball: Ball, position: BallPosition) => {
    const existingBall = balls.value.find(b => b.id === ball.id);
    if (existingBall) {
      existingBall.x = position.x;
      existingBall.y = position.y;
      if (existingBall.paths) {
        existingBall.paths.forEach(path => {
          if (path.ballId === existingBall.id && path.points.length > 0) {
            path.points[0] = { x: position.x, y: position.y };
          }
        });
      }
    }
  };

  const updateBallPathsDuringDrag = (ball: Ball) => {
    if (ball.paths) {
      ball.paths.forEach(path => {
        if (path.ballId === ball.id && path.points.length > 0) {
          path.points[0] = { x: ball.x, y: ball.y };
        }
      });
    }
  };

  const deleteBall = (ballId: string) => {
    balls.value = balls.value.filter(b => b.id !== ballId);
  };

  return {
    createBallCopy,
    updateBallPosition,
    updateBallPathsDuringDrag,
    deleteBall
  };
};
