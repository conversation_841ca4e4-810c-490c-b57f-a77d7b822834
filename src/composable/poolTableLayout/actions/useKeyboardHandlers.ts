import { type Ref } from 'vue';

import {
  type ActivePoint,
  type Ball,
  type TargetingBall,
  type TargetingZone,
  type TextBox,
  type SpeedBar
} from '../config/poolTableConfig';

import { useTargetingBallActions } from './useTargetingBallActions';
import { useBallActions } from './useBallActions';
import { usePathActions } from './usePathActions';
import { useTargetZoneActions } from './useTargetingZoneActions';
import { useTextBoxActions } from './useTextBoxActions';
import { useSpeedBarActions } from './useSpeedBarActions';

export const useKeyboardHandlers = (
  canvasId: Ref<string>,
  balls: Ref<Ball[]>,
  targetingBalls: Ref<TargetingBall[]>,
  targetingZones: Ref<TargetingZone[]>,
  textBoxes: Ref<TextBox[]>,
  speedBars: Ref<SpeedBar[]>,
  activePoint: Ref<ActivePoint | null>,
  drawCanvas: (id: string) => void
) => {
  const { deleteBall } = useBallActions(balls);
  const { deletePathPoint } = usePathActions(canvasId, balls, activePoint, targetingBalls, drawCanvas);
  const { deleteSpeedBar } = useSpeedBarActions(speedBars);
  const { deleteTargetingBall } = useTargetingBallActions(targetingBalls);
  const { deleteTextBox } = useTextBoxActions(textBoxes);
  const { deleteTargetingZone } = useTargetZoneActions(targetingZones);

  const handleDeletePoint = () => {
    if (!activePoint.value) return;

    if (activePoint.value.isTargetingBall) {
      deleteTargetingBall(activePoint.value.ball.id);
      drawCanvas(canvasId.value);
      return;
    }

    if (activePoint.value.isPoint) {
      deletePathPoint(activePoint.value.ball.id, activePoint.value.pointIndex);
    } else if (activePoint.value.isZone) {
      deleteTargetingZone(activePoint.value!.zone!.id);
    } else if (activePoint.value.isTextBox) {
      deleteTextBox(activePoint.value.textBox!.id);
    } else if (activePoint.value.isSpeedBar) {
      deleteSpeedBar(activePoint.value.speedBar!.id);
    } else {
      deleteBall(activePoint.value.ball.id);
      activePoint.value = null;
    }

    drawCanvas(canvasId.value);
  };

  const handleKeyUp = (event: KeyboardEvent) => {
    if (event.key === 'Delete' || event.key === 'Backspace') {
      handleDeletePoint();
    }
  };

  return {
    handleDeletePoint,
    handleKeyUp
  };
};
