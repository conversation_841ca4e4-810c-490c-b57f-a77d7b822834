import { type Ref } from 'vue';
import { SpeedBarConfig, type SpeedBar } from '../config/poolTableConfig';

export const useSpeedBarActions = (speedBars: Ref<SpeedBar[]>) => {
  const deleteSpeedBar = (speedBarId: string) => {
    speedBars.value = speedBars.value.filter(speedBar => speedBar.id !== speedBarId);
  };

  const updateSpeedBarValue = (speedBar: SpeedBar, clickX: number) => {
    if (!speedBar.isActive) return;

    const { segmentCount, padding } = SpeedBarConfig.DEFAULT;
    const innerWidth = speedBar.width - padding * 2;

    const speedBarLeft = speedBar.x - speedBar.width / 2;
    const innerLeft = speedBarLeft + padding;

    if (clickX < innerLeft || clickX > innerLeft + innerWidth) return;

    const relativeX = clickX - innerLeft;
    const segmentWidth = innerWidth / segmentCount;
    const segmentIndex = Math.floor(relativeX / segmentWidth);
    const newValue = segmentIndex + 1;

    if (newValue >= 1 && newValue <= segmentCount) {
      speedBar.value = newValue;
    }
  };

  return {
    deleteSpeedBar,
    updateSpeedBarValue
  };
};
