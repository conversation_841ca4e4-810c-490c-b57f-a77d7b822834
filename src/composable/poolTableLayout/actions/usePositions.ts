import {
  BallConfig,
  TableConfig,
  ToolboxConfig,
  TargetingBallConfig,
  TargetingZoneConfig,
  TextBoxConfig,
  SpeedBarConfig,
  type BallPosition,
  type TargetingBall,
  type Ball,
  type TargetingZone,
  type Point,
  type TextBox,
  type SpeedBar
} from '../config/poolTableConfig';

export const usePositions = () => {
  const isPointInTable = (point: BallPosition): boolean => {
    const { TABLE } = TableConfig;
    return (
      point.x >= TABLE.LEFT &&
      point.x <= TABLE.LEFT + TABLE.WIDTH &&
      point.y >= TABLE.TOP &&
      point.y <= TABLE.TOP + TABLE.HEIGHT
    );
  };

  const isPositionInTableBounds = (position: BallPosition, radius: number): boolean => {
    const { TABLE } = TableConfig;
    return (
      position.x >= TABLE.LEFT + radius &&
      position.x <= TABLE.LEFT + TABLE.WIDTH - radius &&
      position.y >= TABLE.TOP + radius &&
      position.y <= TABLE.TOP + TABLE.HEIGHT - radius
    );
  };

  const constrainCirclePositionToTable = (position: BallPosition, radius: number): BallPosition => {
    const { TABLE } = TableConfig;
    return {
      x: Math.max(TABLE.LEFT + radius, Math.min(position.x, TABLE.LEFT + TABLE.WIDTH - radius)),
      y: Math.max(TABLE.TOP + radius, Math.min(position.y, TABLE.TOP + TABLE.HEIGHT - radius))
    };
  };

  const constrainQuadrilateralPositionToTable = (position: Point, zone: TargetingZone): Point => {
    const { TABLE } = TableConfig;

    const offsets = [
      {
        x: zone.corners.topLeft.x - zone.x,
        y: zone.corners.topLeft.y - zone.y
      },
      {
        x: zone.corners.topRight.x - zone.x,
        y: zone.corners.topRight.y - zone.y
      },
      {
        x: zone.corners.bottomRight.x - zone.x,
        y: zone.corners.bottomRight.y - zone.y
      },
      {
        x: zone.corners.bottomLeft.x - zone.x,
        y: zone.corners.bottomLeft.y - zone.y
      }
    ];

    let minX = -Infinity;
    let maxX = Infinity;
    offsets.forEach(off => {
      // For the corner to be inside the table:
      // TABLE.LEFT <= newCenter.x + off.x <= TABLE.LEFT + TABLE.WIDTH
      // Rearranged gives the allowed range for newCenter.x
      const candidateMinX = TABLE.LEFT - off.x;
      const candidateMaxX = TABLE.LEFT + TABLE.WIDTH - off.x;
      minX = Math.max(minX, candidateMinX);
      maxX = Math.min(maxX, candidateMaxX);
    });

    let minY = -Infinity;
    let maxY = Infinity;
    offsets.forEach(off => {
      // TABLE.TOP <= newCenter.y + off.y <= TABLE.TOP + TABLE.HEIGHT
      const candidateMinY = TABLE.TOP - off.y;
      const candidateMaxY = TABLE.TOP + TABLE.HEIGHT - off.y;
      minY = Math.max(minY, candidateMinY);
      maxY = Math.min(maxY, candidateMaxY);
    });

    return {
      x: Math.max(minX, Math.min(position.x, maxX)),
      y: Math.max(minY, Math.min(position.y, maxY))
    };
  };

  const constrainRectanglePositionToTable = (position: Point, width: number, height: number): Point => {
    const { TABLE } = TableConfig;
    return {
      x: Math.max(TABLE.LEFT + width / 2, Math.min(position.x, TABLE.LEFT + TABLE.WIDTH - width / 2)),
      y: Math.max(TABLE.TOP + height / 2, Math.min(position.y, TABLE.TOP + TABLE.HEIGHT - height / 2))
    };
  };

  const isPointOutsideTable = (x: number, y: number): boolean => {
    const { TABLE } = TableConfig;
    return x < TABLE.LEFT || x > TABLE.LEFT + TABLE.WIDTH || y < TABLE.TOP || y > TABLE.TOP + TABLE.HEIGHT;
  };

  const isPointInBall = (point: BallPosition, ball: Ball): boolean => {
    const dx = point.x - ball.x;
    const dy = point.y - ball.y;
    return Math.sqrt(dx * dx + dy * dy) < ball.radius * 1.5;
  };

  const isPointInTargetingTool = (point: BallPosition): boolean => {
    const { TABLE } = TableConfig;
    const x = TABLE.LEFT - TABLE.RAIL_THICKNESS + ToolboxConfig.CONSTANTS.TARGETING_TOOL_OFFSET;
    const y = TABLE.TOP + TABLE.HEIGHT + 160;

    return Math.sqrt(Math.pow(point.x - x, 2) + Math.pow(point.y - y, 2)) < TargetingBallConfig.DEFAULT.radius;
  };

  const isPointInTargetingBall = (point: BallPosition, ball: TargetingBall): boolean => {
    const dx = point.x - ball.x;
    const dy = point.y - ball.y;
    return Math.sqrt(dx * dx + dy * dy) < ball.radius;
  };

  const isPointInCenterDot = (point: BallPosition, ball: TargetingBall): boolean => {
    const dx = point.x - (ball.x + (ball.centerDot?.x || 0));
    const dy = point.y - (ball.y + (ball.centerDot?.y || 0));
    // Increase hit area for easier interaction (3x the visual radius)
    return Math.sqrt(dx * dx + dy * dy) < ball.centerDot.radius * 3;
  };

  const constrainCenterDotPosition = (point: BallPosition, ball: TargetingBall): BallPosition => {
    const dx = point.x - ball.x;
    const dy = point.y - ball.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    // Constrain within the entire targeting ball's bounds
    if (distance > ball.radius) {
      const angle = Math.atan2(dy, dx);
      return {
        x: ball.x + Math.cos(angle) * ball.radius,
        y: ball.y + Math.sin(angle) * ball.radius
      };
    }
    return point;
  };

  const isPointInTargetingZoneTool = (position: BallPosition): boolean => {
    const zoneX = TableConfig.TABLE.LEFT + TableConfig.TABLE.WIDTH - ToolboxConfig.CONSTANTS.TARGETING_ZONE_OFFSET;
    const zoneY = TableConfig.TABLE.TOP + TableConfig.TABLE.HEIGHT + 160;
    const zoneSize = TargetingZoneConfig.DEFAULT.size;

    return (
      position.x >= zoneX - zoneSize / 2 &&
      position.x <= zoneX + zoneSize / 2 &&
      position.y >= zoneY - zoneSize / 2 &&
      position.y <= zoneY + zoneSize / 2
    );
  };

  const isPointInTextBoxTool = (point: Point): boolean => {
    const { TABLE } = TableConfig;

    const toolboxBallDiameter = BallConfig.DIAMETER * BallConfig.TOOLBOX.SCALE;
    const toolboxBallSpacing = toolboxBallDiameter * BallConfig.TOOLBOX.SPACING_MULTIPLIER;
    const padding = toolboxBallDiameter * BallConfig.TOOLBOX.PADDING_MULTIPLIER;

    const ballWrapperWidth = 17 * toolboxBallSpacing + padding * 2;
    const ballWrapperTop = TABLE.TOP + TABLE.HEIGHT + ToolboxConfig.DIMENSIONS.BALL_LIST_TOP_OFFSET;
    const ballWrapperHeight = toolboxBallDiameter * 1.5;

    const toolBoxOffset = TABLE.LEFT - TABLE.RAIL_THICKNESS;
    const targetingToolMarginX = ToolboxConfig.CONSTANTS.TARGETING_TOOL_OFFSET - TargetingBallConfig.DEFAULT.radius;
    const ballWrapperMarginRight = targetingToolMarginX * 1.5;
    const ballWrapperOffsetWidth = ToolboxConfig.CONSTANTS.WRAPPER_OFFSET + ballWrapperWidth + ballWrapperMarginRight;

    const x = toolBoxOffset + ballWrapperOffsetWidth - TextBoxConfig.DEFAULT.width / 2;
    const y = ballWrapperTop + ballWrapperHeight / 2 - TextBoxConfig.DEFAULT.height / 2;
    const dx = point.x - x;
    const dy = point.y - y;

    return dx >= 0 && dx <= TextBoxConfig.DEFAULT.width && dy >= 0 && dy <= TextBoxConfig.DEFAULT.height;
  };

  const isPointInTextBox = (canvas: HTMLCanvasElement, point: Point, textBox: TextBox): boolean => {
    const ctx = canvas.getContext('2d');
    if (!ctx) return false;

    const fontSize = TextBoxConfig.DEFAULT.fontSize;
    const fontFamily = TextBoxConfig.DEFAULT.fontFamily;
    ctx.font = `bold ${fontSize}px ${fontFamily}`;

    const metrics = ctx.measureText(textBox.value);
    const textBoxWidth = metrics.width;
    const textBoxHeight = metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent;

    const pivotX = textBox.x - textBoxWidth / 2;
    const pivotY = textBox.y;
    const rotationAngle = textBox.rotation ? (textBox.rotation * Math.PI) / 180 : 0;

    const dx = point.x - pivotX;
    const dy = point.y - pivotY;

    // Trigonometry formulas: https://www.geeksforgeeks.org/trigonometry-formulas/
    // cos(-a) = cos(a)
    // sin(-a) = -sin(a)
    const cosTheta = Math.cos(rotationAngle);
    const sinTheta = Math.sin(rotationAngle);

    // Rotating matrix: https://www.geeksforgeeks.org/rotation-matrix/
    // When a text box is being rotated by an angle 'θ', it mean it being multiplied by the rotation matrix:
    //     R(θ) = [ cos(θ)  -sin(θ) ]
    //            [ sin(θ)   cos(θ) ]
    // For easy to check if point in the text box or not, just 'Inverse Rotation' the rotated text box so it
    // become a normal rectangle zone using the rotation matrix.
    //     R(-θ) = [ cos(θ)   sin(θ) ]
    //             [ -sin(θ)  cos(θ) ]
    const localX = dx * cosTheta + dy * sinTheta;
    const localY = -dx * sinTheta + dy * cosTheta;

    return localX >= 0 && localX <= textBoxWidth && localY >= -textBoxHeight / 2 && localY <= textBoxHeight / 2;
  };

  const isPointInTextBoxIcon = (canvas: HTMLCanvasElement, point: Point, textBox: TextBox, offset: number): boolean => {
    const ctx = canvas.getContext('2d');
    if (!ctx) return false;

    const metrics = ctx.measureText(textBox.value);
    const textBoxWidth = metrics.width;
    const textBoxHeight = metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent;

    const iconX = textBox.x - textBoxWidth / 2 - offset;
    const iconY = textBox.y - textBoxHeight;

    return (
      point.x >= iconX &&
      point.x <= iconX + TextBoxConfig.ICON.width &&
      point.y >= iconY &&
      point.y <= iconY + TextBoxConfig.ICON.height
    );
  };

  const isPointInTextBoxEditValue = (canvas: HTMLCanvasElement, point: Point, textBox: TextBox): boolean =>
    isPointInTextBoxIcon(canvas, point, textBox, 30);

  const isPointInTextBoxRotateRight = (canvas: HTMLCanvasElement, point: Point, textBox: TextBox): boolean =>
    isPointInTextBoxIcon(canvas, point, textBox, 55);

  const isPointInTextBoxRotateLeft = (canvas: HTMLCanvasElement, point: Point, textBox: TextBox): boolean =>
    isPointInTextBoxIcon(canvas, point, textBox, 75);

  const isPointInTargetingZoneOrCorner = (position: BallPosition, zone: TargetingZone): boolean => {
    const corners = [zone.corners.topLeft, zone.corners.topRight, zone.corners.bottomRight, zone.corners.bottomLeft];

    return isPointInPolygon(position, zone) || corners.some(corner => isPointInCorner(position, corner));
  };

  const isPointInSpeedBarTriangleMark = (point: Point, speedBar: SpeedBar): boolean => {
    const { padding, segmentSpacing, segmentCount } = SpeedBarConfig.DEFAULT;
    const { hitBoxBuffer, size } = SpeedBarConfig.TRIANGLE_MARK;

    const innerWidth = speedBar.width - padding * 2;
    const totalSpacing = segmentSpacing * (segmentCount - 1);
    const segmentWidth = (innerWidth - totalSpacing) / segmentCount;

    const activeIndex = Math.round(speedBar.value) - 1;

    // Starting X position of the first segment
    const startX = speedBar.x - speedBar.width / 2 + padding;
    const activeCenterX = startX + activeIndex * (segmentWidth + segmentSpacing) + segmentWidth / 2;

    const triangleSide = segmentWidth * size;
    // The height of the equilateral triangle is given by the formula:
    // h = ½(√3a), where h is the height of equilateral triangle and a is the side length
    // https://www.cuemath.com/geometry/height-of-equilateral-triangle/
    const triangleHeight = (triangleSide * Math.sqrt(3)) / 2;

    // Triangle's tip (equilateral triangle pointing downward).
    const tip = {
      x: activeCenterX,
      y: speedBar.y - speedBar.height / 2 - padding / 2 + hitBoxBuffer
    };
    const y = tip.y - triangleHeight - hitBoxBuffer;
    const left = {
      x: tip.x - triangleSide / 2 - hitBoxBuffer,
      y
    };
    const right = {
      x: tip.x + triangleSide / 2 + hitBoxBuffer,
      y
    };

    // Calculate the area of a triangle given three vertices using the determinant formula
    //                | x1  y1  1 |
    // Area = (1/2) * | x2  y2  1 | = |x1(y2 − y3) + x2(y3 − y1) + x3(y1 − y2)| / 2
    //                | x3  y3  1 |
    // https://www.cuemath.com/geometry/area-of-triangle-in-coordinate-geometry/
    const triangleArea = (a: Point, b: Point, c: Point): number => {
      return Math.abs(a.x * (b.y - c.y) + b.x * (c.y - a.y) + c.x * (a.y - b.y)) / 2;
    };

    const areaTotal = triangleArea(tip, left, right);
    const area1 = triangleArea(point, tip, left);
    const area2 = triangleArea(point, left, right);
    const area3 = triangleArea(point, right, tip);

    // Use a tolerance to account for floating point precision
    const tolerance = 0.1;

    return Math.abs(area1 + area2 + area3 - areaTotal) < tolerance;
  };

  // Ray-Casting Algorithm (Even-Odd Rule)
  // https://medium.com/@girishajmera/exploring-algorithms-to-determine-points-inside-or-outside-a-polygon-038952946f87
  const isPointInPolygon = (point: BallPosition, zone: TargetingZone): boolean => {
    const vertices = [zone.corners.topLeft, zone.corners.topRight, zone.corners.bottomRight, zone.corners.bottomLeft];

    let insidePolygon = false;
    for (let i = 0, j = vertices.length - 1; i < vertices.length; j = i++) {
      // Current vertex
      const xi = vertices[i].x,
        yi = vertices[i].y;
      // Previous vertex
      const xj = vertices[j].x,
        yj = vertices[j].y;

      // Line Segment Intersection
      const isBetween = yi > point.y !== yj > point.y; // Checks if the point's y-coordinate is between the y-coordinates of the edge vertices
      const isToTheRight = point.x < ((xj - xi) * (point.y - yi)) / (yj - yi) + xi; // Checks if the point's x-coordinate is to the left of the intersection point
      const intersect = isBetween && isToTheRight;

      if (intersect) insidePolygon = !insidePolygon;
    }

    return insidePolygon;
  };

  const isPointInTargetingZoneCorner = (position: BallPosition, zone: TargetingZone, cornerIndex: number): boolean => {
    const corners = [zone.corners.topLeft, zone.corners.topRight, zone.corners.bottomRight, zone.corners.bottomLeft];
    const corner = corners[cornerIndex];

    const hitBoxSize = TargetingZoneConfig.DEFAULT.cornerRadius;

    return (
      position.x >= corner.x - hitBoxSize &&
      position.x <= corner.x + hitBoxSize &&
      position.y >= corner.y - hitBoxSize &&
      position.y <= corner.y + hitBoxSize
    );
  };

  const isPointInCorner = (position: BallPosition, corner: BallPosition): boolean => {
    const hitBoxSize = TargetingZoneConfig.DEFAULT.cornerRadius;

    return (
      position.x >= corner.x - hitBoxSize &&
      position.x <= corner.x + hitBoxSize &&
      position.y >= corner.y - hitBoxSize &&
      position.y <= corner.y + hitBoxSize
    );
  };

  const isPointInSpeedBarTool = (point: Point): boolean => {
    const { TABLE } = TableConfig;
    const { width, height } = SpeedBarConfig.DEFAULT;
    const { TARGETING_TOOL_OFFSET, WRAPPER_OFFSET, TARGETING_ZONE_OFFSET } = ToolboxConfig.CONSTANTS;

    const toolboxBallDiameter = BallConfig.DIAMETER * BallConfig.TOOLBOX.SCALE;
    const toolboxBallSpacing = toolboxBallDiameter * BallConfig.TOOLBOX.SPACING_MULTIPLIER;
    const padding = toolboxBallDiameter * BallConfig.TOOLBOX.PADDING_MULTIPLIER;

    const ballWrapperWidth = 17 * toolboxBallSpacing + padding * 2;
    const ballWrapperTop = TABLE.TOP + TABLE.HEIGHT + ToolboxConfig.DIMENSIONS.BALL_LIST_TOP_OFFSET;
    const ballWrapperHeight = toolboxBallDiameter * 1.5;

    const toolBoxOffset = TABLE.LEFT - TABLE.RAIL_THICKNESS;
    const targetingToolMarginX = TARGETING_TOOL_OFFSET - TargetingBallConfig.DEFAULT.radius;
    const ballWrapperMarginRight = targetingToolMarginX * 1.5;
    const ballWrapperOffsetWidth = WRAPPER_OFFSET + ballWrapperWidth + ballWrapperMarginRight;
    const targetingZoneOffset = TABLE.LEFT + TABLE.WIDTH - TARGETING_ZONE_OFFSET;

    const barX =
      toolBoxOffset +
      ballWrapperOffsetWidth +
      (targetingZoneOffset - (toolBoxOffset + ballWrapperOffsetWidth)) +
      TargetingZoneConfig.DEFAULT.size +
      width / 2;
    const barY = ballWrapperTop + ballWrapperHeight / 2;

    const rectX = barX - width / 2;
    const rectY = barY - height / 2;

    return point.x >= rectX && point.x <= rectX + width && point.y >= rectY && point.y <= rectY + height;
  };

  const isPointInSpeedBar = (point: Point, speedBar: SpeedBar): boolean => {
    const { x, y, width, height } = speedBar;
    const rectX = x - width / 2;
    const rectY = y - height / 2;

    return point.x >= rectX && point.x <= rectX + width && point.y >= rectY && point.y <= rectY + height;
  };

  return {
    isPointInTable,
    isPositionInTableBounds,
    constrainQuadrilateralPositionToTable,
    isPointInSpeedBar,
    isPointInSpeedBarTool,
    isPointOutsideTable,
    isPointInBall,
    isPointInCenterDot,
    isPointInSpeedBarTriangleMark,
    isPointInTargetingBall,
    isPointInTargetingTool,
    isPointInTargetingZoneTool,
    isPointInTargetingZoneOrCorner,
    isPointInTargetingZoneCorner,
    isPointInTextBox,
    isPointInTextBoxEditValue,
    isPointInTextBoxRotateLeft,
    isPointInTextBoxRotateRight,
    isPointInTextBoxTool,
    constrainCirclePositionToTable,
    constrainCenterDotPosition,
    constrainRectanglePositionToTable
  };
};
