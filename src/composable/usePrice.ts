import filters from '@/utils/filters';

interface PricingState {
  isFree: boolean;
  showOriginalOnly: boolean;
  isOnSale: boolean;
  showSalePercentage: boolean;
}

export function usePrice() {
  const normalizePrice = (price: number | null): number => {
    return price ?? 0;
  };

  const isFree = (price: number | null, salePrice: number | null): boolean => {
    const normalizedPrice = normalizePrice(price);
    const normalizedSalePrice = normalizePrice(salePrice);

    return normalizedPrice === 0 && normalizedSalePrice === 0;
  };

  const isShowOriginalPrice = (price: number | null, salePrice: number | null): boolean => {
    if (isFree(price, salePrice)) return false;

    const normalizedPrice = normalizePrice(price);
    const normalizedSalePrice = normalizePrice(salePrice);

    return normalizedPrice === normalizedSalePrice || salePrice == null;
  };

  const isSaleOff = (price: number | null, salePrice: number | null): boolean => {
    if (isFree(price, salePrice)) return false;

    const normalizedPrice = normalizePrice(price);
    const normalizedSalePrice = normalizePrice(salePrice);

    return normalizedPrice > normalizedSalePrice && salePrice != null;
  };

  const getPricingState = (price: number | null, salePrice: number | null): PricingState => {
    const free = isFree(price, salePrice);
    const showOriginal = isShowOriginalPrice(price, salePrice);
    const onSale = isSaleOff(price, salePrice);

    return {
      isFree: free,
      showOriginalOnly: showOriginal,
      isOnSale: onSale,
      showSalePercentage: onSale && salePrice != null
    };
  };

  const formattedPrice = (price: number | null): string => {
    return filters.formattedPrice(normalizePrice(price)) as string;
  };

  const getSalePercent = (price: number | null, salePrice: number | null): number => {
    return Math.round(((normalizePrice(price) - normalizePrice(salePrice)) / normalizePrice(price)) * 100);
  };

  return {
    formattedPrice,
    getPricingState,
    getSalePercent,
    isFree,
    isSaleOff,
    isShowOriginalPrice
  };
}
