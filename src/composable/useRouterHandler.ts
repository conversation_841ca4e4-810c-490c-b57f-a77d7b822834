import { RouteLocationNormalized } from 'vue-router';
import { ROUTE_PATH, GUARD } from '@/utils/constant';
// import { useAuthAdminStore } from '@/store/admin/auth';
import { useUserAuthStore } from '@/store/user/auth';
import { useTeacherAuthStore } from '@/store/teacher/auth';

interface RouterHandlerParams {
  guard: string;
  to: RouteLocationNormalized;
  from: RouteLocationNormalized;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default function useRouterHandler({ guard, to, from }: RouterHandlerParams) {
  // const authAdminStore = useAuthAdminStore();
  const handleAdminAuthRequiredRoute = async (): Promise<string | void> => {
    // await authAdminStore.profile();

    return;
  };

  const handleUserAuthRequiredRoute = async (): Promise<string | void> => {
    const userAuthStore = useUserAuthStore();
    await userAuthStore.getProfile();
  };

  const handleTeacherAuthRequiredRoute = async (): Promise<string | void> => {
    const teacherAuthStore = useTeacherAuthStore();
    await teacherAuthStore.getProfile();

    const { teacherProfile } = storeToRefs(teacherAuthStore);
    const finishSetup = teacherProfile.value?.basicEntered || false;

    if (!finishSetup) {
      return ROUTE_PATH.TEACHER_SETUP;
    }
  };

  const handleAuthRequiredRoutes = (): Promise<string | void> => {
    switch (guard) {
      case GUARD.USER:
        return handleUserAuthRequiredRoute();
      case GUARD.TEACHER:
        return handleTeacherAuthRequiredRoute();
      case GUARD.ADMIN:
        return handleAdminAuthRequiredRoute();
      default:
        return Promise.resolve(ROUTE_PATH.NOT_FOUND);
    }
  };

  const authRequired = to.meta.authRequired;

  const handleRoute = (): Promise<string | void> | void => {
    if (!authRequired) {
      return;
    }
    return handleAuthRequiredRoutes();
  };

  return {
    handleRoute
  };
}
