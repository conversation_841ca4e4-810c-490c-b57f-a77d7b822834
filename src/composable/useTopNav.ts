import { useRoute } from 'vue-router';
import { teacherMenuItems, adminMenuItems, publicMenuItems } from '@/utils/menu';
import { ROUTE_PREFIX_ENUMS } from '@/utils/constant';
import { MenuItem } from '@/utils/interface/menu';

const getCurrentMenu = (isPublicRoute: boolean): MenuItem[] => {
  const pathname = window.location.pathname;

  if (isPublicRoute) {
    return publicMenuItems;
  } else {
    if (pathname.startsWith(`${ROUTE_PREFIX_ENUMS.TEACHER}`)) {
      return teacherMenuItems;
    } else if (pathname.startsWith(`${ROUTE_PREFIX_ENUMS.ADMIN}`)) {
      return adminMenuItems;
    } else {
      return [];
    }
  }
};

const isMenuItemActive = (isPublicRoute: boolean, item: MenuItem, currentPath: string): boolean => {
  if (item.link) {
    if (isPublicRoute) {
      if (currentPath === '/') return item.activeOverridePath === currentPath;

      return currentPath.includes(item.link);
    } else {
      const itemSegments = item.activeOverridePath
        ? item.activeOverridePath.split('/').filter(Boolean)
        : item.link.split('/').filter(Boolean);
      const currentSegments = currentPath.split('/').filter(Boolean);

      if (itemSegments.length <= currentSegments.length) {
        for (let i = 0; i < itemSegments.length - 1; i++) {
          if (itemSegments[i] !== currentSegments[i]) return false;
        }

        const lastItemSegment = itemSegments[itemSegments.length - 1];
        if (lastItemSegment === 'new') {
          return true;
        }

        return itemSegments[itemSegments.length - 1] === currentSegments[itemSegments.length - 1];
      }
    }
  }

  if (item.subItems && item.subItems.some(subItem => isMenuItemActive(isPublicRoute, subItem, currentPath))) {
    return true;
  }

  return false;
};

const hasItems = (item: MenuItem): boolean => {
  return Array.isArray(item.subItems) && item.subItems.length > 0;
};

export function useTopMenu(isPublicRoute = false) {
  const route = useRoute();
  const menuItems = ref<MenuItem[]>(getCurrentMenu(isPublicRoute));

  const isActive = (item: MenuItem): boolean => {
    return isMenuItemActive(isPublicRoute, item, route.path);
  };

  return {
    menuItems,
    hasItems,
    isMenuItemActive: isActive
  };
}
