import { useWindowSize as useWindowSizeCore } from '@vueuse/core';

import { SCREEN_SIZE_BREAKPOINT_ENUMS } from '@/utils/constant';

const { width } = useWindowSizeCore();

export function useWindowSize() {
  // min/max values
  const isExtraSmallSize = computed(
    () =>
      width.value >= SCREEN_SIZE_BREAKPOINT_ENUMS.XS.minWidth && width.value <= SCREEN_SIZE_BREAKPOINT_ENUMS.XS.maxWidth
  );
  const isSmallSize = computed(
    () =>
      width.value >= SCREEN_SIZE_BREAKPOINT_ENUMS.SM.minWidth && width.value <= SCREEN_SIZE_BREAKPOINT_ENUMS.SM.maxWidth
  );
  const isMediumSize = computed(
    () =>
      width.value >= SCREEN_SIZE_BREAKPOINT_ENUMS.MD.minWidth && width.value <= SCREEN_SIZE_BREAKPOINT_ENUMS.MD.maxWidth
  );
  const isLargeSize = computed(
    () =>
      width.value >= SCREEN_SIZE_BREAKPOINT_ENUMS.LG.minWidth && width.value <= SCREEN_SIZE_BREAKPOINT_ENUMS.LG.maxWidth
  );
  const isExtraLargeSize = computed(
    () =>
      width.value >= SCREEN_SIZE_BREAKPOINT_ENUMS.XL.minWidth && width.value <= SCREEN_SIZE_BREAKPOINT_ENUMS.XL.maxWidth
  );
  const isExtraExtraLargeSize = computed(
    () =>
      width.value >= SCREEN_SIZE_BREAKPOINT_ENUMS.XXL.minWidth &&
      width.value <= SCREEN_SIZE_BREAKPOINT_ENUMS.XXL.maxWidth
  );

  // min-width only
  const isAtLeastExtraExtraLarge = computed(() => width.value >= SCREEN_SIZE_BREAKPOINT_ENUMS.XXL.minWidth);
  const isAtLeastExtraLarge = computed(() => width.value >= SCREEN_SIZE_BREAKPOINT_ENUMS.XL.minWidth);
  const isAtLeastLarge = computed(() => width.value >= SCREEN_SIZE_BREAKPOINT_ENUMS.LG.minWidth);
  const isAtLeastMedium = computed(() => width.value >= SCREEN_SIZE_BREAKPOINT_ENUMS.MD.minWidth);
  const isAtLeastSmall = computed(() => width.value >= SCREEN_SIZE_BREAKPOINT_ENUMS.SM.minWidth);

  return {
    isAtLeastExtraExtraLarge,
    isAtLeastExtraLarge,
    isAtLeastLarge,
    isAtLeastMedium,
    isAtLeastSmall,
    isExtraExtraLargeSize,
    isExtraLargeSize,
    isExtraSmallSize,
    isLargeSize,
    isMediumSize,
    isSmallSize,
    width
  };
}
