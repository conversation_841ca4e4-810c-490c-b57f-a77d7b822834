import type { AxiosResponse } from 'axios';

import { fetchOptions } from '@/services/teacher/repositories/selectOption';

import { CustomAxiosRequestConfig } from '@/utils/interface/axios';

interface SelectOptionsInterface {
  highlightOptions?: OptionInterface[];
}

interface SelectOptionsResponseInterface {
  selectOptions: SelectOptionsInterface;
}

interface OptionInterface {
  label: string;
  value: string | number | number[] | boolean;
  icon?: string;
}

const selectOptions = ref<SelectOptionsResponseInterface['selectOptions']>({});

export default function useTeacherSelectOptions() {
  function fetchTeacherSelectOptions(
    keys: string[],
    params: Record<string, any> = {},
    apiOptions?: CustomAxiosRequestConfig
  ) {
    const options = fetchOptions(keys, params, apiOptions).then(
      (response: AxiosResponse<SelectOptionsResponseInterface, any>) => {
        selectOptions.value = response.data.selectOptions;
        return response.data.selectOptions;
      }
    );

    return options;
  }

  return {
    selectOptions,
    fetchTeacherSelectOptions
  };
}
