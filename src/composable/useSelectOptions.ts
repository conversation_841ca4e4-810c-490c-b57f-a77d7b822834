import type { AxiosResponse } from 'axios';

import { fetchOptions } from '@/services/public/repositories/common';

import { CustomAxiosRequestConfig } from '@/utils/interface/axios';
import { SelectOptionsResponseInterface } from '@/utils/interface/select-options';

const selectOptions = ref<SelectOptionsResponseInterface['selectOptions']>({});

export default function useSelectOptions() {
  function fetchSelectOptions(keys: string[], params: Record<string, any> = {}, apiOptions?: CustomAxiosRequestConfig) {
    const options = fetchOptions(keys, params, apiOptions).then(
      (response: AxiosResponse<SelectOptionsResponseInterface, any>) => {
        selectOptions.value = response.data.selectOptions;
        return response.data.selectOptions;
      }
    );

    return options;
  }

  return {
    selectOptions,
    fetchSelectOptions
  };
}
