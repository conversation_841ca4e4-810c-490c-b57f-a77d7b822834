import type { UserCourseInterface } from '@/utils/interface/user/userCourse';

const course = ref<UserCourseInterface | null>(null);

const completedCount = computed(() => {
  if (!course.value) return 0;
  return course.value.courseSections
    .flatMap(section => section.courseSectionItems || [])
    .filter(item => item.isCompleted).length;
});

const completedProgress = computed(() => {
  if (!course.value || !course.value.sectionItemCount) return 0;
  return Math.round((completedCount.value / course.value.sectionItemCount) * 100);
});

const updateCompletedProgress = (id: number, isCompleted: boolean) => {
  if (!course.value) return;
  const allItems = course.value.courseSections.flatMap(section => section.courseSectionItems || []);
  const item = allItems.find(i => i.id === id);
  if (item) item.isCompleted = isCompleted;
};

export const useCourseProgressSave = () => ({
  course,
  completedCount,
  completedProgress,
  updateCompletedProgress,
  initCourse: (c: UserCourseInterface) => (course.value = c)
});
