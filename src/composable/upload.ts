import { v7 as uuidv7 } from 'uuid';
import { upload, post } from '@/services/base/repositories/file';

// Types
interface ChunkUploadOptions {
  chunkSize?: number;
  path?: string;
  maxConcurrent?: number;
  onProgress?: (progress: UploadProgress) => void;
  retries?: number;
  site: string;
}

interface ChunkMetadata {
  chunkIndex: number;
  totalChunks: number;
  fileId: string;
  contentType: string;
  fileName: string;
  fileDescription?: string;
}

export interface UploadProgress {
  fileId: string;
  uploadedChunks: number;
  totalChunks: number;
  percentComplete: number;
  activeUploads: number;
}

interface UploadResult {
  success: boolean;
  fileId: string;
  filename: string;
  url?: string;
  error?: string;
  videoID?: number;
}

interface FileUploadInput {
  parentType?: string;
  parentId?: number;
  title: string;
  description?: string;
}

interface QueueItem {
  chunk: Blob;
  metadata: ChunkMetadata;
  retryCount?: number;
}

export function useChunkUpload() {
  const defaultOptions: ChunkUploadOptions = {
    chunkSize: 10 * 1024 * 1024, // 10MB chunks
    path: 'chunking_uploads',
    maxConcurrent: 5,
    retries: 3,
    site: 'teacher'
  };

  const uploadFileInChunks = async (
    file: File,
    input: FileUploadInput,
    options: ChunkUploadOptions
  ): Promise<UploadResult> => {
    const opts = { ...defaultOptions, ...options };
    const { chunkSize, path, maxConcurrent, onProgress, retries, site } = opts;

    const totalChunks = Math.ceil(file.size / chunkSize!);
    const fileId = uuidv7();

    // Create a queue of chunks to upload
    const queue: QueueItem[] = [];

    // Flag to track if upload has failed
    let uploadFailed = false;
    let failureReason = '';

    // Track upload progress
    let uploadedChunks = 0;
    let activeUploads = 0;

    // Split file into chunks and prepare queue
    for (let i = 0; i < totalChunks; i++) {
      queue.push({
        chunk: file.slice(i * chunkSize!, Math.min((i + 1) * chunkSize!, file.size)),
        metadata: {
          chunkIndex: i,
          totalChunks,
          fileId,
          contentType: file.type,
          fileName: input.title,
          fileDescription: input?.description
        },
        retryCount: 0
      });
    }

    // Process queue with controlled concurrency
    const processQueue = async (): Promise<void> => {
      // Stop processing if an upload has already failed
      if (uploadFailed) return;

      if (queue.length === 0) return;
      if (activeUploads >= maxConcurrent!) return;

      const item = queue.shift();
      if (!item) return;

      activeUploads++;

      try {
        let attempt = item.retryCount || 0;
        try {
          // Upload the chunk
          await uploadChunk(item.chunk, item.metadata, path!, site);

          // Update progress
          uploadedChunks++;

          if (onProgress) {
            onProgress({
              fileId,
              uploadedChunks,
              totalChunks,
              percentComplete: Math.round((uploadedChunks / totalChunks) * 100),
              activeUploads
            });
          }
        } catch (error) {
          attempt++;
          if (attempt < retries!) {
            // Calculate exponential backoff delay
            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 8000);
            console.warn(
              `Upload failed for chunk ${item.metadata.chunkIndex}, retrying in ${delay}ms... (${attempt}/${retries})`
            );

            // Wait for backoff period
            await new Promise(resolve => setTimeout(resolve, delay));

            // Put the chunk back in queue with updated retry count
            queue.unshift({
              ...item,
              retryCount: attempt
            });
          } else {
            // Mark the whole upload as failed
            uploadFailed = true;
            failureReason = `Failed to upload chunk ${item.metadata.chunkIndex} after ${retries} attempts: ${error}`;
            console.error(failureReason);

            // Clear the queue to stop further processing
            queue.length = 0;
            throw new Error(failureReason);
          }
        }
      } finally {
        activeUploads--;
        // Only continue processing if upload hasn't failed
        if (!uploadFailed) {
          processQueue();
        }
      }
    };

    // Start processing queue with maximum concurrency
    const workers = Array(maxConcurrent)
      .fill(null)
      .map(() => processQueue());

    try {
      // Wait for all workers to complete
      await Promise.all(workers);

      // If upload failed, return error result
      if (uploadFailed) {
        return {
          success: false,
          fileId,
          filename: file.name,
          error: failureReason
        };
      }

      // Ensure all chunks are uploaded
      while (uploadedChunks < totalChunks || queue.length > 0) {
        // If there are chunks left in the queue or active uploads, wait and check again
        await new Promise(resolve => setTimeout(resolve, 200));

        // Start additional workers if needed
        const additionalWorkers = Math.min(maxConcurrent! - activeUploads, queue.length);

        if (additionalWorkers > 0) {
          const newWorkers = Array(additionalWorkers)
            .fill(null)
            .map(() => processQueue());
          await Promise.all(newWorkers);
        }

        // If upload failed during this process, return error
        if (uploadFailed) {
          return {
            success: false,
            fileId,
            filename: file.name,
            error: failureReason
          };
        }
      }

      // Only finalize if no upload failures occurred
      const finalResult = await finalizeUpload(fileId, path!, input?.parentType, input?.parentId, site);

      return {
        success: true,
        fileId,
        filename: file.name,
        url: finalResult?.url,
        videoID: finalResult?.videoID
      };
    } catch (error) {
      // Handle any unhandled errors during the upload process
      const errorMessage = error instanceof Error ? error.message : 'Unknown upload error';

      return {
        success: false,
        fileId,
        filename: file.name,
        error: errorMessage
      };
    }
  };

  const uploadChunk = async (chunk: Blob, metadata: ChunkMetadata, path: string, site: string): Promise<any> => {
    const formData = new FormData();
    formData.append('chunk', chunk);
    formData.append('metadata', JSON.stringify(metadata));

    const response = await upload(formData, path, site);
    return response.data;
  };

  const finalizeUpload = async (
    fileId: string,
    path: string,
    parentType?: string,
    parentId?: number,
    site?: string
  ): Promise<any> => {
    const response = await post({ fileId, parentType, parentId }, `${path}/finish`, site || 'teacher');
    return response.data;
  };

  return {
    uploadFileInChunks
  };
}
