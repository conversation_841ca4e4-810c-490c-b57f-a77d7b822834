import { DrillInterface } from '@/utils/interface/drill/drill';
import { POOL_TABLE_PLACEHOLDER_URL } from '@/utils/constant';

export function useDrill() {
  const getDrillImageUrl = (drill: DrillInterface): string => {
    if (!drill || !drill.diagrams || drill.diagrams.length === 0) {
      return POOL_TABLE_PLACEHOLDER_URL;
    }

    return drill.diagrams[0].imageUrl;
  };

  return {
    getDrillImageUrl
  };
}
