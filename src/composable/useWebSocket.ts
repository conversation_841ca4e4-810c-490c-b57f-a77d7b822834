import { useWebSocket } from '@vueuse/core';

interface WebSocketOptions {
  autoReconnect?: boolean | { retries?: number; delay?: number; onFailed?: () => void };
  heartbeat?: boolean | { message?: string; interval?: number };
  onConnected?: (ws: WebSocket) => void;
  onDisconnected?: (ws: WebSocket, event: CloseEvent) => void;
  onError?: (ws: WebSocket, event: Event) => void;
  onMessage?: (ws: WebSocket, event: MessageEvent) => void;
  protocols?: string[];
  immediate?: boolean;
}

export interface MessageType {
  id: string;
  type: string;
  payload?: {
    follow_events?: string[];
    match_id?: string;
    match_hash?: string;
    player1_score?: number;
    player2_score?: number;
    submit?: boolean;
  };
}

interface WebSocketReturn<T> {
  status: Ref<'OPEN' | 'CONNECTING' | 'CLOSED'>;
  data: Ref<T | null>;
  send: (message: MessageType) => boolean;
  open: () => void;
  close: () => void;
  ws: Ref<WebSocket | undefined>;
}

export function useWebSocketConnection<T>(
  url: string | Ref<string>,
  options: WebSocketOptions = {}
): WebSocketReturn<T> {
  // Mặc định các options
  const defaultOptions: WebSocketOptions = {
    autoReconnect: {
      retries: 3,
      delay: 1000,
      onFailed: () => console.warn('Connected failed')
    },
    immediate: true,
    ...options
  };

  const {
    status,
    data: originalData,
    send: originalSend,
    open,
    close,
    ws
  } = useWebSocket(url, {
    autoReconnect: defaultOptions.autoReconnect,
    heartbeat: defaultOptions.heartbeat,
    onConnected: defaultOptions.onConnected,
    onDisconnected: defaultOptions.onDisconnected,
    onError: defaultOptions.onError,
    onMessage: (ws, event) => {
      defaultOptions.onMessage?.(ws, event);
    },
    protocols: defaultOptions.protocols,
    immediate: defaultOptions.immediate
  });

  const send = (message: MessageType) => {
    return originalSend(JSON.stringify(message));
  };

  const data = computed(() => {
    return JSON.parse(originalData.value);
  });

  return {
    status,
    data,
    send,
    open,
    close,
    ws
  };
}
