import Swal from 'sweetalert2';
import { inject } from 'vue-demi';
import { SweetAlertResult } from 'sweetalert2';

import { SwalOptions } from '@/utils/swal-options';

export default function useSwal() {
  const SwalInstance = inject<typeof Swal>('Swal', Swal);

  async function confirming(options: SwalOptions): Promise<boolean> {
    const confirmation: SweetAlertResult<any> = await SwalInstance.fire(options);

    return confirmation.isConfirmed;
  }

  return {
    confirming
  };
}
