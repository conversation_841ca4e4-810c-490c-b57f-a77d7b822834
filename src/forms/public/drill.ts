import { BaseInputType } from '@/utils/types/base-input-type';
import { DrillListQueryInput } from '@/utils/interface/public/drill';
import parseQueryParams from '@/utils/parseQueryParams';

export class DrillListQueryFormModel extends BaseInputType implements DrillListQueryInput {
  titleCont = '';
  levelIn: number[] = [];
  skillIDIn: number[] = [];
  ownerName = '';
  isFree: boolean | null = null;

  constructor(form: Partial<DrillListQueryFormModel> = {}) {
    super();
    this.assignAttributes(form);
    this.levelIn = parseQueryParams.parseIdsArray(form.levelIn) || [];
    this.skillIDIn = parseQueryParams.parseIdsArray(form.skillIDIn) || [];
  }
}

export class RelatedDrillListQueryFormModel extends BaseInputType {
  levelIn: string[] = [];
  skillIDIn: number[] = [];

  constructor(form: Partial<RelatedDrillListQueryFormModel> = {}) {
    super();
    this.assignAttributes(form);
    this.skillIDIn = parseQueryParams.parseIdsArray(form.skillIDIn) || [];
  }
}
