import { CourseListQueryInput } from '@/utils/interface/public/course';
import { BaseInputType } from '@/utils/types/base-input-type';

export class CourseListQueryFormModel extends BaseInputType implements CourseListQueryInput {
  titleCont? = '';
  categoryCont? = '';
  teacherNameCont? = '';
  teacherIdEq? = '';
  levelEq? = '';
  salePriceRange?: [number, number] = undefined;

  constructor(form: Partial<CourseListQueryFormModel> = {}) {
    super();
    this.assignAttributes(form);
  }
}
