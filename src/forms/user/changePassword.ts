import { ChangePasswordInterface } from '@/utils/interface/user/auth';
import { BaseInputType } from '@/utils/types/base-input-type';

export class ChangePasswordForm extends BaseInputType implements ChangePasswordInterface {
  password? = '';
  newPassword? = '';
  confirmPassword? = '';

  constructor(form: Partial<ChangePasswordInterface> = {}) {
    super();
    this.assignAttributes(form);
  }
}
