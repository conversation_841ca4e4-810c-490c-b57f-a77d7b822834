import {
  PracticeSubmissionCreateInterface,
  PracticeSubmissionsQueryInput
} from '@/utils/interface/user/practiceSubmission';
import { BaseInputType } from '@/utils/types/base-input-type';

export class PracticeSubmissionForm extends BaseInputType implements PracticeSubmissionCreateInterface {
  practiceId = '';
  practiceType = '';
  content = '';
  videoIDs = '';

  constructor(form: Partial<PracticeSubmissionCreateInterface> = {}) {
    super();
    this.assignAttributes(form);
  }
}

export class PracticeSubmissionQueryFormModel extends BaseInputType implements PracticeSubmissionsQueryInput {
  statusEq = null;
  practiceIDEq = null;
  practiceTypeEq = null;

  constructor(form: Partial<PracticeSubmissionQueryFormModel> = {}) {
    super();
    this.assignAttributes(form);
  }
}
