import { UserCoursesQueryInput } from '@/utils/interface/user/userCourse';
import { BaseInputType } from '@/utils/types/base-input-type';

export class UserCoursesQueryFormModel extends BaseInputType implements UserCoursesQueryInput {
  titleCont? = '';
  descriptionCont? = '';
  categoryCont? = '';

  constructor(form: Partial<UserCoursesQueryInput> = {}) {
    super();
    this.assignAttributes(form);
  }
}
