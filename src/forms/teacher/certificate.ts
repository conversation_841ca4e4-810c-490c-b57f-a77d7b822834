import { BaseInputType } from '@/utils/types/base-input-type';
import { CertificateFormInterface } from '@/utils/interface/teacher/certificate';

export class CourseSectionForm extends BaseInputType implements CertificateFormInterface {
  title = '';
  isActive = false;
  description = '';

  constructor(form: Partial<CertificateFormInterface> = {}) {
    super();
    this.assignAttributes(form);
  }
}
