import { courseSectionItemUsersQueryInput } from '@/utils/interface/teacher/courseSectionItemUser';
import { BaseInputType } from '@/utils/types/base-input-type';

export class courseSectionItemUsersQueryFormModel extends BaseInputType implements courseSectionItemUsersQueryInput {
  nameCont?: '';
  statusEq?: '';
  contentSlug = '';

  constructor(form: Partial<courseSectionItemUsersQueryFormModel> = {}) {
    super();
    this.assignAttributes(form);
  }
}
