
import { BaseInputType } from '@/utils/types/base-input-type';
import { HighlightInputInterface, HighlightQueryInput } from '@/utils/interface/teacher/highlight';

export class HighlightListQueryFormModel extends BaseInputType implements HighlightQueryInput {
  titleCont?: '';

  constructor(form: Partial<HighlightListQueryFormModel> = {}) {
    super();
    this.assignAttributes(form);
  }
}

export class HighlightForm extends BaseInputType implements HighlightInputInterface {
  title = '';
  description = '';
  iconId = 0;

  constructor(form: Partial<HighlightInputInterface> = {}) {
    super();
    this.assignAttributes(form);
  }
}
