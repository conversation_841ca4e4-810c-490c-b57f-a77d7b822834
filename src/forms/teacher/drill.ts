import { BaseInputType } from '@/utils/types/base-input-type';
import { DrillDiagramInputInterface } from '@/utils/interface/drill/diagram';
import { DrillInputInterface } from '@/utils/interface/drill/drill';
import { DrillListQueryInput } from '@/utils/interface/teacher/drill';
import { DrillStepInputInterface } from '@/utils/interface/drill/step';
import { VideoInterface } from '@/utils/interface/video';

export class DrillListQueryFormModel extends BaseInputType implements DrillListQueryInput {
  titleCont?: '';
  levelIn?: [];
  skillIDIn?: [];
  idNotIn?: [];

  constructor(form: Partial<DrillListQueryFormModel> = {}) {
    super();
    this.assignAttributes(form);
  }
}

export class DrillForm extends BaseInputType implements DrillInputInterface {
  title = '';
  description?: string;
  level?: string;
  price?: number;
  salePrice?: number;
  skillIds?: number[];
  step: DrillStepInputInterface[] = [];
  diagramAttributes: DrillDiagramInputInterface[] = [];
  videos: VideoInterface[] = [];
  isMaster?: boolean;

  constructor(form: Partial<DrillInputInterface> = {}) {
    super();
    this.assignAttributes(form);
  }
}

export class DrillStepForm extends BaseInputType implements DrillStepInputInterface {
  description = '';
  diagramImage = '';
  setting = {
    balls: [],
    speedBars: [],
    textBoxes: [],
    targetingBalls: [],
    targetingZones: []
  };

  constructor(form: Partial<DrillStepInputInterface> = {}) {
    super();
    this.assignAttributes(form);
  }
}
