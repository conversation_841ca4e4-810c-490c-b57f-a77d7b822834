import {
  CourseFormInterface,
  CourseInputInterface,
  CourseInterface,
  CourseListQueryInput,
  CoursePackageInterface,
  CourseSettingInterface
} from '@/utils/interface/teacher/course';
import { BaseInputType } from '@/utils/types/base-input-type';
import { pick } from 'lodash';

export class CourseListQueryFormModel extends BaseInputType implements CourseListQueryInput {
  titleCont? = '';
  descriptionCont? = '';
  statusEq? = '';

  constructor(form: Partial<CourseListQueryFormModel> = {}) {
    super();
    this.assignAttributes(form);
  }
}

export class CourseFormModel extends BaseInputType implements CourseFormInterface {
  id = null;
  teacherId = '';
  title = '';
  description = '';
  status = '';
  instructionalLevel = '';
  salePrice: number | null = null;
  slug = '';
  price: number | null = null;
  bonusPoint: number | null = null;
  bonusPointPercent: number | null = null;
  isPublic?: boolean | false;
  banner = '';
  createdAt = '';
  updatedAt = '';
  courseCensorHistories?: [];
  courseSections?: [];
  sectionItemCount = 0;
  sectionCount = 0;

  constructor(form: Partial<CourseFormModel | CourseInterface> = {}) {
    super();
    this.assignAttributes(form);
  }

  public createInput() {
    this.formatAttributes();

    return pick(this, ['title']);
  }

  public updateInput() {
    this.formatAttributes();

    return omit(
      this,
      'id',
      'status',
      'slug',
      'teacherId',
      'createdAt',
      'updatedAt',
      'courseCensorHistories',
      'courseSections',
      'sectionItemCount',
      'sectionCount'
    );
  }

  private formatAttributes() {
    this.price = this.toNumberOrNull(this.price);
    this.salePrice = this.toNumberOrNull(this.salePrice);
    this.bonusPoint = this.toNumberOrNull(this.bonusPoint);
    this.bonusPointPercent = this.toNumberOrNull(this.bonusPointPercent);
  }
}

export class CourseForm extends BaseInputType implements CourseInputInterface {
  title = '';
  description?: string;
  salePrice?: number;
  price?: number;
  status?: string;
  bonusPoint?: number;
  instructionalLevel?: string;
  slug?: string;
  banner?: string;
  isPublic?: boolean;
  coursePackageAttributes?: CoursePackageInterface[] | [];
  courseSetting?: CourseSettingInterface | undefined;

  constructor(form: Partial<CourseInputInterface> = {}) {
    super();
    this.assignAttributes(form);
  }

  public updateInput() {
    this.formatAttributes();

    return this;
  }

  private formatAttributes() {
    this.coursePackageAttributes = this.coursePackageAttributes?.map((item: CoursePackageInterface) => ({
      price: item.price,
      salePrice: item.salePrice,
      packageDealId: String(item.packageDealId),
      isDeleted: item.isDeleted
    }));
  }
}
