import { BasicFormInterface } from '@/utils/interface/teacher/setup';
import { BaseInputType } from '@/utils/types/base-input-type';

export class BasicFormModel extends BaseInputType implements BasicFormInterface {
  name? = '';
  description? = '';
  contactEmail? = '';
  phoneNumber? = '';
  address? = '';
  imageUrl? = '';

  constructor(form: Partial<BasicFormInterface> = {}) {
    super();
    this.assignAttributes(form);
  }
}
