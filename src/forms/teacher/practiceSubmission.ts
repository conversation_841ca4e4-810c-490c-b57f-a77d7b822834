import { PracticeSubmissionsQueryInput } from '@/utils/interface/teacher/practiceSubmission';
import { BaseInputType } from '@/utils/types/base-input-type';

export class PracticeSubmissionQueryFormModel extends BaseInputType implements PracticeSubmissionsQueryInput {
  statusEq = null;

  constructor(form: Partial<PracticeSubmissionQueryFormModel> = {}) {
    super();
    this.assignAttributes(form);
  }
}
