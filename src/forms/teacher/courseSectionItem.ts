import { BaseInputType } from '@/utils/types/base-input-type';
import { COURSE_SECTION_ITEM_TYPE } from '@/utils/constant';
import { CourseSectionItemInputInterface } from '@/utils/interface/admin/courseSectionItem';

export class CourseSectionItemForm extends BaseInputType implements CourseSectionItemInputInterface {
  title = '';
  type = COURSE_SECTION_ITEM_TYPE.TEXT;
  content = '';
  isFree = false;

  constructor(form: Partial<CourseSectionItemInputInterface> = {}) {
    super();
    this.assignAttributes(form);
  }
}
