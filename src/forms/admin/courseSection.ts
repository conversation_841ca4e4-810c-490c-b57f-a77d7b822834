import { CourseSectionFormInterface } from '@/utils/interface/admin/courseSection';
import { BaseInputType } from '@/utils/types/base-input-type';

export class CourseSectionForm extends BaseInputType implements CourseSectionFormInterface {
  id = null;
  title = '';

  public createInput() {
    return omit(this, 'id');
  }

  public updateInput() {
    return omit(this, 'id', 'courseId', 'createdAt', 'updatedAt');
  }
}
