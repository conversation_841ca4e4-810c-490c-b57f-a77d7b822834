import { CourseListQueryInput } from '@/utils/interface/admin/course';
import { BaseInputType } from '@/utils/types/base-input-type';

export class CourseListQueryFormModel extends BaseInputType implements CourseListQueryInput {
  titleCont? = '';
  teacherIdEq? = '';
  statusIn? = [];

  constructor(form: Partial<CourseListQueryFormModel> = {}) {
    super();
    this.assignAttributes(form);
    if (this.statusIn && !Array.isArray(this.statusIn)) {
      this.statusIn = [this.statusIn];
    }
  }
}
