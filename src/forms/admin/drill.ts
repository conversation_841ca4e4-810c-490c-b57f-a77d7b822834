import parseQueryParams from '@/utils/parseQueryParams';
import { BaseInputType } from '@/utils/types/base-input-type';
import { DrillDiagramInputInterface } from '@/utils/interface/drill/diagram';
import { DrillInputInterface } from '@/utils/interface/drill/drill';
import { DrillListQueryInput } from '@/utils/interface/admin/drill';
import { DrillStepInputInterface } from '@/utils/interface/drill/step';

export class DrillListQueryFormModel extends BaseInputType implements DrillListQueryInput {
  titleCont?: '';
  levelIn?: number[];
  skillIDIn?: number[];

  constructor(form: Partial<DrillListQueryFormModel> = {}) {
    super();
    this.assignAttributes(form);
    this.levelIn = parseQueryParams.parseIdsArray(form.levelIn);
    this.skillIDIn = parseQueryParams.parseIdsArray(form.skillIDIn);
  }
}

export class DrillForm extends BaseInputType implements DrillInputInterface {
  title = '';
  description?: string;
  level?: string;
  price?: number;
  salePrice?: number;
  skillIds?: number[];
  step: DrillStepInputInterface[] = [];
  diagramAttributes: DrillDiagramInputInterface[] = [];

  constructor(form: Partial<DrillInputInterface> = {}) {
    super();
    this.assignAttributes(form);
  }
}

export class DrillStepForm extends BaseInputType implements DrillStepInputInterface {
  description = '';
  diagramImage = '';
  setting = {
    balls: [],
    speedBars: [],
    textBoxes: [],
    targetingBalls: [],
    targetingZones: []
  };

  constructor(form: Partial<DrillStepInputInterface> = {}) {
    super();
    this.assignAttributes(form);
  }
}
