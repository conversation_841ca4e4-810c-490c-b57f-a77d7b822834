import { CourseSectionItemFormInterface } from '@/utils/interface/admin/courseSectionItem';
import { BaseInputType } from '@/utils/types/base-input-type';
import { DrillForm } from './drill';
import { VideoInterface } from '@/utils/interface/video';
export class CourseSectionItemForm extends BaseInputType implements CourseSectionItemFormInterface {
  id = '';
  courseSectionId = '';
  title = '';
  content = '';
  type = '';
  drills: DrillForm[] = [];
  videos: VideoInterface[] = [];

  constructor(data?: Partial<CourseSectionItemForm>) {
    super();
    this.assignAttributes(data);
  }

  public createInput() {
    return omit(this, 'id', 'courseSectionId', 'drills');
  }

  public updateInput() {
    return omit(this, 'id', 'courseSectionId', 'drills', 'createdAt', 'updatedAt');
  }
}
