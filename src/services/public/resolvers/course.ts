import gql from 'graphql-tag';
import { metadataFragment } from '@/services/base/fragments/shared';
import { publicCourseBaseFragment, publicTeacherBaseFragment } from '@/services/public/fragments/index';

export const publicCoursesGql = gql`
  query publicCourses($input: PagyInput, $query: CourseQueryInput, $orderBy: String) {
    courses(input: $input, query: $query, orderBy: $orderBy) {
      collection {
        ...courseBaseFields
        totalRateCount
        teacher {
          ...teacherBaseFields
        }
      }
      metadata {
        ...commonMetadata
      }
    }
  }

  ${metadataFragment}
  ${publicCourseBaseFragment}
  ${publicTeacherBaseFragment}
`;

export const publicCourseGql = gql`
  ${publicCourseBaseFragment}
  ${publicTeacherBaseFragment}
  query Course($slug: String!) {
    course(slug: $slug) {
      ...courseBaseFields
      totalRateCount
      teacher {
        ...teacherBaseFields
      }
      courseHighlights {
        id
        highlight {
          icon {
            class
          }
          title
          description
        }
      }
      courseSections {
        id
        title
        courseId
        position
        createdAt
        updatedAt
        courseSectionItems {
          id
          title
        }
      }
      certificate {
        title
        isActive
        description
      }
    }
  }
`;

export const publicRelatedCoursesGql = gql`
  ${metadataFragment}
  ${publicCourseBaseFragment}
  ${publicTeacherBaseFragment}
  query RelatedCourses($input: PagyInput, $courseSlug: String!) {
    relatedCourses(input: $input, courseSlug: $courseSlug) {
      collection {
        ...courseBaseFields
        totalRateCount
        teacher {
          ...teacherBaseFields
        }
        totalRateCount
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;
