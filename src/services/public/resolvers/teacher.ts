import gql from 'graphql-tag';
import { commentCommonFragment, metadataFragment, teacherFieldsFragment } from '@/services/base/fragments/shared';
import { publicExperienceBaseFragment, publicTeacherBaseFragment } from '@/services/public/fragments/index';

export const publicTeachersGql = gql`
  ${metadataFragment}
  ${publicTeacherBaseFragment}
  ${commentCommonFragment}
  ${publicExperienceBaseFragment}
  query publicTeachers($input: PagyInput, $query: TeacherQueryInput, $orderBy: String) {
    teachers(input: $input, query: $query, orderBy: $orderBy) {
      collection {
        ...teacherBaseFields
        joinedStudentCount
        ratings {
          ...commonComment
        }
        experiences {
          ...experienceBaseFields
          icon {
            id
            key
            class
          }
        }
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;

export const publicTeacherGql = gql`
  ${teacherFieldsFragment}
  ${commentCommonFragment}
  ${publicExperienceBaseFragment}
  query publicTeacher($slug: String!) {
    teacher(slug: $slug) {
      ...TeacherFields
      socialLinks {
        facebook
        instagram
        youtube
        twitter
        telegram
        discord
        zalo
        tiktok
      }
      ratings {
        ...commonComment
      }
      experiences {
        ...experienceBaseFields
        icon {
          id
          key
          class
        }
      }
    }
  }
`;
