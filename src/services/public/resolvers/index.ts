import gql from 'graphql-tag';

export const fetchSelectOptionsGQL = gql`
  query ($input: selectOptionsInput!) {
    selectOptions(input: $input) {
      levelOptions {
        value
        label
        description
      }
      skillOptions {
        value
        label
      }
      courseStatusOptions {
        value
        label
      }
      teacherOptions {
        value
        label
      }
      courseInstructionalLevelOptions {
        value
        label
      }
      packageDealOptions {
        value
        label
      }
      iconOptions {
        value
        label
        class
      }
    }
  }
`;
