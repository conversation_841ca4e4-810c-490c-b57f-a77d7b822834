import gql from 'graphql-tag';
import { metadataFragment } from '@/services/base/fragments/shared';
import { publicDrillBaseFragment } from '../fragments';

export const publicDrillsGql = gql`
  ${publicDrillBaseFragment}
  ${metadataFragment}
  query Drills($input: PagyInput, $query: DrillQueryInput, $orderBy: String) {
    drills(input: $input, query: $query, orderBy: $orderBy) {
      collection {
        ...drillBaseFields
        stepCount
        videoCount
        ownerType
        createdAt
        step
        skills {
          id
          name
          nameI18n
          description
        }
        diagrams {
          id
          imageUrl
        }
        owner {
          id
          slug
          name
          imageUrl
        }
        videos {
          id
        }
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;

export const publicDrillGql = gql`
  ${publicDrillBaseFragment}
  query Drill($slug: String!) {
    drill(slug: $slug) {
      ...drillBaseFields
      step
      stepCount
      videoCount
      isVisible
      censor
      censorI18n
      ownerType
      skills {
        id
        name
        nameI18n
        description
      }
      diagrams {
        id
        parentID
        parentType
        imageUrl
        setting
        position
      }
      videos {
        id
        title
        thumbnailURL
        isPlayable
        videoPlatforms {
          status
        }
      }
      owner {
        id
        name
        imageUrl
        contactEmail
      }
    }
  }
`;

export const publicRelatedDrillsGql = gql`
  ${publicDrillBaseFragment}
  ${metadataFragment}
  query RelatedDrills($input: PagyInput, $query: RelatedDrillInput, $drillSlug: String!) {
    relatedDrills(input: $input, query: $query, drillSlug: $drillSlug) {
      collection {
        ...drillBaseFields
        skills {
          id
          name
          nameI18n
          description
        }
        diagrams {
          id
          imageUrl
        }
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;
