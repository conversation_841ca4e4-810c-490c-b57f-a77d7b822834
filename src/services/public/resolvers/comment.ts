import gql from 'graphql-tag';
import { metadataFragment, commentCommonFragment } from '@/services/base/fragments/shared';
import { publicCourseBaseFragment } from '../fragments';

export const publicReviewsGql = gql`
  ${metadataFragment}
  ${commentCommonFragment}
  ${publicCourseBaseFragment}

  query reviews($input: PagyInput, $targetID: ID!, $targetType: String!) {
    reviews(input: $input, targetID: $targetID, targetType: $targetType) {
      collection {
        ...commonComment

        authorUser {
          id
          name
          gender
          birthDate
          imageUrl
          createdAt
          updatedAt
        }
        relatedCourse {
          ...courseBaseFields
        }
      }
      stats {
        star1
        star2
        star3
        star4
        star5
        total
        averageRating
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;

export const publicFeatureReviewsGql = gql`
  ${metadataFragment}
  ${commentCommonFragment}
  ${publicCourseBaseFragment}

  query featuredReviews($input: PagyInput, $targetType: String!) {
    featuredReviews(input: $input, targetType: $targetType) {
      collection {
        ...commonComment

        authorUser {
          id
          name
          gender
          birthDate
          imageUrl
          createdAt
          updatedAt
        }
        relatedCourse {
          ...courseBaseFields
        }
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;
