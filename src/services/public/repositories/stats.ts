import { graphqlApiClients } from '@/utils/api-clients';
import {
  fetchCourseOverviewStatsGql,
  fetchDrillStatsGql,
  fetchOverviewStatsGql,
  fetchTeacherOverviewStatsGql
} from '../resolvers/stats';

export async function fetchOverviewStats() {
  return await graphqlApiClients.PUBLIC.query(fetchOverviewStatsGql);
}

export async function fetchTeacherOverviewStats() {
  return await graphqlApiClients.PUBLIC.query(fetchTeacherOverviewStatsGql);
}

export async function fetchCourseOverviewStats() {
  return await graphqlApiClients.PUBLIC.query(fetchCourseOverviewStatsGql);
}
export async function fetchDrillStats() {
  return await graphqlApiClients.PUBLIC.query(fetchDrillStatsGql);
}
