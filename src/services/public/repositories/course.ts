import { graphqlApiClients } from '@/utils/api-clients';

import { publicCoursesGql, publicCourseGql, publicRelatedCoursesGql } from '../resolvers/course';
import { joinCoursesGql } from '../mutations/course';

import { UserCourseListInput } from '@/utils/interface/user/userCourse';
import { RelatedCoursesParamsInterface } from '@/utils/interface/public/course';

export async function courseList(params: UserCourseListInput) {
  const res = await graphqlApiClients.PUBLIC.query(publicCoursesGql, params);

  return res.data;
}

export async function relatedCourses(params: RelatedCoursesParamsInterface) {
  const res = await graphqlApiClients.PUBLIC.query(publicRelatedCoursesGql, {
    input: params.input,
    courseSlug: params.courseSlug
  });

  return res.data;
}

export async function showCourse(slug: string) {
  const res = await graphqlApiClients.PUBLIC.query(publicCourseGql, {
    slug: slug
  });

  return res.data;
}

export async function joinCourse(params: any) {
  return await graphqlApiClients.USER.query(joinCoursesGql, {
    id: params.id,
    verifyCode: params.verifyCode,
    coursePackageID: params.coursePackageID
  });
}
