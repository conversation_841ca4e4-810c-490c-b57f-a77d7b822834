import { graphqlApiClients } from '@/utils/api-clients';
import { publicTeacherGql, publicTeachersGql } from '../resolvers/teacher';
import { TeacherListInput } from '@/utils/interface/public/teacher';

export async function teacherList(params: TeacherListInput) {
  const res = await graphqlApiClients.PUBLIC.query(publicTeachersGql, {
    input: params.input,
    query: params.query,
    orderBy: params.orderBy
  });
  return res.data;
}

export async function teacherDetail(slug: string) {
  const res = await graphqlApiClients.PUBLIC.query(publicTeacherGql, { slug });

  return res.data;
}
