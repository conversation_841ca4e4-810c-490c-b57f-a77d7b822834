import { graphqlApiClients } from '@/utils/api-clients';

import { publicFeatureReviewsGql, publicReviewsGql } from '@/services/public/resolvers/comment';

import { ReviewListInputInterface } from '@/utils/interface/public/comment';

export async function reviewsList(params: ReviewListInputInterface) {
  const res = await graphqlApiClients.PUBLIC.query(publicReviewsGql, params);

  return res.data;
}

export async function featureReviewsList(params: ReviewListInputInterface) {
  const res = await graphqlApiClients.PUBLIC.query(publicFeatureReviewsGql, params);

  return res.data;
}
