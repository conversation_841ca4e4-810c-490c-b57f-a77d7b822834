import { graphqlApiClients } from '@/utils/api-clients';
import { CustomAxiosRequestConfig } from '@/utils/interface/axios';

import { fetchSelectOptionsGQL } from '../resolvers';

export function fetchOptions(keys: string[], params: Record<string, any>, apiOptions?: CustomAxiosRequestConfig) {
  return graphqlApiClients.PUBLIC.query(
    fetchSelectOptionsGQL,
    {
      input: { keys, params }
    },
    { ...apiOptions }
  );
}
