import type { AxiosResponse } from 'axios';

import { publicDrillGql, publicDrillsGql, publicRelatedDrillsGql } from '@/services/public/resolvers/drill';
import { graphqlApiClients } from '@/utils/api-clients';

import { DrillListParamsInterface, DrillResponseInterface } from '@/utils/interface/public/drill';
import { RelatedDrillListQueryFormModel } from '@/forms/public/drill';

export async function drillList(params: DrillListParamsInterface) {
  const res = await graphqlApiClients.PUBLIC.query(publicDrillsGql, {
    input: params.input,
    query: params.query,
    orderBy: params.orderBy
  });

  return res.data;
}

export async function relatedDrills(params: RelatedDrillListQueryFormModel) {
  const res = await graphqlApiClients.PUBLIC.query(publicRelatedDrillsGql, {
    input: params.input,
    query: params.query,
    drillSlug: params.drillSlug
  });

  return res.data;
}

export function drillShow(slug: string): Promise<AxiosResponse<DrillResponseInterface>> {
  return graphqlApiClients.PUBLIC.query(publicDrillGql, { slug });
}
