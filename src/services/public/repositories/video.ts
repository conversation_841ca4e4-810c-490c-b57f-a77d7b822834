import type { AxiosResponse } from 'axios';
import { VideoPlayBackGql } from '@/services/public/resolvers/video';
import { graphqlApiClients } from '@/utils/api-clients';

import { VideoPlayBackResponseInterface } from '@/utils/interface/public/video';

export function videoPlayBack(videoId: string): Promise<AxiosResponse<VideoPlayBackResponseInterface>> {
  return graphqlApiClients.PUBLIC.query(VideoPlayBackGql, { videoId });
}
