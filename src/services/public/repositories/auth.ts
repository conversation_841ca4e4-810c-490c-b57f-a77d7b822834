import { graphqlApiClients } from '@/utils/api-clients';
import {
  ResetPasswordInterface,
  ResetPasswordVerifyInterface,
  SignInInputInterface,
  SignUpInputInterface
} from '@/utils/interface/user/auth';
import {
  signInGql,
  signOutGql,
  signUpGql,
  refreshTokenGql,
  signUpVerifyGql,
  signUpVerifyResendGql,
  resetPasswordRequestGql,
  resetPasswordVerifyGql,
  resetPasswordGql
} from '@/services/public/mutations/auth';

export function signIn(input: SignInInputInterface) {
  return graphqlApiClients.PUBLIC.query(signInGql, { input });
}

export function signOut(tokenSource: string) {
  return graphqlApiClients.PUBLIC.query(signOutGql, {}, { tokenSource });
}

export function signUp(input: SignUpInputInterface) {
  return graphqlApiClients.PUBLIC.query(signUpGql, { input });
}

export function refreshToken(refreshToken: string) {
  return graphqlApiClients.PUBLIC.query(refreshTokenGql, { refreshToken }, { toast: false });
}

export function signUpVerify(phoneNumber: string, code: string) {
  return graphqlApiClients.PUBLIC.query(signUpVerifyGql, { phoneNumber, code });
}

export function signUpVerifyResend(phoneNumber: string) {
  return graphqlApiClients.PUBLIC.query(signUpVerifyResendGql, { phoneNumber });
}

export function resetPasswordRequest(identifier: string) {
  return graphqlApiClients.PUBLIC.query(resetPasswordRequestGql, { identifier });
}

export function resetPasswordVerify(input: ResetPasswordVerifyInterface) {
  return graphqlApiClients.PUBLIC.query(resetPasswordVerifyGql, { input: input });
}

export function resetPassword(input: ResetPasswordInterface) {
  return graphqlApiClients.PUBLIC.query(resetPasswordGql, { input: input });
}
