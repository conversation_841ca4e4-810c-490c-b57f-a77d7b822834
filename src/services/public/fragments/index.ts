import gql from 'graphql-tag';

export const publicDrillBaseFragment = gql`
  fragment drillBaseFields on Drill {
    id
    title
    slug
    description
    level
    levelI18n
    salePrice
    price
  }
`;

export const publicCourseBaseFragment = gql`
  fragment courseBaseFields on CoursePayload {
    id
    teacherId
    title
    slug
    description
    salePrice
    price
    bonusPoint
    status
    bonusPointPercent
    sectionCount
    sectionItemCount
    banner
    joinedUserCount
    instructionalLevel
    instructionalLevelI18n
    averageRating
    isPublic
    createdAt
    updatedAt
  }
`;

export const publicTeacherBaseFragment = gql`
  fragment teacherBaseFields on TeacherPayload {
    id
    name
    award
    address
    slug
    phoneNumber
    contactEmail
    description
    basicEntered
    canInviteStudents
    imageUrl
    approvedCourseCount
    approvedDrillCount
    averageRating
    studentCount
    isProfessional
    createdAt
    updatedAt
  }
`;

export const publicRatingBaseFragment = gql`
  fragment ratingBaseFields on CommentPayload {
    id
    authorID
    authorType
    targetID
    targetType
    content
    rating
    createdAt
    updatedAt
  }
`;

export const publicExperienceBaseFragment = gql`
  fragment experienceBaseFields on ExperiencePayload {
    id
    ownerID
    ownerType
    title
    description
    iconID
    experienceType
    experienceTime
    isOutstanding
    createdAt
    updatedAt
  }
`;
