import gql from 'graphql-tag';

export const signInGql = gql`
  mutation SignIn($input: SignInInput!) {
    signIn(input: $input) {
      message
      accessToken
      refreshToken
      profile {
        id
        username
        email
        phoneNumber
        teacher {
          id
          active
        }
        user {
          id
          active
        }
      }
    }
  }
`;

export const signOutGql = gql`
  mutation SignOut {
    signOut {
      message
    }
  }
`;

export const signUpGql = gql`
  mutation SignUp($input: SignUpInput!) {
    signUp(input: $input) {
      message
    }
  }
`;

export const refreshTokenGql = gql`
  mutation RefreshToken($refreshToken: String!) {
    refreshToken(refreshToken: $refreshToken) {
      accessToken
      refreshToken
      message
    }
  }
`;

export const signUpVerifyGql = gql`
  mutation SignUpVerify($phoneNumber: String, $code: String) {
    signUpVerify(phoneNumber: $phoneNumber, code: $code) {
      message
    }
  }
`;

export const signUpVerifyResendGql = gql`
  mutation SignUpVerifyResend($phoneNumber: String) {
    signUpVerifyResend(phoneNumber: $phoneNumber) {
      message
    }
  }
`;

export const resetPasswordRequestGql = gql`
  mutation ResetPasswordRequest($identifier: String) {
    resetPasswordRequest(identifier: $identifier) {
      message
    }
  }
`;

export const resetPasswordVerifyGql = gql`
  mutation ResetPasswordVerify($input: resetPasswordVerifyInput!) {
    resetPasswordVerify(input: $input) {
      message
      resetPasswordUrl
    }
  }
`;

export const resetPasswordGql = gql`
  mutation ResetPassword($input: passwordResetInput!) {
    resetPassword(input: $input) {
      message
    }
  }
`;
