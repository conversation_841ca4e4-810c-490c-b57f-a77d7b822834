import { print, DocumentNode } from 'graphql';
import { ApiClient } from '@/plugin/axios';
import { AXIOS_METHOD_ENUMS, GQL_API_BASE_URL_ENUMS } from '@/utils/constant';
import { CustomAxiosRequestConfig } from '@/utils/interface/axios';

export class GraphQLApiClient extends ApiClient {
  private site: string;

  constructor(endpoint: string) {
    super({
      baseURL: GQL_API_BASE_URL_ENUMS[endpoint as keyof typeof GQL_API_BASE_URL_ENUMS]
    });
    this.site = endpoint;
  }

  public query(query: DocumentNode, variables: object = {}, options: CustomAxiosRequestConfig = {}) {
    return this.request(
      AXIOS_METHOD_ENUMS.POST,
      '',
      {
        query: print(query),
        variables: variables
      },
      {
        ...options,
        site: this.site
      }
    );
  }
}
