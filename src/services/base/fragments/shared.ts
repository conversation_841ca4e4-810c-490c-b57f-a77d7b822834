import gql from 'graphql-tag';

export const metadataFragment = gql`
  fragment commonMetadata on Metadata {
    total
    perPage
    page
    pages
    count
    next
    prev
    from
    to
  }
`;

export const practiceSubmissionCommonFragment = gql`
  fragment commonPracticeSubmission on PracticeSubmissionPayload {
    id
    practiceId
    practiceType
    userId
    content
    status
    statusI18n
    createdAt
    updatedAt
  }
`;

export const commentCommonFragment = gql`
  fragment commonComment on CommentPayload {
    id
    authorID
    authorType
    targetID
    targetType
    content
    rating
    createdAt
    updatedAt
  }
`;

export const userCommonFragment = gql`
  fragment commonUser on UserPayload {
    id
    name
    imageUrl
    active
    phoneNumber
    gender
    birthDate
    createdAt
    updatedAt
  }
`;

export const teacherFieldsFragment = gql`
  fragment TeacherFields on TeacherPayload {
    id
    name
    active
    award
    address
    phoneNumber
    slug
    contactEmail
    description
    basicEntered
    canInviteStudents
    imageUrl
    averageRating
    approvedCourseCount
    approvedDrillCount
    studentCount
    createdAt
    updatedAt
    joinedStudentCount
    isProfessional
  }
`;
