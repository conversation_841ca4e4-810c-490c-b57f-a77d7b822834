import { ApiClient } from '@/plugin/axios';
import { AXIOS_METHOD_ENUMS, DEFAULT_AXIOS_HEADER_ENUMS } from '@/utils/constant';
import { CustomAxiosRequestConfig } from '@/utils/interface/axios';

export class RestApiClient extends Api<PERSON>lient {
  public post(path: string, data: any, options: CustomAxiosRequestConfig = {}) {
    return this.request(AXIOS_METHOD_ENUMS.POST, path, data, options);
  }

  public get(path: string, data: any, options: CustomAxiosRequestConfig = {}) {
    return this.request(AXIOS_METHOD_ENUMS.GET, path, data, options);
  }

  public postForm(path: string, data: any, options: CustomAxiosRequestConfig = {}) {
    return this.request(AXIOS_METHOD_ENUMS.POST, path, data, {
      headers: DEFAULT_AXIOS_HEADER_ENUMS.FORM_DATA,
      ...options
    });
  }
}
