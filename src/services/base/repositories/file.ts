import { restApiClient } from '@/utils/api-clients';

export function upload(filesData: FormData, path: string, site: string) {
  return restApiClient.postForm(`${site}/${path}`, filesData, { site });
}

export function post(data: any, path: string, site: string) {
  return restApiClient.post(`${site}/${path}`, data, { site });
}

export function get(id: any, path: string, site: string) {
  return restApiClient.get(`${site}/${path}/${id}`, {}, { site });
}
