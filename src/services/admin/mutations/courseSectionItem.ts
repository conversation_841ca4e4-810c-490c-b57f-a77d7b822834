import gql from 'graphql-tag';

export const createCourseSectionItemGql = gql`
  mutation createCourseSectionItem($courseId: ID!, $courseSectionId: ID!, $input: CourseSectionItemInput!) {
    courseSectionItemCreate(courseId: $courseId, courseSectionId: $courseSectionId, input: $input) {
      message
      sectionItem {
        id
        courseSectionId
        title
        content
        type
        createdAt
        updatedAt
      }
    }
  }
`;

export const updateCourseSectionItemGql = gql`
  mutation updateCourseSectionItem($id: ID!, $courseId: ID!, $courseSectionId: ID!, $input: CourseSectionItemInput!) {
    courseSectionItemUpdate(id: $id, courseId: $courseId, courseSectionId: $courseSectionId, input: $input) {
      message
      sectionItem {
        id
        courseSectionId
        title
        content
        type
        createdAt
        updatedAt
      }
    }
  }
`;

export const deleteCourseSectionItemGql = gql`
  mutation deleteCourseSectionItem($id: ID!) {
    courseSectionItemDestroy(id: $id) {
      message
    }
  }
`;
