import gql from 'graphql-tag';
import { courseBaseFragment } from '../fragments';

export const adminCourseCreateGql = gql`
  mutation CourseCreate($input: CourseMutateInput!) {
    courseCreate(input: $input) {
      message
      course {
        id
      }
    }
  }
`;

export const adminCourseUpdateGql = gql`
  ${courseBaseFragment}
  mutation CourseUpdate($id: ID!, $input: CourseMutateInput!) {
    courseUpdate(id: $id, input: $input) {
      message
      course {
        ...courseBase
      }
    }
  }
`;

export const adminCoursesDeleteGql = gql`
  mutation CourseDelete($id: ID!) {
    courseDelete(id: $id) {
      message
    }
  }
`;

export const adminCourseCensorGql = gql`
  mutation CourseCensor($id: ID!, $censor: String!, $feedback: String) {
    courseCensor(id: $id, censor: $censor, feedback: $feedback) {
      message
    }
  }
`;
