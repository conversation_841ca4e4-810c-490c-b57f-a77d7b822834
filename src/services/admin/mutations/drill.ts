import gql from 'graphql-tag';

export const drillDestroyGql = gql`
  mutation DrillDelete($id: ID!) {
    drillDelete(id: $id) {
      message
    }
  }
`;

export const drillCreateGql = gql`
  mutation DrillCreate($input: DrillInput!) {
    drillCreate(input: $input) {
      message
      drill {
        id
      }
    }
  }
`;

export const drillUpdateGql = gql`
  mutation DrillUpdate($id: ID!, $input: DrillInput!) {
    drillUpdate(id: $id, input: $input) {
      message
      drill {
        id
        title
        description
        level
        salePrice
        price
        step
        skills {
          id
          name
          description
        }
        diagrams {
          id
          parentID
          parentType
          imageUrl
          setting
          position
        }
      }
    }
  }
`;

export const drillPublishGql = gql`
  mutation DrillPublish($id: ID!, $status: String!) {
    drillPublish(id: $id, status: $status) {
      message
    }
  }
`;

export const drillCensorGql = gql`
  mutation DrillCensor($id: ID!, $censor: String!, $feedback: String) {
    drillCensor(id: $id, censor: $censor, feedback: $feedback) {
      message
    }
  }
`;
