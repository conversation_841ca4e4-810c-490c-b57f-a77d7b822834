import gql from 'graphql-tag';

export const adminCreateCourseSectionGql = gql`
  mutation createCourseSection($courseId: ID!, $input: CourseSectionInput!) {
    courseSectionCreate(courseId: $courseId, input: $input) {
      message
      courseSection {
        id
        title
        courseId
        position
      }
    }
  }
`;

export const adminUpdateCourseSectionGql = gql`
  mutation CourseSectionUpdate($id: ID!, $courseId: ID!, $input: CourseSectionInput!) {
    courseSectionUpdate(id: $id, courseId: $courseId, input: $input) {
      message
      courseSection {
        id
        title
        courseId
        position
      }
    }
  }
`;

export const adminDeleteCourseSectionGql = gql`
  mutation CourseSectionDelete($id: ID!, $courseID: ID!) {
    courseSectionDelete(id: $id, courseID: $courseID) {
      message
    }
  }
`;

export const adminCourseSectionSwapPositionGql = gql`
  mutation CourseSectionSwapPosition($courseId: ID!, $courseSectionId: ID!, $newIndex: Int!) {
    courseSectionSwapPosition(courseId: $courseId, courseSectionId: $courseSectionId, newIndex: $newIndex) {
      message
    }
  }
`;
