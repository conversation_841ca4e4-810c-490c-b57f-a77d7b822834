import { graphqlApiClients } from '@/utils/api-clients';
import { UserListInput } from '@/utils/interface/admin/user';
import { adminUsersGql } from '@/services/admin/resolvers/user';
import { toggleUserActiveGql } from '@/services/admin/mutations/user';

export async function userList(params: UserListInput) {
  const res = await graphqlApiClients.ADMIN.query(adminUsersGql, {
    input: params.input,
    query: params.query
  });

  return res.data;
}

export async function toggleUserActive(id: string) {
  const res = await graphqlApiClients.ADMIN.query(toggleUserActiveGql, { id });

  return res.data;
}
