import { graphqlApiClients } from '@/utils/api-clients';

import {
  adminCreateCourseSectionGql,
  adminDeleteCourseSectionGql,
  adminUpdateCourseSectionGql,
  adminCourseSectionSwapPositionGql
} from '@/services/admin/mutations/courseSection';

import { CourseSectionInputInterface } from '@/utils/interface/admin/courseSection';

export async function courseSectionCreate(courseId: string | number, input: CourseSectionInputInterface) {
  return await graphqlApiClients.ADMIN.query(adminCreateCourseSectionGql, { courseId, input }, { toast: true });
}

export async function courseSectionUpdate(
  id: string | number,
  courseId: string | number,
  input: CourseSectionInputInterface
) {
  return await graphqlApiClients.ADMIN.query(adminUpdateCourseSectionGql, { id, courseId, input }, { toast: true });
}

export async function courseSectionDelete(id: string | number, courseID: string | number) {
  return await graphqlApiClients.ADMIN.query(
    adminDeleteCourseSectionGql,
    { id: id, courseID: courseID },
    { toast: true }
  );
}

export async function courseSectionSwapPosition(
  courseId: string | number,
  courseSectionId: string | number,
  newIndex: string | number
) {
  return await graphqlApiClients.ADMIN.query(
    adminCourseSectionSwapPositionGql,
    { courseId, courseSectionId, newIndex },
    { toast: true }
  );
}
