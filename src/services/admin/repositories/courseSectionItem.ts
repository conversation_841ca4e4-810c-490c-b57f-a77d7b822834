import { graphqlApiClients } from '@/utils/api-clients';

import {
  createCourseSectionItemGql,
  deleteCourseSectionItemGql,
  updateCourseSectionItemGql
} from '@/services/admin/mutations/courseSectionItem';

import { CourseSectionItemInputInterface } from '@/utils/interface/admin/courseSectionItem';
import { AdminCourseSectionItemSql } from '../resolvers/courseSectionItem';

export async function courseSectionItemCreate(
  courseId: string | number,
  courseSectionId: string | number,
  input: CourseSectionItemInputInterface
) {
  return await graphqlApiClients.ADMIN.query(
    createCourseSectionItemGql,
    { courseId, courseSectionId, input },
    { toast: true }
  );
}

export async function courseSectionItemUpdate(
  id: string | number,
  courseId: string | number,
  courseSectionId: string | number,
  input: CourseSectionItemInputInterface
) {
  return await graphqlApiClients.ADMIN.query(
    updateCourseSectionItemGql,
    { id, courseId, courseSectionId, input },
    { toast: true }
  );
}

export async function courseSectionItemDelete(id: string | number) {
  return await graphqlApiClients.ADMIN.query(deleteCourseSectionItemGql, { id }, { toast: true });
}

export async function adminCourseSectionItemDetail(id: string | number) {
  const res = await graphqlApiClients.ADMIN.query(AdminCourseSectionItemSql, { id });
  return res.data.courseSectionItem;
}
