import { graphqlApiClients } from '@/utils/api-clients';
import { TeacherListParams } from '@/utils/interface/admin/teacher';
import { adminTeachersGql } from '../resolvers/teachers';
import { permitTeacherInviteStudentGql, toggleTeacherActiveGql } from '@/services/admin/mutations/teacher';

export async function teacherList(params: TeacherListParams) {
  const res = await graphqlApiClients.ADMIN.query(adminTeachersGql, {
    input: params.input,
    query: params.query
  });

  return res.data;
}

export async function permitTeacherInviteStudent(id: string) {
  const res = await graphqlApiClients.ADMIN.query(permitTeacherInviteStudentGql, { id });

  return res.data;
}

export async function toggleTeacherActive(id: string) {
  const res = await graphqlApiClients.ADMIN.query(toggleTeacherActiveGql, { id });

  return res.data;
}
