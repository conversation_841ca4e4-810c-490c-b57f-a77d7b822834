import { graphqlApiClients } from '@/utils/api-clients';
import { selfVideoDeleteGql, saveAndUploadDrillVideoGql } from '../mutations/video';

export async function selfVideoDelete(videoId: string) {
  const res = await graphqlApiClients.ADMIN.query(selfVideoDeleteGql, {
    videoId
  });
  return res.data;
}

export async function saveAndUploadDrillVideo(videoId: string, drillId: string) {
  const res = await graphqlApiClients.ADMIN.query(saveAndUploadDrillVideoGql, {
    videoId,
    drillId
  });
  return res.data;
}
