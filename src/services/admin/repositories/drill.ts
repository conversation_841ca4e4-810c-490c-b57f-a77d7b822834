import type { AxiosResponse } from 'axios';

import {
  drillCensorGql,
  drillCreateGql,
  drillDestroyGql,
  drillPublishGql,
  drillUpdateGql
} from '@/services/admin/mutations/drill';
import { drillGql, drillsGql } from '@/services/admin/resolvers/drill';
import { graphqlApiClients } from '@/utils/api-clients';

import {
  DrillCreateResponseInterface,
  DrillListParamsInterface,
  DrillResponseInterface,
  DrillUpdateResponseInterface
} from '@/utils/interface/admin/drill';
import { DrillInputInterface } from '@/utils/interface/drill/drill';
import { MessageInfoInterface } from '@/utils/interface/common';

export async function drillList(params: DrillListParamsInterface) {
  const res = await graphqlApiClients.ADMIN.query(drillsGql, {
    input: params.input,
    query: params.query
  });

  return res.data;
}

export function drillShow(id: string): Promise<AxiosResponse<DrillResponseInterface>> {
  return graphqlApiClients.ADMIN.query(drillGql, { id });
}

export function drillDelete(id: string): Promise<AxiosResponse<MessageInfoInterface>> {
  return graphqlApiClients.ADMIN.query(drillDestroyGql, { id });
}

export function drillCreate(input: DrillInputInterface): Promise<AxiosResponse<DrillCreateResponseInterface>> {
  return graphqlApiClients.ADMIN.query(drillCreateGql, { input });
}

export function drillUpdate(
  id: string,
  input: DrillInputInterface,
  toast = false
): Promise<AxiosResponse<DrillUpdateResponseInterface>> {
  return graphqlApiClients.ADMIN.query(drillUpdateGql, { id, input }, { toast: toast });
}

export function drillPublish(id: string, status: string): Promise<AxiosResponse<MessageInfoInterface>> {
  return graphqlApiClients.ADMIN.query(drillPublishGql, { id, status });
}

export function drillCensor(
  id: string,
  censor: string,
  feedback?: string
): Promise<AxiosResponse<MessageInfoInterface>> {
  return graphqlApiClients.ADMIN.query(drillCensorGql, { id, censor, feedback });
}
