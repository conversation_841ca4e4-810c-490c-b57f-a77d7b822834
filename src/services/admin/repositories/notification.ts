import { graphqlApiClients } from '@/utils/api-clients';
import { NotificationQueryInput } from '@/utils/interface/public/notification';
import { notificationListGql } from '../resolvers/notification';
import { PagyInput } from '@/utils/interface/common';

export async function notificationList(input: PagyInput, query: NotificationQueryInput) {
  const res = await graphqlApiClients.ADMIN.query(notificationListGql, { input, query });

  return res.data;
}
