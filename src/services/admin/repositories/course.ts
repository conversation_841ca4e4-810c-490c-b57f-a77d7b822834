import type { AxiosResponse } from 'axios';
import { graphqlApiClients } from '@/utils/api-clients';

import { adminCourseGql, adminCoursesGql } from '@/services/admin/resolvers/course';
import {
  adminCourseCensorGql,
  adminCourseCreateGql,
  adminCoursesDeleteGql,
  adminCourseUpdateGql
} from '../mutations/course';

import { CourseFormInterface, CourseListParamsInterface } from '@/utils/interface/admin/course';
import { MessageInfoInterface } from '@/utils/interface/common';

export async function adminCourseList(params: CourseListParamsInterface) {
  const res = await graphqlApiClients.ADMIN.query(adminCoursesGql, {
    input: params.input,
    query: params.query
  });

  return res.data;
}

export async function adminCourseDetail(id: string) {
  const res = await graphqlApiClients.ADMIN.query(adminCourseGql, { id });
  return res.data;
}

export async function adminCourseCreate(input: CourseFormInterface) {
  const res = await graphqlApiClients.ADMIN.query(adminCourseCreateGql, { input: input });
  return res.data;
}

export async function adminCourseUpdate(id: string, input: CourseFormInterface) {
  const res = await graphqlApiClients.ADMIN.query(adminCourseUpdateGql, { id: id, input: input });
  return res.data;
}

export async function adminCourseDelete(id: string): Promise<AxiosResponse<MessageInfoInterface>> {
  return graphqlApiClients.ADMIN.query(adminCoursesDeleteGql, { id });
}

export async function adminCourseCensor(
  id: string,
  censor: string,
  feedback: string | null = null
): Promise<AxiosResponse<MessageInfoInterface>> {
  return graphqlApiClients.ADMIN.query(adminCourseCensorGql, { id, censor, feedback });
}
