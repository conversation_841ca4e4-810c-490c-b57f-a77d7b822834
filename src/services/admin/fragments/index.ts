import gql from 'graphql-tag';

export const courseBaseFragment = gql`
  fragment courseBase on CoursePayload {
    id
    teacherId
    title
    description
    salePrice
    price
    status
    statusI18n
    banner
    joinedUserCount
    instructionalLevel
    instructionalLevelI18n
    averageRating
    sectionCount
    bonusPoint
    bonusPointPercent
    joinedUserCount
    createdAt
    updatedAt
  }
`;

export const courseSectionBaseFragment = gql`
  fragment courseSectionBase on CourseSectionPayload {
    id
    courseId
    title
    position
    createdAt
    updatedAt
  }
`;

export const courseSectionItemBaseFragment = gql`
  fragment adminCourseSectionItemBase on CourseSectionItemPayload {
    id
    courseSectionId
    title
    type
    content
    createdAt
    updatedAt
  }
`;

export const adminTeacherBaseFragment = gql`
  fragment adminTeacherBaseFields on TeacherPayload {
    id
    name
    active
    award
    address
    phoneNumber
    contactEmail
    description
    basicEntered
    canInviteStudents
    imageUrl
    createdAt
    updatedAt
  }
`;
