import { metadataFragment } from '@/services/base/fragments/shared';
import gql from 'graphql-tag';

export const notificationListGql = gql`
  ${metadataFragment}
  query Notifications($input: PagyInput, $query: NotificationQueryInput) {
    notifications(input: $input, query: $query) {
      collection {
        id
        isRead
        title
        body
        noticeKind
        createdAt
        readAt
        sender {
          id
          name
          imageUrl
          type
        }
        entity {
          id
          title
          slug
          type
        }
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;
