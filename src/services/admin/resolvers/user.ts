import gql from 'graphql-tag';
import { metadataFragment, userCommonFragment } from '@/services/base/fragments/shared';

export const adminUsersGql = gql`
  ${metadataFragment}
  ${userCommonFragment}

  query Users($input: PagyInput, $query: UserQueryInput) {
    users(input: $input, query: $query) {
      collection {
        ...commonUser
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;
