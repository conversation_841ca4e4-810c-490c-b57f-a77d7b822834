import gql from 'graphql-tag';
import { metadataFragment } from '@/services/base/fragments/shared';
import { adminTeacherBaseFragment } from '../fragments';

export const adminTeachersGql = gql`
  ${metadataFragment}
  ${adminTeacherBaseFragment}
  query Teachers($input: PagyInput, $query: TeacherQueryInput) {
    teachers(input: $input, query: $query) {
      collection {
        ...adminTeacherBaseFields
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;
