import gql from 'graphql-tag';
import { metadataFragment } from '@/services/base/fragments/shared';
import { courseBaseFragment, courseSectionBaseFragment, courseSectionItemBaseFragment } from '../fragments';

export const adminCoursesGql = gql`
  ${metadataFragment}
  ${courseBaseFragment}
  query Courses($input: PagyInput, $query: CourseQueryInput) {
    courses(input: $input, query: $query) {
      collection {
        ...courseBase
        teacher {
          name
        }
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;

export const adminCourseGql = gql`
  ${courseBaseFragment}
  ${courseSectionBaseFragment}
  ${courseSectionItemBaseFragment}
  query Course($id: ID!) {
    course(id: $id) {
      ...courseBase
      teacher {
        id
        name
        phoneNumber
        imageUrl
        contactEmail
      }
      courseSections {
        ...courseSectionBase
        courseSectionItems {
          ...adminCourseSectionItemBase
        }
      }
      courseCensorHistories {
        id
        createdBy
        creator {
          id
          name
        }
        feedback
        status
        statusI18n
        createdAt
        updatedAt
      }
    }
  }
`;
