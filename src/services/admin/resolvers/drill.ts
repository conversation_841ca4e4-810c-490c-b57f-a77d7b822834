import gql from 'graphql-tag';
import { metadataFragment } from '@/services/base/fragments/shared';

export const drillsGql = gql`
  ${metadataFragment}
  query Drills($input: PagyInput, $query: DrillQueryInput) {
    drills(input: $input, query: $query) {
      collection {
        id
        title
        description
        level
        levelI18n
        salePrice
        price
        status
        statusI18n
        ownerType
        ownerID
        censor
        censorI18n
        skills {
          id
          name
          nameI18n
          description
        }
        diagrams {
          id
          imageUrl
        }
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;

export const drillGql = gql`
  query Drill($id: ID!) {
    drill(id: $id) {
      id
      title
      description
      level
      levelI18n
      salePrice
      price
      step
      ownerType
      ownerID
      status
      slug
      statusI18n
      censor
      censorI18n
      isMaster
      skills {
        id
        name
        nameI18n
        description
      }
      diagrams {
        id
        parentID
        parentType
        imageUrl
        setting
        position
      }
      videos {
        id
        title
        thumbnailURL
        isPlayable
      }
      censorHistories {
        id
        parentID
        parentType
        status
        statusI18n
        createdBy
        createdAt
        creator {
          id
          name
        }
        feedback
      }
      userDrillCount
    }
  }
`;
