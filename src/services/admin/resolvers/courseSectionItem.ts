import gql from 'graphql-tag';

export const AdminCourseSectionItemSql = gql`
  query courseSectionItem($id: ID!) {
    courseSectionItem(id: $id) {
      id
      courseSectionId
      title
      slug
      type
      content
      createdAt
      updatedAt
      videos {
        id
        status
        title
        thumbnailURL
        videoPlatforms {
          status
        }
      }
      drills {
        id
        title
        description
        level
        levelI18n
        salePrice
        price
        status
        step
        ownerID
        ownerType
        skills {
          id
          nameI18n
          name
          description
          createdAt
          updatedAt
        }
        diagrams {
          id
          parentID
          parentType
          imageUrl
          setting
          position
          createdAt
          updatedAt
        }
      }
    }
  }
`;
