import gql from 'graphql-tag';
import { metadataFragment, commentCommonFragment } from '@/services/base/fragments/shared';

export const myCoursesGql = gql`
  ${metadataFragment}
  query MyCourses($input: PagyInput, $query: CourseQueryInput) {
    myCourses(input: $input, query: $query) {
      collection {
        id
        title
        description
        salePrice
        price
        bonusPoint
        bonusPointPercent
        joined
        processPercent
        averageRating
        totalRateCount
        joinedUserCount
        instructionalLevel
        instructionalLevelI18n
        sectionItemCount
        banner
        slug
        createdAt
        updatedAt
        teacher {
          id
          name
          award
          address
          phoneNumber
          contactEmail
          description
          basicEntered
          canInviteStudents
          imageUrl
          createdAt
          updatedAt
        }
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;

export const userCoursesGql = gql`
  ${metadataFragment}
  query UserCourses($input: PagyInput, $query: CourseQueryInput) {
    userCourses(input: $input, query: $query) {
      collection {
        id
        title
        description
        salePrice
        price
        bonusPoint
        bonusPointPercent
        sectionItemCount
        joined
        processPercent
        banner
        slug
        joinedUserCount
        instructionalLevel
        instructionalLevelI18n
        averageRating
        totalRateCount
        createdAt
        updatedAt
        teacher {
          id
          name
          award
          address
          phoneNumber
          contactEmail
          description
          basicEntered
          canInviteStudents
          imageUrl
          createdAt
          updatedAt
        }
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;

export const myCourseGql = gql`
  ${commentCommonFragment}

  query MyCourse($slug: String!) {
    myCourse(slug: $slug) {
      id
      teacherId
      title
      description
      salePrice
      price
      bonusPoint
      bonusPointPercent
      sectionCount
      sectionItemCount
      joined
      processPercent
      isPublic
      status
      banner
      slug
      createdAt
      updatedAt
      courseUserMetadata {
        completedSectionCount
        completedSectionItemCount
        lastAccessSectionSlug
        isReviewTeacherInCourse
      }
      teacher {
        id
        name
        slug
        imageUrl
        award
        address
        createdAt
        updatedAt
      }
      courseSections {
        id
        title
        slug
        isCompleted
        courseId
        position
        courseSectionItems {
          id
          courseSectionId
          title
          slug
          type
          isCompleted
          isLocked
          currentUserSectionItem {
            id
            isCompleted
          }
          content
          createdAt
          updatedAt
          videos {
            id
            duration
            currentPosition
          }
        }
      }
      myReview {
        ...commonComment

        authorUser {
          id
          name
          gender
          birthDate
          imageUrl
          createdAt
          updatedAt
        }
      }
      myPackage {
        id
        price
        salePrice
        packageDeal {
          id
          name
        }
      }
    }
  }
`;

export const userCourseGql = gql`
  query UserCourse($slug: String!) {
    userCourse(slug: $slug) {
      id
      teacherId
      title
      description
      salePrice
      price
      bonusPoint
      bonusPointPercent
      sectionCount
      sectionItemCount
      joinedUserCount
      joined
      processPercent
      banner
      isPublic
      slug
      totalRateCount
      createdAt
      updatedAt
      teacher {
        id
        name
        slug
        award
        address
        phoneNumber
        contactEmail
        description
        basicEntered
        imageUrl
      }
      courseSections {
        id
        title
        slug
        isCompleted
        courseId
        position
        courseSectionItems {
          id
          title
          isFree
        }
      }
      currentUserJoining {
        id
        courseId
        userId
        status
        createdAt
        updatedAt
      }
      coursePackages {
        id
        price
        salePrice
        packageDeal {
          id
          name
          description
          approvalSubmissionRequired
        }
      }
      courseHighlights {
        id
        highlight {
          icon {
            class
          }
          title
          description
        }
      }
      certificate {
        title
        isActive
        description
      }
    }
  }
`;

export const coursePreviewGql = gql`
  query CoursePreview($slug: String!) {
    coursePreview(slug: $slug) {
      teacher {
        id
        name
        award
        address
        phoneNumber
        contactEmail
        description
        basicEntered
        canInviteStudents
        imageUrl
        createdAt
        updatedAt
      }
      sectionCount
      sectionItemCount
      id
      slug
      totalRateCount
      courseSections {
        id
        title
        courseId
        position
        courseSectionItems {
          id
          title
          content
          slug
          isFree
          type
        }
      }
    }
  }
`;

export const courseSectionItemPreviewGql = gql`
  query CourseSectionItemPreview($courseSlug: String!, $itemSlug: String) {
    courseSectionItemPreview(courseSlug: $courseSlug, itemSlug: $itemSlug) {
      id
      courseSectionId
      title
      slug
      type
      content
      drills {
        id
        title
        description
        level
        levelI18n
        salePrice
        price
        status
        step
        ownerID
        ownerType
        skills {
          id
          name
          nameI18n
          description
          createdAt
          updatedAt
        }
        diagrams {
          id
          parentID
          parentType
          imageUrl
          setting
          position
          createdAt
          updatedAt
        }
      }
      videos {
        id
        status
        title
        thumbnailURL
        isPlayable
        duration
        currentPosition
        videoPlatforms {
          status
        }
      }
    }
  }
`;
