import gql from 'graphql-tag';

export const myCourseSectionItemsGql = gql`
  query MyCourseSectionItems($courseSlug: String!, $courseSectionSlug: String!) {
    myCourseSectionItems(courseSlug: $courseSlug, courseSectionSlug: $courseSectionSlug) {
      id
      courseSectionId
      title
      slug
      type
      content
      isLocked
      drills {
        id
        title
        description
        level
        salePrice
        price
        status
        step
        ownerID
        ownerType
        skills {
          id
          name
          description
          createdAt
          updatedAt
        }
        diagrams {
          id
          parentID
          parentType
          imageUrl
          setting
          position
          createdAt
          updatedAt
        }
      }
    }
  }
`;

export const myCourseSectionItemGql = gql`
  query MyCourseSectionItem($courseSlug: String!, $itemSlug: String) {
    myCourseSectionItem(courseSlug: $courseSlug, itemSlug: $itemSlug) {
      id
      courseSectionId
      title
      slug
      type
      isCompleted
      isLocked
      currentUserSectionItem {
        id
        isCompleted
      }
      content
      drills {
        id
        title
        description
        level
        levelI18n
        salePrice
        price
        status
        step
        ownerID
        ownerType
        skills {
          id
          name
          nameI18n
          description
          createdAt
          updatedAt
        }
        diagrams {
          id
          parentID
          parentType
          imageUrl
          setting
          position
          createdAt
          updatedAt
        }
      }
      videos {
        id
        status
        title
        thumbnailURL
        isPlayable
        duration
        currentPosition
        videoPlatforms {
          status
        }
      }
    }
  }
`;
