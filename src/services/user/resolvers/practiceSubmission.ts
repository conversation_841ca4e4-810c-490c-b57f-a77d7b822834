import gql from 'graphql-tag';
import {
  metadataFragment,
  practiceSubmissionCommonFragment,
  commentCommonFragment
} from '@/services/base/fragments/shared';

export const practiceSubmissionsGql = gql`
  ${metadataFragment}
  ${practiceSubmissionCommonFragment}
  ${commentCommonFragment}

  query PracticeSubmissions($input: PagyInput, $query: PracticeSubmissionsQueryInput) {
    practiceSubmissions(input: $input, query: $query) {
      collection {
        ...commonPracticeSubmission

        comments {
          ...commonComment

          authorUser {
            id
            name
            gender
            birthDate
            imageUrl
            createdAt
            updatedAt
          }
          authorTeacher {
            id
            name
            award
            address
            basicEntered
            phoneNumber
            contactEmail
            description
            imageUrl
            createdAt
            updatedAt
          }
        }
        videos {
          id
          status
          title
          thumbnailURL
          isPlayable
          videoPlatforms {
            status
          }
        }
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;

export const practiceSubmissionGql = gql`
  ${practiceSubmissionCommonFragment}

  query PracticeSubmission($id: ID!) {
    practiceSubmission(id: $id) {
      ...commonPracticeSubmission
    }
  }
`;
