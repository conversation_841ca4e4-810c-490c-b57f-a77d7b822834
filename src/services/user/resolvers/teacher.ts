import { teacherFieldsFragment } from '@/services/base/fragments/shared';
import { publicExperienceBaseFragment } from '@/services/public/fragments';
import gql from 'graphql-tag';

export const userTeacherGql = gql`
  ${teacherFieldsFragment}
  ${publicExperienceBaseFragment}
  query Teacher($slug: String!) {
    teacher(slug: $slug) {
      ...TeacherFields
      availableRating
      socialLinks {
        facebook
        instagram
        youtube
        twitter
        telegram
        discord
        zalo
        tiktok
      }

      experiences {
        ...experienceBaseFields
        icon {
          id
          key
          class
        }
      }
    }
  }
`;
