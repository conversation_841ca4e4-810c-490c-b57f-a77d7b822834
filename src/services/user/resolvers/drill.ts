import gql from 'graphql-tag';
import { userDrillBaseFragment } from '../fragments';
import { metadataFragment } from '@/services/base/fragments/shared';

export const drillGql = gql`
  ${userDrillBaseFragment}
  query Drill($slug: String!) {
    drill(slug: $slug) {
      ...userDrillBaseFields
      stepCount
      videoCount
      isVisible
      censor
      censorI18n
      ownerType
      skills {
        id
        name
        nameI18n
        description
      }
      diagrams {
        id
        parentID
        parentType
        imageUrl
        setting
        position
      }
      videos {
        id
        status
        title
        isPlayable
        thumbnailURL
        videoPlatforms {
          status
        }
      }
      owner {
        id
        slug
        name
        imageUrl
      }
    }
  }
`;

export const myDrillGql = gql`
  query MyDrill($courseId: ID!, $itemId: ID!, $drillId: ID!) {
    myDrill(courseId: $courseId, itemId: $itemId, drillId: $drillId) {
      videos {
        id
        status
        title
        isPlayable
        thumbnailURL
      }
    }
  }
`;

export const userListDrillGql = gql`
  ${metadataFragment}
  ${userDrillBaseFragment}
  query Drills($input: PagyInput, $query: DrillQueryInput, $orderBy: String) {
    drills(input: $input, query: $query, orderBy: $orderBy) {
      collection {
        ...userDrillBaseFields
        stepCount
        videoCount
        ownerType
        skills {
          id
          name
          nameI18n
          description
          createdAt
          updatedAt
        }
        diagrams {
          id
          parentID
          parentType
          imageUrl
          setting
          position
        }
        owner {
          id
          slug
          name
          imageUrl
        }
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;

export const userPurchasedDrillListGql = gql`
  ${userDrillBaseFragment}
  ${metadataFragment}
  query MyDrills($input: PagyInput) {
    myDrills(input: $input) {
      collection {
        ...userDrillBaseFields
        status
        statusI18n
        censor
        censorI18n
        isMaster
        ownerID
        ownerType
        videoCount
        stepCount
        diagrams {
          id
          parentID
          parentType
          imageUrl
          setting
          position
          createdAt
          updatedAt
        }
        skills {
          id
          name
          nameI18n
          description
          createdAt
          updatedAt
        }
        owner {
          id
          name
          imageUrl
          slug
        }
        videos {
          id
        }
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;
