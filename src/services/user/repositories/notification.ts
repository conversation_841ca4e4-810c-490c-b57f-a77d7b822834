import { graphqlApiClients } from '@/utils/api-clients';
import { makeNotificationReadGql } from '../mutations/notification';
import { notificationListGql } from '../resolvers/notification';
import { PagyInput } from '@/utils/interface/common';
import { NotificationQueryInput } from '@/utils/interface/public/notification';

export async function makeNotificationRead(id: string) {
  const res = await graphqlApiClients.USER.query(
    makeNotificationReadGql,
    {
      id: id
    },
    {
      toast: false
    }
  );

  return res.data;
}

export async function notificationList(input: PagyInput, query: NotificationQueryInput) {
  const res = await graphqlApiClients.USER.query(notificationListGql, { input, query });

  return res.data;
}
