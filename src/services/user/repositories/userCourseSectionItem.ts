import { graphqlApiClients } from '@/utils/api-clients';
import { myCourseSectionItemGql, myCourseSectionItemsGql } from '../resolvers/userCourseSectionItem';
import {
  setSectionItemStatusGql,
  markVideoProgressGql,
  courseUserSetLastAccessItemSlugSql
} from '../mutations/userCourseSectionItem';

export async function showMyCourseSectionItems(courseSlug: string, courseSectionSlug: string) {
  const res = await graphqlApiClients.USER.query(myCourseSectionItemsGql, {
    courseSlug: courseSlug,
    courseSectionSlug: courseSectionSlug
  });

  return res.data;
}

export async function showMyCourseSectionItem(courseSlug: string, itemSlug?: string) {
  const res = await graphqlApiClients.USER.query(myCourseSectionItemGql, {
    courseSlug: courseSlug,
    itemSlug: itemSlug
  });

  return res.data;
}

export async function setSectionItemStatus(id: string, courseId: string, status: string) {
  return await graphqlApiClients.USER.query(setSectionItemStatusGql, { id, courseId, status }, { toast: false });
}

export async function markVideoProgress(videoId: string, progress: number) {
  return await graphqlApiClients.USER.query(
    markVideoProgressGql,
    {
      videoId: videoId,
      progress: progress
    },
    { toast: false }
  );
}

export async function courseUserSetLastAccessItemSlug(itemSlug: string, courseId: string) {
  return await graphqlApiClients.USER.query(
    courseUserSetLastAccessItemSlugSql,
    {
      itemSlug: itemSlug,
      courseId: courseId
    },
    { toast: false }
  );
}
