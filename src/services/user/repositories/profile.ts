import { graphqlApiClients } from '@/utils/api-clients';
import { updateProfileGql, selfVideoDeleteGql } from '../mutations/self';
import { UserProfileModel } from '@/forms/user/profile';

export async function updateProfile(input: UserProfileModel) {
  const res = await graphqlApiClients.USER.query(updateProfileGql, {
    input
  });

  return res.data;
}

export async function selfVideoDelete(videoId: string) {
  const res = await graphqlApiClients.USER.query(selfVideoDeleteGql, {
    videoId
  });
  return res.data;
}
