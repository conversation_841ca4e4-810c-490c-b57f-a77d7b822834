import { graphqlApiClients } from '@/utils/api-clients';
import { practiceSubmissionsGql } from '../resolvers/practiceSubmission';
import { practiceSubmissionCreateGql } from '../mutations/practiceSubmission';

import {
  PracticeSubmissionCreateInterface,
  PracticeSubmissionListInput
} from '@/utils/interface/user/practiceSubmission';

export async function practiceSubmissionCreate(input: PracticeSubmissionCreateInterface) {
  const res = await graphqlApiClients.USER.query(practiceSubmissionCreateGql, {
    input: input
  });

  return res.data;
}

export async function practiceSubmissionList(params: PracticeSubmissionListInput) {
  const res = await graphqlApiClients.USER.query(practiceSubmissionsGql, {
    input: params.input,
    query: params.query
  });

  return res.data;
}
