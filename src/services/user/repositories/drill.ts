import { graphqlApiClients } from '@/utils/api-clients';
import { drillGql, myDrillGql, userListDrillGql, userPurchasedDrillListGql } from '../resolvers/drill';
import { drillBuyGql } from '../mutations/drill';
import { UserDrillListInterface, DrillListParamsInterface } from '@/utils/interface/public/drill';

export async function showDrill(slug: string) {
  const res = await graphqlApiClients.USER.query(drillGql, { slug: slug }, { toast: false });

  return res.data;
}

export async function showMyDrill(courseId: string, itemId: string, drillId: string) {
  const res = await graphqlApiClients.USER.query(
    myDrillGql,
    {
      courseId: courseId,
      itemId: itemId,
      drillId: drillId
    },
    { toast: false }
  );

  return res.data.myDrill;
}

export async function userFetchListDrill(params: DrillListParamsInterface) {
  const res = await graphqlApiClients.USER.query(userListDrillGql, {
    input: params.input,
    query: params.query,
    orderBy: params.orderBy
  });

  return res.data;
}

export async function buyDrill(id: string) {
  const res = await graphqlApiClients.USER.query(drillBuyGql, { id: id });

  return res.data;
}

export async function drillListUserPurchased(params: UserDrillListInterface) {
  const res = await graphqlApiClients.USER.query(userPurchasedDrillListGql, {
    input: params.input
  });
  return res.data;
}
