import { graphqlApiClients } from '@/utils/api-clients';
import { commentCreateGql, reviewCreateGql, reviewUpdateGql } from '@/services/user/mutations/comment';

import { CommentCreateInterface, ReviewCreateInterface, ReviewUpdateInterface } from '@/utils/interface/user/comment';
import { myReviewGql } from '../resolvers/comment';

export async function commentCreate(input: CommentCreateInterface) {
  const res = await graphqlApiClients.USER.query(commentCreateGql, {
    input: input
  });

  return res.data;
}

export async function reviewCreate(input: ReviewCreateInterface) {
  const res = await graphqlApiClients.USER.query(reviewCreateGql, {
    input: input
  });

  return res.data;
}

export async function reviewUpdate(reviewID: string, input: ReviewUpdateInterface) {
  const res = await graphqlApiClients.USER.query(reviewUpdateGql, {
    reviewID: reviewID,
    input: input
  });

  return res.data;
}

export async function myReview(targetID: string, targetType: string) {
  const res = await graphqlApiClients.USER.query(myReviewGql, {
    targetID: targetID,
    targetType: targetType
  });

  return res.data;
}
