import { graphqlApiClients } from '@/utils/api-clients';
import { selfInfoGql } from '../resolvers/self';
import { ChangePasswordInterface } from '@/utils/interface/user/auth';
import { changePasswordGql } from '../mutations/self';

export async function selfInfo() {
  const res = await graphqlApiClients.USER.query(selfInfoGql);

  return res.data;
}

export async function changePassword(input: ChangePasswordInterface) {
  const res = await graphqlApiClients.USER.query(changePasswordGql, {
    input: input
  });

  return res.data;
}
