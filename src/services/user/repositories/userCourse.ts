import { graphqlApiClients } from '@/utils/api-clients';
import {
  myCoursesGql,
  userCoursesGql,
  myCourseGql,
  userCourseGql,
  coursePreviewGql,
  courseSectionItemPreviewGql
} from '../resolvers/userCourse';
import { UserCourseListInput } from '@/utils/interface/user/userCourse';

export async function myCourseList(params: UserCourseListInput) {
  const res = await graphqlApiClients.USER.query(myCoursesGql, {
    input: params.input,
    query: params.query
  });

  return res.data;
}

// TODO: delete if not in use anymore
export async function userCourseList(params: UserCourseListInput) {
  const res = await graphqlApiClients.USER.query(userCoursesGql, {
    input: params.input,
    query: params.query
  });

  return res.data;
}

export async function showMyCourse(slug: string) {
  const res = await graphqlApiClients.USER.query(myCourseGql, {
    slug: slug
  });

  return res.data;
}

export async function showUserCourse(slug: string) {
  const res = await graphqlApiClients.USER.query(userCourseGql, {
    slug: slug
  });

  return res.data;
}

export async function coursePreview(slug: string) {
  const res = await graphqlApiClients.USER.query(coursePreviewGql, {
    slug: slug
  });

  return res.data;
}

export async function courseSectionItemPreview(courseSlug: string, itemSlug?: string) {
  const res = await graphqlApiClients.USER.query(courseSectionItemPreviewGql, {
    courseSlug: courseSlug,
    itemSlug: itemSlug
  });

  return res.data;
}
