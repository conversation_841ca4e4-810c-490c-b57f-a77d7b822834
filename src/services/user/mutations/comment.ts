import gql from 'graphql-tag';
import { commentCommonFragment } from '@/services/base/fragments/shared';

export const commentCreateGql = gql`
  ${commentCommonFragment}

  mutation CommentCreate($input: CommentCreateInput!) {
    commentCreate(input: $input) {
      message
      comment {
        ...commonComment

        authorUser {
          id
          name
          gender
          birthDate
          imageUrl
          createdAt
          updatedAt
        }
      }
    }
  }
`;

export const reviewCreateGql = gql`
  ${commentCommonFragment}

  mutation ReviewCreate($input: ReviewCreateInput!) {
    reviewCreate(input: $input) {
      message
      comment {
        ...commonComment

        authorUser {
          id
          name
          gender
          birthDate
          imageUrl
          createdAt
          updatedAt
        }
      }
    }
  }
`;

export const reviewUpdateGql = gql`
  ${commentCommonFragment}

  mutation ReviewUpdate($reviewID: ID!, $input: ReviewUpdateInput!) {
    reviewUpdate(reviewID: $reviewID, input: $input) {
      message
      comment {
        ...commonComment

        authorUser {
          id
          name
          gender
          birthDate
          imageUrl
          createdAt
          updatedAt
        }
      }
    }
  }
`;
