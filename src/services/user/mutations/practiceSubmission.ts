import gql from 'graphql-tag';
import { practiceSubmissionCommonFragment } from '@/services/base/fragments/shared';

export const practiceSubmissionCreateGql = gql`
  ${practiceSubmissionCommonFragment}

  mutation PracticeSubmissionCreate($input: PracticeSubmissionCreateInput!) {
    practiceSubmissionCreate(input: $input) {
      message
      practiceSubmission {
        ...commonPracticeSubmission
      }
    }
  }
`;
