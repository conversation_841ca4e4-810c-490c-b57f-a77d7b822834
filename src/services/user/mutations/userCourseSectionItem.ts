import gql from 'graphql-tag';

export const setSectionItemStatusGql = gql`
  mutation SetSectionItemStatus($id: ID!, $courseId: ID!, $status: String!) {
    setSectionItemStatus(id: $id, courseId: $courseId, status: $status) {
      message
    }
  }
`;

export const markVideoProgressGql = gql`
  mutation MarkVideoProgress($videoId: ID!, $progress: Int!) {
    markVideoProgress(videoId: $videoId, progress: $progress) {
      message
    }
  }
`;

export const courseUserSetLastAccessItemSlugSql = gql`
  mutation CourseUserSetLastAccessItemSlug($itemSlug: String!, $courseId: ID!) {
    courseUserSetLastAccessItemSlug(itemSlug: $itemSlug, courseId: $courseId) {
      message
    }
  }
`;
