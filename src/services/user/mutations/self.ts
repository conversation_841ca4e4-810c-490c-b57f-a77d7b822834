import gql from 'graphql-tag';

export const updateProfileGql = gql`
  mutation UpdateSelfInfo($input: SelfInfoInput!) {
    updateSelfInfo(input: $input) {
      message
    }
  }
`;

export const selfVideoDeleteGql = gql`
  mutation SelfVideoDelete($videoId: ID!) {
    selfVideoDelete(videoId: $videoId) {
      message
    }
  }
`;

export const changePasswordGql = gql`
  mutation ChangePassword($input: ChangePasswordInput!) {
    changePassword(input: $input) {
      message
    }
  }
`;
