import { graphqlApiClients } from '@/utils/api-clients';

import {
  courseSectionItemDeleteVideoGql,
  courseSectionItemSql,
  courseSectionItemsSql
} from '@/services/teacher/resolvers/courseSectionItem';
import {
  courseSectionItemAddDrillGql,
  courseSectionItemAddVideoGql,
  courseSectionItemRemoveDrillGql,
  courseSectionItemSwapPositionGql,
  createCourseSectionItemGql,
  deleteCourseSectionItemGql,
  updateCourseSectionItemGql
} from '@/services/teacher/mutations/courseSectionItem';
import {
  CourseSectionItemInputInterface,
  CourseSectionItemResponseInterface
} from '@/utils/interface/teacher/courseSectionItem';
import { AxiosResponse } from 'axios';

export async function courseSectionItemList(courseId: string | number, courseSectionId: string | number) {
  const res = await graphqlApiClients.TEACHER.query(courseSectionItemsSql, { courseId, courseSectionId });

  return res.data.courseSectionItems;
}

export async function courseSectionItemDetail(
  id: string,
  courseId: string,
  courseSectionId: string
): Promise<AxiosResponse<CourseSectionItemResponseInterface>> {
  return await graphqlApiClients.TEACHER.query(courseSectionItemSql, {
    id,
    courseId,
    courseSectionId
  });
}

export async function courseSectionItemCreate(
  courseId: string | number,
  courseSectionId: string | number,
  input: CourseSectionItemInputInterface
) {
  return await graphqlApiClients.TEACHER.query(
    createCourseSectionItemGql,
    {
      courseId,
      courseSectionId,
      input
    },
    {
      toast: true
    }
  );
}

export async function courseSectionItemUpdate(
  id: string,
  courseId: string,
  courseSectionId: string,
  input: CourseSectionItemInputInterface
) {
  return await graphqlApiClients.TEACHER.query(
    updateCourseSectionItemGql,
    {
      id,
      courseId,
      courseSectionId,
      input
    },
    {
      toast: true
    }
  );
}

export async function courseSectionItemDelete(id: string, courseId: string, courseSectionId: string) {
  return await graphqlApiClients.TEACHER.query(
    deleteCourseSectionItemGql,
    {
      id,
      courseId,
      courseSectionId
    },
    {
      toast: true
    }
  );
}

export async function courseSectionItemAddDrill(
  itemId: string,
  courseId: string,
  courseSectionId: string,
  drillId: string
) {
  return await graphqlApiClients.TEACHER.query(
    courseSectionItemAddDrillGql,
    {
      itemId,
      courseId,
      courseSectionId,
      drillId
    },
    {
      toast: true
    }
  );
}

export async function courseSectionItemRemoveDrill(
  itemId: string,
  courseId: string,
  courseSectionId: string,
  drillId: string
) {
  return await graphqlApiClients.TEACHER.query(
    courseSectionItemRemoveDrillGql,
    {
      itemId,
      courseId,
      courseSectionId,
      drillId
    },
    {
      toast: true
    }
  );
}

export async function courseSectionItemAddVideo(itemId: string, courseId: string, videoId: string) {
  return await graphqlApiClients.TEACHER.query(
    courseSectionItemAddVideoGql,
    {
      itemId,
      courseId,
      videoId
    },
    {
      toast: true
    }
  );
}

export async function courseSectionItemDeleteVideo(videoId: string, courseId: string) {
  const res = await graphqlApiClients.TEACHER.query(courseSectionItemDeleteVideoGql, {
    videoId,
    courseId
  });
  return res.data;
}

export async function courseSectionItemSwapPosition(
  courseId: string,
  courseSectionId: string,
  courseSectionItemId: string,
  newIndex: number
) {
  return await graphqlApiClients.TEACHER.query(
    courseSectionItemSwapPositionGql,
    {
      courseId,
      courseSectionId,
      courseSectionItemId,
      newIndex
    },
    {
      toast: true
    }
  );
}
