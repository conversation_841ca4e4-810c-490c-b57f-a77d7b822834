import type { AxiosResponse } from 'axios';

import {
  drillCreateGql,
  drillDestroyGql,
  drillPublishGql,
  drillUpdateGql,
  saveAndUploadDrillVideoGql,
  submitDrillGql
} from '@/services/teacher/mutations/drill';
import { drillGql, drillsGql } from '@/services/teacher/resolvers/drill';
import { graphqlApiClients } from '@/utils/api-clients';

import {
  DrillCreateResponseInterface,
  DrillListParamsInterface,
  DrillResponseInterface,
  DrillUpdateResponseInterface
} from '@/utils/interface/teacher/drill';
import { DrillInputInterface } from '@/utils/interface/drill/drill';
import { MessageInfoInterface } from '@/utils/interface/common';

export async function drillList(params: DrillListParamsInterface) {
  const res = await graphqlApiClients.TEACHER.query(drillsGql, {
    input: params.input,
    query: params.query
  });

  return res.data;
}

export function drillShow(slug: string): Promise<AxiosResponse<DrillResponseInterface>> {
  return graphqlApiClients.TEACHER.query(drillGql, { slug });
}

export function drillDelete(id: string): Promise<AxiosResponse<MessageInfoInterface>> {
  return graphqlApiClients.TEACHER.query(drillDestroyGql, { id });
}

export function drillCreate(input: DrillInputInterface): Promise<AxiosResponse<DrillCreateResponseInterface>> {
  return graphqlApiClients.TEACHER.query(drillCreateGql, { input });
}

export function drillUpdate(
  id: string,
  input: DrillInputInterface,
  toast = false
): Promise<AxiosResponse<DrillUpdateResponseInterface>> {
  return graphqlApiClients.TEACHER.query(
    drillUpdateGql,
    {
      id,
      input
    },
    {
      toast: toast
    }
  );
}

export function drillPublish(id: string, status: string): Promise<AxiosResponse<MessageInfoInterface>> {
  return graphqlApiClients.TEACHER.query(drillPublishGql, { id, status });
}

export async function saveAndUploadDrillVideo(videoId: string, drillId: string) {
  const res = await graphqlApiClients.TEACHER.query(saveAndUploadDrillVideoGql, {
    videoId,
    drillId
  });
  return res.data;
}

export function drillSubmit(id: string): Promise<AxiosResponse<MessageInfoInterface>> {
  return graphqlApiClients.TEACHER.query(submitDrillGql, { id });
}
