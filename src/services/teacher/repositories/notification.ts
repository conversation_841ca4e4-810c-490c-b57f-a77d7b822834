import { graphqlApiClients } from '@/utils/api-clients';
import { notificationListGql } from '../resolvers/notification';
import { PagyInput } from '@/utils/interface/common';
import { NotificationQueryInput } from '@/utils/interface/public/notification';

export async function notificationList(input: PagyInput, query: NotificationQueryInput) {
  const res = await graphqlApiClients.TEACHER.query(notificationListGql, { input, query });

  return res.data;
}
