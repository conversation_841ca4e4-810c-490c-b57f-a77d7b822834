import { graphqlApiClients } from '@/utils/api-clients';
import { highlightCreateGql, highlightUpdateGql } from '../mutations/highlight';
import { highlightsGql, highlightGql } from '../resolvers/highlight';
import { HighlightModifyInterface, HighlightQueryInput } from '@/utils/interface/teacher/highlight';
import { PagyInput } from '@/utils/interface/common';

export async function highlightCreate(input: HighlightModifyInterface) {
  const res = await graphqlApiClients.TEACHER.query(highlightCreateGql, {
    input: input
  });

  return res.data;
}

export async function highlightUpdate(id: string, input: HighlightModifyInterface) {
  const res = await graphqlApiClients.TEACHER.query(highlightUpdateGql, {
    id: id,
    input: input
  });

  return res.data;
}

export async function highlightShow(id: string) {
  const res = await graphqlApiClients.TEACHER.query(highlightGql, {
    id: id
  });

  return res.data;
}

export async function highlightList(params: { input: PagyInput; query: HighlightQueryInput }) {
  const res = await graphqlApiClients.TEACHER.query(highlightsGql, { input: params.input, query: params.query });

  return res.data;
}
