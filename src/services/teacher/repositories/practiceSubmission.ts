import { graphqlApiClients } from '@/utils/api-clients';
import { practiceSubmissionsGql, practiceSubmissionGql } from '@/services/teacher/resolvers/practiceSubmission';

import { PracticeSubmissionListInput } from '@/utils/interface/teacher/practiceSubmission';
import { changePracticeSubmissionStatusGql } from '../mutations/practiceSubmission';

export async function practiceSubmissionList(params: PracticeSubmissionListInput) {
  const res = await graphqlApiClients.TEACHER.query(practiceSubmissionsGql, {
    input: params.input,
    query: params.query
  });

  return res.data;
}

export async function practiceSubmissionDetail(id: string) {
  const res = await graphqlApiClients.TEACHER.query(practiceSubmissionGql, { id });

  return res.data;
}

export async function practiceSubmissionChangeStatus(id: string, status: string) {
  const res = await graphqlApiClients.TEACHER.query(changePracticeSubmissionStatusGql, { id, status });

  return res.data;
}
