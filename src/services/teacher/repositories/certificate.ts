import type { AxiosResponse } from 'axios';

import { graphqlApiClients } from '@/utils/api-clients';

import { courseCertificateSettingsGraphql } from '../mutations/certificate';
import { CertificateFormInterface } from '@/utils/interface/teacher/certificate';

export async function courseCertificateSetting(
  courseId: string,
  input: CertificateFormInterface
): Promise<AxiosResponse> {
  const res = await graphqlApiClients.TEACHER.query(courseCertificateSettingsGraphql, {
    courseId: courseId,
    input: input
  });

  return res.data;
}
