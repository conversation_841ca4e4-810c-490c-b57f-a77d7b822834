import { graphqlApiClients } from '@/utils/api-clients';

import { CourseSectionsGql, CourseSectionsWithPracticesGql } from '@/services/teacher/resolvers/courseSection';
import {
  createCourseSectionGql,
  deleteCourseSectionGql,
  updateCourseSectionGql,
  courseSectionSwapPositionGql
} from '@/services/teacher/mutations/courseSection';

import { CourseSectionInputInterface } from '@/utils/interface/teacher/courseSection';

export async function teacherCourseSectionList(courseId: number | string) {
  const res = await graphqlApiClients.TEACHER.query(CourseSectionsGql, { courseId });

  return res.data.courseSections;
}

export async function courseSectionsWithPracticesList(courseSlug: string) {
  const res = await graphqlApiClients.TEACHER.query(CourseSectionsWithPracticesGql, { courseSlug });

  return res.data.courseSectionsWithPractices;
}

export async function courseSectionDetail(id: string, courseID: string) {
  return await graphqlApiClients.TEACHER.query(CourseSectionsGql, {
    id,
    courseID
  });
}

export async function courseSectionCreate(courseId: string, input: CourseSectionInputInterface) {
  return await graphqlApiClients.TEACHER.query(
    createCourseSectionGql,
    {
      courseId,
      input
    },
    {
      toast: true
    }
  );
}

export async function courseSectionUpdate(id: string, courseId: string, input: CourseSectionInputInterface) {
  return await graphqlApiClients.TEACHER.query(
    updateCourseSectionGql,
    {
      id,
      courseId,
      input
    },
    {
      toast: true
    }
  );
}

export async function courseSectionDelete(id: string, courseID: string) {
  return await graphqlApiClients.TEACHER.query(
    deleteCourseSectionGql,
    {
      id,
      courseID
    },
    {
      toast: true
    }
  );
}

export async function courseSectionSwapPosition(courseId: string, courseSectionId: string, newIndex: number) {
  return await graphqlApiClients.TEACHER.query(
    courseSectionSwapPositionGql,
    {
      courseId,
      courseSectionId,
      newIndex
    },
    {
      toast: true
    }
  );
}
