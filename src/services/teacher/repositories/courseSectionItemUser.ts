import { graphqlApiClients } from '@/utils/api-clients';
import { courseSectionItemUsersWithPracticesGql } from '../resolvers/courseSectionItemUser';
import { courseSectionItemUsersParamsInterface } from '@/utils/interface/teacher/courseSectionItemUser';

export async function courseSectionItemUsersWithPractices(params: courseSectionItemUsersParamsInterface) {
  const res = await graphqlApiClients.TEACHER.query(courseSectionItemUsersWithPracticesGql, {
    courseSlug: params.courseSlug,
    sectionItemSLug: params.sectionItemSLug,
    input: params.input,
    query: params.query
  });

  return res.data;
}
