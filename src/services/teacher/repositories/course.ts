import type { AxiosResponse } from 'axios';

import { graphqlApiClients } from '@/utils/api-clients';

import { coursesGql, courseGql, courseUsersGql } from '../resolvers/course';
import {
  courseCreateGql,
  courseSubmitGql,
  courseUpdateGql,
  coursesDeleteGql,
  teacherCoursePublicGql,
  teacherInviteUserGql
} from '../mutations/course';

import {
  CourseFormInterface,
  CourseInputInterface,
  CourseListParamsInterface,
  CourseResponseInterface,
  CourseUpdateResponseInterface,
  CourseUserListParams,
  InviteUserParams
} from '@/utils/interface/teacher/course';

export async function courseList(params: CourseListParamsInterface) {
  const res = await graphqlApiClients.TEACHER.query(coursesGql, {
    input: params.input,
    query: params.query
  });

  return res.data;
}

export function showCourse(slug: string): Promise<AxiosResponse<CourseResponseInterface>> {
  return graphqlApiClients.TEACHER.query(courseGql, { slug });
}

export async function courseUserList(params: CourseUserListParams) {
  const res = await graphqlApiClients.TEACHER.query(courseUsersGql, {
    courseId: params.courseId,
    input: params.input,
    query: params.query
  });

  return res.data;
}

export async function courseCreate(input: CourseFormInterface) {
  const res = await graphqlApiClients.TEACHER.query(courseCreateGql, {
    input: input
  });

  return res.data;
}

export async function courseUpdate(
  id: string,
  input: CourseInputInterface,
  toast = false
): Promise<AxiosResponse<CourseUpdateResponseInterface>> {
  return graphqlApiClients.TEACHER.query(
    courseUpdateGql,
    {
      id: id,
      input: input
    },
    {
      toast: toast
    }
  );
}

export async function courseDelete(id: string | number) {
  {
    const res = await graphqlApiClients.TEACHER.query(coursesDeleteGql, {
      id: id
    });

    return res.data;
  }
}

export async function courseSubmit(id: string) {
  const res = await graphqlApiClients.TEACHER.query(courseSubmitGql, {
    id
  });
  return res.data;
}

export async function inviteUser(params: InviteUserParams) {
  const res = await graphqlApiClients.TEACHER.query(teacherInviteUserGql, {
    courseId: params.courseId,
    phoneNumber: params.phoneNumber,
    coursePackageID: params.coursePackageID
  });

  return res.data;
}

export async function coursePublic(id: string) {
  const res = await graphqlApiClients.TEACHER.query(teacherCoursePublicGql, {
    id
  });
  return res.data;
}
