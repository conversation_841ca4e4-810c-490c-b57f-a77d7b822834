import { graphqlApiClients } from '@/utils/api-clients';
import { selfInfoGql } from '../resolvers/self';
import { setupInfoGql, selfVideoDeleteGql, selfUpdateInfoGql } from '../mutations/self';
import { BasicFormInterface } from '@/utils/interface/teacher/setup';

export async function selfInfo() {
  const res = await graphqlApiClients.TEACHER.query(selfInfoGql);

  return res.data;
}

export async function setupInfo(input: BasicFormInterface) {
  const res = await graphqlApiClients.TEACHER.query(setupInfoGql, {
    input
  });

  return res.data;
}

export async function selfVideoDelete(videoId: string) {
  const res = await graphqlApiClients.TEACHER.query(selfVideoDeleteGql, {
    videoId
  });
  return res.data;
}

export async function selfUpdateInfo(input: BasicFormInterface) {
  const res = await graphqlApiClients.TEACHER.query(selfUpdateInfoGql, { input });

  return res.data;
}
