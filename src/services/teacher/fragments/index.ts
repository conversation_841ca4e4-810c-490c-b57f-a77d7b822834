import gql from 'graphql-tag';

export const courseBaseFragment = gql`
  fragment teacherCourseBase on CoursePayload {
    id
    teacherId
    title
    slug
    description
    salePrice
    price
    status
    statusI18n
    instructionalLevel
    instructionalLevelI18n
    bonusPoint
    bonusPointPercent
    sectionCount
    sectionItemCount
    joinedUserCount
    isPublic
    banner
    createdAt
    updatedAt
  }
`;

export const courseSectionBaseFragment = gql`
  fragment courseSectionBase on CourseSectionPayload {
    id
    courseId
    title
    position
    createdAt
    updatedAt
  }
`;

export const courseSectionItemBaseFragment = gql`
  fragment courseSectionItemBase on CourseSectionItemPayload {
    id
    courseSectionId
    title
    slug
    position
    type
    content
    createdAt
    updatedAt
  }
`;

export const teacherStudentFragment = gql`
  fragment studentFields on UserPayload {
    id
    name
    active
    phoneNumber
    imageUrl
    gender
    birthDate
    createdAt
    updatedAt
  }
`;

export const teacherDrillBaseFragment = gql`
  fragment teacherDrillBaseFields on Drill {
    id
    title
    slug
    description
    level
    levelI18n
    salePrice
    ownerID
    ownerType
    isMaster
    censor
    price
    step
    status
    statusI18n
    censor
    censorI18n
  }
`;
