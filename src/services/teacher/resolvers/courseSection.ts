import gql from 'graphql-tag';
import { courseSectionItemBaseFragment } from '../fragments';

export const CourseSectionsGql = gql`
  ${courseSectionItemBaseFragment}
  query CourseSections($courseId: ID!) {
    courseSections(courseId: $courseId) {
      id
      title
      courseId
      position
      courseSectionItems {
        ...courseSectionItemBase
      }
    }
  }
`;

export const CourseSectionsWithPracticesGql = gql`
  ${courseSectionItemBaseFragment}
  query CourseSectionsWithPractices($courseSlug: String!) {
    courseSectionsWithPractices(courseSlug: $courseSlug) {
      id
      title
      courseId
      position
      courseSectionItems {
        ...courseSectionItemBase
        submittedPracticeCount
      }
    }
  }
`;

export const TeacherCourseSectionGql = gql`
  ${courseSectionItemBaseFragment}
  query courseSection($id: ID!, $courseID: ID!) {
    courseSection(id: $id, courseID: $courseID) {
      id
      title
      courseId
      position
      courseSectionItems {
        ...courseSectionItemBase
        updatedAt
      }
    }
  }
`;
