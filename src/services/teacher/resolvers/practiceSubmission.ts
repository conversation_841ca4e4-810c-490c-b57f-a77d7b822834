import gql from 'graphql-tag';
import {
  metadataFragment,
  practiceSubmissionCommonFragment,
  commentCommonFragment
} from '@/services/base/fragments/shared';

export const practiceSubmissionsGql = gql`
  ${metadataFragment}
  ${practiceSubmissionCommonFragment}

  query PracticeSubmissions($input: PagyInput, $query: PracticeSubmissionsQueryInput) {
    practiceSubmissions(input: $input, query: $query) {
      collection {
        ...commonPracticeSubmission

        videos {
          id
          status
          title
          thumbnailURL
          isPlayable
          videoPlatforms {
            status
          }
        }
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;

export const practiceSubmissionGql = gql`
  ${practiceSubmissionCommonFragment}
  ${commentCommonFragment}

  query PracticeSubmission($id: ID!) {
    practiceSubmission(id: $id) {
      ...commonPracticeSubmission

      comments {
        ...commonComment

        authorUser {
          id
          name
          gender
          birthDate
          imageUrl
          createdAt
          updatedAt
        }
        authorTeacher {
          id
          name
          award
          address
          basicEntered
          phoneNumber
          contactEmail
          description
          imageUrl
          createdAt
          updatedAt
        }
      }
      videos {
        id
        status
        title
        thumbnailURL
        isPlayable
        videoPlatforms {
          status
        }
      }
    }
  }
`;
