import gql from 'graphql-tag';
import { metadataFragment } from '@/services/base/fragments/shared';
import { teacherDrillBaseFragment } from '../fragments';

export const drillsGql = gql`
  ${metadataFragment}
  ${teacherDrillBaseFragment}
  query Drills($input: PagyInput, $query: DrillQueryInput) {
    drills(input: $input, query: $query) {
      collection {
        ...teacherDrillBaseFields
        skills {
          id
          name
          nameI18n
          description
        }
        diagrams {
          id
          imageUrl
        }
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;

export const drillGql = gql`
  ${teacherDrillBaseFragment}
  query Drill($slug: String!) {
    drill(slug: $slug) {
      ...teacherDrillBaseFields
      skills {
        id
        name
        nameI18n
        description
      }
      diagrams {
        id
        parentID
        parentType
        imageUrl
        setting
        position
      }
      videos {
        id
        title
        thumbnailURL
        isPlayable
        videoPlatforms {
          status
        }
      }
      censorHistories {
        id
        parentID
        parentType
        status
        statusI18n
        createdBy
        createdAt
        feedback
      }
    }
  }
`;
