import gql from 'graphql-tag';
import { metadataFragment, practiceSubmissionCommonFragment } from '@/services/base/fragments/shared';

export const courseSectionItemUsersWithPracticesGql = gql`
  ${metadataFragment}
  ${practiceSubmissionCommonFragment}

  query SectionItemUsersWithPractices(
    $courseSlug: String!
    $sectionItemSLug: String!
    $input: PagyInput
    $query: SectionItemUsersWithPracticesQueryInput
  ) {
    sectionItemUsersWithPractices(
      courseSlug: $courseSlug
      sectionItemSLug: $sectionItemSLug
      input: $input
      query: $query
    ) {
      collection {
        id
        name
        phoneNumber
        imageUrl
        gender
        birthDate
        createdAt
        updatedAt
        LatestSubmissionTime
        LatestSubmissionStatus
        LatestSubmissionStatusI18n
        practiceSubmissions {
          ...commonPracticeSubmission
        }
      }
      Stats {
        TotalEnrolledUsers
        TotalSubmittedUsers
        TotalViewedSectionUsers
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;
