import gql from 'graphql-tag';
import { metadataFragment } from '@/services/base/fragments/shared';
import { teacherStudentFragment } from '../fragments';

export const teacherUsersGql = gql`
  query Users($input: PagyInput, $query: UsersQueryInput) {
    users(input: $input, query: $query) {
      collection {
        ...studentFields
        active
      }
      metadata {
        ...commonMetadata
      }
    }
  }

  ${metadataFragment}
  ${teacherStudentFragment}
`;
