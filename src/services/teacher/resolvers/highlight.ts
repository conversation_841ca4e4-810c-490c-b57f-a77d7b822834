import gql from 'graphql-tag';

export const highlightsGql = gql`
  query Highlights($input: PagyInput, $query: HighlightQueryInput) {
    highlights(input: $input, query: $query) {
      collection {
        id
        title
        description
        iconId
        icon {
          id
          key
          class
        }
      }
      metadata {
        total
        perPage
        page
        pages
        count
        next
        prev
        from
        to
      }
    }
  }
`;

export const highlightGql = gql`
  query Highlight($id: ID!) {
    highlight(id: $id) {
      id
      title
      description
      iconId
    }
  }
`;
