import gql from 'graphql-tag';

export const courseSectionItemsSql = gql`
  query courseSectionItems($courseId: ID!, $courseSectionId: ID!) {
    courseSectionItems(courseId: $courseId, courseSectionId: $courseSectionId) {
      id
      courseSectionId
      title
      content
      isFree
      type
      createdAt
      updatedAt
    }
  }
`;

export const courseSectionItemSql = gql`
  query courseSectionItem($id: ID!, $courseId: ID!, $courseSectionId: ID!) {
    courseSectionItem(id: $id, courseId: $courseId, courseSectionId: $courseSectionId) {
      id
      courseSectionId
      title
      slug
      type
      isFree
      drills {
        id
        title
        slug
        description
        level
        levelI18n
        salePrice
        price
        status
        step
        skills {
          id
          name
          nameI18n
          description
        }
        diagrams {
          id
          imageUrl
        }
      }
      videos {
        id
        status
        title
        thumbnailURL
        isPlayable
        videoPlatforms {
          status
        }
      }
      content
      createdAt
      updatedAt
    }
  }
`;

export const courseSectionItemDeleteVideoGql = gql`
  mutation CourseSectionItemDeleteVideo($videoId: ID!, $courseId: ID!) {
    courseSectionItemDeleteVideo(videoId: $videoId, courseId: $courseId) {
      message
    }
  }
`;
