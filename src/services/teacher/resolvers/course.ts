import gql from 'graphql-tag';
import { metadataFragment } from '@/services/base/fragments/shared';
import { courseBaseFragment, teacherStudentFragment } from '../fragments';

export const coursesGql = gql`
  ${metadataFragment}
  ${courseBaseFragment}
  query ($input: PagyInput, $query: CourseQueryInput) {
    courses(input: $input, query: $query) {
      collection {
        ...teacherCourseBase
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;

export const courseGql = gql`
  ${courseBaseFragment}
  query Course($slug: String!) {
    course(slug: $slug) {
      ...teacherCourseBase
      sectionCount
      sectionItemCount
      courseSections {
        id
        title
        courseId
        position
        courseSectionItems {
          id
          title
          content
          isFree
          slug
          type
        }
      }
      courseCensorHistories {
        id
        createdBy
        feedback
        status
        statusI18n
        createdAt
        updatedAt
      }
      coursePackages {
        id
        price
        salePrice
        packageDealId
        packageDeal {
          id
          name
          description
          approvalSubmissionRequired
        }
      }
      courseHighlights {
        id
        highlight {
          id
          icon {
            class
          }
          title
          description
        }
      }
      courseSetting {
        lockingItem
      }
      certificate {
        title
        isActive
        description
      }
    }
  }
`;

export const courseUsersGql = gql`
  ${metadataFragment}
  ${teacherStudentFragment}
  query CourseUsers($courseId: ID!, $input: PagyInput) {
    courseUsers(courseId: $courseId, input: $input) {
      collection {
        id
        courseId
        userId
        createdAt
        joinedAt
        updatedAt
        user {
          ...studentFields
        }
        courseUserMetadata {
          completedSectionCount
          completedSectionItemCount
        }
      }
      metadata {
        ...commonMetadata
      }
    }
  }
`;
