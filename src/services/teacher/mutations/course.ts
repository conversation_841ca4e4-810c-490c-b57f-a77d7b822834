import gql from 'graphql-tag';
import { courseBaseFragment } from '../fragments';

export const courseCreateGql = gql`
  mutation CourseCreate($input: CourseCreateInput!) {
    courseCreate(input: $input) {
      message
      course {
        id
        title
        slug
        description
        salePrice
        price
        bonusPoint
        bonusPointPercent
      }
    }
  }
`;
export const courseUpdateGql = gql`
  ${courseBaseFragment}
  mutation CourseUpdate($id: ID!, $input: CourseUpdateInput!) {
    courseUpdate(id: $id, input: $input) {
      message
      course {
        ...teacherCourseBase
      }
    }
  }
`;
export const coursesDeleteGql = gql`
  mutation CourseDelete($id: ID!) {
    courseDelete(id: $id) {
      message
    }
  }
`;

export const courseSubmitGql = gql`
  mutation CourseSubmit($id: ID!) {
    courseSubmit(id: $id) {
      message
    }
  }
`;

export const teacherInviteUserGql = gql`
  mutation InviteUser($courseId: ID!, $phoneNumber: String, $coursePackageID: ID) {
    inviteUser(courseId: $courseId, phoneNumber: $phoneNumber, coursePackageID: $coursePackageID) {
      message
    }
  }
`;

export const teacherCoursePublicGql = gql`
  mutation CoursePublic($id: ID!) {
    coursePublic(id: $id) {
      message
    }
  }
`;
