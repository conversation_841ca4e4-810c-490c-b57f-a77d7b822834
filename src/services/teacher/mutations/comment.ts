import gql from 'graphql-tag';
import { commentCommonFragment } from '@/services/base/fragments/shared';

export const commentCreateGql = gql`
  ${commentCommonFragment}

  mutation CommentCreate($input: CommentCreateInput!) {
    commentCreate(input: $input) {
      message
      comment {
        ...commonComment

        authorTeacher {
          id
          name
          award
          address
          basicEntered
          phoneNumber
          contactEmail
          description
          imageUrl
          createdAt
          updatedAt
        }
      }
    }
  }
`;
