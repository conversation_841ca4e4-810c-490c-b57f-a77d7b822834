import gql from 'graphql-tag';

export const createCourseSectionGql = gql`
  mutation createCourseSection($courseId: ID!, $input: CourseSectionInput!) {
    courseSectionCreate(courseId: $courseId, input: $input) {
      message
      courseSection {
        id
        title
        courseId
        position
      }
    }
  }
`;

export const updateCourseSectionGql = gql`
  mutation CourseSectionUpdate($id: ID!, $courseId: ID!, $input: CourseSectionInput!) {
    courseSectionUpdate(id: $id, courseId: $courseId, input: $input) {
      message
      courseSection {
        id
        title
        courseId
        position
      }
    }
  }
`;

export const deleteCourseSectionGql = gql`
  mutation CourseSectionDelete($id: ID!, $courseID: ID!) {
    courseSectionDelete(id: $id, courseID: $courseID) {
      message
    }
  }
`;

export const courseSectionSwapPositionGql = gql`
  mutation CourseSectionSwapPosition($courseId: ID!, $courseSectionId: ID!, $newIndex: Int!) {
    courseSectionSwapPosition(courseId: $courseId, courseSectionId: $courseSectionId, newIndex: $newIndex) {
      message
    }
  }
`;
