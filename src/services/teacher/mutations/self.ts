import gql from 'graphql-tag';

export const setupInfoGql = gql`
  mutation SetupInfo($input: BasicSetupInput!) {
    setupInfo(input: $input) {
      message
    }
  }
`;

export const selfVideoDeleteGql = gql`
  mutation SelfVideoDelete($videoId: ID!) {
    selfVideoDelete(videoId: $videoId) {
      message
    }
  }
`;

export const selfUpdateInfoGql = gql`
  mutation UpdateSelfInfo($input: BasicSetupInput!) {
    updateSelfInfo(input: $input) {
      message
    }
  }
`;
