import gql from 'graphql-tag';

export const drillDestroyGql = gql`
  mutation DrillDelete($id: ID!) {
    drillDelete(id: $id) {
      message
    }
  }
`;

export const drillCreateGql = gql`
  mutation DrillCreate($input: DrillInput!) {
    drillCreate(input: $input) {
      message
      drill {
        id
        slug
      }
    }
  }
`;

export const drillUpdateGql = gql`
  mutation DrillUpdate($id: ID!, $input: DrillInput!) {
    drillUpdate(id: $id, input: $input) {
      message
      drill {
        id
        title
        description
        level
        salePrice
        price
        step
        slug
        skills {
          id
          name
          description
        }
        diagrams {
          id
          parentID
          parentType
          imageUrl
          setting
          position
        }
      }
    }
  }
`;

export const drillPublishGql = gql`
  mutation DrillPublish($id: ID!, $status: String!) {
    drillPublish(id: $id, status: $status) {
      message
    }
  }
`;

export const saveAndUploadDrillVideoGql = gql`
  mutation SaveAndUploadDrillVideo($videoId: ID!, $drillId: ID!) {
    saveAndUploadDrillVideo(videoId: $videoId, drillId: $drillId) {
      message
    }
  }
`;

export const submitDrillGql = gql`
  mutation DrillSubmit($id: ID!) {
    drillSubmit(id: $id) {
      message
    }
  }
`;
