import gql from 'graphql-tag';

export const highlightCreateGql = gql`
  mutation HighlightCreate($input: HighlightInput!) {
    highlightCreate(input: $input) {
      message
      highlight {
        id
        title
        description
        iconId
      }
    }
  }
`;

export const highlightUpdateGql = gql`
  mutation HighlightUpdate($id: ID!, $input: HighlightInput!) {
    highlightUpdate(id: $id, input: $input) {
      message
      highlight {
        id
        title
        description
        iconId
      }
    }
  }
`;
