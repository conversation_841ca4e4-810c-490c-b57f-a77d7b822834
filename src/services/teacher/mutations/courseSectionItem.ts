import gql from 'graphql-tag';

export const createCourseSectionItemGql = gql`
  mutation createCourseSectionItem($courseId: ID!, $courseSectionId: ID!, $input: CourseSectionItemInput!) {
    courseSectionItemCreate(courseId: $courseId, courseSectionId: $courseSectionId, input: $input) {
      message
      sectionItem {
        id
        courseSectionId
        title
        content
        isFree
        type
        createdAt
        updatedAt
      }
    }
  }
`;

export const updateCourseSectionItemGql = gql`
  mutation updateCourseSectionItem($id: ID!, $courseId: ID!, $courseSectionId: ID!, $input: CourseSectionItemInput!) {
    courseSectionItemUpdate(id: $id, courseId: $courseId, courseSectionId: $courseSectionId, input: $input) {
      message
      sectionItem {
        id
        courseSectionId
        title
        content
        isFree
        type
        createdAt
        updatedAt
      }
    }
  }
`;

export const deleteCourseSectionItemGql = gql`
  mutation deleteCourseSectionItem($id: ID!, $courseId: ID!, $courseSectionId: ID!) {
    courseSectionItemDestroy(id: $id, courseId: $courseId, courseSectionId: $courseSectionId) {
      message
    }
  }
`;

export const courseSectionItemAddDrillGql = gql`
  mutation courseSectionItemAddDrill($itemId: ID!, $courseId: ID!, $courseSectionId: ID!, $drillId: ID!) {
    courseSectionItemAddDrill(
      itemId: $itemId
      courseId: $courseId
      courseSectionId: $courseSectionId
      drillId: $drillId
    ) {
      message
    }
  }
`;

export const courseSectionItemRemoveDrillGql = gql`
  mutation courseSectionItemRemoveDrill($itemId: ID!, $courseId: ID!, $courseSectionId: ID!, $drillId: ID!) {
    courseSectionItemRemoveDrill(
      itemId: $itemId
      courseId: $courseId
      courseSectionId: $courseSectionId
      drillId: $drillId
    ) {
      message
    }
  }
`;
export const courseSectionItemAddVideoGql = gql`
  mutation CourseSectionItemAddVideo($itemId: ID!, $courseId: ID!, $videoId: ID!) {
    courseSectionItemAddVideo(itemId: $itemId, courseId: $courseId, videoId: $videoId) {
      message
    }
  }
`;

export const courseSectionItemSwapPositionGql = gql`
  mutation CourseSectionItemSwapPosition(
    $courseId: ID!
    $courseSectionId: ID!
    $courseSectionItemId: ID!
    $newIndex: Int!
  ) {
    courseSectionItemSwapPosition(
      courseId: $courseId
      courseSectionId: $courseSectionId
      courseSectionItemId: $courseSectionItemId
      newIndex: $newIndex
    ) {
      message
    }
  }
`;
