export const useLayoutStore = defineStore('layout', () => {
  const layoutType = ref<string>('horizontal');
  const layoutWidth = ref<string>('fluid');
  const leftSidebarType = ref<string>('light');
  const loader = ref<boolean>(false);
  const mode = ref<string>('light');
  const topbar = ref<string>('colored');

  function changeLayoutType(value: string) {
    layoutType.value = value;
  }
  function changeLayoutWidth(layoutWidthValue: string) {
    layoutWidth.value = layoutWidthValue;
  }
  function changeLeftSidebarType(leftSidebarTypeValue: string) {
    leftSidebarType.value = leftSidebarTypeValue;
  }
  function changeTopbar(topBarValue: string) {
    topbar.value = topBarValue;
  }
  function changeLoaderValue(loaderValue: boolean) {
    loader.value = loaderValue;
  }
  function changeMode(modeValue: string) {
    mode.value = modeValue;
  }

  return {
    layoutType,
    layoutWidth,
    leftSidebarType,
    loader,
    mode,
    topbar,
    changeLayoutType,
    changeLayoutWidth,
    changeLeftSidebarType,
    changeLoaderValue,
    changeMode,
    changeTopbar
  };
});
