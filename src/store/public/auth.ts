import { signIn, signOut, signUp, refreshToken } from '@/services/public';
import { SignInInputInterface, SignUpInputInterface } from '@/utils/interface/user/auth';
import { ROUTE_PATH } from '@/utils/constant';
import { useLocalStorage } from '@vueuse/core';
import { useUserAuthStore } from '@/store/user/auth';

export const useAuthPublicStore = defineStore('publicAuth', () => {
  const router = useRouter();
  const accessToken = ref(useLocalStorage('accessToken', ''));
  const userRefreshToken = ref(useLocalStorage('userRefreshToken', ''));
  const userAuthStore = useUserAuthStore();

  async function login(input: SignInInputInterface) {
    let errors: object[] = [];

    await signIn(input)
      .then(res => {
        const data = res.data.signIn;

        setToken(data.accessToken);
        setRefreshToken(data.refreshToken);

        if (!data.profile.user) {
          router.push(ROUTE_PATH.TEACHER_COURSES);
          return;
        }

        const pendingAccessData = sessionStorage.getItem('pendingAccess');
        if (pendingAccessData) {
          const pendingAccess = JSON.parse(pendingAccessData);

          if (pendingAccess && pendingAccess.type === 'course_invitation') {
            sessionStorage.removeItem('pendingAccess');
            router.push(pendingAccess.returnUrl);

            return;
          }
        } else {
          router.push(ROUTE_PATH.HOMEPAGE);
        }
      })
      .catch(err => {
        errors = err.errors;
      });

    return errors;
  }

  function logout() {
    userAuthStore.clearProfile();
    if (!accessToken.value) {
      clearRefreshToken();
      router.push(ROUTE_PATH.LOGIN);
      return;
    }

    signOut('user')
      .then(() => {
        clearTokens();
        router.push(ROUTE_PATH.LOGIN);
      })
      .catch(err => {
        console.error('sign out error:', err);
      });
  }

  async function handleRefreshToken() {
    const res = await refreshToken(userRefreshToken.value);

    setToken(res.data.refreshToken.accessToken);
    setRefreshToken(res.data.refreshToken.refreshToken);

    return res;
  }

  function setToken(tokenValue: string) {
    accessToken.value = tokenValue;
  }

  function clearAccessToken() {
    accessToken.value = '';
  }

  function setRefreshToken(tokenValue: string) {
    userRefreshToken.value = tokenValue;
  }

  function clearRefreshToken() {
    userRefreshToken.value = '';
  }

  function clearTokens() {
    clearAccessToken();
    clearRefreshToken();
  }

  async function register(input: SignUpInputInterface) {
    await signUp(input)
      .then(() => {
        router.push({
          path: '/register-verify',
          query: { phoneNumber: input.phoneNumber }
        });
      })
      .catch(err => {
        console.error('sign up error:', err);
      });
  }

  return { login, logout, register, handleRefreshToken, clearTokens, accessToken };
});
