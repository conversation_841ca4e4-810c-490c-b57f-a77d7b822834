import { signIn } from '@/services/admin';
import { signOut, refreshToken } from '@/services/public';
import { SignInInputInterface } from '@/utils/interface/user/auth';
import { ROUTE_PATH } from '@/utils/constant';
import { useLocalStorage } from '@vueuse/core';

export const useAuthAdminStore = defineStore('adminAuth', () => {
  const router = useRouter();
  const adminAccessToken = ref(useLocalStorage('adminAccessToken', ''));
  const adminRefreshToken = ref(useLocalStorage('adminRefreshToken', ''));

  async function login(input: SignInInputInterface) {
    let errors: object[] = [];

    await signIn(input)
      .then(res => {
        setToken(res.data.signIn.accessToken);
        setRefreshToken(res.data.signIn.refreshToken);
        router.push(ROUTE_PATH.ADMIN_TEACHERS);
      })
      .catch(err => {
        errors = err.errors;
      });

    return errors;
  }

  function logout() {
    if (!adminAccessToken.value) {
      clearRefreshToken();
      router.push(ROUTE_PATH.ADMIN_SIGNIN);
      return;
    }

    signOut('admin')
      .then(() => {
        clearTokens();
        router.push(ROUTE_PATH.ADMIN_SIGNIN);
      })
      .catch(err => {
        console.error('sign out error:', err);
      });
  }

  async function handleRefreshToken() {
    const res = await refreshToken(adminRefreshToken.value);

    setToken(res.data.refreshToken.accessToken);
    setRefreshToken(res.data.refreshToken.refreshToken);

    return res;
  }

  function setToken(tokenValue: string) {
    adminAccessToken.value = tokenValue;
  }

  function clearAccessToken() {
    adminAccessToken.value = '';
  }

  function setRefreshToken(tokenValue: string) {
    adminRefreshToken.value = tokenValue;
  }

  function clearRefreshToken() {
    adminRefreshToken.value = '';
  }

  function clearTokens() {
    clearAccessToken();
    clearRefreshToken();
  }

  return { login, logout, handleRefreshToken, clearTokens, adminAccessToken };
});
