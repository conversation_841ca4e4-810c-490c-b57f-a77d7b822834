import { useLocalStorage } from '@vueuse/core';
import i18n from '@/plugin/i18n';
import { LANGUAGES } from '@/utils/constant';

export const useGlobalStore = defineStore('global', () => {
  const errors = reactive({}) as any;
  const language = ref(useLocalStorage('language', LANGUAGES.VI));

  // TODO: Apply Client Switch Language
  function setLanguage(lang = language.value) {
    i18n.global.locale.value = lang;
    language.value = lang;
    document.cookie = `language=${lang}; path=/; max-age=31536000`;
    document.documentElement.lang = lang;
  }

  function setErrors(data: any) {
    errors.value = data;
  }

  function getErrors(key: string) {
    return get(errors.value, key, []);
  }

  return {
    errors,
    language,
    getErrors,
    setErrors,
    setLanguage
  };
});
