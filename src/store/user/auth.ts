import { useLocalStorage, StorageSerializers } from '@vueuse/core';
import { selfInfo } from '@/services/user';
import { UserInterface } from '@/utils/interface/user/user';

export const useUserAuthStore = defineStore('userAuth', () => {
  const userProfile = ref(
    useLocalStorage<UserInterface | null>('userProfile', null, { serializer: StorageSerializers.object })
  );

  function getProfile() {
    selfInfo().then(res => {
      userProfile.value = res.selfInfo;
    });
  }

  function clearProfile() {
    userProfile.value = null;
  }

  return {
    userProfile,
    getProfile,
    clearProfile
  };
});
