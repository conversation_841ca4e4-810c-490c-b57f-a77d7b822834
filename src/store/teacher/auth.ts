import { useLocalStorage, StorageSerializers } from '@vueuse/core';
import { selfInfo } from '@/services/teacher';
import { TeacherInterface } from '@/utils/interface/teacher/teacher';

export const useTeacherAuthStore = defineStore('teacherAuth', () => {
  const teacherProfile = ref(
    useLocalStorage<TeacherInterface | null>('teacherProfile', null, { serializer: StorageSerializers.object })
  );

  function getProfile(): Promise<TeacherInterface> {
    return selfInfo()
      .then(res => {
        teacherProfile.value = res.selfInfo;
        return res.selfInfo;
      })
      .catch(error => {
        console.error('Failed to fetch teacher profile:', error);
        throw error;
      });
  }

  return {
    teacherProfile,
    getProfile
  };
});
