<template>
  <Loader :loading="loading">
    <div class="table-responsive">
      <TableList
        :fields="fields"
        :items="users"
        :metadata="metadata"
        hover
        :tbody-tr-class="rowClass"
        @page-change="changePage"
      >
        <template #cell(imageUrl)="{ item }">
          <BAvatar :src="item.imageUrl ? item.imageUrl : dummyAvatar" :alt="item.name"></BAvatar>
        </template>

        <template #cell(name)="{ item }">
          <div class="d-flex flex-column">
            <span>{{ item.name }}</span>
            <div v-if="!item.active" class="d-flex align-items-center text-danger gap-1 mt-1">
              <i class="mdi" :class="item.active ? 'mdi-checkbox-marked-circle-outline' : 'mdi-alert-outline'"></i>
              {{ $t('teacher.user.form.status.description') }}
            </div>
          </div>
        </template>

        <template #cell(gender)="{ item }"> {{ genderMapping[item.gender] }} </template>

        <template #cell(createdAt)="{ item }">
          <span>{{ filters.formatDate(item.createdAt) }}</span>
        </template>

        <template #cell(active)="{ item }">
          <span
            class="badge rounded-pill font-size-12 gap-1"
            :class="item.active ? 'badge-soft-success' : 'badge-soft-danger'"
          >
            <i class="mdi" :class="item.active ? 'mdi-checkbox-marked-circle-outline' : 'mdi-alert-outline'"></i>
            {{ item.active ? $t('teacher.user.form.status.active') : $t('teacher.user.form.status.de_active') }}
          </span>
        </template>
      </TableList>
    </div>
  </Loader>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';
  import filters from '@/utils/filters';

  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';

  import { MetaDataInterface } from '@/utils/interface/common';
  import { UserInterface } from '@/utils/interface/teacher/user';

  const emit = defineEmits(['changePage']);

  defineProps({
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    },
    users: {
      type: Array as PropType<UserInterface[]>,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  });

  const genderMapping: Record<string, string> = {
    Male: 'Nam',
    Female: 'Nữ'
  };

  const fields = ref<{ key: string; label: string; class?: string }[]>([
    { key: 'imageUrl', label: '' },
    { key: 'name', label: i18n.global.t('teacher.user.form.name') },
    { key: 'gender', label: i18n.global.t('teacher.user.form.gender') },
    { key: 'birthDate', label: i18n.global.t('teacher.user.form.birth_date') },
    { key: 'phoneNumber', label: i18n.global.t('teacher.user.form.phone') },
    { key: 'createdAt', label: i18n.global.t('teacher.user.form.joined_at') },
    { key: 'active', label: i18n.global.t('teacher.user.form.status.label') }
  ]);

  const rowClass = (item: UserInterface, type: string) => {
    return type === 'row' && item.active ? '' : 'de-active-row';
  };

  const changePage = (e: { page: number }) => {
    emit('changePage', e);
  };
</script>

<style lang="scss" scoped>
  :deep(tr.de-active-row),
  :deep(tr.de-active-row > td) {
    background-color: rgb(254 242 242) !important;
    td {
      &:not(:last-child) {
        opacity: 0.7;
      }
    }
  }
</style>
