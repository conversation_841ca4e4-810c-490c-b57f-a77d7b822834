<template>
  <div class="course-user-list">
    <div v-if="hasInvitePermission" class="d-flex justify-content-end py-2">
      <Button class="mx-2" variant="outline-primary" @click="copyCourseLink()">
        {{ $t('teacher.course.copy_course_link') }}
        <i v-if="copied" class="mdi mdi-check ms-2"></i>
        <i v-else class="mdi mdi-content-copy ms-2"></i>
      </Button>

      <Button variant="success" @click="inviteModal = true">{{ $t('common.invite') }}</Button>
    </div>

    <BCard no-body>
      <BCardHeader>
        <h4 class="card-title">{{ $t('teacher.course_user.list_title') }}</h4>
      </BCardHeader>

      <BCardBody>
        <Loader :loading="loading">
          <BTable v-if="items.length > 0" :items="items" :fields="fields">
            <template #cell(imageUrl)="{ item }">
              <img
                v-if="item.user.imageUrl"
                :src="item.user.imageUrl"
                class="rounded-circle avatar-sm"
                :alt="item.name"
              />
              <img
                v-else
                src="@/assets/images/users/user-dummy-img.png"
                class="rounded-circle avatar-sm"
                :alt="item.name"
              />
            </template>

            <template #cell(name)="{ item }">
              {{ item.user.name }}
            </template>

            <template #cell(phoneNumber)="{ item }"> {{ item.user.phoneNumber }}</template>

            <template #cell(itemCount)="{ item }">
              {{ completedCourseItemCount(item) }}
            </template>

            <template #cell(joinedAt)="{ item }">
              {{ filters.formatDatetime(item.joinedAt) }}
            </template>
          </BTable>

          <div v-else class="flex-between">
            <img src="@/assets/images/data_empty.png" alt="empty data" />
          </div>
        </Loader>
      </BCardBody>

      <Pagination :metadata="metadata" @change="changePage"></Pagination>
    </BCard>

    <BModal
      v-model="inviteModal"
      size="lg"
      :title="$t('teacher.course.confirm_invite.title')"
      title-class="font-18 fw-bold"
      lazy
      no-footer
      centered
      hide-header-close
    >
      <template #modal-header="{ close }">
        <div class="d-flex justify-content-between align-items-center w-100">
          <button type="button" class="btn-close" @click="close()"></button>
        </div>
      </template>

      <div class="confirmation-box mb-4">
        <p class="mb-0">{{ $t('teacher.course.confirm_invite.content') }}</p>
      </div>

      <BaseFormValidator :label="$t('teacher.course.basic_form.form.phone')" name="phoneNumber" required class="pb-4">
        <BFormInput
          v-model="userPhoneNumber"
          type="tel"
          class="form-control"
          id="phoneNumber"
          name="phoneNumber"
          :placeholder="$t('teacher.setup.form.phone_number_placeholder')"
        />
      </BaseFormValidator>

      <div class="mb-4">
        <div class="row g-3">
          <CoursePackageCard
            v-for="cpk in courseForm.coursePackages"
            :key="cpk.packageDealId"
            :coursePackage="cpk"
            :packageName="getPackageName(cpk)"
            :packageDetail="getPackageDetail(cpk)"
            :selected="isSelected(cpk)"
            :column-size="getResponsiveColumnClass(courseForm.coursePackages?.length || 0)"
            @select-package="selectPackage"
          />
        </div>
      </div>

      <div class="d-flex justify-content-end gap-2">
        <Button variant="outline-secondary" @click="inviteModal = false">{{ $t('common.cancel') }}</Button>
        <Button variant="primary" @click="handleInvite()" :disabled="!userPhoneNumber">
          {{ $t('common.invite') }}
        </Button>
      </div>
    </BModal>
  </div>
</template>

<script lang="ts" setup>
  import { useGoList, useBreadcrumb } from '@bachdx/b-vuse';
  import { useTeacherAuthStore } from '@/store/teacher/auth';

  import filters from '@/utils/filters';
  import i18n from '@/plugin/i18n';

  import { CourseUserListQueryFormModel } from '@/forms/teacher/courseUser';

  import Button from '@/components/base/Button.vue';
  import CoursePackageCard from './CoursePackageCard.vue';

  // ===== APIs =====
  import { courseUserList } from '@/services/teacher';
  import { inviteUser } from '@/services/teacher';
  import { CourseInterface } from '@/utils/interface/teacher/course';
  import { InviteUserParams } from '@/utils/interface/teacher/course';

  import { packageDetails } from '@/utils/config-data-info';
  import { useCoursePackage } from '@/composable/useCoursePackage';

  const route = useRoute();
  const router = useRouter();
  const { setBreadcrumb } = useBreadcrumb({});

  const courseForm = defineModel('courseForm', {
    type: Object as PropType<CourseInterface>,
    required: true
  });

  const { items, metadata, changePage, parseQueryAndFetch } = useGoList({
    fetchListFnc: courseUserList,
    fetchKey: 'courseUsers',
    route: route,
    router: router,
    queryFormModels: CourseUserListQueryFormModel,
    perPage: 10,
    extraParams: {
      courseId: String(courseForm.value.id)
    }
  });

  const teacherAuthStore = useTeacherAuthStore();
  const { teacherProfile } = storeToRefs(teacherAuthStore);

  const userPhoneNumber = ref('');
  const coursePackageID = ref('');
  const loading = ref(false);
  const inviteModal = ref(false);
  const copied = ref(false);

  const hasInvitePermission = computed(() => {
    return teacherProfile.value?.canInviteStudents;
  });

  setBreadcrumb({
    title: i18n.global.t('teacher.course.title'),
    items: [
      {
        text: i18n.global.t('teacher.course.title'),
        href: '/teacher/courses'
      },
      {
        text: i18n.global.t('common.detail'),
        href: `/teacher/courses/${route.params.slug}/detail`
      },
      {
        text: i18n.global.t('teacher.course.form.steps.labels.student')
      }
    ]
  });

  const fields = [
    { key: 'imageUrl', label: '' },
    { key: 'name', label: i18n.global.t('teacher.course_user.fields.name') },
    { key: 'phoneNumber', label: i18n.global.t('teacher.course_user.fields.phone') },
    { key: 'itemCount', label: i18n.global.t('teacher.course_user.fields.item_count') },
    // { key: 'email', label: 'Email' },
    // { key: 'phoneNumber', label: 'Số điện thoại' },
    { key: 'joinedAt', label: i18n.global.t('teacher.course_user.fields.joined_at') }
  ];

  const { getPackageName, getPackageDetail, isSelected, selectPackage, getResponsiveColumnClass } = useCoursePackage(
    coursePackageID,
    packageDetails
  );

  const completedCourseItemCount = (item: any) => {
    if (!item.courseUserMetadata) return 0;
    return item.courseUserMetadata.completedSectionItemCount;
  };

  const handleInvite = async () => {
    try {
      const params: InviteUserParams = {
        courseId: String(courseForm.value.id),
        phoneNumber: userPhoneNumber.value
      };

      params.coursePackageID = String(coursePackageID.value);

      await inviteUser(params);

      inviteModal.value = false;
      userPhoneNumber.value = '';

      await parseQueryAndFetch();
    } catch (error) {
      console.error('Error inviting user:', error);
    }
  };

  const copyCourseLink = async () => {
    const url = `${window.location.origin}/courses/${courseForm.value.slug}?invited=true`;
    try {
      await navigator.clipboard.writeText(url);
      copied.value = true;
      setTimeout(() => {
        copied.value = false;
      }, 2000);
    } catch (err) {
      console.error('Error copying course link:', err);
    }
  };

  onMounted(async () => {
    await parseQueryAndFetch();

    const packages = courseForm.value.coursePackages;
    coursePackageID.value = packages.length ? String(packages[0]?.id) : '';
  });
</script>

<style scoped>
  .avatar-sm {
    width: 36px;
    height: 36px;
    object-fit: cover;
  }

  .flex-between {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .confirmation-box {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    color: #495057;
  }
</style>
