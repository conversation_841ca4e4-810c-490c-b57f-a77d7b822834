<template>
  <div :class="getColumnClass()" @click="$emit('selectPackage', coursePackage.id)">
    <div class="package-card" :class="specificPackageClass()">
      <div class="package-header">
        <div class="d-flex align-items-center">
          <i class="package-icon" :class="`${packageDetail.headerIcon.icon} ${packageDetail.headerIcon.class}`"></i>
          <span class="package-name">{{ coursePackage.packageDeal?.name }}</span>
          <span v-if="packageDetail.headerBadge" :class="packageDetail.headerBadge.class">{{
            packageDetail.headerBadge.title
          }}</span>
        </div>

        <i v-if="selected" class="mdi mdi-check-circle-outline text-success selected-icon"></i>
      </div>

      <div class="package-description">
        <p class="mb-2">{{ packageDetail?.description }}</p>
      </div>

      <PriceDisplay :price="Number(coursePackage.price)" :salePrice="Number(coursePackage.salePrice)" show-label />

      <div class="package-features" v-for="feature in packageDetail.features" :key="feature">
        <div class="feature-item">
          <i :class="`${feature.icon} ${feature.class}`"></i>
          <span>{{ feature.content }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { CoursePackageInterface } from '@/utils/interface/teacher/course';

  import PriceDisplay from '@/components/base/PriceDisplay.vue';

  const props = defineProps({
    coursePackage: {
      type: Object as PropType<CoursePackageInterface>,
      required: true
    },
    packageDetail: {
      type: Object,
      required: true
    },
    packageName: {
      type: String,
      default: ''
    },
    selected: {
      type: Boolean,
      default: false
    },
    columnSize: {
      type: String,
      default: 'col-md-6'
    }
  });

  defineEmits(['selectPackage']);

  const specificPackageClass = () => {
    let className = `${props.packageName}-package`;

    if (props.selected) className += ' selected';
    return className;
  };

  const getColumnClass = () => {
    return props.columnSize;
  };
</script>

<style scoped>
  .package-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .package-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  }

  .package-card.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
  }

  .advance-package {
    border-color: #6c5ce7;
  }

  .advance-package.selected {
    border-color: #6c5ce7;
    background-color: #f8f7ff;
    box-shadow: 0 4px 12px rgba(108, 92, 231, 0.2);
  }

  .package-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .package-icon {
    font-size: 20px;
    margin-right: 8px;
  }

  .package-name {
    font-weight: 600;
    font-size: 16px;
  }

  .popular-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
    margin-left: 8px;
  }

  .selected-icon {
    color: #28a745;
    font-size: 20px;
  }

  .package-description {
    margin-bottom: 16px;
    color: #6c757d;
    font-size: 14px;
  }

  .package-features {
    display: flex;
    flex-direction: column;
    gap: 8px;
    /* margin-top: auto; */
  }

  .feature-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #495057;
  }

  .feature-item i {
    margin-right: 8px;
    font-size: 16px;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .package-card {
      margin-bottom: 1rem;
    }
  }
</style>
