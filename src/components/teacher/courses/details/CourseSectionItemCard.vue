<template>
  <div
    class="lesson-card ps-4 pe-2 py-2 cursor-pointer"
    :class="{ active: lesson.slug == contentSlug }"
    @click="handleLesson"
  >
    <div class="d-flex align-items-center justify-content-between gap-2 ps-lg-2">
      <div class="d-flex align-items-center gap-2">
        <i class="mdi mdi-vector-square"></i>
        <h6 class="mb-0">{{ lesson.title }}</h6>
      </div>
      <BBadge v-if="lesson.submittedPracticeCount" variant="danger" class="ms-1 font-size-11">
        {{ lesson.submittedPracticeCount }}
      </BBadge>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { CourseSectionItemWithPracticeInterface } from '@/utils/interface/teacher/courseSectionItem';

  const props = defineProps({
    lesson: {
      type: Object as PropType<CourseSectionItemWithPracticeInterface>,
      required: true
    },
    index: {
      type: Number,
      required: true
    }
  });

  const route = useRoute();
  const router = useRouter();

  const courseSlug = computed(() => route.params.slug.toString());
  const lessonSlug = computed(() => props.lesson.slug?.toString());
  const contentSlug = computed(() => route.query.contentSlug?.toString());

  const handleLesson = async () => {
    if (!lessonSlug.value || !courseSlug.value) return;

    router.push({
      name: route.name,
      params: route.params,
      query: {
        contentSlug: props.lesson.slug
      }
    });
  };
</script>
