<template>
  <div class="py-2 cursor-pointer" @click="toggleCollapse">
    <div class="d-flex align-items-center justify-content-between">
      <div class="d-flex gap-1 align-items-center">
        <i
          :class="['mdi', isCollapsed ? 'mdi-chevron-down' : 'mdi-chevron-right', 'font-size-20', 'text-muted', 'lh-1']"
        ></i>
        <h6 class="mb-0 pe-2">{{ section.title }}</h6>
      </div>
      <BBadge v-if="totalSubmittedPracticeCount" variant="danger" class="ms-1 font-size-11 me-2">
        {{ totalSubmittedPracticeCount }}
      </BBadge>
    </div>
  </div>

  <BCollapse v-model="isCollapsed">
    <BCardBody class="p-0 text-black">
      <CourseSectionItemCard
        v-for="(lesson, index) in section.courseSectionItems"
        :key="index"
        :lesson="lesson"
        :index="index"
      />
    </BCardBody>
  </BCollapse>
</template>

<script lang="ts" setup>
  import CourseSectionItemCard from './CourseSectionItemCard.vue';
  import { CourseSectionWithPracticeInterface } from '@/utils/interface/teacher/courseSection';

  const isCollapsed = ref(false);

  const props = defineProps({
    section: {
      type: Object as PropType<CourseSectionWithPracticeInterface>,
      required: true
    },
    index: {
      type: Number,
      required: true
    }
  });

  const route = useRoute();

  const totalSubmittedPracticeCount = computed(() => {
    return props.section.courseSectionItems?.reduce((total, item) => {
      return total + (item.submittedPracticeCount || 0);
    }, 0);
  });

  const toggleCollapse = () => {
    if (props.section.courseSectionItems?.length) {
      isCollapsed.value = !isCollapsed.value;
    }
  };

  watchEffect(() => {
    const lessonSlug = route.query.contentSlug?.toString();
    const isMatch = props.section.courseSectionItems?.some(item => item.slug?.toString() === lessonSlug);
    if (isMatch) {
      isCollapsed.value = true;
    }
  });
</script>
