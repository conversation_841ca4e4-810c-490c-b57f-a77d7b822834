<template>
  <BRow class="py-4 border-bottom">
    <BCol
      v-for="(card, index) in progressCards"
      :key="index"
      lg="4"
      class="me-3 border border-gray rounded-3 p-3 d-flex flex-column"
    >
      <span class="text-muted small">{{ $t(card.label) }}</span>

      <div class="d-flex justify-content-between align-items-end mt-2">
        <span class="h4 fw-bold mb-0">{{ card.numerator }}/{{ card.denominator }}</span>
        <span class="text-muted small">{{ card.percent }}%</span>
      </div>

      <BProgress height="0.5rem" class="mt-2" :value="card.percent" max="100" variant="primary" />
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import { statsInterface } from '@/utils/interface/teacher/courseSectionItemUser';

  const props = defineProps({
    stats: {
      type: Object as PropType<statsInterface>,
      required: true
    }
  });

  const submittedPercent = computed(() =>
    props.stats.TotalViewedSectionUsers > 0
      ? Math.round((props.stats.TotalSubmittedUsers / props.stats.TotalViewedSectionUsers) * 100)
      : 0
  );

  const viewedPercent = computed(() =>
    props.stats.TotalEnrolledUsers > 0
      ? Math.round((props.stats.TotalViewedSectionUsers / props.stats.TotalEnrolledUsers) * 100)
      : 0
  );

  const progressCards = computed(() => [
    {
      label: 'teacher.practice_submission.search_form.status.submitted',
      numerator: props.stats.TotalSubmittedUsers,
      denominator: props.stats.TotalViewedSectionUsers,
      percent: submittedPercent.value
    },
    {
      label: 'teacher.practice_submission.search_form.status.viewed',
      numerator: props.stats.TotalViewedSectionUsers,
      denominator: props.stats.TotalEnrolledUsers,
      percent: viewedPercent.value
    }
  ]);
</script>
