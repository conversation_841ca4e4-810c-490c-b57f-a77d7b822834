<template>
  <TableList :items="sections" :fields="fields" class="mt-3">
    <template #cell(username)="row">
      <div class="d-flex align-items-center gap-2">
        <BAvatar variant="warning" :src="row.item.imageUrl ? row.item.imageUrl : dummyAvatar" size="25"></BAvatar>
        <div class="d-flex flex-column">
          <span class="fw-bold">{{ row.item.name || row.item.phoneNumber }}</span>
          <small v-if="row.item.practiceSubmissions.length" class="text-primary">
            {{ $t('teacher.practice_submission.search_form.status.submitted') }}
            {{ row.item.practiceSubmissions.length }} {{ $t('teacher.practice_submission.count') }}
          </small>
        </div>
      </div>
    </template>

    <template #cell(LatestSubmissionStatusI18n)="row">
      <span class="badge rounded-pill font-size-10" :class="getStatusClass(row.item.LatestSubmissionStatus)">
        {{ row.item.LatestSubmissionStatusI18n }}
      </span>
    </template>

    <template #cell(LatestSubmissionTime)="row">
      {{ filters.formatDatetime(row.item.LatestSubmissionTime) }}
    </template>

    <template #cell(actions)="row">
      <BButton
        v-if="row.item.practiceSubmissions.length"
        variant="light"
        class="d-flex align-items-center gap-2 bg-white"
        @click="row.toggleDetails"
      >
        <i class="mdi mdi-eye font-size-16 text-secondary"></i>
        <span>{{ $t('teacher.practice_submission.form.detail_btn') }}</span>
        <i
          class="mdi font-size-20 text-muted lh-1 cursor-pointer"
          :class="row.detailsShowing ? 'mdi-chevron-up' : 'mdi-chevron-down'"
        ></i>
      </BButton>
    </template>

    <template #row-details="row">
      <h6 class="fw-bold">{{ $t('teacher.practice_submission.form.detail_title') }}</h6>
      <BCard body-class="py-0 table-submission-card">
        <TableList :items="row.item.practiceSubmissions" :fields="submissionFields">
          <template #cell(createdAt)="row">
            {{ filters.formatDatetime(row.item.createdAt) }}
          </template>

          <template #cell(status)="row">
            <span class="badge rounded-pill font-size-10" :class="getStatusClass(row.item.status)">
              {{ row.item.statusI18n }}
            </span>
          </template>

          <template #cell(actions)="row">
            <BTd>
              <ul class="list-unstyled hstack gap-1 mb-0">
                <li
                  data-bs-toggle="tooltip"
                  data-bs-placement="top"
                  aria-label="View"
                  @click="openDetailModal(row.item)"
                >
                  <a data-bs-toggle="modal" class="btn btn-sm btn-soft-info"><i class="mdi mdi-eye"></i></a>
                </li>
              </ul>
            </BTd>
          </template>
        </TableList>
      </BCard>
    </template>
  </TableList>

  <Pagination :metadata="metadata" @change="changePage"></Pagination>

  <BModal
    v-model="isDetailModalOpen"
    :title="$t(`teacher.practice_submission.modal_detail.title`)"
    size="xl"
    title-class="font-18"
    no-footer
    lazy
    unmount-lazy
    @hide="closeDetailModal()"
  >
    <SubmissionDetail v-model:submission="submissionDetail" @update="handleUpdate" />
  </BModal>
</template>

<script setup lang="ts">
  import i18n from '@/plugin/i18n';
  import filters from '@/utils/filters';
  import Pagination from '@/components/base/Pagination.vue';
  import SubmissionDetail from '@/components/teacher/practiceSubmissions/Detail.vue';
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';

  import { MetaDataInterface } from '@/utils/interface/common';
  import { CourseSectionItemUsersWithPracticeInterface } from '@/utils/interface/teacher/courseSectionItemUser';
  import { practiceSubmissionDetail } from '@/services/teacher';
  import { PracticeSubmissionInterface } from '@/utils/interface/teacher/practiceSubmission';

  defineProps({
    sections: {
      type: Array as PropType<CourseSectionItemUsersWithPracticeInterface[]>,
      required: true
    },
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    }
  });

  const emit = defineEmits(['fetch-list', 'update']);

  const isDetailModalOpen = ref(false);
  const submissionDetail = ref<PracticeSubmissionInterface | undefined>(undefined);

  const fields = [
    {
      key: 'username',
      label: i18n.global.t('teacher.practice_submission.form.user')
    },
    {
      key: 'LatestSubmissionTime',
      label: i18n.global.t('teacher.practice_submission.form.time')
    },
    {
      key: 'LatestSubmissionStatusI18n',
      label: i18n.global.t('teacher.practice_submission.form.status')
    },
    {
      key: 'actions',
      label: i18n.global.t('teacher.practice_submission.form.action')
    }
  ];

  const submissionFields = [
    {
      key: 'content',
      label: i18n.global.t('teacher.practice_submission.form.content'),
      thStyle: { width: '40%' }
    },
    {
      key: 'createdAt',
      label: i18n.global.t('teacher.practice_submission.form.time')
    },
    {
      key: 'status',
      label: i18n.global.t('teacher.practice_submission.form.status')
    },
    {
      key: 'actions',
      label: i18n.global.t('teacher.practice_submission.form.action')
    }
  ];

  async function openDetailModal(submission: PracticeSubmissionInterface) {
    const res = await practiceSubmissionDetail(submission.id.toString());
    submissionDetail.value = res.practiceSubmission;
    isDetailModalOpen.value = true;
  }

  function closeDetailModal() {
    isDetailModalOpen.value = false;
    submissionDetail.value = undefined;
  }

  const getStatusClass = (status: string) => {
    switch (status) {
      case 'submitted':
        return 'badge-soft-primary';
      case 'approved':
        return 'badge-soft-success';
      case 'rejected':
        return 'badge-soft-danger';
      default:
        return 'badge-soft-dark';
    }
  };

  function handleUpdate() {
    isDetailModalOpen.value = false;
    emit('update');
  }

  function changePage(page: number) {
    emit('fetch-list', page);
  }
</script>
