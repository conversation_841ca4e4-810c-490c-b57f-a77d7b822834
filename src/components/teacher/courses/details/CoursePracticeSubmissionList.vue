<template>
  <BRow>
    <BCol lg="12">
      <BCard no-body>
        <BRow>
          <BCol lg="4">
            <CourseContentCard :sections="courseSections" />
          </BCol>
          <BCol v-if="goListInstance" lg="8">
            <ProgressSubmissionCard v-if="stats" :stats="stats" />

            <div class="py-3 border-bottom">
              <SearchForm
                v-model:query="goListInstance.query"
                :search-fields-list="searchFieldsList"
                :disabled="!lessonSlug"
                @search="goListInstance.search"
                @reset="handleReset"
              ></SearchForm>
            </div>

            <CourseSectionSubmission
              :sections="goListInstance.items"
              :metadata="goListInstance.metadata"
              @fetch-list="goListInstance.changePage"
              @update="handleUpdateStatus"
            />
          </BCol>
        </BRow>
      </BCard>
    </BCol>
  </BRow>
</template>

<script setup lang="ts">
  import CourseContentCard from './CourseContentCard.vue';
  import i18n from '@/plugin/i18n';
  import CourseSectionSubmission from './CourseSectionSubmission.vue';
  import useDynamicSearch from '@/composable/dynamicSearch';
  import SearchForm from '@/components/base/SearchForm.vue';
  import ProgressSubmissionCard from './progressSubmissionCard.vue';

  import { useGoList } from '@bachdx/b-vuse';
  import { courseSectionsWithPracticesList } from '@/services/teacher';
  import { CourseSectionWithPracticeInterface } from '@/utils/interface/teacher/courseSection';
  import { courseSectionItemUsersWithPractices } from '@/services/teacher/repositories/courseSectionItemUser';
  import { courseSectionItemUsersQueryFormModel } from '@/forms/teacher/courseSectionItemUser';
  import SearchField, { SearchFieldOptions } from '@/utils/search-fields';
  import { statsInterface } from '@/utils/interface/teacher/courseSectionItemUser';

  const { searchFieldsList, searchComponents } = useDynamicSearch();

  const route = useRoute();
  const router = useRouter();

  const courseSections = ref<CourseSectionWithPracticeInterface[]>([]);
  const goListInstance = ref<ReturnType<typeof useGoList> | null>(null);
  const stats = ref<statsInterface | null>(null);

  const statusOptions = [
    { value: 'notSubmitted', label: i18n.global.t('teacher.practice_submission.search_form.status.notSubmitted') },
    { value: 'submitted', label: i18n.global.t('teacher.practice_submission.search_form.status.submitted') },
    { value: 'approved', label: i18n.global.t('teacher.practice_submission.search_form.status.approved') },
    { value: 'rejected', label: i18n.global.t('teacher.practice_submission.search_form.status.rejected') }
  ];

  searchFieldsList.value = [
    new SearchField(
      i18n.global.t('teacher.course.search_form.title.label'),
      'nameCont',
      'bx bx-search-alt',
      searchComponents.TextInputField,
      { lg: 4 }
    ),
    new SearchField(
      i18n.global.t('teacher.course.search_form.status.label'),
      'statusEq',
      'bx bx-upload',
      searchComponents.SingleSelectField,
      { lg: 4 },
      new SearchFieldOptions({
        selectOptions: statusOptions
      })
    )
  ];

  const courseSlug = computed(() => route.params.slug.toString());
  const lessonSlug = computed(() => route.query.contentSlug?.toString());

  const fetchCourseSection = async () => {
    courseSections.value = await courseSectionsWithPracticesList(courseSlug.value);
  };

  const fetchCourseSectionItemUsersWithPractices = async (params: any) => {
    const response = await courseSectionItemUsersWithPractices(params);
    stats.value = response?.sectionItemUsersWithPractices.Stats || {};
    return {
      ...response,
      collection: response.collection
    };
  };

  const createGoList = () => {
    goListInstance.value = useGoList({
      fetchListFnc: fetchCourseSectionItemUsersWithPractices,
      fetchKey: 'sectionItemUsersWithPractices',
      route: route,
      router: router,
      perPage: 12,
      queryFormModels: courseSectionItemUsersQueryFormModel,
      extraParams: {
        courseSlug: courseSlug.value,
        sectionItemSLug: lessonSlug.value
      }
    });
  };

  const handleReset = () => {
    goListInstance.value?.reset({ defaultQuery: { contentSlug: lessonSlug.value } });
  };

  const handleUpdateStatus = () => {
    goListInstance.value.parseQueryAndFetch();
    fetchCourseSection();
  };

  watch(lessonSlug, (newVal, oldVal) => {
    if (newVal !== oldVal && newVal) {
      createGoList();
      goListInstance.value.parseQueryAndFetch();
    }
  });

  onMounted(async () => {
    await fetchCourseSection();
    createGoList();
    if (lessonSlug.value) {
      goListInstance.value.parseQueryAndFetch();
    }
  });
</script>
