<template>
  <div class="section-list border shadow-none card p-3">
    <h5 class="fw-bold mb-3">{{ $t('teacher.practice_submission.section') }}</h5>

    <CourseSectionCard v-for="(section, index) in sections" :key="index" :section="section" :index="index" />
  </div>
</template>

<script setup lang="ts">
  import CourseSectionCard from './CourseSectionCard.vue';

  import { CourseSectionWithPracticeInterface } from '@/utils/interface/teacher/courseSection';

  defineProps({
    sections: {
      type: Array as PropType<CourseSectionWithPracticeInterface[]>,
      required: true
    }
  });
</script>
