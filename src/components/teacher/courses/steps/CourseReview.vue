<template>
  <div v-if="courseForm" class="course-detail mt-4">
    <div class="banner rounded-4 text-white py-2 d-none d-lg-block">
      <BRow class="w-100 h-100">
        <BCol lg="11" xl="11" xxl="9" class="position-relative h-100">
          <div class="course-detail-banner d-flex flex-column gap-2 gap-lg-4 px-0 px-lg-4 py-2">
            <h2 class="text-break">{{ courseForm.title }}</h2>

            <div class="d-flex flex-column flex-lg-row align-items-start align-items-lg-center gap-1 gap-lg-3">
              <div class="d-none d-lg-flex align-items-center gap-1">
                <i class="mdi mdi-book-open-page-variant font-size-18"></i>
                <span>{{ courseForm.sectionCount }} {{ $t('public.course.course_section') }}</span>
              </div>

              <div class="d-flex align-items-center gap-1">
                <i class="mdi mdi-web font-size-18"></i>
                <span>{{ $t('public.course.vietnamese') }}</span>
              </div>
            </div>
          </div>
        </BCol>
      </BRow>
    </div>

    <BRow class="w-100 mt-4">
      <BCol lg="8" class="order-2 order-lg-1 px-0 px-lg-2">
        <BCard class="course-intro-content text-black">
          <div class="d-flex flex-column gap-4">
            <div class="course-introduction border-bottom pb-4">
              <h4 class="fw-bold">{{ $t('public.course.introduction') }}</h4>
              <pre class="pt-2 pre-content">{{ courseForm.description }}</pre>
            </div>

            <CourseSectionItem :course="courseForm" />
          </div>
        </BCard>
      </BCol>

      <BCol lg="4" class="order-1 order-lg-2 px-0 px-lg-2">
        <BCard class="course-intro-sidebar">
          <template #img>
            <div class="ratio ratio-4x3">
              <img
                :src="courseForm.banner ? courseForm.banner : dummyBanner"
                alt="Course banner"
                class="course-image rounded-4 object-cover"
              />
            </div>
          </template>

          <div class="d-flex flex-column gap-3">
            <div class="d-block d-lg-none">
              <div class="course-detail-banner d-flex flex-column gap-2 gap-lg-4 px-0 px-lg-4 py-2">
                <h2>{{ courseForm.title }}</h2>

                <div class="d-flex flex-column flex-lg-row align-items-start align-items-lg-center gap-1 gap-lg-3">
                  <div class="d-none d-lg-flex align-items-center gap-1">
                    <i class="mdi mdi-play-circle-outline font-size-18"></i>
                    <span>{{ courseForm.sectionCount }} {{ $t('public.course.section_item') }}</span>
                  </div>

                  <div class="d-flex align-items-center gap-1">
                    <i class="mdi mdi-web font-size-18"></i>
                    <span>{{ $t('public.course.vietnamese') }}</span>
                  </div>
                </div>
              </div>
            </div>

            <PriceDisplay :price="courseForm.price" :salePrice="courseForm.salePrice" show-label></PriceDisplay>

            <div>
              <h6>{{ $t('public.course.intro') }}:</h6>
              <div v-for="(item, index) in courseIncludes" :key="index" class="d-flex align-items-center gap-2 pb-1">
                <i class="mdi font-size-20 text-warning" :class="item.icon"></i>
                <span>
                  <span v-if="index == 0">{{ courseForm.sectionCount }}</span>
                  <span v-else-if="index == 1">{{ courseForm.sectionItemCount }}</span>
                  {{ $t(`public.course.${item.text}`) }}
                </span>
              </div>
            </div>
          </div>
        </BCard>
      </BCol>
    </BRow>
  </div>
</template>

<script setup lang="ts">
  import { useBreadcrumb } from '@bachdx/b-vuse';

  import i18n from '@/plugin/i18n';

  import { CourseInterface } from '@/utils/interface/teacher/course';

  import PriceDisplay from '@/components/base/PriceDisplay.vue';
  import dummyBanner from '@/assets/images/dummy_banner.png';
  import CourseSectionItem from '../CourseSectionItem.vue';

  const route = useRoute();
  const { setBreadcrumb } = useBreadcrumb({});

  setBreadcrumb({
    title: i18n.global.t('teacher.course.title'),
    items: [
      {
        text: i18n.global.t('teacher.course.title'),
        href: '/teacher/courses'
      },
      {
        text: i18n.global.t('common.detail'),
        href: `/teacher/courses/${route.params.slug}/detail`
      },
      {
        text: i18n.global.t('teacher.course.form.steps.labels.review')
      }
    ]
  });

  const courseIncludes = [
    { icon: 'mdi-book-open-page-variant', text: 'course_section' },
    { icon: 'mdi-play-circle-outline', text: 'section_item' },
    { icon: 'mdi-certificate-outline', text: 'certificate' },
    { icon: 'mdi-tablet-cellphone', text: 'access' },
    { icon: 'mdi-infinity', text: 'timeline' }
  ];

  const courseForm = defineModel('courseForm', {
    type: Object as PropType<CourseInterface>,
    required: true
  });
</script>
