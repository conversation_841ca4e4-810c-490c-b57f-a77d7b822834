<template>
  <Loader :loading="loading">
    <BRow>
      <BCol lg="3" md="6" sm="12" v-for="course in courses" :key="course.id" class="mt-3 mt-lg-4">
        <router-link :to="`courses/${course.slug}/detail`" class="course-card d-block h-100">
          <BCard body-class="d-flex flex-column justify-content-between gap-3 border" class="mb-0 h-100">
            <template #img>
              <div class="ratio ratio-4x3">
                <img
                  :src="course.banner ? course.banner : dummyBanner"
                  alt="Course banner"
                  class="course-banner object-cover"
                />
              </div>
            </template>

            <div class="d-flex flex-column gap-2">
              <BCardTitle class="mb-0 line-clamp-2">
                {{ course.title }}
              </BCardTitle>

              <div class="d-flex justify-content-between align-items-end status">
                <span class="px-2 fw-bold rounded-pill" :class="course.status">
                  {{ $t(`teacher.course.search_form.status.${course.status}`) }}
                </span>

                <span
                  class="px-2 fw-bold rounded-pill"
                  style="color: white"
                  :class="course.isPublic ? 'bg-warning' : 'bg-danger'"
                >
                  {{ $t(`teacher.course.basic_form.form.is_status_${course.isPublic ? 'public' : 'private'}`) }}
                </span>
              </div>

              <div class="d-flex align-items-center justify-content-between">
                <p class="d-flex align-items-center gap-1 mb-0">
                  <i class="bx bx-book-open text-warning"></i>
                  <span>{{ course.sectionCount || 0 }} {{ $t('teacher.course.section_item') }}</span>
                </p>

                <p v-if="course.joinedUserCount" class="d-flex align-items-center gap-1 mb-0">
                  <i class="bx bx bx-user-check text-success" />{{ course.joinedUserCount }}
                  <span>{{ $t('teacher.course.form.steps.labels.student') }}</span>
                </p>
              </div>
            </div>

            <PriceDisplay :price="course.price" :salePrice="course.salePrice" display-style="compact"></PriceDisplay>
          </BCard>
        </router-link>
      </BCol>
    </BRow>

    <Pagination :metadata="metadata" @change="$emit('fetchList', $event)"></Pagination>
  </Loader>
</template>

<script lang="ts" setup>
  import { CourseInterface } from '@/utils/interface/teacher/course';
  import { MetaDataInterface } from '@/utils/interface/common';

  import PriceDisplay from '@/components/base/PriceDisplay.vue';
  import dummyBanner from '@/assets/images/dummy_banner.png';

  defineEmits(['fetchList']);
  defineProps({
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    courses: {
      type: Array as PropType<CourseInterface[]>,
      required: true
    }
  });
</script>

<style scoped lang="scss">
  .status {
    position: absolute;
    top: 10px;
    left: 5px;
    width: calc(100% - 10px);
  }

  .status span {
    &.draft {
      color: #1f2937;
      background-color: #f3f4f6;
    }

    &.approved {
      color: #15803d;
      background-color: #e8fff2;
    }

    &.submitted {
      color: #1e40af;
      background-color: #dbeafe;
    }

    &.rejected {
      color: #991b1b;
      background-color: #fee2e2;
    }
  }
</style>
