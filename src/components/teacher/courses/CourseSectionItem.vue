<template>
  <div class="list-lesson d-flex flex-column gap-3">
    <div v-for="(section, index) in course.courseSections" :key="index">
      <div
        class="lesson-card d-flex align-items-center justify-content-between px-3 py-2 rounded-top-3 cursor-pointer"
        :class="{ 'rounded-bottom-3': !isCollapsed[index] }"
        @click="toggleSection(index, section)"
      >
        <div class="d-flex align-items-center gap-3">
          <div class="mark-order">{{ index + 1 }}</div>
          <div class="d-flex flex-column course-section-title gap-1">
            <span class="fw-medium">{{ section.title }}</span>
            <div class="user-site-badge badge rounded-pill d-flex align-items-center font-size-12 gap-1">
              <span>{{ section.courseSectionItems?.length }}</span>
              <span class="text-lowercase">{{ $t('public.course.section_item') }}</span>
            </div>
          </div>
        </div>
        <i class="mdi font-size-22 text-muted" :class="isCollapsed[index] ? 'mdi-chevron-up' : 'mdi-chevron-down'"></i>
      </div>
      <BCollapse v-model="isCollapsed[index]" class="course-lesson-collapse border-top-0 rounded-bottom-3 px-3">
        <ul class="list-unstyled mb-0 d-flex flex-column pt-2">
          <li
            v-for="(item, i) in section.courseSectionItems"
            :key="i"
            class="d-flex align-items-center justify-content-between px-3 py-2 py-lg-3 rounded-2 mb-2"
          >
            <div class="d-flex align-items-center gap-3">
              <div class="d-flex flex-column">
                <div class="d-flex align-items-center gap-2">
                  <i class="mdi mdi-lock-outline text-muted font-size-16"></i>
                  {{ item.title }}
                </div>
              </div>
            </div>
          </li>
        </ul>
      </BCollapse>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { CourseInterface } from '@/utils/interface/teacher/course';

  defineProps({
    course: {
      type: Object as PropType<CourseInterface>,
      required: true
    }
  });

  const isCollapsed = ref<Record<number, boolean>>({});

  function toggleSection(index: number, section: CourseInterface['courseSections'][number]) {
    if (section.courseSectionItems?.length) {
      isCollapsed.value[index] = !isCollapsed.value[index];
    }
  }
</script>
<style lang="scss"></style>
