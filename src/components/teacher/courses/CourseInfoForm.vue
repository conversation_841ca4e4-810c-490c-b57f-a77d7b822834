<template>
  <BRow>
    <BCol lg="12">
      <BCard no-body>
        <BCardBody class="course-info-container">
          <BTabs v-model="activeTab" @update:modelValue="handleTabChange">
            <BTab v-for="(step, index) in steps" :key="step.id" :title="step.name" :disabled="tabDisable(index + 1)" />
          </BTabs>

          <div class="settings-content px-2">
            <router-view v-slot="{ Component }" v-model:course-form="courseForm">
              <keep-alive>
                <component :is="Component" v-model:course-form="courseForm" />
              </keep-alive>
            </router-view>
          </div>
        </BCardBody>
      </BCard>
    </BCol>
  </BRow>
</template>

<script setup lang="ts">
  import i18n from '@/plugin/i18n';

  import { CourseFormInterface } from '@/utils/interface/teacher/course';
  import { Step } from '@/utils/interface/common';

  const emits = defineEmits(['cancel', 'fetchCourse']);

  const router = useRouter();
  const route = useRoute();

  const courseForm = defineModel('courseForm', {
    type: Object as PropType<CourseFormInterface>,
    required: true
  });

  const steps: Step[] = [
    {
      id: 1,
      name: i18n.global.t('teacher.course.form.steps.labels.base'),
      tip: i18n.global.t('teacher.course.form.steps.tips.base'),
      route: { name: 'CourseBaseInfo' }
    },
    {
      id: 2,
      name: i18n.global.t('teacher.course.form.steps.labels.student'),
      tip: i18n.global.t('teacher.course.form.steps.tips.student'),
      route: { name: 'CourseUserList' }
    },
    {
      id: 3,
      name: i18n.global.t('teacher.course.form.steps.labels.practice_submission'),
      tip: i18n.global.t('teacher.course.form.steps.tips.practice_submission'),
      route: { name: 'CoursePracticeSubmissionList' }
    }
  ];

  const activeTab = ref(steps.findIndex(step => step.route.name === route.name));

  const handleTabChange = (tabIdx: number) => {
    if (!courseForm.value.id && tabIdx !== 0) return;

    if (tabIdx === 0) emits('fetchCourse');
    router.push(steps[tabIdx].route);
  };

  const tabDisable = (tabId: number): boolean => tabId !== 1 && !courseForm.value?.id;

  watch(
    () => route.name,
    newName => {
      const idx = steps.findIndex(step => step.route.name === newName);
      if (idx !== -1) activeTab.value = idx;
    }
  );
</script>

<style lang="scss" scoped>
  .step-item.completed {
    color: var(--bs-success);
  }

  .step-item.completed .step-number {
    background: var(--bs-success);
    color: white;
  }

  .step-item.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .step-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s ease;
  }

  .step-item:hover {
    background: rgba(var(--bs-primary-rgb), 0.1);
  }

  .step-item.active {
    background: var(--bs-primary);
    color: white;
  }

  .step-number i {
    font-size: 14px;
  }

  .step-number {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: rgba(var(--bs-primary-rgb), 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    font-weight: 500;
  }

  .step-item.active .step-number {
    background: rgba(255, 255, 255, 0.2);
  }

  .info-tip {
    margin-bottom: 15px;
    padding: 0.75rem;
    background: rgba(var(--bs-info-rgb), 0.1);
    border-radius: 6px;
    color: var(--bs-info);
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
</style>
