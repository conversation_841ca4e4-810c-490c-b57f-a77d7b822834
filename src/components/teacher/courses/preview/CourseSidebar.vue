<template>
  <BCard class="course-side-bar mb-0" body-class="p-0">
    <div class="border-bottom pb-4 p-lg-4">
      <BCardTitle class="pb-2">
        <h5>
          <b class="text-warning">{{ course.sectionItemCount }}</b> {{ $t('user.user_course.section_item.title') }}
        </h5>
      </BCardTitle>
      <div class="d-flex justify-content-between pb-1">
        <span>{{ $t('user.user_course.progress') }}</span>
        <span class="text-warning fw-bold">0%</span>
      </div>
      <BProgress height="5px" value="0" :max="100" variant="primary"></BProgress>
    </div>

    <div class="list-lesson px-lg-3 mt-3">
      <CourseSectionCard v-for="(section, index) in course.courseSections" :key="index" :section="section" />
    </div>

    <div class="text-center text-muted mt-4 py-3 border-top">
      {{ $t('user.user_course.section_item.status.completed') }}
      {{ completedCount }}/{{ course.sectionItemCount }}
      <span class="text-lowercase">{{ $t('user.user_course.section_item.title') }}</span>
    </div>
  </BCard>
</template>
<script lang="ts" setup>
  import CourseSectionCard from './CourseSectionCard.vue';
  import { CourseInterface } from '@/utils/interface/teacher/course';

  const props = defineProps({
    course: {
      type: Object as PropType<CourseInterface>,
      required: true
    }
  });

  const completedCount = computed(() => {
    return props.course.courseSections
      .flatMap(section => section.courseSectionItems || [])
      .filter(item => item.isCompleted).length;
  });
</script>
