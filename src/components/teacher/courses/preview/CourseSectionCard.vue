<template>
  <BCard class="mb-0 border" body-class="p-0">
    <div class="px-2 px-lg-3 py-2 py-lg-3 cursor-pointer" @click="toggleCollapse">
      <div class="d-flex justify-content-between align-items-center">
        <h5 class="mb-0 pe-2">{{ section.title }}</h5>
        <i
          v-if="section.courseSectionItems?.length"
          :class="['mdi', isCollapsed ? 'mdi-chevron-down' : 'mdi-chevron-right', 'font-size-24', 'text-muted', 'lh-1']"
        ></i>
      </div>
      <span class="text-muted">({{ completedCount }}/{{ section.courseSectionItems?.length || 0 }})</span>
    </div>

    <BCollapse v-model="isCollapsed">
      <BCardBody class="border-top p-0 text-black">
        <CourseSectionItemCard v-for="(lesson, index) in section.courseSectionItems" :key="index" :lesson="lesson" />
      </BCardBody>
    </BCollapse>
  </BCard>
</template>

<script lang="ts" setup>
  import { CourseSectionInterface } from '@/utils/interface/teacher/courseSection';

  import CourseSectionItemCard from '@/components/teacher/courses/preview/CourseSectionItemCard.vue';

  const props = defineProps({
    section: {
      type: Object as PropType<CourseSectionInterface>,
      required: true
    }
  });

  const route = useRoute();

  const isCollapsed = ref(false);

  const completedCount = computed(() => {
    return props.section.courseSectionItems?.filter(item => item.isCompleted).length || 0;
  });

  const toggleCollapse = () => {
    if (props.section.courseSectionItems?.length) {
      isCollapsed.value = !isCollapsed.value;
    }
  };

  watchEffect(() => {
    const lessonSlug = route.query.courseContentSlug?.toString();
    const isMatch = props.section.courseSectionItems?.some(item => item.slug?.toString() === lessonSlug);
    if (isMatch) {
      isCollapsed.value = true;
    }
  });
</script>
