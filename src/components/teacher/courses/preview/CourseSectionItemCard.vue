<template>
  <div
    class="lesson-card border px-4 py-3 cursor-pointer"
    :class="{ active: lesson.slug == lessonSlug, 'bg-active': lesson.isCompleted }"
    @click="handleLesson"
  >
    <div class="d-flex gap-2 ps-lg-3">
      <i
        class="complete-icon mdi mdi-checkbox-marked-circle-outline lh-1"
        :class="{ 'text-success': lesson.isCompleted }"
      ></i>
      <i class="action-icon mdi mdi-radiobox-marked text-warning"></i>
      <div class="d-flex flex-column gap-1">
        <h6 class="mb-0">{{ lesson.title }}</h6>
        <div class="lesson-type">
          <div v-if="lesson.type == 'text'" class="text-muted">
            <i class="mdi mdi-file-document-outline"></i>
            {{ $t('user.user_course.section_item.type.text') }}
          </div>
          <div v-if="lesson.type == 'drill'" class="text-muted">
            <i class="mdi mdi-vector-square"></i>
            {{ $t('user.user_course.section_item.type.drill') }}
          </div>
          <div v-if="lesson.type == 'video'" class="text-muted">
            <i class="mdi mdi-play-circle-outline"></i>
            {{ $t('user.user_course.section_item.type.video') }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { CourseSectionItemInterface } from '@/utils/interface/teacher/courseSectionItem';

  const props = defineProps({
    lesson: {
      type: Object as PropType<CourseSectionItemInterface>,
      required: true
    }
  });

  const route = useRoute();
  const router = useRouter();

  const lessonSlug = computed(() => route.query.courseContentSlug?.toString());

  const handleLesson = async () => {
    router.push({
      name: route.name,
      params: route.params,
      query: {
        ...route.query,
        courseContentSlug: props.lesson.slug
      }
    });
  };
</script>
