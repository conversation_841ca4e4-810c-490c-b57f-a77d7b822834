<template>
  <div class="drill-section position-relative w-100 py-3 d-flex flex-column align-items-center gap-2 gap-lg-3">
    <div class="d-flex flex-column justify-content-center align-items-center w-100">
      <div
        :ref="setFullscreenTarget"
        class="diagram-section position-relative d-flex justify-content-center align-items-center w-100"
      >
        <div class="diagram-image mx-2 mx-lg-0 text-center">
          <img class="w-100" :src="currentDrill.diagrams[currentDiagramIndex]?.imageUrl" alt="Diagram" />

          <div class="pt-3">
            <span
              v-for="item in currentDrill.skills"
              class="badge-soft-primary badge rounded-pill mx-1 font-size-12"
              :key="item.id"
            >
              {{ item.nameI18n }}
            </span>
          </div>

          <span class="fullscreen-action" @click="toggleFullScreen">
            <i class="mdi" :class="isFullscreen ? 'mdi-fullscreen-exit' : 'mdi-fullscreen'"></i>
          </span>
        </div>
      </div>

      <div class="tab-list-diagram d-flex gap-2 mt-3">
        <Button
          v-for="(diagram, diagramIndex) in currentDrill.diagrams"
          size="sm"
          :key="diagramIndex"
          :variant="currentDiagramIndex == diagramIndex ? 'dark' : 'light'"
          @click="selectDiagram(diagramIndex)"
        >
          {{ diagramIndex + 1 }}
        </Button>
      </div>
    </div>

    <div class="tab-list-drill w-100 d-flex flex-wrap gap-2 p-2 p-lg-4">
      <Button
        v-for="(drill, drillIndex) in drills"
        classes="drill-btn"
        :key="drillIndex"
        :class="{ active: currentDrillIndex === drillIndex }"
        @click="selectDrill(drillIndex)"
      >
        {{ $t('user.user_course.section_item.type.drill') }} {{ drillIndex + 1 }}: {{ drill.title }}
      </Button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { CourseSectionItemInterface } from '@/utils/interface/teacher/courseSectionItem';
  import { DrillInterface } from '@/utils/interface/drill/drill';

  import Button from '@/components/base/Button.vue';

  const props = defineProps({
    lesson: {
      type: Object as PropType<CourseSectionItemInterface>,
      required: true
    },
    drills: {
      type: Array as PropType<DrillInterface[]>,
      required: true
    }
  });

  const currentDrillIndex = ref(0);
  const currentDiagramIndex = ref(0);

  const isFullscreen = ref(false);
  const fullscreenTarget = ref<HTMLElement | null>(null);

  const currentDrill = computed(() => {
    return props.drills?.[currentDrillIndex.value];
  });

  const setFullscreenTarget = (el: Element | ComponentPublicInstance | null) => {
    if (el instanceof HTMLElement) {
      fullscreenTarget.value = el;
    } else {
      fullscreenTarget.value = null;
    }
  };

  const toggleFullScreen = async () => {
    if (!document.fullscreenElement && fullscreenTarget.value) {
      await fullscreenTarget.value.requestFullscreen();
      isFullscreen.value = true;
    } else if (document.fullscreenElement) {
      await document.exitFullscreen();
      isFullscreen.value = false;
    }
  };

  document.addEventListener('fullscreenchange', () => {
    isFullscreen.value = !!document.fullscreenElement;
  });

  const selectDrill = (index: number) => {
    currentDrillIndex.value = index;
    currentDiagramIndex.value = 0;
  };

  const selectDiagram = (index: number) => {
    currentDiagramIndex.value = index;
  };

  watch(
    () => props.lesson,
    () => {
      currentDrillIndex.value = 0;
      currentDiagramIndex.value = 0;
    },
    { immediate: true }
  );
</script>
