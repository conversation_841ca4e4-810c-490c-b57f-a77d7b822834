<template>
  <BCard no-body>
    <BCardBody>
      <div class="d-flex flex-wrap">
        <div class="me-2">
          <BCardTitle class="mb-3">{{ $t(`teacher.practice_submission.comment.title`) }}</BCardTitle>
        </div>
      </div>

      <simplebar data-simplebar style="max-height: 310px">
        <BListGroup>
          <BListGroupItem v-for="comment in comments" :key="comment" class="py-3">
            <div class="d-flex">
              <div class="avatar-xs me-3">
                <BAvatar variant="warning" :src="getAvatar(comment)" size="25" />
              </div>
              <div class="flex-grow-1">
                <h5 class="font-size-14 mb-1">
                  <BBadge variant="primary">
                    {{ comment.authorType }}
                  </BBadge>

                  {{ getAuthorName(comment) }}

                  <small class="text-muted float-end">{{ filters.formatDatetime(comment.createdAt) }}</small>
                </h5>

                <p v-html="comment.content" class="text-muted comment-content mt-2"></p>
              </div>
            </div>
          </BListGroupItem>
        </BListGroup>
      </simplebar>
    </BCardBody>
  </BCard>
</template>

<script setup lang="ts">
  import simplebar from 'simplebar-vue';
  import filters from '@/utils/filters';
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';

  import { CommentInterface } from '@/utils/interface/teacher/comment';

  defineProps({
    comments: {
      type: Array as PropType<CommentInterface[]>,
      default: () => []
    }
  });

  function getAvatar(comment: CommentInterface) {
    const authorType = comment.authorType;
    const avatar = authorType === 'Teacher' ? comment.authorTeacher.imageUrl : comment.authorUser.imageUrl;

    return avatar || dummyAvatar;
  }

  function getAuthorName(comment: CommentInterface) {
    return comment.authorType === 'Teacher' ? comment.authorTeacher.name : comment.authorUser.name;
  }
</script>

<style lang="scss" scoped>
  .comment-content {
    white-space: pre-wrap;
  }
</style>
