<template>
  <BContainer class="mt-4">
    <BCard class="mb-4 shadow-sm">
      <VideoPlayer
        v-if="submission.videos?.[0] && submission.videos?.[0].isPlayable"
        :video-id="String(submission.videos[0]?.id)"
        :thumbnail="String(submission.videos[0]?.thumbnailURL)"
        :video-title="submission.videos[0]?.title"
      />
      <div v-else>
        <img class="btn w-100 video-thumbnail-img" src="/processing-video.png" alt="video processing" />
      </div>

      <h5 class="mt-3">{{ submission.content }}</h5>
      <hr />
      <Comment :comments="submission.comments" />

      <BForm>
        <BFormTextarea
          v-model="content"
          :placeholder="$t(`teacher.practice_submission.comment.form.content.placeholder`)"
          rows="3"
        />

        <div class="d-flex justify-content-end gap-2">
          <BButton size="sm" variant="primary" class="mt-2" :disabled="!content" @click="submitReply()">
            {{ $t(`teacher.practice_submission.comment.create_btn`) }}
          </BButton>
          <BButton
            v-if="submission.status == 'submitted'"
            size="sm"
            variant="success"
            class="mt-2"
            :disabled="!submission.videos"
            @click="changeStatus('approved')"
          >
            {{ $t(`teacher.practice_submission.comment.approve_btn`) }}
          </BButton>
          <BButton
            v-if="submission.status == 'submitted'"
            size="sm"
            variant="danger"
            class="mt-2"
            :disabled="!submission.videos"
            @click="changeStatus('rejected')"
          >
            {{ $t(`teacher.practice_submission.comment.reject_btn`) }}
          </BButton>
        </div>
      </BForm>
    </BCard>
  </BContainer>
</template>

<script lang="ts" setup>
  import Comment from '@/components/teacher/practiceSubmissions/Comment.vue';
  import VideoPlayer from '@/components/base/VideoPlayer.vue';

  import { PracticeSubmissionInterface } from '@/utils/interface/teacher/practiceSubmission';

  import { commentCreate, practiceSubmissionChangeStatus } from '@/services/teacher';

  const submission = defineModel('submission', {
    type: Object as PropType<PracticeSubmissionInterface>,
    default: () => ({}) as PracticeSubmissionInterface
  });

  const content = ref('');

  const emits = defineEmits(['update']);

  async function submitReply() {
    if (!content.value) return;

    const input = {
      targetID: submission.value.id.toString(),
      targetType: 'PracticeSubmission',
      content: content.value
    };

    const res = await commentCreate(input);

    if (!submission.value.comments) submission.value.comments = [];

    submission.value.comments.push(res.commentCreate.comment);

    content.value = '';
  }

  async function changeStatus(status: string) {
    await practiceSubmissionChangeStatus(submission.value.id.toString(), status);
    emits('update');
  }
</script>

<style lang="scss" scoped>
  .comment-content {
    white-space: pre-wrap;
  }
</style>
