<template>
  <BModal
    :title="highlightId ? $t('teacher.highlight.form.edit_highlight') : $t('teacher.highlight.form.add_highlight')"
    size="md"
    @hidden="resetForm"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :ok-disabled="isSubmitting"
    :cancel-disabled="isSubmitting"
    cancel-title="Cancel"
    ok-title="Save"
    ok-variant="primary"
    :loading="isSubmitting"
  >
    <BForm>
      <BaseFormValidator name="iconID" :label="$t('teacher.highlight.form.icon')" required> </BaseFormValidator>

      <BFormGroup class="mb-3">
        <template #label>
          <div v-if="form.iconId">
            <i :class="getSelectedIcon?.class" class="text-warning font-size-24"></i>
          </div>
        </template>
        <BFormInput v-model="searchQuery" :placeholder="$t('teacher.highlight.form.search_icons')" class="mb-2" />
        <div class="icon-selection">
          <div
            v-for="icon in filteredIcons"
            :key="icon.value"
            class="icon-option"
            :class="{ selected: form.iconId === icon.value }"
            @click="form.iconId = icon.value"
            :title="icon.label"
          >
            <i :class="icon.class" class="font-size-16 text-warning"> </i>
          </div>
          <div v-if="filteredIcons.length === 0" class="text-center py-3 w-100">
            {{ $t('teacher.highlight.form.no_icons_found') }}
          </div>
        </div>
      </BFormGroup>

      <BaseFormValidator name="title" :label="$t('teacher.highlight.form.title')" class="mb-3" required>
        <BFormInput
          id="title"
          name="title"
          v-model="form.title"
          :placeholder="$t('teacher.highlight.form.enter_highlight_title')"
          required
        />
      </BaseFormValidator>

      <BaseFormValidator name="description" :label="$t('teacher.highlight.form.description')" class="mb-3">
        <BFormTextarea
          id="description"
          name="description"
          v-model="form.description"
          :placeholder="$t('teacher.highlight.form.enter_highlight_description')"
          rows="4"
        />
      </BaseFormValidator>
    </BForm>
  </BModal>
</template>

<script lang="ts" setup>
  import { ref, computed, PropType } from 'vue';
  import { HighlightModifyInterface } from '@/utils/interface/teacher/highlight';
  import { SelectOptionsInterface } from '@/utils/interface/select-options';

  const props = defineProps({
    form: {
      type: Object as PropType<HighlightModifyInterface>,
      required: true
    },
    isSubmitting: {
      type: Boolean,
      default: false
    },
    icons: {
      type: Array as PropType<SelectOptionsInterface['iconOptions']>,
      required: true
    },
    highlightId: {
      type: String,
      default: ''
    }
  });

  const emit = defineEmits(['submit', 'cancel', 'edit']);

  const searchQuery = ref('');

  const filteredIcons = computed(() => {
    const icons = props.icons || [];
    if (!searchQuery.value) return icons;

    const query = searchQuery.value.toLowerCase();
    return icons.filter(icon => icon.label.toLowerCase().includes(query) || icon.class.toLowerCase().includes(query));
  });

  const getSelectedIcon = computed(() => {
    return props.icons?.find(icon => icon.value === props.form.iconId);
  });

  const handleSubmit = () => {
    if (props.highlightId) {
      emit('edit');
    } else {
      emit('submit');
    }
  };

  const handleCancel = () => {
    emit('cancel');
  };

  const resetForm = () => {
    emit('cancel');
  };
</script>

<style scoped>
  .icon-selection {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
    gap: 10px;
    max-height: 200px;
    overflow-y: auto;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    margin-top: 0.5rem;
  }

  .icon-selection .text-center {
    grid-column: 1 / -1;
    color: #6c757d;
  }

  .icon-option {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    aspect-ratio: 1;
    background-color: #fff9e6;
    padding: 0.25rem;
    font-size: 1.25rem;
    width: 100%;
    height: 0;
    padding-bottom: 100%;
    position: relative;
  }

  .icon-option i {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    line-height: 1;
  }

  .icon-option:hover {
    background-color: #fff3cc;
    border-color: #86b7fe;
    color: #0d6efd;
  }

  .icon-option.selected {
    border-color: #86b7fe;
    color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }
</style>
