<template>
  <Loader :loading="loading">
    <div>
      <div v-if="highlights.length === 0" class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-star"></i>
        </div>
        <h3>{{ $t('teacher.highlight.no_highlights.title') }}</h3>
        <p>{{ $t('teacher.highlight.no_highlights.description') }}</p>
      </div>

      <div v-else class="highlights-grid">
        <div
          v-for="(highlight, index) in highlights"
          :key="highlight.id || index"
          class="highlight-card"
          @click="$emit('highlight-click', highlight)"
        >
          <div class="highlight-item">
            <i :class="highlight.icon.class" class="text-warning font-size-24"></i>
            <div>
              <h3 class="title">{{ highlight.title || 'Untitled Highlight' }}</h3>
              <p class="description" v-if="highlight.description">
                {{ truncate(highlight.description, 100) }}
              </p>
            </div>
            <button class="action-btn ms-auto" @click.stop="$emit('edit', highlight)">
              <i class="mdi mdi-dots-vertical text-primary font-size-16"></i>
            </button>
          </div>
        </div>
      </div>

      <div class="pagination-wrapper" v-if="highlights.length > 0">
        <Pagination :metadata="metadata" @change="$emit('fetchList', $event)"></Pagination>
      </div>
    </div>
  </Loader>
</template>

<script lang="ts" setup>
  import { HighlightInterface } from '@/utils/interface/teacher/highlight';
  import { MetaDataInterface } from '@/utils/interface/common';

  const props = defineProps({
    highlights: {
      type: Array as PropType<HighlightInterface[]>,
      required: true,
      default: () => []
    },
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  });

  const emit = defineEmits(['fetchList', 'highlight-click', 'edit', 'delete']);

  const truncate = (text: string, length: number) => {
    if (!text) return '';
    return text.length > length ? text.substring(0, length) + '...' : text;
  };
</script>

<style scoped>
  .empty-state {
    text-align: center;
    padding: 4rem 1rem;
    color: #6c757d;
  }

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #e9ecef;
  }

  .empty-state h3 {
    margin: 0.5rem 0;
    font-weight: 600;
  }

  .empty-state p {
    margin: 0;
    color: #adb5bd;
  }

  .highlights-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  @media (max-width: 1200px) {
    .highlights-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (max-width: 992px) {
    .highlights-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 576px) {
    .highlights-grid {
      grid-template-columns: 1fr;
    }
  }

  .highlight-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 1.5rem;
    transition:
      transform 0.2s,
      box-shadow 0.2s;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid #e9ecef;
  }

  .highlight-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
  }

  .icon-wrapper {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.1rem;
    flex-shrink: 0;
  }

  .title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #212529;
    word-break: break-word;
  }

  .description {
    color: #6c757d;
    margin: 0 0 1.5rem 0;
    line-height: 1.5;
    flex-grow: 1;
    word-break: break-word;
  }

  .card-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .date {
    font-size: 0.85rem;
    color: #868e96;
  }

  .action-btn {
    background: none;
    border: none;
    color: #adb5bd;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s;
  }

  .action-btn:hover {
    background-color: #f1f3f5;
    color: #495057;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .highlights-grid {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
  }

  @media (max-width: 480px) {
    .highlights-container {
      padding: 1rem;
    }

    .highlights-grid {
      grid-template-columns: 1fr;
    }
  }

  .highlight-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 1rem;
    height: 100%;

    i {
      padding-top: 0.25rem;
    }

    h5 {
      font-weight: 600;
      margin-bottom: 0.25rem;
    }

    p {
      color: #6c757d;
      font-size: 0.875rem;
    }
  }
</style>
