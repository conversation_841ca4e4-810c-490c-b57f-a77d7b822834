<template>
  <div
    class="parent-preloader-component position-relative"
    :class="{
      'position-relative': loading
    }"
  >
    <div
      class="preloader-component"
      :class="{
        'd-block': loading,
        'd-none': !loading
      }"
    >
      <div
        class="status"
        :class="{
          'd-block': loading,
          'd-none': !loading
        }"
      >
        <div class="spinner-chase">
          <div class="chase-dot"></div>
          <div class="chase-dot"></div>
          <div class="chase-dot"></div>
          <div class="chase-dot"></div>
          <div class="chase-dot"></div>
          <div class="chase-dot"></div>
        </div>
      </div>
    </div>
    <slot />
  </div>
</template>

<script lang="ts" setup>
  defineProps({
    loading: {
      type: Boolean,
      default: false
    }
  });
</script>

<style scoped>
  .preloader-component {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bs-tertiary-bg);
    z-index: 999;
    width: 100%;
  }
  .status {
    width: 40px;
    height: 40px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -20px 0 0 -20px;
  }
</style>
