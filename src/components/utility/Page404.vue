<template>
  <div class="error-page-container d-flex justify-content-center align-items-center">
    <div class="pool-light pool-light-left"></div>
    <div class="pool-light pool-light-right"></div>
    <div class="light-fixture light-fixture-left"></div>
    <div class="light-fixture light-fixture-right"></div>

    <div class="chalk-dust chalk-dust-1"></div>
    <div class="chalk-dust chalk-dust-2"></div>
    <div class="chalk-dust chalk-dust-3"></div>

    <div class="container-fluid h-100">
      <div class="row h-100 justify-content-center align-items-center">
        <div class="col-12 col-md-8 col-lg-6">
          <div class="text-center fade-in-up">
            <div class="mb-5">
              <div class="d-flex justify-content-center align-items-center mb-4 error-number">
                <span class="display-1 fw-bold text-dark">4</span>
                <div class="mx-4 position-relative">
                  <div class="eight-ball gentle-bounce">
                    <div class="eight-ball-number">
                      <span class="fw-bold">8</span>
                    </div>
                    <div class="ball-highlight-1"></div>
                    <div class="ball-highlight-2"></div>
                  </div>
                  <div class="ball-shadow"></div>
                </div>
                <span class="display-1 fw-bold text-dark">4</span>
              </div>

              <h1 class="h2 fw-semibold text-secondary mb-3">
                {{ $t('error_page.not_found.title') }}
              </h1>
              <p class="text-muted fs-5 mb-5">
                {{ $t('error_page.not_found.description') }}
              </p>
            </div>

            <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center mb-5 position-relative">
              <div class="chalk-cube d-none d-sm-block"></div>

              <Button
                classes="btn btn-primary btn-lg px-4 py-3 fw-medium shadow hover-lift"
                icon="bx-home"
                variant="success"
                @click="goHome"
              >
                {{ $t('error_page.not_found.back_to_home') }}
              </Button>

              <Button
                classes="btn btn-primary btn-lg px-4 py-3 fw-medium shadow hover-lift"
                icon="bx-arrow-back"
                @click="goBack"
              >
                {{ $t('error_page.not_found.back_to_previous') }}
              </Button>

              <div class="triangle-rack d-none d-sm-block"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import Button from '../base/Button.vue';

  const router = useRouter();

  const goHome = () => {
    router.push(`/`);
  };

  const goBack = () => {
    router.go(-1);
  };
</script>

<style scoped>
  /* Main container */
  .error-page-container {
    min-height: 100vh;
    position: relative;
    overflow: hidden;
  }

  /* Pool hall lighting */
  .pool-light {
    position: absolute;
    top: 0;
    width: 2px;
    height: 4rem;
    background-color: #6c757d;
    opacity: 0.2;
  }

  .pool-light-left {
    left: 25%;
  }
  .pool-light-right {
    right: 25%;
  }

  .light-fixture {
    position: absolute;
    top: 4rem;
    width: 6rem;
    height: 2rem;
    background: linear-gradient(to bottom, #ffc107, #fd7e14);
    border-radius: 0 0 0.5rem 0.5rem;
    opacity: 0.3;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .light-fixture-left {
    left: calc(25% - 3rem);
  }
  .light-fixture-right {
    right: calc(25% - 3rem);
  }

  /* Floating chalk dust */
  .chalk-dust {
    position: absolute;
    width: 4px;
    height: 4px;
    background-color: #007bff;
    border-radius: 50%;
    opacity: 0.4;
    animation: float 3s ease-in-out infinite;
  }

  .chalk-dust-1 {
    top: 5rem;
    left: 2.5rem;
  }
  .chalk-dust-2 {
    top: 8rem;
    right: 4rem;
    animation-delay: 1s;
  }
  .chalk-dust-3 {
    top: 10rem;
    left: 33%;
    animation-delay: 2s;
  }

  /* Error number styling */
  .error-number {
    font-size: 6rem;
  }

  @media (min-width: 768px) {
    .error-number {
      font-size: 8rem;
    }
  }

  /* 8-ball styling */
  .eight-ball {
    width: 5rem;
    height: 5rem;
    background: linear-gradient(135deg, #343a40, #000);
    border-radius: 50%;
    position: relative;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  }

  @media (min-width: 768px) {
    .eight-ball {
      width: 6rem;
      height: 6rem;
    }
  }

  .eight-ball-number {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2rem;
    height: 2rem;
    background-color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: black;
    font-size: 1.2rem;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  @media (min-width: 768px) {
    .eight-ball-number {
      width: 2.5rem;
      height: 2.5rem;
      font-size: 1.5rem;
    }
  }

  .ball-highlight-1 {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    width: 1rem;
    height: 1rem;
    background-color: white;
    opacity: 0.4;
    border-radius: 50%;
    filter: blur(2px);
  }

  .ball-highlight-2 {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    width: 0.5rem;
    height: 0.5rem;
    background-color: white;
    opacity: 0.6;
    border-radius: 50%;
  }

  .ball-shadow {
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4rem;
    height: 1rem;
    background-color: black;
    opacity: 0.2;
    border-radius: 50%;
    filter: blur(4px);
  }

  /* Chalk cube */
  .chalk-cube {
    position: absolute;
    left: -4rem;
    top: 50%;
    transform: translateY(-50%) rotate(12deg);
    width: 2rem;
    height: 2rem;
    background: linear-gradient(135deg, #007bff, #0056b3);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
  }

  .chalk-cube:hover {
    transform: translateY(-50%) rotate(17deg);
  }

  .chalk-cube::before {
    content: '';
    position: absolute;
    top: 0.25rem;
    left: 0.25rem;
    width: 0.5rem;
    height: 0.5rem;
    background-color: rgba(255, 255, 255, 0.6);
  }

  .chalk-cube::after {
    content: '';
    position: absolute;
    bottom: -0.25rem;
    left: 50%;
    transform: translateX(-50%);
    width: 1.5rem;
    height: 0.25rem;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    filter: blur(2px);
  }

  /* Triangle rack */
  .triangle-rack {
    position: absolute;
    right: -4rem;
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
    width: 2.5rem;
    height: 2.5rem;
    border: 2px solid #6c757d;
  }

  .triangle-rack::before {
    content: '';
    position: absolute;
    inset: 0.25rem;
    border: 1px solid #adb5bd;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2) !important;
  }

  /* Animations */
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes gentleBounce {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-5px);
    }
  }

  @keyframes fadeInUp {
    0% {
      opacity: 0;
      transform: translateY(30px);
    }
    100% {
      opacity: 1;
      transform: translateY(0px);
    }
  }

  .gentle-bounce {
    animation: gentleBounce 3s ease-in-out infinite;
  }

  .fade-in-up {
    animation: fadeInUp 0.8s ease-out;
  }

  /* Responsive adjustments */
  @media (max-width: 576px) {
    .error-number {
      font-size: 4rem;
    }

    .eight-ball {
      width: 4rem;
      height: 4rem;
    }

    .eight-ball-number {
      width: 1.5rem;
      height: 1.5rem;
      font-size: 1rem;
    }
  }
</style>
