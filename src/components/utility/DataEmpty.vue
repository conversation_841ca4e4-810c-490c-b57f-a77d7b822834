<template>
  <div class="pt-3 pt-lg-5 d-flex flex-column justify-content-center align-items-center">
    <img src="@/assets/images/data_empty.png" class="empty-img" :height="height" />
    <div class="text-secondary">{{ message }}</div>
  </div>
</template>

<script lang="ts" setup>
  defineProps({
    message: {
      type: String,
      required: true
    },
    height: {
      type: String,
      default: '150'
    }
  });
</script>

<style lang="scss" scoped>
  .empty-img {
    @media (max-width: 675px) {
      height: 120px;
    }
  }
</style>
