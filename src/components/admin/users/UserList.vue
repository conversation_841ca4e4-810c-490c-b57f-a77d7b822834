<template>
  <Loader :loading="loading">
    <div class="table-responsive">
      <TableList
        :fields="fields"
        :items="users"
        :metadata="metadata"
        hover
        :tbody-tr-class="rowClass"
        @page-change="changePage"
      >
        <template #cell(imageUrl)="{ item }">
          <BAvatar :src="item.imageUrl ? item.imageUrl : dummyAvatar" :alt="item.name"></BAvatar>
        </template>

        <template #cell(name)="{ item }">
          <div class="d-flex flex-column">
            <span>{{ item.name }}</span>
            <div v-if="!item.active" class="d-flex align-items-center text-danger gap-1 mt-1">
              <i class="mdi" :class="item.active ? 'mdi-checkbox-marked-circle-outline' : 'mdi-alert-outline'"></i>
              {{ $t('admin.user.form.status.description') }}
            </div>
          </div>
        </template>

        <template #cell(gender)="{ item }"> {{ genderMapping[item.gender] }} </template>

        <template #cell(createdAt)="{ item }">
          <span>{{ filters.formatDate(item.createdAt) }}</span>
        </template>

        <template #cell(active)="{ item }">
          <span
            v-if="item"
            class="badge rounded-pill font-size-12 gap-1"
            :class="item.active ? 'badge-soft-success' : 'badge-soft-danger'"
          >
            <i class="mdi" :class="item.active ? 'mdi-checkbox-marked-circle-outline' : 'mdi-alert-outline'"></i>
            {{ item.active ? $t('admin.user.form.status.active') : $t('admin.user.form.status.de_active') }}
          </span>
        </template>

        <template #cell(actions)="{ item }">
          <BDropdown class="card-drop mx-auto" variant="white" right lazy toggle-class="p-0">
            <template v-slot:button-content>
              <i class="mdi mdi-dots-horizontal font-size-18"></i>
            </template>

            <BDropdownItem @click="handleToggleActive(item)">
              <i :class="item.active ? 'bx bxs-user-x text-danger' : 'bx bxs-user-check text-success'" class="me-1" />
              {{ item.active ? $t('admin.user.dropdown.de_active') : $t('admin.user.dropdown.active') }}
            </BDropdownItem>
          </BDropdown>
        </template>
      </TableList>
    </div>
  </Loader>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';
  import filters from '@/utils/filters';

  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';

  import { MetaDataInterface } from '@/utils/interface/common';
  import { UserInterface } from '@/utils/interface/admin/user';
  import { SwalIconOptions, SwalOptions } from '@/utils/swal-options';

  import { toggleUserActive } from '@/services/admin/repositories/user';

  import useSwal from '@/composable/swal';

  const emit = defineEmits(['changePage']);

  defineProps({
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    },
    users: {
      type: Array as PropType<UserInterface[]>,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  });

  const { confirming } = useSwal();

  const genderMapping: Record<string, string> = {
    Male: 'Nam',
    Female: 'Nữ'
  };

  const fields = ref<{ key: string; label: string; class?: string }[]>([
    { key: 'imageUrl', label: '' },
    { key: 'name', label: i18n.global.t('admin.user.form.name') },
    { key: 'gender', label: i18n.global.t('admin.user.form.gender') },
    { key: 'birthDate', label: i18n.global.t('admin.user.form.birth_date') },
    { key: 'phoneNumber', label: i18n.global.t('admin.user.form.phone') },
    { key: 'createdAt', label: i18n.global.t('admin.user.form.joined_at') },
    { key: 'active', label: i18n.global.t('admin.user.form.status.label') },
    { key: 'actions', label: i18n.global.t('common.actions'), class: 'max-w-75' }
  ]);

  const rowClass = (item: UserInterface, type: string) => {
    return type === 'row' && item.active ? '' : 'de-active-row';
  };

  const changePage = (e: { page: number }) => {
    emit('changePage', e);
  };

  const handleToggleActive = async (user: UserInterface) => {
    const keyLocale = user.active ? 'de_active' : 'active';
    const title = i18n.global.t(`admin.user.confirm_modal.${keyLocale}.title`);
    const message = i18n.global.t(`admin.user.confirm_modal.${keyLocale}.content`);

    const confirmed = await confirming(
      new SwalOptions({
        title: title,
        html: message,
        icon: SwalIconOptions.Warning
      })
    );

    if (!confirmed) return;

    try {
      await toggleUserActive(String(user.id));
      user.active = !user.active;
    } catch (error) {
      console.log(error);
    }
  };
</script>

<style lang="scss" scoped>
  :deep(tr.de-active-row),
  :deep(tr.de-active-row > td) {
    background-color: rgb(254 242 242) !important;
    td {
      &:not(:last-child) {
        opacity: 0.7;
      }
    }
  }
</style>
