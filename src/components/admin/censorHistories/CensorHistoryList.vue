<template>
  <BCard v-if="histories && histories?.length > 0" no-body class="mt-4">
    <BCardBody>
      <BCardTitle class="mb-4 pb-3 border-bottom">
        <span>{{ $t('admin.course.tabs.censor_history') }}</span>
      </BCardTitle>
      <div>
        <div v-for="history in histories" :key="history.id" class="border-bottom">
          <div class="history-item cursor-pointer" @click="showDetail(history)">
            <div class="d-flex justify-content-between align-items-center">
              <span class="text-muted">{{ history?.creator?.name }}</span>
              <BBadge pill :variant="getStatusClass(history.status)">{{ history.statusI18n }}</BBadge>
            </div>
            <pre v-if="history.feedback" class="mb-2 feedback-preview pre-content">{{ history.feedback }}</pre>
            <small class="text-muted">{{ formatDate(history.createdAt) }}</small>
          </div>
        </div>
      </div>
    </BCardBody>

    <BModal v-model="isDetailModalOpen" size="lg" :title="$t('admin.course.tabs.censor_history')" lazy no-footer>
      <template v-if="selectedHistory">
        <div class="mb-4">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
              <h5 class="mb-1">{{ selectedHistory.creator.name }}</h5>
              <small class="text-muted">{{ formatDate(selectedHistory.createdAt) }}</small>
            </div>
            <BBadge pill :variant="getStatusClass(selectedHistory.status)">
              {{ selectedHistory.statusI18n }}
            </BBadge>
          </div>
          <div v-if="selectedHistory.feedback" class="p-3 bg-light rounded feedback-modal-content">
            {{ selectedHistory.feedback }}
          </div>
        </div>
      </template>
    </BModal>
  </BCard>
</template>

<script lang="ts" setup>
  import { CensorHistory } from '@/utils/interface/censorHistory';

  defineProps<{
    histories?: CensorHistory[];
  }>();

  const isDetailModalOpen = ref(false);
  const selectedHistory = ref<CensorHistory | null>(null);

  const getStatusClass = (status: string) => {
    const classes = {
      feedback: 'danger',
      submitted: 'warning'
    };
    return classes[status as keyof typeof classes] || '';
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleString();
  };

  const showDetail = (history: CensorHistory) => {
    selectedHistory.value = history;
    isDetailModalOpen.value = true;
  };
</script>

<style scoped>
  .cursor-pointer {
    cursor: pointer;
  }

  .cursor-pointer:hover {
    opacity: 0.8;
  }

  .history-item {
    padding: 8px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
  }

  .history-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
  }

  .feedback-preview {
    display: -webkit-box;
    -webkit-line-clamp: 5;
    line-clamp: 5;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    /* white-space: normal; */
  }

  .feedback-modal-content {
    max-height: 800px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.5;
    font-size: 14px;
    padding-right: 16px;
  }

  .feedback-modal-content::-webkit-scrollbar {
    width: 6px;
  }

  .feedback-modal-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .feedback-modal-content::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
  }

  .feedback-modal-content::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
</style>
