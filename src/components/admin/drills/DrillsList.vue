<template>
  <Loader :loading="loading">
    <BRow>
      <BCol lg="4" md="6" sm="12" v-for="drill in drills" :key="drill.id">
        <BCard no-body class="shadow-none">
          <BCardHeader header-bg-variant="light" class="bg-transparent">
            <div class="text-center">
              <h5>
                <BadgeLevel :level="drill.level" :value="drill.levelI18n"></BadgeLevel>
              </h5>
              <h3>
                <router-link :to="`/admin/drills/${drill.id}`" class="text-black text-truncate d-block">
                  {{ drill.title }}
                </router-link>
              </h3>
            </div>
          </BCardHeader>

          <BCardBody>
            <div class="d-flex justify-content-between align-items-end gap-2 status">
              <span class="px-2 fw-bold rounded-pill" :class="drill.censor">
                {{ $t(`teacher.course.search_form.status.${drill.censor}`) }}
              </span>

              <span
                class="px-2 fw-bold rounded-pill text-white"
                :class="drill.status === COURSE_PUBLIC_STATUS_ENUMS.PUBLIC ? 'bg-warning' : 'bg-danger'"
              >
                {{
                  $t(
                    `teacher.course.basic_form.form.is_status_${
                      drill.status === COURSE_PUBLIC_STATUS_ENUMS.PUBLIC
                        ? COURSE_PUBLIC_STATUS_ENUMS.PUBLIC
                        : COURSE_PUBLIC_STATUS_ENUMS.PRIVATE
                    }`
                  )
                }}
              </span>
            </div>

            <router-link :to="`/admin/drills/${drill.id}`">
              <img
                class="btn p-0 w-100"
                :src="
                  drill.diagrams && drill.diagrams.length && drill.diagrams[0].imageUrl
                    ? drill.diagrams[0].imageUrl
                    : '/pool-table.png'
                "
              />
            </router-link>
          </BCardBody>

          <BCardFooter footer-bg-variant="light" class="text-center bg-transparent">
            <BButtonGroup class="action-btn-group">
              <router-link :to="`/admin/drills/${drill.id}`" class="btn btn-md bg-gradient btn-white">
                <i class="bx bx-info-circle"></i>
                {{ $t('common.detail') }}
              </router-link>
              <template v-if="drill.ownerType === OWNER_TYPE_ENUMS.ADMIN">
                <Button icon="bx-edit-alt" variant="white" @click="handleEdit(drill.id)">
                  {{ $t('common.edit') }}
                </Button>
                <Button icon="bx-x" variant="white" @click="handleDelete(drill.id)">
                  {{ $t('common.delete') }}
                </Button>
              </template>
            </BButtonGroup>

            <div>
              <span v-for="item in drill.skills" class="badge-soft-primary badge rounded-pill mx-1" :key="item.id">
                {{ item.nameI18n }}
              </span>
            </div>
          </BCardFooter>
        </BCard>
      </BCol>
    </BRow>

    <Pagination :metadata="metadata" @change="$emit('fetchList', $event)"></Pagination>
  </Loader>
</template>

<script lang="ts" setup>
  import BadgeLevel from '@/components/base/DrillBadgeLevel.vue';
  import Button from '@/components/base/Button.vue';
  import Pagination from '@/components/base/Pagination.vue';

  import { COURSE_PUBLIC_STATUS_ENUMS, OWNER_TYPE_ENUMS } from '@/utils/constant';
  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { MetaDataInterface } from '@/utils/interface/common';

  const emit = defineEmits(['fetchList', 'edit', 'delete']);
  defineProps({
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    drills: {
      type: Array as PropType<DrillInterface[]>,
      required: true
    }
  });

  function handleEdit(id: number) {
    emit('edit', id);
  }

  function handleDelete(id: number) {
    emit('delete', id);
  }
</script>

<style lang="scss" scoped>
  .action-btn-group {
    .btn {
      &:hover {
        color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity));
      }
    }
  }

  .status {
    position: absolute;
    top: 135px;
    left: 50px;
    width: calc(100% - 100px);
  }

  .status span {
    &.draft {
      color: #1f2937;
      background-color: #f3f4f6;
    }

    &.approved {
      color: #166534;
      background-color: #dcfce7;
    }

    &.submitted {
      color: #1e40af;
      background-color: #dbeafe;
    }

    &.rejected {
      color: #991b1b;
      background-color: #fee2e2;
    }
  }
</style>
