<template>
  <Loader :loading="loading">
    <BRow>
      <BCol xl="6" lg="12" sm="12" v-for="drill in drills" :key="drill.id">
        <BCard no-body class="shadow-none">
          <BCardHeader header-bg-variant="light" class="bg-transparent">
            <div class="text-center">
              <h5>
                <BadgeLevel :level="drill.level!" :value="drill.levelI18n!"></BadgeLevel>
              </h5>
              <h3>
                <router-link :to="`/admin/drills/${drill.id}`" class="text-black text-truncate d-block">
                  {{ drill.title }}
                </router-link>
              </h3>
            </div>
          </BCardHeader>
          <BCardBody>
            <router-link :to="`/admin/drills/${drill.id}`">
              <img
                class="btn p-0 w-100"
                :src="
                  drill.diagrams
                    ? drill.diagrams.length
                      ? drill.diagrams[0].imageUrl
                      : '/pool-table.png'
                    : '/pool-table.png'
                "
              />
            </router-link>
          </BCardBody>
          <BCardFooter footer-bg-variant="light" class="text-center bg-transparent">
            <BButtonGroup class="action-btn-group">
              <div>
                <router-link :to="`/admin/drills/${drill.id}`" class="btn btn-md bg-gradient btn-white">
                  <i class="bx bx-info-circle"></i>
                  {{ $t('common.detail') }}
                </router-link>
              </div>
            </BButtonGroup>

            <div>
              <span v-for="item in drill.skills" class="badge-soft-primary badge rounded-pill mx-1" :key="item.id">
                {{ item.nameI18n }}
              </span>
            </div>
          </BCardFooter>
        </BCard>
      </BCol>
    </BRow>
  </Loader>
</template>

<script lang="ts" setup>
  import BadgeLevel from '@/components/base/DrillBadgeLevel.vue';
  import { DrillForm } from '@/forms/admin/drill';

  defineProps({
    loading: {
      type: Boolean,
      default: false
    },
    drills: {
      type: Array as PropType<DrillForm[]>,
      required: true
    }
  });
</script>

<style lang="scss" scoped>
  .action-btn-group {
    .btn {
      &:hover {
        color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity));
      }
    }
  }
</style>
