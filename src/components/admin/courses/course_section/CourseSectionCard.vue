<template>
  <BCard class="course-section-card text-center border border-primary bg-light" no-body header-class="rounded-2">
    <template #header>
      <div class="d-flex justify-content-between align-items-center">
        <h5 class="my-0">
          <strong class="pe-2">{{ $t('admin.course_section.form.title') }} {{ index + 1 }} :</strong>
          <i class="mdi mdi-file-document-outline pe-1"></i>
          <span>{{ courseSection.title }}</span>
        </h5>
      </div>
    </template>
    <BCardBody>
      <CourseSectionItemCard
        v-for="(sectionItem, sectionItemIdex) in item.courseSectionItems"
        :key="sectionItemIdex"
        :sectionItem="sectionItem"
        :sectionItemIdex="sectionItemIdex"
      />
    </BCardBody>
  </BCard>
</template>
<script setup lang="ts">
  import CourseSectionItemCard from '@/components/admin/courses/course_section/CourseSectionItemCard.vue';

  import { CourseSectionForm as Form } from '@/forms/admin/courseSection';

  const props = defineProps({
    item: {
      type: Object as PropType<Form>,
      required: true
    },
    index: {
      type: Number,
      required: true
    }
  });

  const courseSection = ref(new Form());

  watch(
    () => props.item,
    newItem => {
      if (newItem) {
        courseSection.value.assignAttributes(newItem);
      }
    },
    { immediate: true, deep: true }
  );
</script>

<style lang="scss">
  .btn {
    &.custom-hover {
      &:hover {
        background-color: #eef0fd;
      }
    }
  }

  .course-section-card {
    &.no-header {
      .card-header {
        display: none;
      }
    }
  }
</style>
