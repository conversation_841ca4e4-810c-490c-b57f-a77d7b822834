<template>
  <div class="bg-body rounded-2 py-2 px-3">
    <div class="w-100">
      <BCard no-body>
        <BTabs card>
          <BTab
            :title="$t('admin.course_section_item.type.text')"
            :active="isActiveTab('text')"
            @click="switchItemType('text')"
          >
            <BCardText>
              <div v-if="courseSectionItem.type == 'text'" class="ms-1">
                <QuillEditor v-model="courseSectionItem.content" read-only :site="ROUTE_PREFIX_ENUMS.ADMIN" />
              </div>
            </BCardText>
          </BTab>
          <BTab
            :title="$t('admin.course_section_item.type.video')"
            :active="isActiveTab('video')"
            @click="switchItemType('video')"
          >
            <BCardText>
              <BCardBody v-if="courseSectionItem.videos && courseSectionItem.videos.length > 0">
                <VideoList :videos="courseSectionItem.videos"> </VideoList>
              </BCardBody>

              <BCardBody v-else>
                <DataEmpty class="pb-5" :message="$t('video.data_empty')" height="150" />
              </BCardBody>
            </BCardText>
          </BTab>
          <BTab
            :title="$t('admin.course_section_item.type.drill')"
            :active="isActiveTab('drill')"
            @click="switchItemType('drill')"
          >
            <BCardBody v-if="courseSectionItem.drills && courseSectionItem.drills.length > 0">
              <SectionItemDrills :drills="courseSectionItem.drills || []" :loading="isTableLoading" />
            </BCardBody>

            <BCardBody v-else>
              <DataEmpty class="pb-5" :message="$t('admin.drills.data_empty')" height="150" />
            </BCardBody>
          </BTab>
        </BTabs>
      </BCard>
    </div>

    <BCardBody class="p-0">
      <div class="d-flex justify-content-end gap-2">
        <Button variant="outline-primary" @click="$emit('cancel')">
          {{ $t('common.close') }}
        </Button>
      </div>
    </BCardBody>
  </div>
</template>

<script lang="ts" setup>
  import Button from '@/components/base/Button.vue';
  import DataEmpty from '@/components/utility/DataEmpty.vue';
  import QuillEditor from '@/components/base/quill/QuillEditor.vue';
  import SectionItemDrills from '@/components/admin/drills/SectionItemDrills.vue';
  import VideoList from '@/components/base/video/VideoList.vue';

  import { CourseSectionItemForm } from '@/forms/admin/courseSectionItem';

  import { ROUTE_PREFIX_ENUMS } from '@/utils/constant';

  const props = defineProps({
    sectionItemIdex: {
      type: Number,
      required: true
    },
    courseSectionItem: {
      type: Object as PropType<CourseSectionItemForm>,
      required: true
    }
  });

  defineEmits(['cancel']);

  const isTableLoading = ref<boolean>(false);

  const switchItemType = (type: string) => {
    props.courseSectionItem.type = type;
  };

  const isActiveTab = (tab: string) => {
    return props.courseSectionItem.type === tab;
  };
</script>
