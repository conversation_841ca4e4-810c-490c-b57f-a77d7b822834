<template>
  <BCard class="text-center border border-secondary bg-body mb-2 rounded-2" no-body>
    <div class="d-flex justify-content-between align-items-center py-2 px-3" @click="toggleCollapse">
      <div class="d-flex align-items-center my-0">
        <a class="btn p-0 cursor-pointer border-0">
          <i class="mdi mdi-chevron-down font-size-24 pe-2"></i>
        </a>
        <h5 class="mb-0 pe-2">{{ $t('admin.course_section_item.form.title') }} {{ sectionItemIdex + 1 }} :</h5>
        <i class="mdi pe-1 font-size-16" :class="getIconClass(sectionItem.type)"></i>
        <span>{{ sectionItem.title }}</span>
      </div>
    </div>

    <BCollapse v-model="isCollapsed">
      <BCardBody class="border-top border-secondary">
        <CourseSectionItemDetail
          :course-section-item="courseSectionItem"
          :section-item-idex="sectionItemIdex"
          @cancel="isCollapsed = false"
        />
      </BCardBody>
    </BCollapse>
  </BCard>
</template>

<script setup lang="ts">
  import { CourseSectionItemForm as Form } from '@/forms/admin/courseSectionItem';
  import CourseSectionItemDetail from '@/components/admin/courses/course_section/CourseSectionItemDetail.vue';
  import { adminCourseSectionItemDetail } from '@/services/admin/repositories/courseSectionItem';
  const props = defineProps({
    sectionItem: {
      type: Object as PropType<Form>,
      required: true
    },
    sectionItemIdex: {
      type: Number,
      required: true
    }
  });

  const isCollapsed = ref(false);
  const courseSectionItem = ref(new Form());

  const getIconClass = (item: string) => {
    switch (item) {
      case 'text':
        return 'mdi-file-document-outline';
      case 'video':
        return 'mdi-play-circle-outline';
      case 'drill':
        return 'mdi-vector-square';
      default:
        return '';
    }
  };

  const toggleCollapse = () => {
    isCollapsed.value = !isCollapsed.value;
  };

  watch(isCollapsed, async () => {
    if (courseSectionItem.value.id) {
      await adminCourseSectionItemDetail(courseSectionItem.value.id.toString()).then(res => {
        courseSectionItem.value.assignAttributes(res);
      });
    }
  });

  watch(
    () => props.sectionItem,
    newItem => {
      if (newItem) {
        courseSectionItem.value.assignAttributes(newItem);
      }
    },
    { immediate: true, deep: true }
  );
</script>
