<template>
  <div class="mt-3">
    <div v-for="(item, index) in list" :key="String(item.id)">
      <CourseSectionCard :item="item" :index="index" @refresh="$emit('refresh')" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import CourseSectionCard from '@/components/admin/courses/course_section/CourseSectionCard.vue';
  import { CourseSectionForm } from '@/forms/admin/courseSection';

  defineProps({
    list: {
      type: Array as PropType<CourseSectionForm[]>,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    courseId: {
      type: String,
      required: true
    }
  });
</script>
<style scoped></style>
