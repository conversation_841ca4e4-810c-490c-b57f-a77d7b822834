<template>
  <div>
    <BRow>
      <div
        v-for="(field, key) in courseFields"
        :key="key"
        class="d-flex gap-2 pb-3"
        :class="{
          'align-items-center ': key !== 'description',
          'align-items-start justify-content-start': key == 'description'
        }"
      >
        <div class="d-flex align-items-center gap-2">
          <i :class="field.icon" class="font-size-16"></i>
          <b>{{ $t(`admin.course.course_modal.form.${key}`) }}:</b>
        </div>

        <div v-if="key === 'description' && field.value" class="description">
          <div class="d-flex flex-column" :class="{ 'align-items-center': field.value.toString().length > 150 }">
            <span class="des-ele">
              {{ !isExpanded ? shortenValue(field.value, 150) : field.value }}
              <span
                v-if="!isExpanded && field.value && field.value.toString().length > 150"
                class="expand"
                @click="isExpanded = true"
              >
                {{ $t(`admin.course.course_modal.expand`) }}
              </span>
            </span>
            <span v-if="isExpanded" @click="isExpanded = false" class="expand">
              {{ $t(`admin.course.course_modal.collapse`) }}
            </span>
          </div>
        </div>

        <span v-else>
          {{ field.value }}
        </span>
      </div>

      <div class="d-flex align-items-center gap-2 pb-3">
        <i class="mdi mdi-account font-size-16"></i>
        <b>{{ $t('admin.course.course_modal.form.teacher') }}:</b>
        <div class="d-flex align-items-center gap-2">
          <img
            v-if="course.teacher?.imageUrl"
            :src="course.teacher.imageUrl"
            class="rounded-circle avatar-xs"
            :alt="course.teacher.name"
          />
          <span>{{ course.teacher?.name }}</span>
        </div>
      </div>

      <div class="d-flex align-items-center gap-2 pb-3">
        <i class="mdi mdi-phone font-size-16"></i>
        <b>{{ $t('admin.course.course_modal.form.phone') }}:</b>
        <span>{{ course.teacher?.phoneNumber }}</span>
      </div>

      <div class="d-flex align-items-center gap-2 pb-3">
        <i class="mdi mdi-email font-size-16"></i>
        <b>{{ $t('admin.course.course_modal.form.email') }}:</b>
        <span>{{ course.teacher?.contactEmail }}</span>
      </div>
    </BRow>

    <BRow class="mt-3 pt-3 border-top">
      <BCol>
        <div class="text-end">
          <Button v-if="false" variant="danger" @click="isDeleteModalOpen = true" classes="actions-btn me-2 d-none">
            {{ $t('common.delete') }}
          </Button>

          <Button
            v-if="course.status == 'submitted'"
            classes="actions-btn me-2"
            variant="success"
            @click="isApproveModalOpen = true"
          >
            {{ $t(`common.approved`) }}
          </Button>

          <Button
            v-if="course.status !== 'rejected' && course.status !== 'draft'"
            classes="actions-btn me-2"
            variant="warning"
            :disabled="course.status === 'rejected'"
            @click="isRejectModalOpen = true"
          >
            {{ $t(`common.rejected`) }}
          </Button>
        </div>
      </BCol>
    </BRow>

    <BModal
      v-model="isApproveModalOpen"
      id="modal-standard"
      @ok="handleApprove"
      :ok-title="$t(`common.approved`)"
      :cancel-title="$t('common.cancel')"
      :title="$t(`admin.course.confirm_censor.approve_title`)"
      title-class="font-18"
      size="lg"
      ok-variant="success"
      lazy
    >
      <p>{{ $t(`admin.course.confirm_censor.approved_content`) }}</p>
    </BModal>

    <BModal
      v-model="isRejectModalOpen"
      id="modal-standard"
      :cancel-title="$t('common.cancel')"
      :title="$t(`admin.course.confirm_censor.reject_title`)"
      title-class="font-18"
      size="lg"
      no-footer
      lazy
    >
      <p>{{ $t(`admin.course.confirm_censor.rejected_content`) }}</p>

      <BAlert :model-value="showCourseRejectAlert" variant="warning" class="d-flex mb-4 mx-0 mx-lg-3">
        <i class="mdi mdi-alert-circle-outline lh-1 me-2 font-size-16"></i>
        <div class="d-flex flex-column">
          <h5 class="lh-1">{{ $t('user.practice_submission.form.alert.title') }}</h5>
          <span>{{ $t('admin.course.confirm_censor.reject_alert') }}</span>
        </div>
      </BAlert>

      <BaseFormValidator name="feedback" :label="$t(`admin.course.confirm_censor.feedback`)" required>
        <BFormTextarea
          v-model="feedback"
          :placeholder="$t('admin.course.confirm_censor.reject_placeholder')"
          rows="10"
        />
      </BaseFormValidator>

      <div class="d-flex justify-content-end gap-2 pt-2">
        <Button variant="light" @click="isRejectModalOpen = false">
          {{ $t('common.cancel') }}
        </Button>
        <Button variant="warning" @click="handleReject">
          {{ $t('common.rejected') }}
        </Button>
      </div>
    </BModal>

    <BModal
      v-model="isDeleteModalOpen"
      id="modal-standard"
      @ok="onDelete"
      :ok-title="$t('common.delete')"
      :cancel-title="$t('common.cancel')"
      :title="$t('admin.course.confirm_delete.title')"
      title-class="font-18"
      size="lg"
      ok-variant="danger"
      lazy
    >
      <p>{{ $t('admin.course.confirm_delete.content') }}</p>
    </BModal>
  </div>
</template>

<script lang="ts" setup>
  import { CourseFormInterface } from '@/utils/interface/admin/course';

  import { adminCourseDelete, adminCourseCensor } from '@/services/admin';

  import Button from '@/components/base/Button.vue';

  const route = useRoute();
  const router = useRouter();

  const emits = defineEmits(['refresh']);
  const props = defineProps({
    course: {
      type: Object as PropType<CourseFormInterface>,
      required: true
    }
  });

  const courseForm = ref<CourseFormInterface>({ ...props.course });
  const feedback = ref<string>();
  const isApproveModalOpen = ref<boolean>(false);
  const isDeleteModalOpen = ref<boolean>(false);
  const isRejectModalOpen = ref<boolean>(false);
  const isExpanded = ref<boolean>(false);

  const courseFields = computed(() => ({
    title: {
      value: props.course.title,
      icon: 'mdi mdi-book-open-page-variant'
    },
    description: {
      value: props.course.description,
      icon: 'mdi mdi-text-box-outline'
    },
    status: {
      value: props.course.statusI18n,
      icon: 'mdi mdi-text-box-outline'
    },
    sale_price: {
      value: props.course.salePrice,
      icon: 'mdi mdi-sale'
    },
    price: {
      value: props.course.price,
      icon: 'mdi mdi-cash'
    },
    bonus_point: {
      value: props.course.bonusPoint,
      icon: 'mdi mdi-star-circle'
    },
    bonus_point_percent: {
      value: props.course.bonusPointPercent,
      icon: 'mdi mdi-percent'
    }
  }));

  const showCourseRejectAlert = computed(() => {
    return !!(props.course.joinedUserCount && props.course.joinedUserCount > 0);
  });
  const courseId = route.params.id as string;

  async function onDelete() {
    await adminCourseDelete(courseId).then(() => {
      router.push(`/admin/courses`);
    });
  }

  async function handleApprove() {
    await adminCourseCensor(courseId, 'approved');

    isApproveModalOpen.value = false;
    emits('refresh');
  }

  const handleReject = async () => {
    try {
      await adminCourseCensor(courseId, 'rejected', feedback.value);

      feedback.value = undefined;
      isRejectModalOpen.value = false;
      emits('refresh');
    } catch (e) {
      console.log(e);
    }
  };

  function shortenValue(value: any, maxLength = 50) {
    if (typeof value !== 'string') return value;
    return value.length > maxLength ? value.slice(0, maxLength) + '…' : value;
  }

  watch(
    () => props.course,
    newVal => {
      courseForm.value = { ...newVal };
    },
    { immediate: true, deep: true }
  );
</script>

<style scoped>
  .expand {
    color: grey;
    font-size: small;
    text-decoration: underline;
  }

  .expand:hover {
    cursor: pointer;
    color: black;
  }

  .description {
    width: 80%;
    overflow-wrap: break-word;
    word-break: break-word;

    .des-ele {
      display: block;
      max-width: 100%;
      overflow-wrap: break-word;
      word-break: break-word;
    }
  }
</style>
