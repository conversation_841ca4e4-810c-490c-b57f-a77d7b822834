<template>
  <Loader :loading="loading">
    <div class="table-responsive">
      <TableList
        :fields="fields"
        :items="teachers"
        :metadata="metadata"
        hover
        :tbody-tr-class="rowClass"
        @page-change="pageChange"
      >
        <template #cell(imageUrl)="{ item }">
          <BAvatar :src="item.imageUrl ? item.imageUrl : dummyAvatar" :alt="item.name"></BAvatar>
        </template>

        <template #cell(name)="{ item }">
          <div class="d-flex flex-column">
            <span>{{ item.name }}</span>
            <div v-if="!item.active" class="d-flex align-items-center text-danger gap-1 mt-1">
              <i class="mdi" :class="item.active ? 'mdi-checkbox-marked-circle-outline' : 'mdi-alert-outline'"></i>
              {{ $t('admin.teacher.form.status.description') }}
            </div>
          </div>
        </template>

        <template #cell(canInviteStudents)="{ item }">
          <div class="d-flex justify-content-between">
            <BBadge :variant="getInviteStudentVariant(item.canInviteStudents)">{{
              getTextInviteStudent(item.canInviteStudents)
            }}</BBadge>
          </div>
        </template>

        <template #cell(active)="{ item }">
          <span
            v-if="item"
            class="badge rounded-pill font-size-12 gap-1"
            :class="item.active ? 'badge-soft-success' : 'badge-soft-danger'"
          >
            <i class="mdi" :class="item.active ? 'mdi-checkbox-marked-circle-outline' : 'mdi-alert-outline'"></i>
            {{ item.active ? $t('admin.teacher.form.status.active') : $t('admin.teacher.form.status.de_active') }}
          </span>
        </template>

        <template #cell(actions)="{ item }">
          <BTd class="d-flex justify-content-between">
            <BDropdown class="card-drop mx-auto" variant="white" right toggle-class="p-0">
              <template v-slot:button-content>
                <i class="mdi mdi-dots-horizontal font-size-18"></i>
              </template>

              <BDropdownItem v-if="item.active" @click="handleToggleInvite(item.id)">
                <i class="bx bxs-user-check text-success me-1"></i>
                {{ $t('admin.teacher.form.permitted_to_invite_student_title') }}
              </BDropdownItem>

              <BDropdownItem @click="handleToggleActive(item)">
                <i :class="item.active ? 'bx bxs-user-x text-danger' : 'bx bxs-user-check text-success'" class="me-1" />
                {{ item.active ? $t('admin.teacher.dropdown.de_active') : $t('admin.teacher.dropdown.active') }}
              </BDropdownItem>
            </BDropdown>
          </BTd>
        </template>
      </TableList>
    </div>
  </Loader>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';

  import { MetaDataInterface } from '@/utils/interface/common';
  import { TeacherInterface } from '@/utils/interface/admin/teacher';
  import { SwalIconOptions, SwalOptions } from '@/utils/swal-options';

  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';

  import { toggleTeacherActive } from '@/services/admin/repositories/teacher';

  import useSwal from '@/composable/swal';

  const emit = defineEmits(['fetchList', 'toggleInvite']);
  defineProps({
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    },
    teachers: {
      type: Array as PropType<TeacherInterface[]>,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  });

  const { confirming } = useSwal();

  const fields = ref<{ key: string; label: string; class?: string }[]>([
    { key: 'imageUrl', label: '' },
    { key: 'name', label: i18n.global.t('admin.teacher.form.name'), class: 'min-w-100' },
    { key: 'address', label: i18n.global.t('admin.teacher.form.address'), class: 'min-w-200' },
    { key: 'phoneNumber', label: i18n.global.t('admin.teacher.form.phone'), class: 'min-w-110' },
    { key: 'contactEmail', label: i18n.global.t('admin.teacher.form.email'), class: 'min-w-150' },
    {
      key: 'canInviteStudents',
      label: i18n.global.t('admin.teacher.form.can_invite_students'),
      class: 'min-w-150'
    },
    { key: 'active', label: i18n.global.t('admin.teacher.form.status.label') },
    { key: 'actions', label: i18n.global.t('common.actions'), class: 'min-w-155 text-center' }
  ]);

  const pageChange = (e: { page: number }) => {
    emit('fetchList', e);
  };

  const handleToggleInvite = (id: number) => {
    emit('toggleInvite', id);
  };

  const getTextInviteStudent = (canInvite: boolean) => {
    return canInvite
      ? i18n.global.t('admin.teacher.permitted_to_invite_student')
      : i18n.global.t('admin.teacher.not_permitted_to_invite_student');
  };

  const getInviteStudentVariant = (canInvite: boolean) => {
    return canInvite ? 'success' : 'warning';
  };

  const rowClass = (item: TeacherInterface, type: string) => {
    return type === 'row' && item.active ? '' : 'de-active-row';
  };

  const handleToggleActive = async (teacher: TeacherInterface) => {
    const keyLocale = teacher.active ? 'de_active' : 'active';
    const title = i18n.global.t(`admin.teacher.confirm_modal.${keyLocale}.title`);
    const message = i18n.global.t(`admin.teacher.confirm_modal.${keyLocale}.content`);

    const confirmed = await confirming(
      new SwalOptions({
        title: title,
        html: message,
        icon: SwalIconOptions.Warning
      })
    );

    if (!confirmed) return;

    try {
      await toggleTeacherActive(String(teacher.id));
      teacher.active = !teacher.active;
    } catch (error) {
      console.log(error);
    }
  };
</script>

<style lang="scss" scoped>
  :deep(tr.de-active-row),
  :deep(tr.de-active-row > td) {
    background-color: rgb(254 242 242) !important;
    td {
      &:not(:last-child) {
        opacity: 0.7;
      }
    }
  }
</style>
