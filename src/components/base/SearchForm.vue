<template>
  <BRow class="g-3 m-0 justify-content-end">
    <BCol
      v-for="(searchField, index) in searchFieldsList"
      :key="index"
      :lg="searchField.colSizes?.lg"
      :xxl="searchField.colSizes?.xxl"
    >
      <div class="position-relative form-group">
        <component
          v-model="query[searchField.ransacker]"
          :classes="searchField.options?.classes"
          :is="searchField.component"
          :label="searchField.label"
          :options="searchField.options"
          :title="searchField.title"
        />
        <span :class="searchField.icon"></span>
      </div>
    </BCol>

    <BCol xxl="2" lg="6">
      <Button :disabled="disabled" class="min-h-40 w-100" icon="bx-reset" variant="light" @click="emits('reset')">
        {{ $t('common.reset_btn') }}
      </Button>
    </BCol>
    <BCol xxl="2" lg="6">
      <Button
        :disabled="disabled"
        :variant="searchBtnVariant"
        class="min-h-40 w-100"
        icon="bx-filter-alt"
        @click="$emit('search')"
      >
        {{ $t('common.search_btn') }}
      </Button>
    </BCol>
    <slot></slot>
  </BRow>
</template>

<script lang="ts" setup>
  import { set } from 'lodash';

  import type SearchField from '@/utils/search-fields';

  import Button from '@/components/base/Button.vue';

  const props = defineProps({
    searchFieldsList: {
      type: Array as PropType<SearchField[]>,
      required: true
    },
    disabled: {
      type: Boolean,
      required: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    searchBtnVariant: {
      type: String,
      default: 'primary'
    }
  });
  const query = defineModel('query', {
    type: Object,
    default: {}
  });
  const emits = defineEmits(['reset', 'search']);

  props.searchFieldsList.forEach(field => {
    set(query, field.ransacker, null);
  });
</script>

<style lang="scss" scoped>
  .form-group {
    .form-control {
      padding: 0 40px;
      min-height: 42px;
      font-size: 13px;
    }
    span {
      position: absolute;
      top: 50%;
      left: 15px;
      transform: translateY(-50%);
      line-height: normal;
    }
  }
</style>
