<template>
  <div class="fullscreen-wrapper">
    <div v-if="isFullscreen" class="fullscreen-overlay d-flex justify-content-center align-items-center">
      <div class="image-section image-fullscreen text-center" :class="{ 'image-landscape': isLandscape }">
        <img
          class="w-100"
          :class="{ 'rotated-img': isMobile }"
          :src="src || 'https://placehold.co/400x200'"
          alt="Image"
        />
        <span class="fullscreen-action" @click="toggleFullScreen">
          <i class="mdi lh-1 text-white mdi-fullscreen-exit"></i>
        </span>
      </div>
    </div>

    <div
      v-else
      ref="fullscreenEl"
      class="image-wrapper position-relative d-flex justify-content-center align-items-center w-100"
    >
      <div class="image-section mx-2 mx-lg-0 text-center">
        <img class="w-100" :src="src || 'https://placehold.co/400x200'" alt="Image" />
        <span class="fullscreen-action" @click="toggleFullScreen">
          <i class="mdi lh-1 text-white mdi-fullscreen"></i>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useWindowSize } from '@vueuse/core';

  defineProps({
    src: {
      type: String,
      default: ''
    }
  });

  const isFullscreen = ref(false);
  const isLandscape = ref(false);

  const { width } = useWindowSize();
  const isMobile = computed(() => width.value < 992);

  const updateOrientation = () => {
    isLandscape.value = window.matchMedia('(orientation: landscape)').matches;
  };

  const toggleFullScreen = () => {
    isFullscreen.value = !isFullscreen.value;
  };

  onMounted(() => {
    updateOrientation();
    window.matchMedia('(orientation: landscape)').addEventListener('change', updateOrientation);
  });

  onBeforeUnmount(() => {
    window.matchMedia('(orientation: landscape)').removeEventListener('change', updateOrientation);
  });
</script>
