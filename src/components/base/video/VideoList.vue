<template>
  <Loader :loading="loading">
    <BRow class="d-flex justify-content-center">
      <BCol xl="6" lg="12" sm="12" v-for="video in videos" :key="video.id" class="p-0">
        <div class="video-thumbnail">
          <div class="video-player-wrapper" @click="openVideoPlayer(video)">
            <img class="btn p-0 w-200 video-thumbnail-img" :src="getVideoThumbnail(video)" :alt="video.title" />
            <div class="play-overlay">
              <i class="fas fa-play"></i>
            </div>
          </div>
          <div v-if="allowDelete" class="delete-wrapper">
            <Button
              classes="rounded-circle p-1 font-size-18 d-flex align-items-center justify-content-center"
              icon-only
              icon="bx-x"
              variant="danger"
              :disabled="isDeletingVideo"
              :loading="isDeletingVideo"
              @click="deleteVideo(video.id)"
            ></Button>
          </div>
        </div>
      </BCol>
      <BCol v-if="videos.length === 0" class="text-center">
        <h5 class="text-muted">{{ $t('video.no_video') }}</h5>
      </BCol>
    </BRow>

    <BModal v-model="showVideoPlayer" size="lg" lazy no-footer title="Video Player">
      <VideoPlayer
        :video-id="String(selectedVideo?.id)"
        :thumbnail="selectedVideo?.thumbnailURL ?? ''"
        :video-title="selectedVideo?.title"
      />
    </BModal>
  </Loader>
</template>

<script lang="ts" setup>
  import { VideoInterface } from '@/utils/interface/video';

  import Button from '../Button.vue';
  import VideoPlayer from '../VideoPlayer.vue';

  const emits = defineEmits(['deleteVideo']);
  defineProps({
    loading: {
      type: Boolean,
      default: false
    },
    isDeletingVideo: {
      type: Boolean,
      default: false
    },
    videos: {
      type: Array as PropType<VideoInterface[]>,
      required: true,
      default: () => []
    },
    allowDelete: {
      type: Boolean,
      default: false
    }
  });

  const showVideoPlayer = ref(false);
  const selectedVideo = ref<VideoInterface | null>(null);

  const openVideoPlayer = (video: VideoInterface) => {
    selectedVideo.value = video;
    showVideoPlayer.value = true;
  };

  const getVideoThumbnail = (video: VideoInterface) => {
    return !video.videoPlatforms || video.videoPlatforms.length == 0 || video.videoPlatforms[0].status != 'available'
      ? '/processing-video.png'
      : video.thumbnailURL
        ? video.thumbnailURL
        : '/processing-video.png';
  };

  const deleteVideo = (videoId: number) => {
    emits('deleteVideo', videoId);
  };
</script>

<style scoped lang="scss">
  .video-thumbnail {
    position: relative;

    .video-player-wrapper {
      cursor: pointer;

      &:hover .play-overlay {
        opacity: 1;
      }
    }

    .video-thumbnail-img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .play-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.3);
      opacity: 0;
      transition: opacity 0.3s;

      i {
        font-size: 3rem;
        color: white;
      }
    }
  }

  .invalid-video-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;

    .invalid-video-img {
      max-width: 800px;
      width: 100%;
      height: auto;
      object-fit: contain;
    }
  }

  .delete-wrapper {
    position: absolute;
    top: -10px;
    right: -10px;
    z-index: 1;
    cursor: pointer;
  }
</style>
