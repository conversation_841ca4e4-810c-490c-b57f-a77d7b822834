<template>
  <div>
    <BCard :class="cardClass" no-body>
      <BCardBody class="py-3 px-0 px-lg-3">
        <BCardTitle class="mb-2">{{ $t('video.upload_title') }}</BCardTitle>
        <p class="text-muted">
          {{ $t('video.upload_description') }}
          <span class="text-primary">({{ $t('video.supported_formats') }})</span>
        </p>

        <div v-if="!isUploading && !enableDefaultTitle" class="mb-3">
          <BaseFormValidator :label="$t(`video.description`)" name="title" required>
            <input type="text" class="form-control" v-model="title" />
          </BaseFormValidator>
        </div>

        <!-- Dropzone Area -->
        <div
          class="dropzone border-dashed border-2 rounded p-2 text-center"
          @dragover.prevent
          @drop.prevent="handleDrop"
          @click="triggerFileInput"
        >
          <div class="dz-message needsclick">
            <div class="mb-1">
              <i class="bx bx-cloud-upload fs-1 text-primary"></i>
            </div>
            <h5>{{ $t('video.drop_files') }}</h5>
            <p class="text-muted" style="font-size: 1rem">{{ $t('video.drop_instructions') }}</p>
          </div>
          <input
            type="file"
            ref="fileInput"
            class="dropzoneFile d-none"
            accept="video/mp4,video/x-m4v,video/webm,video/quicktime,video/mpeg,video/ogg,video/x-matroska"
            @change="handleFileSelect"
          />
        </div>

        <!-- File Preview -->
        <ul class="list-unstyled my-2" id="dropzone-preview" v-if="selectedFile">
          <div class="border rounded">
            <div class="file-grid p-2">
              <div class="file-icon">
                <div class="avatar-sm bg-light rounded">
                  <div class="d-flex align-items-center justify-content-center h-100">
                    <i class="bx bxs-video fs-2 text-primary"></i>
                  </div>
                </div>
              </div>
              <div class="file-info">
                <div class="pt-1">
                  <h5 class="fs-14 mb-1 file-name-clamp">{{ selectedFile.name }}</h5>
                  <p class="fs-13 text-muted mb-0">
                    <strong>{{ formatFileSize(selectedFile.size) }}</strong>
                  </p>
                </div>
              </div>
              <div class="file-actions">
                <BButton variant="danger" size="md" @click="deleteSelectedFile" :disabled="isUploading">
                  {{ $t('video.delete') }}
                </BButton>
              </div>
            </div>
          </div>
        </ul>

        <!-- Upload Progress Section -->
        <div v-if="isUploading" class="mb-4 mt-3">
          <div class="d-flex justify-content-between mb-1">
            <span>{{ $t('video.upload_progress.title') }}:</span>
            <span>{{ uploadProgress.percentComplete }}%</span>
          </div>

          <BProgress :value="uploadProgress.percentComplete" :max="100" animated striped class="mb-2">
            <BProgressBar :value="uploadProgress.percentComplete" :max="100">
              {{ uploadProgress.percentComplete }}%
            </BProgressBar>
          </BProgress>
        </div>

        <div class="d-flex gap-2 mt-3">
          <BButton
            variant="primary"
            @click="startUpload"
            :disabled="!selectedFile || isUploading || !title || disabled"
          >
            {{ isUploading ? $t('video.upload_progress.uploading') : $t('video.upload_progress.start_upload') }}
          </BButton>
        </div>

        <!-- Success or Error Message -->
        <div v-if="uploadMessage" class="mt-3">
          <div
            class="alert"
            :class="{
              'alert-success': uploadStatus === 'success',
              'alert-danger': uploadStatus === 'danger'
            }"
            role="alert"
          >
            {{ uploadMessage }}
          </div>
        </div>
      </BCardBody>
    </BCard>
  </div>
</template>

<script setup lang="ts">
  import { useChunkUpload } from '@/composable/upload';
  import type { UploadProgress } from '@/composable/upload';
  import i18n from '@/plugin/i18n';

  const props = defineProps({
    parentId: {
      type: Number,
      required: false
    },
    parentType: {
      type: String,
      required: true
    },
    site: {
      type: String,
      required: true
    },
    enableDefaultTitle: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    cardClass: {
      type: String,
      default: ''
    }
  });

  const fileInput = ref<HTMLInputElement | null>(null);
  const selectedFile = ref<File | null>(null);
  const isUploading = ref(false);
  const uploadStatus = ref<'success' | 'danger' | ''>('');
  const uploadMessage = ref('');
  const cancelToken = ref<boolean>(false);
  const title = ref<string>('');

  const { uploadFileInChunks } = useChunkUpload();

  const emit = defineEmits(['upload-success']);

  // Maximum file size: 1.5GB in bytes
  const MAX_FILE_SIZE = 1.5 * 1024 * 1024 * 1024; // 1.5GB

  // Initialize upload progress state
  const uploadProgress = reactive<UploadProgress>({
    fileId: '',
    uploadedChunks: 0,
    totalChunks: 0,
    percentComplete: 0,
    activeUploads: 0
  });

  const triggerFileInput = () => {
    if (fileInput.value) {
      fileInput.value.click();
    }
  };

  const validateFile = (file: File): { isValid: boolean; errorMessage?: string } => {
    // Check file type
    const validVideoTypes = [
      'video/mp4',
      'video/x-m4v',
      'video/webm',
      'video/quicktime',
      'video/mpeg',
      'video/ogg',
      'video/x-matroska'
    ];

    if (!validVideoTypes.includes(file.type)) {
      return {
        isValid: false,
        errorMessage: i18n.global.t('video.invalid_format')
      };
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return {
        isValid: false,
        errorMessage:
          i18n.global.t('video.file_too_large', {
            maxSize: formatFileSize(MAX_FILE_SIZE)
          }) || `File size exceeds the maximum limit of ${formatFileSize(MAX_FILE_SIZE)}`
      };
    }

    return { isValid: true };
  };

  const handleFileSelect = (event: Event) => {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      const validation = validateFile(file);

      if (validation.isValid) {
        selectedFile.value = file;
        // Reset any previous upload states
        uploadStatus.value = '';
        uploadMessage.value = '';
        if (props.enableDefaultTitle) {
          title.value = file.name;
        }
      } else {
        uploadStatus.value = 'danger';
        uploadMessage.value = validation.errorMessage || i18n.global.t('video.invalid_file');
        // Reset file input
        if (fileInput.value) {
          fileInput.value.value = '';
        }
      }
    }
  };

  const handleDrop = (event: DragEvent) => {
    if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
      const file = event.dataTransfer.files[0];
      const validation = validateFile(file);

      if (validation.isValid) {
        selectedFile.value = file;
        uploadStatus.value = '';
        uploadMessage.value = '';
        if (props.enableDefaultTitle) {
          title.value = file.name;
        }
      } else {
        uploadStatus.value = 'danger';
        uploadMessage.value = validation.errorMessage || i18n.global.t('video.invalid_file');
      }
    }
  };

  const deleteSelectedFile = () => {
    selectedFile.value = null;
    if (fileInput.value) {
      fileInput.value.value = '';
    }
    uploadStatus.value = '';
    uploadMessage.value = '';
  };

  const startUpload = async () => {
    if (!selectedFile.value) return;

    // Double-check file size before upload
    const validation = validateFile(selectedFile.value);
    if (!validation.isValid) {
      uploadStatus.value = 'danger';
      uploadMessage.value = validation.errorMessage || i18n.global.t('video.invalid_file');
      return;
    }

    try {
      // Reset states
      isUploading.value = true;
      uploadStatus.value = '';
      uploadMessage.value = '';
      cancelToken.value = false;

      // Reset progress tracking
      Object.assign(uploadProgress, {
        fileId: '',
        uploadedChunks: 0,
        totalChunks: 0,
        percentComplete: 0,
        activeUploads: 0,
        speed: undefined,
        remainingTime: undefined
      });

      const result = await uploadFileInChunks(
        selectedFile.value,
        {
          parentType: props.parentType,
          parentId: props.parentId,
          title: title.value
        },
        {
          onProgress: progress => {
            // Update the reactive progress object with the latest values
            Object.assign(uploadProgress, progress);

            // Check for cancelation
            if (cancelToken.value) {
              throw new Error(i18n.global.t('video.upload_canceled'));
            }
          },
          site: props.site
        }
      );

      // Show success message
      if (result.success) {
        uploadStatus.value = 'success';
        uploadMessage.value = i18n.global.t('video.upload_success_message', { filename: result.filename });
        emit('upload-success', result.filename, result.videoID);

        // Reset file selection after successful upload
        selectedFile.value = null;
        if (fileInput.value) {
          fileInput.value.value = '';
        }
      } else {
        uploadStatus.value = 'danger';
        uploadMessage.value = i18n.global.t('video.upload_failed', {
          error: result.error || i18n.global.t('video.unknown_error')
        });
      }
    } catch (error) {
      // Show error message
      uploadStatus.value = 'danger';
      uploadMessage.value = i18n.global.t('video.upload_failed', {
        error: error instanceof Error ? error.message : i18n.global.t('video.unknown_error')
      });
      console.error('Upload failed:', error);
    } finally {
      isUploading.value = false;
    }
  };

  // Utility functions for formatting display values
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + i18n.global.t('video.file_size_units.bytes');
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + i18n.global.t('video.file_size_units.kb');
    if (bytes < 1024 * 1024 * 1024)
      return (bytes / (1024 * 1024)).toFixed(2) + i18n.global.t('video.file_size_units.mb');
    return (bytes / (1024 * 1024 * 1024)).toFixed(2) + i18n.global.t('video.file_size_units.gb');
  };
</script>

<style scoped lang="scss">
  .file-grid {
    display: grid;
    grid-template-columns: auto 1fr auto;
    grid-template-areas: 'icon info actions';
    gap: 1rem;
    align-items: center;
  }

  .file-icon {
    grid-area: icon;
  }

  .file-info {
    grid-area: info;
    min-width: 0; /* Allows text truncation */
  }

  .file-actions {
    grid-area: actions;
  }
  .file-name-clamp {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .dropzone {
    cursor: pointer;
    transition: all 0.3s ease;
    height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .dz-message {
      font-size: 24px;
      margin: 1.5rem 0;
    }
  }

  .dropzone:hover {
    background-color: rgba(0, 0, 0, 0.02);
  }

  .border-dashed {
    border-style: dashed !important;
  }

  @media screen and (max-width: 500px) {
    .file-name-clamp {
      max-width: 150px;
    }
    .dropzone {
      padding-block: 10px !important;
      height: 120px !important;
      .dz-message {
        padding: 20px !important;
        font-size: 12px !important;
        i {
          font-size: 20px !important;
        }

        h5 {
          font-size: 14px !important;
        }

        p {
          font-size: 16px !important;
        }
      }
    }
  }
</style>
