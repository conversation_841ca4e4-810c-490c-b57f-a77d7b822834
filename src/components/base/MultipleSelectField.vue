<template>
  <div :class="classes">
    <Multiselect
      v-model="inputValue"
      mode="tags"
      :closeOnDeselect="false"
      :closeOnSelect="false"
      :hideSelected="false"
      :label="options.multipleSelectOptions ? options.multipleSelectOptions.label : undefined"
      :options="options.selectOptions"
      :placeholder="placeHolder"
      :valueProp="options.multipleSelectOptions ? options.multipleSelectOptions.valueProp : undefined"
      @change="emitValue"
    />
  </div>
</template>

<script lang="ts" setup>
  import { SearchFieldOptions } from '@/utils/search-fields';

  type inputValueType = string[] | number[] | Record<string, any>[];

  const props = defineProps({
    classes: {
      type: String,
      default: () => ''
    },
    options: {
      type: Object as PropType<SearchFieldOptions>,
      default: () => ({ selectOptions: [] })
    },
    title: {
      type: String,
      default: () => ''
    },
    modelValue: {
      type: Array,
      default: () => []
    }
  });

  const emit = defineEmits(['update:modelValue']);

  const inputValue = ref<inputValueType>([]);

  const placeHolder = computed(() => {
    if (props.title) return props.title;
    else return 'Select options...';
  });

  function emitValue(value: string[]) {
    emit('update:modelValue', value);
  }

  function clear() {
    inputValue.value = [];
    emit('update:modelValue', []);
  }

  watch(
    () => props.modelValue as inputValueType,
    (value: inputValueType) => {
      if (value === undefined) return;
      inputValue.value = value || [];
    },
    {
      immediate: true
    }
  );

  defineExpose({
    clear
  });
</script>

<style lang="scss" scoped>
  :deep(.multiselect-placeholder),
  :deep(.multiselect-single-label),
  :deep(.multiselect-tags-search),
  :deep(.multiselect-tags) {
    padding-left: 40px;
  }
</style>
