<template>
  <BFormGroup :class="horizontal ? 'horizontal' : 'default'">
    <div :class="{ 'is-invalid': errors }" class="form-element">
      <div class="d-flex justify-content-center gap-3 mb-2">
        <OtpInput v-model="verifyCode" class="justify-content-center" :num-inputs="numInputs"></OtpInput>
      </div>
      <div v-for="(error, index) in errors" :key="index" class="invalid-feedback text-center">
        <span>{{ label }} {{ error }}</span>
      </div>
    </div>
  </BFormGroup>
</template>

<script setup lang="ts">
  import { useGlobalStore } from '@/store/global';
  import OtpInput from './OtpInput.vue';

  const props = defineProps({
    label: String,
    name: String,
    horizontal: Boolean,
    error: String
  });

  const verifyCode = defineModel<string>();
  const numInputs = 6;

  const globalStore = useGlobalStore();

  const errors = computed(() => {
    if (!props.name) return [];
    return globalStore.getErrors(props.name);
  });
</script>
<style scoped>
  .input--active {
    border-color: var(--bs-primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25) !important;
  }

  .is-invalid .invalid-feedback {
    display: block;
  }
</style>
