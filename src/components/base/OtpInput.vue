<template>
  <OtpInput
    v-model:value="otp"
    v-bind="$attrs"
    :num-inputs="6"
    input-classes="otp-input"
    input-type="letter-numeric"
    should-auto-focus
    @on-change="$emit('onChange', $event)"
    @on-complete="$emit('onComplete', $event)"
  />
</template>

<script lang="ts" setup>
  import OtpInput from 'vue3-otp-input';

  defineEmits(['onComplete', 'onChange']);

  const otp = defineModel<string>('modelValue');
</script>

<style>
  .otp-input {
    width: 55px;
    height: 55px;
    padding: 5px;
    margin: 0 7px;
    font-size: 20px;
    border-radius: 10px;
    border: 2px solid #e5e7eb;
    background-color: #f9fafb;
    text-align: center;
    transition: all 0.5s ease;
  }
  @media (max-width: 992px) {
    .otp-input {
      width: 45px;
      height: 45px;
      padding: 5px;
      margin: 0 5px;
      font-size: 18px;
    }
  }

  .otp-input:focus {
    outline: none;
    border-color: #d69a13;
    box-shadow: 0 0 0 4px rgba(236, 172, 36, 0.4);
  }

  .otp-input.is-complete {
    border-color: #d69a13;
    background-color: #fff8f3 !important;
    opacity: 0.7;
  }

  .otp-input.is-complete:focus {
    opacity: 1;
    border-color: #d69a13;
    box-shadow: 0 0 0 4px rgba(236, 172, 36, 0.4);
    transform: scale(1.05);
  }

  .otp-input::-webkit-inner-spin-button,
  .otp-input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
</style>
