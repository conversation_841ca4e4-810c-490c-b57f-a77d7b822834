<template>
  <div class="price-display-container">
    <template v-if="pricing.isFree">
      <span class="free-price text-danger fw-bold fs-5">{{ $t('public.course.free') }}</span>
    </template>

    <template v-else-if="pricing.showOriginalOnly">
      <span class="original-price fw-bold fs-5">
        {{ formattedPrice(price) }}
      </span>
    </template>

    <template v-else-if="pricing.isOnSale">
      <div class="price-wrapper d-flex align-items-center gap-2">
        <div class="price-display">
          <span class="sale-price fw-bold fs-5">{{ formattedPrice(salePrice) }}</span>
          <span class="original-price text-decoration-line-through fs-5">{{ formattedPrice(price) }}</span>
        </div>

        <BBadge v-if="showDiscountBadge" variant="danger" class="discount-badge">
          -{{ getSalePercent(price, salePrice) }}%
        </BBadge>
      </div>
    </template>

    <template v-else>
      <span class="original-price fw-bold fs-5">
        {{ formattedPrice(price) }}
      </span>
    </template>
  </div>
</template>

<script lang="ts" setup>
  import { usePrice } from '@/composable/usePrice';

  const props = defineProps({
    price: {
      type: Number,
      default: 0
    },
    salePrice: {
      type: Number,
      default: 0
    },
    showDiscountBadge: {
      type: Boolean,
      default: false
    }
  });

  const { formattedPrice, getPricingState, getSalePercent } = usePrice();

  const pricing = computed(() => getPricingState(props.price, props.salePrice));
</script>
