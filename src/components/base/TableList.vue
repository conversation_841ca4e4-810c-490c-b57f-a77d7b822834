<template>
  <Loader :loading="loading">
    <BTable
      v-bind="$attrs"
      show-empty
      ref="refSelectableTable"
      :items="computedItems"
      :fields="fields"
      :empty-text="$t(emptyText)"
      :class="{ selectable: selectable, 'table-hover': selectable }"
      :selectable="selectable"
      :select-mode="selectMode"
      @row-clicked="handleRowClicked"
    >
      <template v-for="(_, name) in $slots" :key="name" v-slot:[name]="scope">
        <slot :name="name" v-bind="scope"></slot>
      </template>

      <template v-if="selectable" #head(selected)>
        <BFormCheckbox v-model="isSelectAllComputed" />
      </template>

      <template v-if="selectable" #cell(selected)="{ item }">
        <BFormCheckbox
          :model-value="item.selected"
          @update:model-value="toggleSelect(item)"
          :disabled="item.disabled"
        />
      </template>

      <template #cell(show_details)="row">
        <slot name="show_details" :row="row"></slot>
      </template>

      <template #row-details="row">
        <slot name="row-details" :row="row"></slot>
      </template>
    </BTable>

    <Pagination v-if="metadata" :metadata="metadata" @change="$emit('pageChange', $event)"></Pagination>
  </Loader>
</template>

<script lang="ts" setup>
  import { BTable } from 'bootstrap-vue-next';
  import Pagination from '@/components/base/Pagination.vue';
  import { MetaDataInterface } from '@/utils/interface/common';

  interface TableListSelectableInterface {
    id?: number | string;
    disabled?: boolean;
    selected?: boolean;
  }

  const props = defineProps({
    items: {
      type: Array as PropType<TableListSelectableInterface[]>,
      required: true,
      default: () => []
    },
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    },
    selectable: {
      type: Boolean,
      default: false
    },
    selectMode: {
      type: String,
      default: 'multi'
    },
    emptyText: {
      type: String,
      default: 'common.empty_text'
    }
  });

  const emits = defineEmits(['pageChange', 'rowClicked', 'selectedItemsChange', 'toggleDetails']);

  const selected = ref<TableListSelectableInterface[]>([]);

  const refSelectableTable = ref<InstanceType<typeof BTable> | null>(null);

  const selectedIds = computed(() => selected.value.map(item => item.id));

  const computedItems = computed(() => {
    if (!props.selectable) return props.items;

    return props.items.map(item => {
      if (selectedIds.value.includes(item.id)) {
        item.selected = true;
      } else {
        item.selected = false;
      }
      return item;
    });
  });

  const isSelectAll = computed(() => {
    const itemsIds = computedItems.value.filter(item => !item.disabled).map(item => item.id);

    if (itemsIds.length == 0 || selectedIds.value.length == 0) return false;

    return itemsIds.every(id => selectedIds.value.includes(id));
  });

  const toggleSelect = (row: TableListSelectableInterface) => {
    const index = props.items.findIndex(item => item.id === row?.id);

    if (props.items[index].selected) {
      selectRow(row, index);
    } else {
      unselectRow(row, index);
    }
  };

  const handleRowClicked = (row: TableListSelectableInterface) => {
    const index = props.items.findIndex((item: TableListSelectableInterface) => item.id === row?.id);

    if (selectedIds.value.includes(row.id)) {
      selectRow(row, index);
    } else {
      unselectRow(row, index);
    }
  };

  const selectRow = (row: TableListSelectableInterface, index: number) => {
    refSelectableTable.value?.unselectRow(index);
    selected.value = selected.value.filter(item => item.id !== row?.id);
  };

  const unselectRow = (row: TableListSelectableInterface, index: number) => {
    refSelectableTable.value?.selectRow(index);
    selected.value.push(row);
  };

  const isSelectAllComputed = computed({
    get: () => isSelectAll.value,
    set: value => {
      handleSelectAll(value);
    }
  });

  const handleSelectAll = (value: boolean) => {
    if (computedItems.value.every(item => item.disabled)) return;

    if (value) {
      computedItems.value.forEach(item => {
        if (!item.disabled && !selectedIds.value.includes(item.id)) {
          item.selected = true;
          selected.value.push(item);
        }
      });
    } else {
      refSelectableTable?.value?.clearSelected();
      computedItems.value.forEach(item => {
        item.selected = false;
        selected.value = selected.value.filter(item => item.id !== item.id);
      });
    }
  };

  const resetSelected = () => {
    selected.value = [];
  };

  watch(
    () => selectedIds.value,
    value => {
      if (value) {
        emits('selectedItemsChange', value);
      }
    }
  );

  defineExpose({
    resetSelected
  });
</script>

<style lang="scss" scoped>
  :deep(.table > tbody) {
    vertical-align: middle;
  }

  :deep(.disabled-row) {
    opacity: 0.6;
    pointer-events: none;
    background-color: #f5f5f5;
  }
</style>
