<template>
  <Swiper
    v-if="thumbsSwiper && props.videos.length > 0"
    :key="swiperKey"
    :style="{
      '--swiper-navigation-color': '#fff',
      '--swiper-pagination-color': '#fff'
    }"
    :spaceBetween="10"
    :navigation="!props.disableChanged"
    :allowTouchMove="!props.disableChanged"
    :allowSlideNext="!props.disableChanged"
    :allowSlidePrev="!props.disableChanged"
    :thumbs="{ swiper: thumbsSwiper }"
    :modules="[FreeMode, Navigation, Thumbs]"
    @slide-change="onSlideChange"
    class="swiper-video-wrapper"
  >
    <SwiperSlide v-for="(video, index) in props.videos" :key="'main-' + video.id">
      <slot name="slide" :video="video" :index="index" />
    </SwiperSlide>
  </Swiper>

  <Swiper
    :key="swiperKey + '-thumbs'"
    @swiper="setThumbsSwiper"
    :spaceBetween="10"
    :slidesPerView="4"
    :freeMode="true"
    :watchSlidesProgress="true"
    :modules="[FreeMode, Navigation, Thumbs]"
    class="swiper-thumb-wrapper"
  >
    <SwiperSlide v-for="(video, index) in props.videos" :key="'thumb-' + video.id">
      <slot name="thumb" :video="video" :index="index" />
    </SwiperSlide>
  </Swiper>
</template>

<script lang="ts" setup>
  import { FreeMode, Navigation, Thumbs } from 'swiper/modules';
  import { Swiper as SwiperClass } from 'swiper/types';
  import { Swiper, SwiperSlide } from 'swiper/vue';

  import { VideoInterface } from '@/utils/interface/video';

  const props = defineProps({
    videos: {
      type: Array as PropType<VideoInterface[]>,
      required: true
    },
    disableChanged: {
      type: Boolean,
      default: false
    }
  });

  const emit = defineEmits(['slideChange']);

  const thumbsSwiper = ref<SwiperClass | null>(null);

  const setThumbsSwiper = (swiper: SwiperClass) => {
    nextTick(() => {
      thumbsSwiper.value = swiper;
    });
  };

  const onSlideChange = (swiper: SwiperClass) => {
    emit('slideChange', swiper.activeIndex);
  };

  const swiperKey = computed(() => {
    return thumbsSwiper.value ? props.videos.map(v => v.id).join('-') : 'loading';
  });
</script>
