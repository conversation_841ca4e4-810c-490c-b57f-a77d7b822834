<template>
  <div :class="classes">
    <Multiselect
      v-model="inputValue"
      :label="options.multipleSelectOptions ? options.multipleSelectOptions.label : undefined"
      :options="options.selectOptions"
      :placeholder="placeHolder"
      :valueProp="options.multipleSelectOptions ? options.multipleSelectOptions.valueProp : undefined"
      @change="emitValue"
    ></Multiselect>
  </div>
</template>

<script lang="ts" setup>
  import { SearchFieldOptions } from '@/utils/search-fields';

  type inputValueType = string | number | Record<string, any> | boolean | null;

  const props = defineProps({
    classes: {
      type: String,
      default: () => ''
    },
    options: {
      type: Object as PropType<SearchFieldOptions>,
      default: () => ({ selectOptions: [] })
    },
    title: {
      type: String,
      default: () => ''
    },
    modelValue: {
      type: [String, Number, Object, Boolean],
      default: null
    }
  });

  const emit = defineEmits(['update:modelValue']);

  const inputValue = ref<inputValueType>('');

  const placeHolder = computed(() => {
    if (props.title) return props.title;
    else return 'Search...';
  });

  function emitValue(value: string) {
    emit('update:modelValue', value);
  }

  function clear() {
    inputValue.value = null;
    emit('update:modelValue', null);
  }

  watch(
    () => props.modelValue,
    (value: inputValueType) => {
      if (typeof value !== 'boolean' && !value) {
        clear();
      } else {
        inputValue.value = value;
      }
    },
    {
      immediate: true
    }
  );

  defineExpose({
    clear
  });
</script>

<style lang="scss" scoped>
  :deep(.multiselect-single-label),
  :deep(.multiselect-placeholder) {
    padding-left: 40px;
  }
</style>
