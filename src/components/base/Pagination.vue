<template>
  <BPagination
    v-if="metadata.pages > 1"
    pills
    class="pagination-rounded justify-content-center mt-4"
    align="center"
    :model-value="metadata.page"
    :total-rows="metadata.total"
    :per-page="metadata.perPage"
    @update:model-value="$emit('change', { page: $event })"
    :class="{ 'bg-orange': site == 'user' }"
  ></BPagination>
</template>

<script lang="ts" setup>
  import { MetaDataInterface } from '@/utils/interface/common';

  defineProps({
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    },
    site: {
      type: String,
      required: false
    }
  });

  defineEmits(['change']);
</script>
