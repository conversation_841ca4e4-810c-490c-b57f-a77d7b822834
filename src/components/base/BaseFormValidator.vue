<template>
  <BFormGroup :class="horizontal ? 'horizontal' : 'default'">
    <legend v-if="!hideLabel && label" class="form-label bv-no-focus-ring col-form-label pt-0" :class="legendClasses">
      {{ label }}
      <span v-if="required" class="required">*</span>
      <span v-if="tooltip" v-b-tooltip.hover.right="{ content: tooltip, delay: 0 }" class="ms-1">
        <i class="mdi mdi-information-outline"></i>
      </span>
    </legend>
    <div :class="{ 'is-invalid': errors.length, 'form-only': !label }" class="form-element">
      <slot></slot>
      <span v-if="subtitle" class="text-muted small">
        {{ subtitle }}
      </span>
      <slot name="custom-subtitle"></slot>
      <div v-for="(error, index) in errors" :key="index" class="invalid-feedback text-start">
        <span>{{ label ? label : $t('common.field_label') }} {{ error }}</span>
      </div>
    </div>
  </BFormGroup>
</template>

<script setup lang="ts">
  import { vBTooltip } from 'bootstrap-vue-next';

  import { useGlobalStore } from '@/store/global';

  const props = defineProps({
    label: String,
    name: String,
    horizontal: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    },
    tooltip: {
      type: String,
      default: ''
    },
    legendClasses: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    hideLabel: {
      type: Boolean,
      default: false
    }
  });

  const globalStore = useGlobalStore();

  const errors = computed(() => {
    if (!props.name) return [];
    return globalStore.getErrors(props.name);
  });
</script>

<style lang="scss" scoped>
  .is-invalid {
    .invalid-feedback,
    .invalid-tooltip {
      display: block;
    }

    :deep(.form-control),
    :deep(.form-select),
    :deep(.multiselect) {
      border-color: var(--bs-form-invalid-border-color) !important;
    }
  }

  .horizontal {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;

    legend {
      flex: 0 0 auto;
      width: 16.66666667%;
    }

    .form-element {
      flex: 0 0 auto;
      width: 83.33333333%;

      &.form-only {
        width: 100%;
      }
    }
  }

  .required {
    color: var(--bs-danger);
  }
</style>
