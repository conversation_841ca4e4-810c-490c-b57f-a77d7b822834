<template>
  <div class="course-card-wrapper position-relative">
    <BCard class="course-card" no-body>
      <div class="course-image position-relative" style="display: block">
        <img :src="course.banner ? course.banner : dummyBanner" :alt="course.title" class="card-img-top" />
        <BBadge
          :variant="getLevelVariant(course.instructionalLevel)"
          class="course-level position-absolute d-flex align-items-center p-2 py-1"
          pill
        >
          <i :class="getLevelIcon(course.instructionalLevel)" class="me-1"></i>
          {{ course.instructionalLevelI18n }}
        </BBadge>

        <div class="course-price position-absolute">
          <BBadge v-if="course.processPercent !== undefined" pill class="progress-badge" variant="success">
            <span v-if="course.processPercent >= 100" class="d-flex align-items-center">
              <i class="mdi mdi-check me-1"></i>
              {{ $t('public.course.complete') }}
            </span>
            <span v-else>{{ Math.round(course.processPercent) }}%</span>
          </BBadge>
          <BBadge v-else pill variant="success" class="px-3">
            <PriceDisplay
              class="price-display"
              :price="course.price"
              :salePrice="course.salePrice"
              display-style="compact"
            ></PriceDisplay>
          </BBadge>
        </div>

        <div v-if="course.processPercent !== undefined" class="progress-bar-container">
          <div class="progress-bar-fill" :style="{ width: `${course.processPercent}%` }"></div>
        </div>

        <div class="course-overlay position-absolute d-flex align-items-center justify-content-center">
          <BButton
            variant="primary"
            class="detail-button d-flex align-items-center"
            @click="$router.push(`/courses/${course.slug}`)"
          >
            <i class="mdi mdi-eye me-2"></i>
            {{ $t('public.course.detail') }}
          </BButton>
        </div>
      </div>

      <BCardBody class="course-content d-flex flex-column pt-3">
        <router-link
          :to="`/courses/${course.slug}`"
          class="course-title mb-2 fw-bold text-dark"
          style="text-decoration: none"
        >
          {{ course.title }}
        </router-link>

        <div class="course-instructor d-flex align-items-center mb-3">
          <router-link
            :to="`/teachers/${course.teacher.slug}`"
            target="_blank"
            class="d-flex align-items-center"
            style="text-decoration: none"
          >
            <BAvatar :src="course.teacher.imageUrl ?? dummyAvatar" :alt="course.teacher.name" size="42" class="me-3" />
            <BCardText class="instructor-name mb-0 text-muted fw-medium">
              {{ course.teacher.name }}
            </BCardText>
          </router-link>
        </div>

        <BRow class="course-meta mb-3 w-100">
          <BCol cols="6">
            <div class="meta-item d-flex align-items-center">
              <i class="mdi mdi-clock-outline me-1 text-primary text-info"></i>
              <span class="text-muted fw-medium text-info"
                >{{ course.studyDuration || 0 }} {{ $t('public.course.hours') }}</span
              >
            </div>
          </BCol>
          <BCol cols="5">
            <div class="meta-item d-flex align-items-center">
              <i class="mdi mdi-book-open-variant me-1 text-primary text-info"></i>
              <span class="text-muted fw-medium text-info"
                >{{ course.sectionItemCount || 0 }} {{ $t('public.course.lessons') }}</span
              >
            </div>
          </BCol>
          <BCol cols="6">
            <div class="meta-item d-flex align-items-center">
              <i class="mdi mdi-account-group me-1 text-primary text-info"></i>
              <span class="text-muted fw-medium text-info"
                >{{ course.joinedUserCount || 0 }} {{ $t('public.course.student') }}</span
              >
            </div>
          </BCol>
        </BRow>

        <div class="course-rating d-flex align-items-center">
          <div class="stars me-2">
            <template v-if="course.averageRating">
              <i
                class="mdi mdi-star text-warning fs-5"
                v-for="n in Math.floor(course.averageRating)"
                :key="'full-' + n"
              ></i>
              <i class="mdi mdi-star-half text-warning fs-5" v-if="course.averageRating % 1 >= 0.5"></i>
              <i
                class="mdi mdi-star-outline text-warning fs-5"
                v-for="n in 5 - Math.ceil(course.averageRating)"
                :key="'empty-' + n"
              ></i>
            </template>
            <template v-else>
              <i class="mdi mdi-star-outline text-warning fs-5" v-for="n in 5" :key="'empty-' + n"></i>
            </template>
          </div>
          <span class="text-muted fw-medium"> ({{ course.totalRateCount ?? 0 }}) </span>
        </div>
      </BCardBody>
    </BCard>

    <router-link :to="`/courses/${course.slug}`" class="mobile-card-link" aria-label="View course" />
  </div>
</template>

<script setup lang="ts">
  import dummyBanner from '@/assets/images/dummy_banner.png';
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';
  import PriceDisplay from '@/components/base/PriceDisplay.vue';

  import type { CourseInterface } from '@/utils/interface/public/course';

  interface Props {
    course: CourseInterface;
    isMyCourse?: boolean;
  }

  const props = defineProps<Props>();

  const courseRoute = computed(() => (props.isMyCourse ? 'my-courses' : 'courses'));

  // Function to get level icon
  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'mdi mdi-bullseye';
      case 'intermediate':
        return 'mdi mdi-trending-up';
      case 'advanced':
        return 'mdi mdi-lightning-bolt';
      case 'allLevels':
        return 'mdi mdi-web';
      default:
        return '';
    }
  };

  // Function to get Bootstrap variant for level
  const getLevelVariant = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'success';
      case 'intermediate':
        return 'info';
      case 'advanced':
        return 'danger';
      case 'allLevels':
        return 'secondary';
      default:
        return 'secondary';
    }
  };
</script>

<style lang="scss" scoped>
  .course-card {
    --primary-color: #0a3d62;
    --secondary-color: #3c6382;
    --accent-color: #f39c12;
    --accent-hover: #e67e22;
    --text-color: #2c3e50;
    --light-text: #7f8c8d;
    --light-bg: #f8f9fa;
    --dark-bg: #2c3e50;
    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    --border-radius: 15px;
    --transition: all 0.3s ease;

    position: relative;
    border-radius: var(--border-radius) !important;
    overflow: hidden;
    transition: all 0.4s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin: 7.5px 0;

    &:hover {
      transform: translateY(-7.5px);
      border: 1px solid rgba(243, 156, 18, 0.3);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;

      .course-content:after {
        opacity: 1;
      }

      .course-image {
        &:before {
          opacity: 1;
        }

        img {
          transform: scale(1.3);
        }
      }

      .course-level,
      .course-price {
        transform: translateY(4px);
      }

      .course-title {
        color: var(--accent-color) !important;
      }

      .b-avatar {
        box-shadow: 0 0 0 2px var(--accent-color);
      }

      .course-overlay {
        opacity: 1;
        visibility: visible;
      }
    }
  }

  :deep(.price-display) {
    .price-text {
      font-size: 0.9rem;
    }
    .fw-bold {
      color: #ffffff !important;
    }
  }

  .course-image {
    position: relative;
    width: 100%;
    overflow: hidden;
    aspect-ratio: 4/3;

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.4) 100%);
      z-index: 1;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.5s ease;
    }
  }

  .course-level {
    top: 12px;
    left: 12px;
    z-index: 4;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    font-size: 0.85rem;
    font-weight: 600;

    i {
      font-size: 0.85rem;
    }
  }

  .course-meta {
    flex-wrap: wrap;
    gap: 5px;
    .text-info {
      font-size: 0.85rem;
    }
    .meta-item {
      white-space: nowrap;
    }
  }

  .course-price {
    bottom: 15px;
    right: 15px;
    z-index: 4;
    transition: transform 0.3s ease;
  }

  .progress-badge {
    color: white;
    font-weight: 600;
    font-size: 0.9rem;

    i {
      font-size: 0.9rem;
    }
  }

  .course-content {
    position: relative;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      height: 4px;
      background: linear-gradient(to bottom, var(--accent-color), var(--accent-hover));
      transition: height 0.3s ease;
      opacity: 0;
    }

    .course-card:hover &:after {
      width: 100%;
    }
  }

  .course-title {
    font-size: 1.05rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2; // Show 2 lines max
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    hyphens: auto;
    min-height: 45px;
  }

  .instructor-name {
    font-size: 0.9rem;
    color: #6c757d;
  }

  .b-avatar {
    transition: box-shadow 0.3s ease;
  }

  .meta-item {
    span {
      font-size: 0.95rem;
    }
  }

  .course-rating {
    .stars {
      line-height: 1;
    }

    span {
      font-size: 0.85rem;
    }
  }

  /* Progress Bar Styles */
  .progress-bar-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background-color: rgba(0, 0, 0, 0.2);
    z-index: 4;
  }

  .progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    transition: width 0.7s ease-out;
    border-radius: 0 2px 2px 0;
  }

  /* Course Overlay Styles */
  .course-overlay {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(2px);
  }

  .detail-button {
    background: rgba(0, 0, 0, 0.6);
    border: none;
    padding: 9px 12px;
    font-weight: 600;
    font-size: 0.8rem;
    border-radius: 50px;
    box-shadow: 0 8px 25px rgba(10, 61, 98, 0.3);
    transition: all 0.3s ease;
    transform: translateY(10px);
    text-decoration: none;

    &:hover {
      transform: translateY(0);
      box-shadow: 0 12px 30px rgba(10, 61, 98, 0.4);
      background: #f1b44c;
    }

    &:active {
      transform: translateY(2px);
    }

    i {
      font-size: 1.1rem;
    }
  }

  .course-card:hover .detail-button {
    transform: translateY(0);
  }

  .course-card-link {
    display: none;
  }

  .mobile-card-link {
    display: none;
  }

  @media (max-width: 768px) {
    .mobile-card-link {
      display: block;
      position: absolute;
      inset: 0;
      z-index: 10;
      background: transparent;
      width: 100%;
      height: 100%;
    }
    .course-overlay {
      opacity: 0 !important;
      visibility: hidden !important;
      pointer-events: none !important;
    }
    .detail-button {
      display: none !important;
    }
  }
</style>
