// bootstrap-vue-next components
import {
  BAccordion,
  BAccordionItem,
  BAlert,
  BAvatar,
  BBadge,
  BButton,
  BButtonGroup,
  BCard,
  BCardBody,
  BCardFooter,
  BCardHeader,
  BCardText,
  BCardTitle,
  BCol,
  BCollapse,
  BContainer,
  BDropdown,
  BDropdownDivider,
  BDropdownItem,
  BForm,
  BFormCheckbox,
  BFormCheckboxGroup,
  BFormGroup,
  BFormInput,
  BFormRadio,
  BFormRadioGroup,
  BFormSelect,
  BFormSelectOption,
  BFormTextarea,
  BImg,
  BLink,
  BListGroup,
  BListGroupItem,
  BModal,
  BPagination,
  BProgress,
  BProgressBar,
  BRow,
  BSpinner,
  BTab,
  BTable,
  BTableSimple,
  BTabs,
  BTbody,
  BTd,
  BTh,
  BThead,
  BTr
} from 'bootstrap-vue-next';

import { Cropper } from 'vue-advanced-cropper';

// custom components
import BaseFormValidator from './BaseFormValidator.vue';
import DatePicker from '@/components/base/DatePicker.vue';
import Loader from '@/components/dashboard/Loader.vue';
import Multiselect from '@vueform/multiselect';
import Pagination from './Pagination.vue';
import TableList from '@/components/base/TableList.vue';

// ANIMATION
import BounceIn from '@/components/base/animations/BounceIn.vue';
import FadeInDown from '@/components/base/animations/FadeInDown.vue';
import FadeInLeft from '@/components/base/animations/FadeInLeft.vue';
import FadeInRight from '@/components/base/animations/FadeInRight.vue';
import FadeInUp from '@/components/base/animations/FadeInUp.vue';
import FlipIn from '@/components/base/animations/FlipIn.vue';
import RotateIn from '@/components/base/animations/RotateIn.vue';
import ScaleIn from '@/components/base/animations/ScaleIn.vue';
import SlideRotate from '@/components/base/animations/SlideRotate.vue';
import StaggeredFade from '@/components/base/animations/StaggeredFade.vue';
import ZoomPulse from '@/components/base/animations/ZoomPulse.vue';

import type { Component } from 'vue';

const components: Record<string, Component> = {
  BAccordion,
  BAccordionItem,
  BAlert,
  BAvatar,
  BBadge,
  BButton,
  BButtonGroup,
  BCard,
  BCardBody,
  BCardFooter,
  BCardHeader,
  BCardText,
  BCardTitle,
  BCol,
  BCollapse,
  BContainer,
  BDropdown,
  BDropdownDivider,
  BDropdownItem,
  BForm,
  BFormCheckbox,
  BFormCheckboxGroup,
  BFormGroup,
  BFormInput,
  BFormRadio,
  BFormRadioGroup,
  BFormSelect,
  BFormSelectOption,
  BFormTextarea,
  BImg,
  BLink,
  BListGroup,
  BListGroupItem,
  BModal,
  BPagination,
  BProgress,
  BProgressBar,
  BRow,
  BSpinner,
  BTab,
  BTable,
  BTableSimple,
  BTabs,
  BTbody,
  BTd,
  BTh,
  BThead,
  BTr,
  BaseFormValidator,
  BounceIn,
  Cropper,
  DatePicker,
  FadeInDown,
  FadeInLeft,
  FadeInRight,
  FadeInUp,
  FlipIn,
  Loader,
  Multiselect,
  Pagination,
  RotateIn,
  ScaleIn,
  SlideRotate,
  StaggeredFade,
  TableList,
  ZoomPulse
};

export default components;
