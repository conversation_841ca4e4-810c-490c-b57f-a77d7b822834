<template>
  <div class="d-flex flex-column flex-wrap gap-2" :class="containerClasses">
    <div v-if="pricing.isFree" class="fw-bold text-danger" :class="textAlign">
      <h5 class="mb-0 price-text">{{ $t('public.course.free') }}</h5>
    </div>

    <div v-else-if="pricing.showOriginalOnly" class="fw-bold">
      <h5 class="mb-0 price-text">
        <template v-if="showLabel">{{ $t('public.drills.form.fields.price') }}: </template>
        {{ formattedPrice(price) }}
      </h5>
    </div>

    <div
      v-else-if="pricing.isOnSale && displayStyle === 'compact'"
      class="d-flex align-items-center flex-wrap gap-2"
      :class="justifyContent"
    >
      <del class="text-muted price-text">{{ formattedPrice(price) }}</del>
      <h5 class="fw-bold mb-0 price-text">{{ formattedPrice(salePrice) }}</h5>
    </div>

    <div v-else class="fw-bold">
      <h5 class="mb-0">
        <template v-if="showLabel">{{ $t('public.drills.form.fields.price') }}: </template>
        {{ formattedPrice(salePrice) }}
      </h5>
    </div>

    <div
      v-if="pricing.showSalePercentage && displayStyle === 'full'"
      class="d-flex align-items-center flex-wrap gap-2"
      :class="justifyContent"
    >
      <h6 class="text-success text-uppercase">
        <del class="text-muted price-text">{{ formattedPrice(price) }}</del>
        -{{ salePercent }}%
      </h6>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { usePrice } from '@/composable/usePrice';

  interface Props {
    price?: number;
    salePrice?: number;
    position?: 'start' | 'center' | 'end';
    showLabel?: boolean;
    displayStyle?: 'full' | 'compact';
  }

  const props = withDefaults(defineProps<Props>(), {
    price: 0,
    salePrice: 0,
    position: 'start',
    showLabel: false,
    displayStyle: 'full'
  });

  const { formattedPrice, getPricingState } = usePrice();

  const alignItems = computed(() => `align-items-${props.position}`);
  const containerClasses = computed(() => `${justifyContent.value} ${alignItems.value}`);
  const justifyContent = computed(() => `justify-content-${props.position}`);
  const pricing = computed(() => getPricingState(props.price, props.salePrice));
  const textAlign = computed(() => `text-${props.position}`);

  const salePercent = computed(() => {
    return Math.round(((props.price - props.salePrice) / props.price) * 100);
  });
</script>
