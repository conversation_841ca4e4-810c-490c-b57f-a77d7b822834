<template>
  <BCard
    no-body
    class="drill-card position-relative bg-white overflow-hidden shadow h-100 border rounded-4"
    :class="{ 'drill-card-hover-animation': !disableDrillCardHoverAnimation }"
  >
    <BaseDrillCardHeader
      :drill="drill"
      :hide-teacher="hideTeacher"
      :show-action-button-group="showActionButtonGroup"
      :show-drill-level-badge="drillLevelBadgePosition === EDrillCardLevelBadgePosition.HEADER"
      :site="site"
      @on-delete-click="onDeleteClick"
      @on-edit-click="onEditClick"
      @on-teacher-click="onTeacherClick"
      @on-view-detail-click="onViewDetailClick"
    />

    <BCardBody class="drill-content position-relative p-4 d-flex flex-column justify-content-between">
      <BaseDrillCardContent
        :drill="drill"
        :show-drill-level-badge="drillLevelBadgePosition === EDrillCardLevelBadgePosition.CONTENT"
        @on-skill-click="onSkillClick"
      />

      <BaseDrillCardFooter
        :drill-stats-direction="drillStatsDirection"
        :drill-stats-justifying="drillStatsJustifying"
        :drill="drill"
        :show-price="showPrice"
        :site="site"
      />
    </BCardBody>
  </BCard>
</template>

<script lang="ts" setup>
  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { EDrillCardStatsDirection, EDrillCardStatsJustifying, EDrillCardLevelBadgePosition } from '@/enums/drill';
  import { SITES } from '@/utils/constant';
  import { SiteType } from '@/utils/interface/common';
  import { SkillInterface } from '@/utils/interface/skill';

  import BaseDrillCardContent from './BaseDrillCardContent.vue';
  import BaseDrillCardFooter from './BaseDrillCardFooter.vue';
  import BaseDrillCardHeader from './BaseDrillCardHeader.vue';

  const emits = defineEmits([
    'on-delete-click',
    'on-edit-click',
    'on-skill-click',
    'on-teacher-click',
    'on-view-detail-click'
  ]);
  const props = defineProps({
    drill: {
      type: Object as PropType<DrillInterface>,
      required: true
    },
    site: {
      type: String as PropType<SiteType>,
      required: true
    },
    drillLevelBadgePosition: {
      type: String as PropType<EDrillCardLevelBadgePosition>,
      default: EDrillCardLevelBadgePosition.HEADER
    },
    disableDrillCardHoverAnimation: {
      type: Boolean,
      default: false
    },
    showActionButtonGroup: {
      type: Boolean,
      default: false
    },
    showPrice: {
      type: Boolean,
      default: false
    },
    drillStatsDirection: {
      type: String as PropType<EDrillCardStatsDirection>,
      default: EDrillCardStatsDirection.HORIZONTAL
    },
    drillStatsJustifying: {
      type: String as PropType<EDrillCardStatsJustifying>,
      default: EDrillCardStatsJustifying.SPACE_BETWEEN
    }
  });

  const isAdmin = computed(() => {
    return props.site === SITES.ADMIN;
  });

  const hideTeacher = computed(() => {
    return isAdmin.value;
  });

  const onTeacherClick = () => {
    emits('on-teacher-click', props.drill.owner);
  };

  const onViewDetailClick = () => {
    emits('on-view-detail-click', props.drill);
  };

  const onSkillClick = (skill: SkillInterface) => {
    emits('on-skill-click', skill);
  };

  const onEditClick = () => {
    emits('on-edit-click', props.drill);
  };

  const onDeleteClick = () => {
    emits('on-delete-click', props.drill);
  };
</script>

<style lang="scss" scoped>
  .drill-card {
    transition: all 0.4s ease;

    &.drill-card-hover-animation {
      &:hover {
        transform: translateY(-10px);
      }
    }

    &:hover {
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;
      border-color: rgba(243, 156, 18, 0.2) !important;

      :deep(.drill-card-image) {
        &:before {
          opacity: 1;
        }

        .drill-level {
          transform: translate(-5px, -5px);
        }

        .drill-card-overlay {
          opacity: 1 !important;

          .action-btn {
            transform: translateY(0);
          }
        }

        .drill-instructor {
          transform: translate(-5px, 5px);
        }
      }
    }
  }
</style>
