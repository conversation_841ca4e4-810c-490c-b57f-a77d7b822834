<template>
  <div class="drill-footer d-flex justify-content-between align-items-center pt-3" :class="getDrillFooterHeight()">
    <div class="drill-stats d-flex gap-2" :class="getDrillStatsClass()">
      <div class="drill-stat d-flex align-items-center gap-2 fs-6">
        <i class="bx bx-book-open"></i>
        <span>{{ drill.stepCount }} {{ $t(`${site}.drills.drill_card.card_footer.stats.steps`) }}</span>
      </div>

      <div class="drill-stat d-flex align-items-center gap-2 fs-6">
        <i class="bx bx-video"></i>
        <span>{{ drill.videoCount }} {{ $t(`${site}.drills.drill_card.card_footer.stats.videos`) }}</span>
      </div>
    </div>

    <BasePriceDisplay v-if="showPrice" :price="drill.price" :salePrice="drill.salePrice" />
  </div>
</template>

<script lang="ts" setup>
  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { SiteType } from '@/utils/interface/common';
  import { EDrillCardStatsDirection, EDrillCardStatsJustifying } from '@/enums/drill';

  import BasePriceDisplay from '../BasePriceDisplay.vue';

  const props = defineProps({
    drill: {
      type: Object as PropType<DrillInterface>,
      required: true
    },
    site: {
      type: String as PropType<SiteType>,
      required: true
    },
    showPrice: {
      type: Boolean,
      default: false
    },
    drillStatsDirection: {
      type: String as PropType<EDrillCardStatsDirection>,
      default: EDrillCardStatsDirection.HORIZONTAL
    },
    drillStatsJustifying: {
      type: String as PropType<EDrillCardStatsJustifying>,
      default: EDrillCardStatsJustifying.SPACE_BETWEEN
    }
  });

  const getDrillFooterHeight = () => {
    return props.showPrice ? 'min-h-75' : '';
  };

  const getDrillStatsWidth = () => {
    return props.drillStatsDirection === EDrillCardStatsDirection.HORIZONTAL ? 'w-100' : '';
  };

  const getDrillStatsJustifying = () => {
    return `justify-content-${props.drillStatsJustifying}`;
  };

  const getDrillStatsClass = () => {
    const statsDirectionClass =
      props.drillStatsDirection === EDrillCardStatsDirection.HORIZONTAL ? 'flex-row' : 'flex-column';
    const statsWidthClass = getDrillStatsWidth();
    const statsJustifyingClass = getDrillStatsJustifying();

    return statsDirectionClass + ' ' + statsWidthClass + ' ' + statsJustifyingClass;
  };
</script>

<style lang="scss" scoped>
  .drill-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);

    .drill-stats {
      .drill-stat {
        color: #64748b;
        font-weight: 500;

        i {
          font-size: 1.1rem;
          color: #f39c12;
        }
      }
    }

    :deep(.price-display-container) {
      text-align: right;

      .free-price {
        color: #34c38f !important;
        font-weight: 700 !important;
        font-size: 1.2rem !important;
        background: -webkit-linear-gradient(45deg, #34c38f, #27ae60);
        -webkit-background-clip: text;
        background-clip: text;
      }

      .price-wrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-end !important;

        .sale-price {
          color: #f46a6a;
          font-weight: 700 !important;
          font-size: 1.2rem !important;
          background: -webkit-linear-gradient(45deg, #f46a6a, #e74c3c);
          -webkit-background-clip: text;
          background-clip: text;
        }
        .original-price {
          color: #adb5bd !important;
          font-size: 0.85rem !important;
        }
      }

      .original-price {
        color: #343a40 !important;
        font-weight: 700 !important;
        font-size: 1.2rem !important;
        background: -webkit-linear-gradient(45deg, #343a40, #495057);
        -webkit-background-clip: text;
        background-clip: text;
      }
    }
  }
</style>
