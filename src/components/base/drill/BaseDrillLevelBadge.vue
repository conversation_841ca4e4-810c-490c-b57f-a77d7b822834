<template>
  <div
    class="drill-level text-white rounded-pill d-flex align-items-center gap-1 shadow bg-gradient"
    :class="drillLevel"
  >
    <i class="bx fs-5" :class="getDrillLevelIcon(drillLevel)"></i>
    {{ drillLevelLabel }}
  </div>
</template>

<script lang="ts" setup>
  import { getDrillLevelIcon } from '@/utils/drillHelper';

  import { EDrillLevel } from '@/enums/drill';

  defineProps({
    drillLevel: {
      type: String as PropType<EDrillLevel>,
      required: true
    },
    drillLevelLabel: {
      type: String,
      required: true
    }
  });
</script>

<style lang="scss" scoped>
  .drill-level {
    transition: all 0.3s ease;

    &.beginner {
      background-color: #27ae60;
    }

    &.intermediate {
      background-color: #2980b9;
    }

    &.advanced {
      background-color: #c0392b;
    }

    &.expert {
      background-color: #8e44ad;
    }
  }
</style>
