<template>
  <div class="drill-content-body">
    <div class="drill-meta fs-6 d-flex mb-2 gap-2 align-items-center">
      <BaseDrillLevelBadge
        v-if="showDrillLevelBadge"
        :drill-level="drill.level"
        :drill-level-label="drill.levelI18n"
      ></BaseDrillLevelBadge>

      <!-- TODO: Drill view count -->
      <span class="text-muted small"><i class="fas fa-eye me-1"></i>0</span>
      <span class="text-muted small">
        <i class="fas fa-calendar-alt me-1"></i>{{ filters.formatDateUTC_DDMMYYYY(drill.createdAt) }}
      </span>
    </div>

    <h3 class="drill-title fs-4 mb-3 lh-base line-clamp-2">
      {{ drill.title }}
    </h3>

    <div class="drill-skills d-flex flex-wrap gap-2 line-clamp-2-32">
      <template v-for="skill in drill.skills" :key="skill.id">
        <span class="skill-badge rounded-4 px-2 py-1" @click="onSkillClick(skill)">
          {{ skill.nameI18n }}
        </span>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import filters from '@/utils/filters';
  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { SkillInterface } from '@/utils/interface/skill';

  import BaseDrillLevelBadge from './BaseDrillLevelBadge.vue';

  const emits = defineEmits(['on-skill-click']);
  defineProps({
    drill: {
      type: Object as PropType<DrillInterface>,
      required: true
    },
    showDrillLevelBadge: {
      type: Boolean,
      default: false
    }
  });

  const onSkillClick = (skill: SkillInterface) => {
    emits('on-skill-click', skill);
  };
</script>

<style lang="scss" scoped>
  .drill-title {
    font-weight: 700;
    transition: all 0.3s ease;

    &:hover {
      cursor: pointer;
      color: #f1b44c !important;
    }
  }

  .drill-skills {
    .skill-badge {
      background-color: rgba(85, 110, 230, 0.1);
      color: #556ee6;
      font-size: 0.75rem;
      font-weight: 500;
      transition: all 0.2s ease;

      &:hover {
        cursor: pointer;
        background-color: rgba(85, 110, 230, 0.2);
      }
    }
  }
</style>
