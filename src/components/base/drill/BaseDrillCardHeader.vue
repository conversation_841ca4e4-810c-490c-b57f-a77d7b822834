<template>
  <BCardHeader class="drill-card-image position-relative overflow-hidden rounded-4 p-0">
    <img class="w-100 h-100 object-fit-cover" :src="drillDiagramsImage" :alt="drill.title" />

    <BaseDrillLevelBadge
      v-if="showDrillLevelBadge"
      class="position-absolute top-15-px left-15-px py-1 px-3 z-1 fw-semibold"
      :drill-level-label="drill.levelI18n"
      :drill-level="drill.level"
    ></BaseDrillLevelBadge>

    <div
      class="drill-card-overlay position-absolute top-0-px left-0-px w-100 h-100 d-flex align-items-center justify-content-center opacity-0 z-1"
    >
      <div class="drill-actions d-flex align-items-center gap-2" v-if="showActionButtonGroup">
        <Button
          circle
          classes="action-btn height-30 width-30"
          hide-border
          icon-only
          icon="bx-show"
          @click="onViewDetailClick"
        ></Button>
        <Button
          circle
          classes="action-btn height-30 width-30"
          hide-border
          icon-only
          icon="bx-pencil"
          @click="onEditClick"
        ></Button>
        <Button
          circle
          classes="action-btn hover-danger height-30 width-30"
          hide-border
          icon-only
          icon="bx-trash"
          @click="onDeleteClick"
        ></Button>
      </div>

      <Button
        v-else
        classes="action-btn px-3 py-2 fw-semibold border border-0"
        hide-border
        icon="bx-show"
        pill
        @click="onViewDetailClick"
      >
        {{ $t(`${site}.drills.drill_card.action_btn.view_detail`) }}
      </Button>
    </div>

    <div
      v-if="!hideTeacher"
      class="drill-instructor bottom-15-px left-15-px py-1 ps-1 pe-2 position-absolute rounded-pill d-flex align-items-center z-1"
      @click="onTeacherClick"
    >
      <template v-if="drill.ownerType === EDrillOwnerType.TEACHER">
        <BAvatar :size="35" :src="ownerAvatarUrl" :alt="drill.owner.name" class="instructor-avatar me-2" />
        <span class="instructor-name fs-6 text-white">{{ drill.owner.name }}</span>
      </template>

      <template v-else>
        <i class="bx bx-bot rounded-circle text-white fs-3 p-1 me-2 instructor-avatar"></i>
        <span class="instructor-name fs-6 text-white">
          {{ $t(`${site}.drills.drill_card.card_header.instructor.admin`) }}
        </span>
      </template>
    </div>
  </BCardHeader>
</template>

<script lang="ts" setup>
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';

  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { SiteType } from '@/utils/interface/common';

  import { EDrillOwnerType } from '@/enums/drill';

  import BaseDrillLevelBadge from './BaseDrillLevelBadge.vue';
  import Button from '../Button.vue';

  const emits = defineEmits(['on-teacher-click', 'on-view-detail-click', 'on-edit-click', 'on-delete-click']);
  const props = defineProps({
    drill: {
      type: Object as PropType<DrillInterface>,
      required: true
    },
    site: {
      type: String as PropType<SiteType>,
      required: true
    },
    hideTeacher: {
      type: Boolean,
      default: false
    },
    showDrillLevelBadge: {
      type: Boolean,
      default: false
    },
    showActionButtonGroup: {
      type: Boolean,
      default: false
    }
  });

  const drillDiagramsImage = computed(() => {
    const diagrams = props.drill.diagrams;

    return diagrams && diagrams.length && diagrams[0].imageUrl ? diagrams[0].imageUrl : '/pool-table.png';
  });

  const ownerAvatarUrl = computed(() => {
    const owner = props.drill.owner;
    return owner ? (owner.imageUrl ? owner.imageUrl : dummyAvatar) : dummyAvatar;
  });

  const onTeacherClick = () => {
    emits('on-teacher-click');
  };

  const onViewDetailClick = () => {
    emits('on-view-detail-click');
  };

  const onEditClick = () => {
    emits('on-edit-click');
  };

  const onDeleteClick = () => {
    emits('on-delete-click');
  };
</script>

<style lang="scss" scoped>
  .drill-card-image {
    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.4) 100%);
      z-index: 1;
      opacity: 0;
      transition: all 0.3s ease;
    }

    img {
      transition: all 0.5s ease;
    }

    .drill-card-overlay {
      transition: all 0.3s ease;

      .action-btn {
        background: rgba(0, 0, 0, 0.6);
        transform: translateY(20px);
        transition: all 0.3s ease;

        &:hover {
          &.hover-danger {
            background-color: #dc3545;
          }

          background-color: #f1b44c;
        }
      }
    }

    .drill-instructor {
      background: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(4px);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;

      .instructor-avatar {
        border: 2px solid #f8f9fa;
      }

      .instructor-name {
        font-weight: 500;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        transition: all 0.3s ease;
      }

      &:hover {
        cursor: pointer;

        .instructor-avatar {
          border-color: #f1b44c;
        }

        .instructor-name {
          color: #f1b44c !important;
        }
      }
    }
  }
</style>
