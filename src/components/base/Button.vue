<template>
  <BButton
    class="bg-gradient"
    loading-text=""
    :class="getButtonClass()"
    :disabled="disabled"
    :loading="loading"
    :pill="pill"
    :size="size"
    :type="type"
    :variant="variant"
    @click="$emit('click')"
  >
    <i v-if="icon" class="bx align-middle" :class="iconStyle()"></i>
    <template v-if="!iconOnly">
      <template v-if="icon">&nbsp;</template><slot></slot><template v-if="appendIcon">&nbsp;</template>
    </template>
    <i v-if="appendIcon" class="bx align-middle" :class="iconStyle(true)"></i>
  </BButton>
</template>

<script lang="ts" setup>
  defineEmits(['click']);
  const props = defineProps({
    disabled: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    variant: {
      type: String,
      default: 'primary'
    },
    classes: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },
    appendIcon: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'button'
    },
    size: {
      type: String,
      default: 'md'
    },
    iconOnly: {
      type: Boolean,
      default: false
    },
    iconSize: {
      type: String,
      default: ''
    },
    pill: {
      type: Boolean,
      default: false
    },
    circle: {
      type: Boolean,
      default: false
    },
    hideBorder: {
      type: Boolean,
      default: false
    }
  });

  const iconStyle = (isAppend = false) => {
    const iconSize = 'fs-' + props.iconSize;
    const iconClass = isAppend ? props.appendIcon : props.icon;

    return iconSize + ' ' + iconClass;
  };

  const getButtonClass = () => {
    const hideBorderClass = props.hideBorder ? 'border border-0' : '';
    const circleClass = props.circle ? 'rounded-circle' : '';
    const baseClass = 'd-flex align-items-center justify-content-center';

    return baseClass + ' ' + props.classes + ' ' + hideBorderClass + ' ' + circleClass;
  };
</script>
