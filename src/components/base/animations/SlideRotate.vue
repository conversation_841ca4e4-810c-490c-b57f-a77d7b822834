<template>
  <AnimatePresence mode="wait">
    <motion.div
      :initial="{ x: 50, rotate: 15, opacity: 0 }"
      :animate="{ x: 0, rotate: 0, opacity: 1 }"
      :exit="{ x: -50, rotate: -15, opacity: 0 }"
      :transition="{ duration: 0.35, type: 'spring', stiffness: 150, damping: 20 }"
    >
      <slot />
    </motion.div>
  </AnimatePresence>
</template>

<script lang="ts" setup>
  import { motion, AnimatePresence } from 'motion-v';
</script>

<style scoped>
  :slotted(*) {
    display: inline-block;
  }
</style>
