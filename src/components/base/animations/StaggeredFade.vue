<template>
  <AnimatePresence mode="wait">
    <motion.div
      :initial="{ opacity: 0 }"
      :animate="{ opacity: 1, transition: { staggerChildren: 0.1 } }"
      :exit="{ opacity: 0 }"
    >
      <slot />
    </motion.div>
  </AnimatePresence>
</template>

<script lang="ts" setup>
  import { motion, AnimatePresence } from 'motion-v';
</script>

<style scoped>
  :slotted(*) {
    display: block;
  }

  :slotted(.stagger-child) {
    opacity: 0;
  }

  :slotted(.stagger-child) {
    transition:
      opacity 0.3s,
      transform 0.3s;
    transform: translateY(10px);
  }

  :slotted(.stagger-child.animate) {
    opacity: 1;
    transform: translateY(0);
  }
</style>
