<template>
  <AnimatePresence mode="wait">
    <motion.div
      :initial="{ rotateX: 90, opacity: 0, y: 30 }"
      :animate="{ rotateX: 0, opacity: 1, y: 0 }"
      :exit="{ rotateX: -90, opacity: 0, y: 30 }"
      :transition="{ duration: 0.4, ease: 'easeOut' }"
    >
      <slot />
    </motion.div>
  </AnimatePresence>
</template>

<script lang="ts" setup>
  import { motion, AnimatePresence } from 'motion-v';
</script>

<style scoped>
  :slotted(*) {
    display: inline-block;
    transform-style: preserve-3d;
  }
</style>
