<template>
  <AnimatePresence mode="wait">
    <motion.div
      :initial="{ scale: 0.7, opacity: 0 }"
      :animate="{ scale: [1, 1.05, 1], opacity: 1 }"
      :exit="{ scale: 0.7, opacity: 0 }"
      :transition="{ duration: 0.5, times: [0, 0.5, 1], repeat: 1, ease: 'easeInOut' }"
    >
      <slot />
    </motion.div>
  </AnimatePresence>
</template>

<script lang="ts" setup>
  import { motion, AnimatePresence } from 'motion-v';
</script>

<style scoped>
  :slotted(*) {
    display: inline-block;
  }
</style>
