<template>
  <AnimatePresence mode="wait">
    <motion.div
      :initial="{ scale: 0.5, opacity: 0, y: 20 }"
      :animate="{ scale: 1, opacity: 1, y: 0 }"
      :exit="{ scale: 0.5, opacity: 0, y: 20 }"
      :transition="{ type: 'spring', stiffness: 200, damping: 15, duration: 0.3 }"
    >
      <slot />
    </motion.div>
  </AnimatePresence>
</template>

<script lang="ts" setup>
  import { motion, AnimatePresence } from 'motion-v';
</script>

<style scoped>
  :slotted(*) {
    display: inline-block;
  }
</style>
