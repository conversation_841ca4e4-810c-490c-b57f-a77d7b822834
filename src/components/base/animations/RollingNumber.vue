<template>
  <div ref="containerRef" class="rolling-number d-flex overflow-hidden"
    :style="{ width: `${digits.length * digitWidth}px` }">
    <div 
      v-for="(digit, index) in digits" 
      class="digit-column overflow-hidden position-relative" 
      :key="index" 
      :class="{'decimal-point': digit === -1}"
      :style="{ height: `${digitHeight}px`, width: `${digitWidth}px` }">

      <div v-if="digit === -1" class="decimal-point"
        :style="{ height: `${digitHeight}px`, lineHeight: `${digitHeight}px` }">
        .
      </div>

      <div v-else class="digit-scroll d-flex flex-column" :class="{ animating: isAnimating }" :style="{
        transform: animationReady ? `translateY(-${(digit + loopOffset) * digitHeight}px)` : 'translateY(0)'
      }">
        <div v-for="n in totalDigits" class="digit" :key="n" :style="{
          height: `${digitHeight}px`,
          lineHeight: `${digitHeight}px`
        }">
          {{ (n - 1) % 10 }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  target: {
    type: Number,
    required: true
  },
  digitHeight: {
    type: Number,
    default: 40
  },
  digitWidth: {
    type: Number,
    default: 25
  },
  threshold: {
    type: Number,
    default: 0.5
  }
});

const containerRef = ref<HTMLElement | null>(null);
const digits = ref<number[]>([]);
const isAnimating = ref<boolean>(false);
const animationReady = ref<boolean>(false);
const hasAnimated = ref<boolean>(false);

let observer: IntersectionObserver | null = null;

const loopOffset = 10;
const totalDigits = 30;

const formatDigits = (num: number) => {
  const numStr = String(num);
  return numStr
    .split('')
    .map(char => {
      if (char === '.') {
        return -1;
      }
      return Number(char);
    });
};

const startAnimation = async () => {
  if (hasAnimated.value) return;

  digits.value = formatDigits(props.target);

  isAnimating.value = false;
  animationReady.value = false;

  await nextTick();

  requestAnimationFrame(() => {
    animationReady.value = true;
    isAnimating.value = true;
    hasAnimated.value = true;

    setTimeout(() => {
      isAnimating.value = false;
    }, 3000);
  });
};

const setupIntersectionObserver = () => {
  if (!containerRef.value) return;

  observer = new IntersectionObserver(
    (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !hasAnimated.value) {
          startAnimation();
        }
      });
    },
    {
      threshold: props.threshold,
      rootMargin: '0px'
    }
  );

  observer.observe(containerRef.value);
};

onMounted(() => {
  digits.value = formatDigits(props.target);

  nextTick(() => {
    setupIntersectionObserver();
  });
});

onUnmounted(() => {
  if (observer) {
    observer.disconnect();
  }
});

watch(
  () => props.target,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      hasAnimated.value = false;
      if (containerRef.value) {
        const rect = containerRef.value.getBoundingClientRect();
        const isVisible = rect.top < window.innerHeight && rect.bottom > 0;

        if (isVisible) {
          startAnimation();
        }
      }
    }
  }
);
</script>

<style lang="scss" scoped>
.digit-scroll {
  transition: transform 3s cubic-bezier(0.2, 1.4, 0.4, 1);
}

.decimal-point {
  width: fit-content !important;
}
</style>
