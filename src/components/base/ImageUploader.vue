<template>
  <BCard no-body class="shadow-none p-0 m-0">
    <BCardBody class="p-0" :class="{ 'd-none': hideDropZone }">
      <BCardTitle class="mb-2">
        {{ $t('uploader.image.upload_title') }}
      </BCardTitle>

      <p class="text-muted">
        {{ $t('uploader.image.upload_description') }}
        <span class="text-primary">({{ $t('uploader.image.supported_formats') }})</span>
      </p>

      <div
        class="dropzone border-dashed border-2 rounded p-2 text-center"
        :class="{ disabled: isUploading }"
        @click="triggerFileInput"
        @dragover.prevent
        @drop.prevent="handleDrop"
      >
        <div class="dz-message needsclick">
          <div class="mb-1">
            <i class="bx bx-cloud-upload fs-1 text-primary"></i>
          </div>
          <h5>
            {{ $t('uploader.image.drop_files') }}
          </h5>
          <p class="text-muted">
            {{ $t('uploader.image.drop_instructions') }}
          </p>
        </div>

        <input
          class="dropzoneFile d-none"
          ref="fileInput"
          type="file"
          :accept="htmlAcceptedImageType"
          :disabled="isUploading"
          @change="handleFileSelect"
        />
      </div>

      <div v-if="uploadMessage" class="mt-3">
        <div
          class="alert"
          :class="{
            'alert-success': uploadStatus === THEME_COLOR_ENUMS.SUCCESS,
            'alert-danger': uploadStatus === THEME_COLOR_ENUMS.DANGER,
            'alert-info': uploadStatus === THEME_COLOR_ENUMS.INFO
          }"
          role="alert"
        >
          {{ uploadMessage }}
        </div>
      </div>
    </BCardBody>

    <BModal
      v-model="showCropper"
      :title="$t('common.crop_image')"
      no-close-on-backdrop
      scrollable
      size="lg"
      @hidden="closeCropper"
      @ok="cropImage"
    >
      <div class="cropper-container">
        <div class="cropper-wrapper">
          <Cropper
            ref="cropperRef"
            :canvas="canvasSettings"
            :src="originalImageSrc"
            :stencil-component="stencilComponent"
            :stencil-props="stencilProps"
            class="cropper"
          />
        </div>
      </div>
    </BModal>
  </BCard>
</template>

<script setup lang="ts">
  import i18n from '@/plugin/i18n';

  import { Cropper, RectangleStencil } from 'vue-advanced-cropper';

  import { upload } from '@/services/base/repositories/file';

  import {
    ASPECT_RATIO_ENUMS,
    FILE_TYPE_ENUMS,
    IMAGE_SMOOTHING_QUALITY_ENUMS,
    THEME_COLOR_ENUMS
  } from '@/utils/constant';

  const emits = defineEmits(['uploadStart', 'uploadError', 'uploadSuccess']);
  const url = defineModel<string>('url', {
    default: ''
  });
  const props = defineProps({
    stencilComponent: {
      type: Object as PropType<typeof RectangleStencil>,
      default: RectangleStencil
    },
    showCropper: {
      type: Boolean,
      default: false
    },
    defaultAspectRatio: {
      type: Number,
      default: ASPECT_RATIO_ENUMS.FREE
    },
    cropperMaxWidth: {
      type: Number,
      default: 1200
    },
    cropperMaxHeight: {
      type: Number,
      default: 675
    },
    hideDropZone: {
      type: Boolean,
      default: false
    },
    site: {
      type: String,
      required: true
    },
    uploadPath: {
      type: String,
      default: 'uploads'
    },
    requireDependedRef: {
      type: Boolean,
      default: false
    }
  });

  type uploadStatusType =
    | typeof THEME_COLOR_ENUMS.SUCCESS
    | typeof THEME_COLOR_ENUMS.DANGER
    | typeof THEME_COLOR_ENUMS.INFO
    | '';
  type acceptedImageTypeType =
    | typeof FILE_TYPE_ENUMS.PNG
    | typeof FILE_TYPE_ENUMS.JPEG
    | typeof FILE_TYPE_ENUMS.GIF
    | typeof FILE_TYPE_ENUMS.JPG;

  const acceptedImageType = ref<acceptedImageTypeType[]>([
    FILE_TYPE_ENUMS.PNG,
    FILE_TYPE_ENUMS.JPEG,
    FILE_TYPE_ENUMS.GIF,
    FILE_TYPE_ENUMS.JPG
  ]);
  const cropperRef = ref<InstanceType<typeof Cropper> | null>(null);
  const dependedRef = ref<InstanceType<any> | null>(null);
  const fileInput = ref<HTMLInputElement | null>(null);
  const htmlAcceptedImageType = ref<string>(acceptedImageType.value.join(','));
  const isUploading = ref<boolean>(false);
  const originalImageSrc = ref<string>();
  const selectedFile = ref<File | null>(null);
  const showCropper = ref<boolean>(false);
  const uploadMessage = ref<string>('');
  const uploadStatus = ref<uploadStatusType>('');

  const stencilProps = computed(() => ({
    aspectRatio: props.defaultAspectRatio
  }));
  const canvasSettings = computed(() => ({
    imageSmoothingEnabled: true,
    imageSmoothingQuality: IMAGE_SMOOTHING_QUALITY_ENUMS.HIGH,
    maxHeight: props.cropperMaxHeight,
    maxWidth: props.cropperMaxWidth
  }));

  const triggerFileInput = () => {
    if (fileInput.value) {
      fileInput.value.click();
    }
  };

  const validateFile = (
    file: File
  ): {
    isValid: boolean;
    errorMessage?: string;
  } => {
    if (!acceptedImageType.value.includes(file.type as acceptedImageTypeType)) {
      return {
        isValid: false,
        errorMessage: i18n.global.t('uploader.image.invalid_format')
      };
    }

    return {
      isValid: true
    };
  };

  const handleGetFile = (file: File) => {
    if (props.showCropper) {
      const reader = new FileReader();

      reader.onload = e => {
        originalImageSrc.value = e.target?.result as string;
        showCropper.value = true;
      };
      reader.readAsDataURL(file);
    } else {
      validateAndUpload(file);
    }
  };

  const validateAndUpload = async (file: File) => {
    if (props.requireDependedRef && !dependedRef.value) return;

    const validation = validateFile(file);

    if (validation.isValid) {
      selectedFile.value = file;
      uploadStatus.value = '';
      uploadMessage.value = '';

      await startUpload();
    } else {
      uploadStatus.value = THEME_COLOR_ENUMS.DANGER;
      uploadMessage.value = validation.errorMessage || 'Invalid file';
      if (fileInput.value) {
        fileInput.value.value = '';
      }
    }
  };

  const handleDrop = (event: DragEvent) => {
    if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
      const file = event.dataTransfer.files[0];

      handleGetFile(file);
    }
  };

  const handleFileSelect = (event: Event) => {
    const input = event.target as HTMLInputElement;

    if (input.files && input.files.length > 0) {
      const file = input.files[0];

      handleGetFile(file);
    }
  };

  const startUpload = async () => {
    if (!selectedFile.value) return;

    const validation = validateFile(selectedFile.value);
    if (!validation.isValid) {
      uploadStatus.value = THEME_COLOR_ENUMS.DANGER;
      uploadMessage.value = validation.errorMessage || 'Invalid file';
      return;
    }

    try {
      emits('uploadStart');

      isUploading.value = true;
      uploadStatus.value = THEME_COLOR_ENUMS.INFO;
      uploadMessage.value = i18n.global.t('uploader.image.upload_progress.uploading');

      const formData = new FormData();
      formData.append('files[]', selectedFile.value);

      const res = await upload(formData, props.uploadPath, props.site);
      url.value = res.data[0].url;
      uploadStatus.value = THEME_COLOR_ENUMS.SUCCESS;
      uploadMessage.value = i18n.global.t('uploader.image.upload_progress.success');
      selectedFile.value = null;

      if (fileInput.value) {
        fileInput.value.value = '';
      }

      emits('uploadSuccess');
    } catch (error) {
      uploadStatus.value = THEME_COLOR_ENUMS.DANGER;
      uploadMessage.value = `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`;

      emits('uploadError');
    } finally {
      isUploading.value = false;
      uploadStatus.value = '';
      uploadMessage.value = '';
    }
  };

  const closeCropper = () => {
    showCropper.value = false;
  };

  const cropImage = async () => {
    if (!cropperRef.value) return;

    const { canvas } = cropperRef.value.getResult();

    if (!canvas) return;

    const blob = await new Promise<Blob>(resolve => {
      canvas.toBlob(
        blob => {
          if (blob) resolve(blob);
        },
        FILE_TYPE_ENUMS.JPEG,
        1
      );
    });

    const fileName = `cropped-banner-${Date.now()}.jpeg`;
    const croppedFile = new File([blob], fileName, {
      type: FILE_TYPE_ENUMS.JPEG
    });

    validateAndUpload(croppedFile);
  };

  defineExpose({
    dependedRef,
    triggerFileInput
  });
</script>

<style scoped lang="scss">
  .dropzone {
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 150px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .dz-message {
      margin: 1.5rem 0;
    }

    &.disabled {
      filter: blur(2px);
      background: rgba(0, 0, 0, 0.1);
      cursor: not-allowed;
    }
  }

  .dropzone:hover:not(.disabled) {
    background-color: rgba(0, 0, 0, 0.02);
  }

  .border-dashed {
    border-style: dashed !important;
  }
</style>
