<template>
  <div class="base-carousel-wrapper">
    <Swiper
      class="base-carousel h-100"
      :auto-height="autoHeight"
      :breakpoints="breakpoints"
      :class="direction"
      :direction="direction"
      :modules="[Navigation]"
      :slides-per-view="1"
      :space-between="spaceBetween"
      :style="{ height: carouselWrapperHeight + '!important' }"
      @swiper="onSwiperInit"
    >
      <SwiperSlide v-for="(item, index) in items" :key="index">
        <slot :item="item" :index="index"></slot>
      </SwiperSlide>
    </Swiper>

    <div v-if="!hideCarouselNav" class="carousel-nav">
      <button @click="goTo('prev')" :disabled="swiper?.isBeginning" class="prev-btn">
        <i class="mdi mdi-chevron-left"></i>
      </button>
      <button @click="goTo('next')" :disabled="swiper?.isEnd" class="next-btn">
        <i class="mdi mdi-chevron-right"></i>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Navigation } from 'swiper/modules';
  import { Swiper, SwiperSlide } from 'swiper/vue';

  import { SWIPER_DIRECTION_ENUMS } from '@/utils/constant';
  import { SwiperType, BreakpointsType, SwiperDirectionType } from '@/utils/interface/swiper';

  defineProps({
    items: {
      type: Array as () => any[],
      required: true
    },
    breakpoints: {
      type: Object as PropType<BreakpointsType>,
      default: () => ({
        768: { slidesPerView: 2 },
        1024: { slidesPerView: 3 },
        1400: { slidesPerView: 4 }
      })
    },
    spaceBetween: {
      type: Number,
      default: 20
    },
    autoHeight: {
      type: Boolean,
      default: false
    },
    direction: {
      type: String as PropType<SwiperDirectionType>,
      default: SWIPER_DIRECTION_ENUMS.HORIZONTAL
    },
    hideCarouselNav: {
      type: Boolean,
      default: false
    },
    carouselWrapperHeight: {
      type: String,
      default: 'unset'
    }
  });

  const swiper = ref<SwiperType | null>(null);

  const onSwiperInit = (instance: SwiperType) => {
    swiper.value = instance;
  };

  const getSlidesPerView = (): number => {
    const value = swiper.value?.params.slidesPerView;
    return typeof value === 'number' ? value : 1;
  };

  const goTo = (direction: 'next' | 'prev') => {
    if (!swiper.value) return;

    const index = swiper.value.activeIndex;
    const step = getSlidesPerView();

    if (direction === 'next' && !swiper.value.isEnd) {
      swiper.value.slideTo(index + step);
    }

    if (direction === 'prev' && !swiper.value.isBeginning) {
      swiper.value.slideTo(Math.max(0, index - step));
    }
  };
</script>
