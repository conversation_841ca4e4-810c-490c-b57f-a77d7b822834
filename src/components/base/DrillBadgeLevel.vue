<template>
  <BBadge pill :variant="getLevelClass(level)">{{ value }}</BBadge>
</template>

<script lang="ts" setup>
  defineProps({
    level: {
      type: String,
      required: true
    },
    value: {
      type: String,
      required: true
    }
  });

  const getLevelClass = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'warning';
      case 'intermediate':
        return 'primary';
      case 'advanced':
        return 'success';
      case 'expert':
        return 'danger';
      default:
        return '';
    }
  };
</script>
