<template>
  <BRow v-if="props.drill" class="drill-detail-container">
    <BCol lg="12">
      <BCard no-body>
        <BCardBody>
          <div class="d-flex align-items-center buttons border-bottom">
            <BCardTitle class="mb-0 flex-grow-1 card-title">
              {{ $t(props.site + '.drills.title') }}
            </BCardTitle>

            <div class="text-end" v-if="isMutable">
              <div class="py-2">
                <Button
                  :classes="
                    props.drill.status === 'public'
                      ? 'bg-warning text-white border-0 ms-2'
                      : 'bg-success text-white border-0 ms-2'
                  "
                  icon="bx-globe"
                  variant="success"
                  @click="isPublishModalOpen = true"
                >
                  {{ $t(`common.${publishStatusText}`) }}
                </Button>

                <Button
                  v-if="isSubmittable"
                  :classes="'bg-warning text-white border-0 ms-2'"
                  icon="bx-send"
                  variant="warning"
                  @click="confirmRequestCensor"
                >
                  {{ $t('common.request_review') }}
                </Button>

                <Button
                  :classes="'bg-primary text-white border-0 ms-2'"
                  icon="bx-edit-alt"
                  variant="primary"
                  @click="emit('edit')"
                >
                  {{ $t('common.edit') }}
                </Button>
              </div>
            </div>
            <div class="text-end" v-if="isAdminSite && !isOwnByAdmin">
              <div class="py-2">
                <Button
                  v-if="drill.censor !== CENSOR_STATUS_ENUMS.APPROVED"
                  :classes="'bg-success text-white border-0 ms-2'"
                  @click="handleApprove"
                >
                  {{ $t('common.approved') }}
                </Button>

                <Button
                  v-if="drill.censor !== CENSOR_STATUS_ENUMS.REJECTED"
                  :classes="'bg-warning text-white border-0 ms-2'"
                  @click="isRejectModalOpen = true"
                >
                  {{ $t('common.rejected') }}
                </Button>
              </div>
            </div>
          </div>
        </BCardBody>

        <BCardBody class="d-flex flex-column flex-lg-row">
          <BCol lg="7" md="12" sm="12" class="relative mb-4 mb-lg-0 px-lg-4">
            <FullscreenWrapper :src="diagramToDisplay?.diagramURL ? diagramToDisplay.diagramURL : '/pool-table.png'" />

            <div v-if="diagrams.length > 1" class="position-relative mt-5">
              <h4>{{ $t('public.drills.form.fields.step') }}</h4>

              <BaseCarousel :items="diagrams" :auto-height="false">
                <template #default="{ item }">
                  <FadeInUp class="drill-list g-4">
                    <div class="product-img diagram-hover-animate p-2">
                      <img
                        :src="item.diagramURL"
                        class="img-fluid mx-auto d-block"
                        :class="{ 'diagram-animate-active': animatedIndex === getDiagramIndex(item) }"
                        @click="
                          onDisplayDiagram(item);
                          onToggleAnimation(getDiagramIndex(item));
                        "
                      />
                    </div>

                    <h5 class="pt-2 mt-2" v-if="getDiagramIndex(item) === 0">
                      {{ $t('common.drill') }}
                    </h5>
                    <h5 v-else class="pt-2 mt-2">
                      {{ $t('public.drills.form.fields.step') }} {{ getDiagramIndex(item) }}
                    </h5>
                  </FadeInUp>
                </template>
              </BaseCarousel>
            </div>
          </BCol>

          <BCol lg="5" md="12" sm="12">
            <div class="text-left px-lg-4 d-flex flex-column justify-content-lg-between" style="height: 100%">
              <div>
                <h3 class="pb-2">{{ drill.title }}</h3>
                <h5 class="my-3">
                  <BadgeLevel :level="drill.level" :value="drill.levelI18n" />
                </h5>

                <PriceDisplay :price="drill.price" :salePrice="drill.salePrice" show-label></PriceDisplay>

                <div class="mb-2">
                  <span
                    v-for="item in drill.skills"
                    :key="item.id"
                    class="badge-soft-primary badge rounded-pill mx-2 my-1"
                  >
                    {{ item.nameI18n }}
                  </span>
                </div>

                <div v-if="drill.description" class="pt-4 fs-4">
                  <pre style="font-size: small; color: gray" class="pre-content">{{ drill.description }}</pre>
                </div>

                <Button
                  v-if="props.drill.videos && props.drill.videos.length > 0"
                  :classes="'bg-warning text-white border-0 mt-3'"
                  icon="bx-video"
                  variant="warning"
                  @click="showVideoModal = true"
                >
                  {{ $t('common.video') }}
                </Button>

                <div v-if="diagramToDisplay?.description" class="fs-5 mt-5">
                  <h4 class="border-bottom pb-2">
                    {{ $t('teacher.drills.form.fields.step') }} <span>{{ getDiagramIndex(diagramToDisplay) }}</span>
                  </h4>

                  <div class="d-flex w-100 des">
                    <i class="bx bx-right-arrow-alt font-size-16 align-middle text-primary me-1 p-1"></i>
                    <pre class="m-0 text-break w-100 pre-content">{{ diagramToDisplay.description }}</pre>
                  </div>
                </div>
              </div>
            </div>
          </BCol>
        </BCardBody>
      </BCard>

      <CensorHistoryList v-if="isAdminSite" :histories="drill.censorHistories" />
    </BCol>
  </BRow>

  <BModal
    v-model="isPublishModalOpen"
    id="modal-standard"
    @ok="handlePublish"
    :ok-title="$t(`common.${publishStatusText}`)"
    :cancel-title="$t('common.cancel')"
    :title="$t(`teacher.drills.confirm_publish.${publishStatusText}_title`)"
    title-class="font-18"
    :ok-variant="drill?.status === 'public' ? 'warning' : 'success'"
    lazy
  >
    <p>{{ $t(`teacher.drills.confirm_publish.${publishStatusText}_message`) }}</p>
  </BModal>

  <BModal v-if="showVideoModal" v-model="showVideoModal" size="lg" title="Video Hướng Dẫn" lazy no-footer>
    <div v-for="video in drill?.videos" :key="video.id" class="mb-5">
      <h5>{{ video.title }}</h5>
      <VideoPlayer
        v-if="video.isPlayable"
        :video-id="String(video.id)"
        :title="video.title"
        :thumbnail="video.thumbnailURL"
      />
      <div v-else>
        <img class="btn p-0 w-100 video-thumbnail-img" src="/processing-video.png" :alt="video.title" />
      </div>
    </div>
  </BModal>

  <BModal
    v-model="isRejectModalOpen"
    id="modal-standard"
    :cancel-title="$t('common.cancel')"
    :title="$t(`admin.drills.confirm_censor.title_reject`)"
    title-class="font-18"
    size="lg"
    no-footer
    lazy
  >
    <p>{{ $t(`admin.drills.confirm_censor.rejected_content`) }}</p>

    <BAlert :model-value="showDrillBoughtAlert" variant="warning" class="d-flex mb-4 mx-0 mx-lg-3">
      <i class="mdi mdi-alert-circle-outline lh-1 me-2 font-size-16"></i>
      <div class="d-flex flex-column">
        <h5 class="lh-1">{{ $t('user.practice_submission.form.alert.title') }}</h5>
        <span>{{ $t('admin.drills.confirm_censor.reject_drill_bought') }}</span>
      </div>
    </BAlert>
    <BaseFormValidator name="feedback" :label="$t(`admin.course.confirm_censor.feedback`)" required>
      <BFormTextarea v-model="feedback" :placeholder="$t('admin.course.confirm_censor.reject_placeholder')" rows="10" />
    </BaseFormValidator>

    <div class="d-flex justify-content-end gap-2 pt-2">
      <Button variant="light" @click="isRejectModalOpen = false">
        {{ $t('common.cancel') }}
      </Button>
      <Button variant="warning" @click="handleReject">
        {{ $t('common.rejected') }}
      </Button>
    </div>
  </BModal>
</template>

<script lang="ts" setup>
  import { useBreadcrumb } from '@bachdx/b-vuse';

  import i18n from '@/plugin/i18n';

  import { drillCensor } from '@/services/admin/repositories/drill';
  import { drillPublish, drillSubmit } from '@/services/teacher/repositories/drill';

  import BadgeLevel from '@/components/base/DrillBadgeLevel.vue';
  import BaseCarousel from './BaseCarousel.vue';
  import Button from '@/components/base/Button.vue';
  import CensorHistoryList from '../admin/censorHistories/CensorHistoryList.vue';
  import FullscreenWrapper from './FullscreenWrapper.vue';
  import PriceDisplay from './PriceDisplay.vue';
  import VideoPlayer from '@/components/base/VideoPlayer.vue';

  import useSwal from '@/composable/swal';

  import { CENSOR_STATUS_ENUMS, SITES, OWNER_TYPE_ENUMS } from '@/utils/constant';
  import { DrillDiagramsInterface } from '@/utils/interface/teacher/drill';
  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { SwalIconOptions, SwalOptions } from '@/utils/swal-options';

  const props = defineProps({
    drill: {
      type: Object as PropType<DrillInterface>,
      required: true
    },
    site: {
      type: String,
      required: true
    }
  });

  const { confirming } = useSwal();

  const emit = defineEmits(['edit', 'fetch']);
  const router = useRouter();

  const isRejectModalOpen = ref<boolean>(false);
  const feedback = ref<string>();

  const { setBreadcrumb } = useBreadcrumb({});

  const isOwnByAdmin = computed(() => {
    return props.drill.ownerType === OWNER_TYPE_ENUMS.ADMIN;
  });

  const isAdminSite = computed(() => {
    return props.site === SITES.ADMIN;
  });

  const isMutable = computed(() => {
    return isAdminSite.value === isOwnByAdmin.value;
  });
  const showDrillBoughtAlert = computed(() => {
    return !!(props.drill.userDrillCount && props.drill.userDrillCount > 0);
  });

  const isSubmittable = computed(() => {
    return (
      props.drill?.censor !== CENSOR_STATUS_ENUMS.APPROVED &&
      props.drill?.censor !== CENSOR_STATUS_ENUMS.SUBMITTED &&
      !isAdminSite.value
    );
  });

  const animatedIndex = ref<number>();
  const diagramToDisplay = ref<DrillDiagramsInterface>();
  const isPublishModalOpen = ref<boolean>(false);
  const publishStatusText = ref<string>('');
  const showVideoModal = ref<boolean>(false);

  setBreadcrumb({
    title: i18n.global.t(`${props.site}.top_menu.drills.detail.label`),
    items: [
      {
        text: i18n.global.t(`${props.site}.top_menu.drills.label`),
        href: `/${props.site}/drills`
      },
      {
        text: i18n.global.t(`${props.site}.top_menu.drills.detail.label`),
        href: ''
      }
    ]
  });

  const diagrams = computed<DrillDiagramsInterface[]>(() => {
    const arr: DrillDiagramsInterface[] = [];
    if (props.drill.diagrams && props.drill.diagrams.length && props.drill.diagrams[0].imageUrl) {
      arr.push({
        title: i18n.global.t('teacher.drills.title'),
        description: '',
        diagramURL: props.drill.diagrams[0].imageUrl
      });
    }
    if (props.drill.step) {
      props.drill.step.forEach(step => {
        arr.push({
          title: i18n.global.t('teacher.drills.form.fields.step'),
          description: step.description,
          diagramURL: step.diagramImage
        });
      });
    }
    return arr;
  });

  function getDiagramIndex(item: DrillDiagramsInterface) {
    return diagrams.value.findIndex(d => d.diagramURL === item.diagramURL);
  }

  function onToggleAnimation(index: number) {
    animatedIndex.value = animatedIndex.value === index ? undefined : index;
  }

  function onDisplayDiagram(diagram: DrillDiagramsInterface) {
    diagramToDisplay.value = diagram;
  }

  const handlePublish = async () => {
    const statusValue = props.drill.status === 'public' ? 'private' : 'public';
    if (props.drill) {
      drillPublish(String(props.drill.id), statusValue).then(res => {
        if (res.data && props.drill) {
          props.drill.status = statusValue;
        }
      });
    }
  };

  const handleApprove = async () => {
    const message = i18n.global.t('teacher.drills.confirm_censor.message');
    const confirmed = await confirming(
      new SwalOptions({
        title: i18n.global.t('common.request_review'),
        html: message,
        icon: SwalIconOptions.Question
      })
    );
    if (!confirmed || !props.drill.isMaster || props.drill.censor === CENSOR_STATUS_ENUMS.DRAFT) return;

    if (confirmed) {
      try {
        await drillCensor(String(props.drill.id), CENSOR_STATUS_ENUMS.APPROVED);

        feedback.value = '';
        emit('fetch');
      } catch (e) {
        console.error(e);
        return;
      }
    }
  };

  const confirmRequestCensor = async () => {
    const message = i18n.global.t('teacher.drills.confirm_censor.message');

    const confirmed = await confirming(
      new SwalOptions({
        title: i18n.global.t('common.request_review'),
        html: message,
        icon: SwalIconOptions.Question
      })
    );

    if (!confirmed || !props.drill.isMaster) return;

    if (confirmed) {
      try {
        await drillSubmit(String(props.drill.id));
      } catch (e) {
        console.error(e);
        return;
      }

      router.push(`/teacher/drills`);
    }
  };

  const handleReject = async () => {
    try {
      await drillCensor(String(props.drill.id), CENSOR_STATUS_ENUMS.REJECTED, feedback.value);

      feedback.value = undefined;
      isRejectModalOpen.value = false;
      emit('fetch');
    } catch (e) {
      console.error(e);
    }
  };

  watch(
    () => props.drill.status,
    newStatus => {
      publishStatusText.value = newStatus === 'public' ? 'private' : 'public';
    },
    { immediate: true }
  );

  onMounted(() => {
    if (!diagramToDisplay.value && diagrams.value.length > 0) {
      diagramToDisplay.value = diagrams.value[0];
    }
  });
</script>

<style lang="scss" scoped>
  .des {
    padding: 15px;
    max-height: 35vh;
    overflow-y: auto;
  }

  .buttons {
    padding-bottom: 15px;
  }
  .diagram-animate-active {
    z-index: 2;
    box-shadow: 6px 10px 26px rgba(95, 156, 216, 0.45);
    transition:
      transform 0.35s cubic-bezier(0.22, 1, 0.36, 1),
      box-shadow 0.35s cubic-bezier(0.22, 1, 0.36, 1);
    border-radius: 16px;
  }

  @media screen and (min-width: 990px) {
    .diagram-img-container {
      min-height: 320px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .diagram-hover-animate {
      transition:
        transform 0.35s cubic-bezier(0.22, 1, 0.36, 1),
        box-shadow 0.35s cubic-bezier(0.22, 1, 0.36, 1);
    }

    .diagram-hover-animate:hover {
      transform: scale(1.15);
      z-index: 2;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.18);
      transition:
        transform 0.35s cubic-bezier(0.22, 1, 0.36, 1),
        box-shadow 0.35s cubic-bezier(0.22, 1, 0.36, 1);
      border-radius: 16px;
    }
  }
  @media screen and (max-width: 500px) {
    .card-title {
      display: none;
    }
  }

  @media screen and (max-width: 990px) {
    .des {
      max-height: 25vh;
      max-width: 75vw;
      margin: auto;
    }
  }
  @media screen and (max-width: 768px) {
    .des {
      max-height: 25vh;
      max-width: 100vw;
      padding: 5px;
    }
  }
</style>
