<template>
  <BRow>
    <BCol lg="12">
      <BCard no-body class="shadow-none">
        <BCardBody class="pb-0">
          <BForm>
            <BaseFormValidator
              horizontal
              name="title"
              required
              :disabled="!isEditable"
              :label="$t(`${site}.drills.modal.form.title.label`)"
            >
              <input
                v-model="drillForm.title"
                class="form-control"
                id="title"
                name="title"
                type="text"
                :disabled="!isEditable"
                :placeholder="$t(`${site}.drills.modal.form.title.placeholder`)"
              />
            </BaseFormValidator>
            <BaseFormValidator
              horizontal
              name="level"
              :disabled="!isEditable"
              :label="$t(`${site}.drills.modal.form.level.label`)"
            >
              <Multiselect
                v-model="drillForm.level"
                label="description"
                valueProp="label"
                :disabled="!isEditable"
                :options="levelOptions"
                :placeholder="$t(`${site}.drills.modal.form.level.placeholder`)"
              />
            </BaseFormValidator>
            <BaseFormValidator :label="$t(`${site}.drills.modal.form.price.label`)" name="price" horizontal>
              <input
                v-model="drillForm.price"
                class="form-control"
                id="price"
                name="price"
                type="number"
                :placeholder="$t(`${site}.drills.modal.form.price.placeholder`)"
                @input="onNumberChange($event as InputEvent)"
              />
            </BaseFormValidator>
            <BaseFormValidator :label="$t(`${site}.drills.modal.form.sale_price.label`)" name="salePrice" horizontal>
              <input
                v-model="drillForm.salePrice"
                class="form-control"
                id="salePrice"
                name="salePrice"
                type="number"
                :placeholder="$t(`${site}.drills.modal.form.sale_price.placeholder`)"
                @input="onNumberChange($event as InputEvent, true)"
              />

              <template v-slot:custom-subtitle>
                <span class="text-muted small">
                  {{ $t(`${site}.drills.modal.form.sale_price.subtitle.1`) }}
                </span>
                <br />
                <span class="text-muted small">
                  {{ $t(`${site}.drills.modal.form.sale_price.subtitle.2`) }}
                </span>
                <br />
                <span class="text-muted small">
                  <b>※ {{ $t('common.note') }}: </b>
                  {{ $t(`${site}.drills.modal.form.sale_price.subtitle.3`) }}
                </span>
              </template>
            </BaseFormValidator>
            <BaseFormValidator
              horizontal
              name="skill"
              :disabled="!isEditable"
              :label="$t(`${site}.drills.modal.form.skill.label`)"
            >
              <Multiselect
                v-model="drillForm.skillIds"
                mode="tags"
                :closeOnDeselect="false"
                :closeOnSelect="false"
                :disabled="!isEditable"
                :hideSelected="false"
                :options="skillOptions"
                :placeholder="$t(`${site}.drills.modal.form.skill.placeholder`)"
              />
            </BaseFormValidator>
            <BaseFormValidator
              horizontal
              name="description"
              :disabled="!isEditable"
              :label="$t(`${site}.drills.modal.form.description.label`)"
            >
              <textarea
                v-model="drillForm.description"
                class="form-control min-h-100"
                id="description"
                name="description"
              />
            </BaseFormValidator>
          </BForm>
        </BCardBody>
      </BCard>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import { DrillInputInterface } from '@/utils/interface/drill/drill';
  import { OptionInterface } from '@/utils/interface/select-options';

  const drillForm = defineModel('drillForm', {
    type: Object as PropType<DrillInputInterface>,
    required: true
  });

  defineProps({
    levelOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    skillOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    site: {
      type: String,
      default: 'admin'
    },
    isEditable: {
      type: Boolean,
      required: true
    }
  });

  const onNumberChange = (event: InputEvent, isSalePrice = false) => {
    const target = event.target as HTMLInputElement;
    let raw = target.value;

    if (raw.length > 9) {
      raw = raw.slice(0, 9);
      target.value = raw;
    }

    if (raw === '') {
      if (isSalePrice) {
        drillForm.value.salePrice = 0;
      } else {
        drillForm.value.price = 0;
      }
    } else {
      const asNumber = Number(raw);
      if (isSalePrice) {
        drillForm.value.salePrice = isNaN(asNumber) ? 0 : asNumber;
      } else {
        drillForm.value.price = isNaN(asNumber) ? 0 : asNumber;
      }
    }
  };
</script>
