<template>
  <div class="pt-4">
    <BRow>
      <BCol xxl="9" xl="10" lg="9" md="8">
        <div
          class="d-flex justify-content-center align-items-center h-100"
          ref="imageColumnRef"
          id="drill-step-carousel"
        >
          <img
            class="p-0 w-100 max-w-1000"
            :src="selectedStep.diagramImage ? `${selectedStep.diagramImage}?t=${timestamp}` : '/pool-table.png'"
            @load="updateCarouselHeight"
          />
        </div>
      </BCol>

      <BCol xxl="3" xl="2" lg="3" md="4" class="overflow-y-hidden">
        <BaseCarousel
          hide-carousel-nav
          ref="baseCarouselRef"
          :auto-height="false"
          :breakpoints="breakpoints"
          :carousel-wrapper-height="carouselHeight + 'px'"
          :direction="SWIPER_DIRECTION_ENUMS.VERTICAL"
          :items="drillSteps"
        >
          <template #default="{ item }">
            <div
              class="product-img cursor-pointer h-100"
              :class="selectedStep.description == item.description ? 'active' : ''"
            >
              <img
                :src="item.diagramImage ? `${item.diagramImage}?t=${timestamp}` : '/pool-table.png'"
                class="h-100 d-block"
                @click="onSelectStep(item)"
              />
            </div>
          </template>
        </BaseCarousel>
      </BCol>
    </BRow>

    <BRow>
      <BCol>
        <div v-if="selectedStep.description" class="mt-5">
          <h4 class="border-bottom pb-2">
            {{ $t(`${site}.drills.step_management.form.description.label`) }}
          </h4>

          <div class="d-flex w-100 des">
            <i class="bx bx-right-arrow-alt font-size-16 align-middle text-primary me-1 p-1"></i>

            <section>
              <pre class="m-0 text-break w-100 pre-content">{{ selectedStep.description }}</pre>
            </section>
          </div>
        </div>
      </BCol>
    </BRow>
  </div>
</template>

<script setup lang="ts">
  import { DrillStepInterface } from '@/utils/interface/drill/step';

  import { SWIPER_DIRECTION_ENUMS } from '@/utils/constant';

  import BaseCarousel from '../../BaseCarousel.vue';

  const props = defineProps({
    drillSteps: {
      type: Array as PropType<DrillStepInterface[]>,
      required: true
    },
    timestamp: {
      type: Number,
      default: () => new Date().getTime()
    },
    site: {
      type: String,
      required: true
    }
  });

  const breakpoints = {
    1400: { slidesPerView: 3 },
    1200: { slidesPerView: 5 },
    992: { slidesPerView: 3 }
  };

  const baseCarouselRef = ref<InstanceType<typeof BaseCarousel> | null>(null);
  const carouselHeight = ref(0);
  const imageColumnRef = ref<HTMLElement | null>(null);
  const isExpanded = ref<boolean>(false);
  const selectedStep = ref<DrillStepInterface>(props.drillSteps[0]);

  function onSelectStep(step: DrillStepInterface) {
    selectedStep.value = step;
    isExpanded.value = false;
  }

  const updateCarouselHeight = () => {
    nextTick(() => {
      if (imageColumnRef.value) {
        carouselHeight.value = imageColumnRef.value.offsetHeight;
      }
    });
  };

  onMounted(() => {
    updateCarouselHeight();
  });
</script>

<style lang="scss" scoped>
  .product-img {
    transition: all 0.2s ease-in-out;

    &:not(.active) {
      filter: blur(1px) grayscale(0.5);
    }
    &:hover {
      filter: none;
    }
  }
</style>
