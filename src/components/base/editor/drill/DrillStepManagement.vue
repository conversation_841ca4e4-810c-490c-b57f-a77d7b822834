<template>
  <BRow>
    <BCol>
      <BCard no-body class="shadow-none">
        <BCardBody class="p-0">
          <BTabs v-model="drillStepTabIndex" nav-wrapper-class="d-none">
            <BTab>
              <DrillStepAccordion
                v-model:drill-form="drillForm"
                :image-url="imageUrl"
                :is-editable="isEditable"
                :site="site"
                :timestamp="timestamp"
                @confirm-delete-step="confirmDeleteStep"
                @edit-step="editStep"
              ></DrillStepAccordion>
            </BTab>

            <BTab v-if="isEditable">
              <DrillStepForm
                ref="drillStepFormRef"
                v-model:drill-form="drillForm"
                v-model:drill-step-form="drillStepForm"
                :drill-diagrams="drillDiagrams"
                :image-url="imageUrl"
                :is-course="isCourse"
                :is-editable="isEditable"
                :is-editing="isEditing"
                :loading="isStepSubmitButtonLoading"
                :site="site"
                :timestamp="timestamp"
                @hidden="handleHiddenStepForm"
                @handle-submit-step="handleSubmitStep"
              ></DrillStepForm>
            </BTab>
          </BTabs>
        </BCardBody>
      </BCard>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';

  import useSwal from '@/composable/swal';

  import DrillStepAccordion from './DrillStepAccordion.vue';
  import DrillStepForm from './DrillStepForm.vue';

  import { DrillStepForm as AdminDrillStepForm } from '@/forms/admin/drill';
  import { DrillStepForm as TeacherDrillStepForm } from '@/forms/teacher/drill';

  import { DiagramInterface } from '@/utils/interface/diagram';
  import { DrillInputInterface } from '@/utils/interface/drill/drill';
  import { DrillStepInputInterface } from '@/utils/interface/drill/step';
  import { SwalIconOptions, SwalOptions } from '@/utils/swal-options';

  import Toast from '@/utils/toast';

  import { upload } from '@/services/base/repositories/file';

  const { confirming } = useSwal();

  const emits = defineEmits(['saveDrill']);

  const drillStepForm = defineModel('drillStepForm', {
    type: Object as PropType<DrillStepInputInterface>,
    required: true
  });
  const drillForm = defineModel('drillForm', {
    type: Object as PropType<DrillInputInterface>,
    required: true
  });
  const drillStepTabIndex = defineModel('drillStepTabIndex', {
    type: Number,
    required: true
  });
  const props = defineProps({
    imageUrl: {
      type: String,
      required: true
    },
    isAdmin: {
      type: Boolean,
      default: true
    },
    site: {
      type: String,
      default: 'admin'
    },
    drillDiagrams: {
      type: Array as () => DiagramInterface[],
      required: true
    },
    timestamp: {
      type: Number,
      required: true
    },
    isEditable: {
      type: Boolean,
      required: true
    },
    isCourse: {
      type: Boolean,
      default: false
    }
  });

  const drillStepFormRef = ref<InstanceType<typeof DrillStepForm> | null>(null);
  const editingIndex = ref<number | null>(null);
  const isEditing = ref(false);
  const isStepSubmitButtonLoading = ref<boolean>(false);

  const deleteStep = (index: number) => {
    drillForm.value.step.splice(index, 1);
  };

  const confirmDeleteStep = async (index: number) => {
    const message = i18n.global.t(`${props.site}.drills.step_management.confirm_delete_message`);

    const confirmed = await confirming(
      new SwalOptions({
        html: message,
        icon: SwalIconOptions.Warning
      })
    );

    if (!confirmed) return;

    if (confirmed) {
      deleteStep(index);
      emits('saveDrill');
    }
  };

  async function uploadImage(file: File) {
    const hasImage = !!drillStepForm.value.diagramImage;
    const formData = new FormData();
    const endpoint = hasImage ? `drill_uploads` : `uploads`;

    if (hasImage) {
      const key = drillStepForm.value.diagramImage.split('/').pop();
      formData.append('key', key as string);
    }

    formData.append('files[]', file);
    const res = await upload(formData, endpoint, props.site);

    return hasImage ? res.data.url : res.data[0].url;
  }

  const handleStep = async () => {
    if (!drillStepForm.value.description.trim() || !drillStepFormRef.value) {
      throw new Error('Description is required');
    }

    for (const diagramItem of drillStepFormRef.value.tableData) {
      const image = await drillStepFormRef.value.generateTableImage();

      if (!image) {
        Toast.error({
          title: i18n.global.t(`${props.site}.pool_table.generate_image_error`)
        });
      }
      const imageUrl = image ? await uploadImage(image) : '';

      drillStepForm.value.diagramImage = imageUrl;
      drillStepForm.value.setting = cloneDeep(diagramItem);
    }

    if (isEditing.value) {
      if (editingIndex.value === null) return;

      drillForm.value.step[editingIndex.value] = cloneDeep(drillStepForm.value);
    } else drillForm.value.step.push({ ...drillStepForm.value });

    drillStepForm.value = cloneDeep({
      description: '',
      diagramImage: '',
      setting: {
        balls: [],
        targetingBalls: [],
        targetingZones: [],
        textBoxes: [],
        speedBars: []
      }
    });
  };

  const handleSubmitStep = async () => {
    isStepSubmitButtonLoading.value = true;
    if (drillStepFormRef.value) drillStepFormRef.value.activePoint = null;

    try {
      await handleStep();
      emits('saveDrill');
    } catch {
      isStepSubmitButtonLoading.value = false;
      return;
    }

    drillStepTabIndex.value = 0;
  };

  const handleShowDrillStepModal = (isNew = true) => {
    if (drillStepFormRef.value) {
      if (isNew) {
        isEditing.value = false;
        if (props.isAdmin) {
          drillStepForm.value = new AdminDrillStepForm();
        } else {
          drillStepForm.value = new TeacherDrillStepForm();
        }
      }

      drillStepFormRef.value.initialize();
      drillStepFormRef.value.activePoint = null;
      isStepSubmitButtonLoading.value = false;
      drillStepTabIndex.value = 1;
    }
  };

  const editStep = (index: number) => {
    isEditing.value = true;
    editingIndex.value = index;
    drillStepForm.value = cloneDeep(drillForm.value.step[index]);

    handleShowDrillStepModal(false);
  };

  const handleHiddenStepForm = () => {
    isEditing.value = false;
    drillStepTabIndex.value = 0;
  };

  defineExpose({
    isEditing,
    isStepSubmitButtonLoading,
    handleHiddenStepForm,
    handleShowDrillStepModal,
    handleSubmitStep
  });
</script>
