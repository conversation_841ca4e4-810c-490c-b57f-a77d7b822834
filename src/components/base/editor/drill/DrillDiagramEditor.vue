<template>
  <BRow>
    <template v-if="isEditable">
      <BCol xxl="12">
        <DrillDiagramActionMenu
          :id="id"
          @export-table-image="exportTableImage"
          @handle-delete-point="handleDeletePoint"
          @load-flipped-layout="loadFlippedLayout"
          @load-rack-layout="loadRackLayout"
        ></DrillDiagramActionMenu>
      </BCol>

      <BCol xxl="12">
        <DrillDiagramCanvas
          ref="drillDiagramCanvas"
          :id="id"
          :handleCanvasClick="handleCanvasClick"
          :handleDragging="handleDragging"
          :handleKeyUp="handleKeyUp"
          :handleMouseMove="handleMouseMove"
          :handleMouseUp="handleMouseUp"
          :site="site"
          :table-data="tableData"
        ></DrillDiagramCanvas>
      </BCol>
    </template>

    <BCol v-else class="mx-auto">
      <div class="text-center mb-4">
        <img
          class="p-0 w-100 cursor-not-allowed max-w-1000"
          :src="imageUrl ? `${imageUrl}?t=${timestamp}` : '/pool-table.png'"
        />
      </div>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import { type DiagramItem } from '@/composable/poolTableLayout/config/poolTableConfig';

  import DrillDiagramActionMenu from './DrillDiagramActionMenu.vue';
  import DrillDiagramCanvas from './DrillDiagramCanvas.vue';

  defineProps({
    tableData: {
      type: Array as () => DiagramItem[],
      required: true
    },
    site: {
      type: String,
      default: 'admin'
    },
    id: {
      type: String,
      default: 'canvas-container'
    },
    handleKeyUp: {
      type: Function as PropType<(event: KeyboardEvent) => void>,
      required: true
    },
    handleDragging: {
      type: Function as PropType<(event: MouseEvent) => void>,
      required: true
    },
    handleMouseMove: {
      type: Function as PropType<(event: MouseEvent) => void>,
      required: true
    },
    handleMouseUp: {
      type: Function as PropType<(event: MouseEvent) => void>,
      required: true
    },
    handleCanvasClick: {
      type: Function as PropType<(event: MouseEvent) => void>,
      required: true
    },
    exportTableImage: {
      type: Function as PropType<() => void>,
      required: true
    },
    handleDeletePoint: {
      type: Function as PropType<() => void>,
      required: true
    },
    loadFlippedLayout: {
      type: Function as PropType<(type: string) => void>,
      required: true
    },
    loadRackLayout: {
      type: Function as PropType<(type: string) => void>,
      required: true
    },
    isEditable: {
      type: Boolean,
      required: true
    },
    imageUrl: {
      type: String,
      required: true
    },
    timestamp: {
      type: Number,
      required: true
    }
  });

  const drillDiagramCanvas = ref<InstanceType<typeof DrillDiagramCanvas> | null>(null);

  defineExpose({
    drillDiagramCanvas
  });
</script>
