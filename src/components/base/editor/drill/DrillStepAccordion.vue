<template>
  <BAccordion free initial-animation>
    <BAccordionItem
      button-class="bg-gradient"
      :show="drillForm.step.length == 0"
      :title="$t(`${site}.drills.step_management.accordion.drill_image.title`)"
    >
      <div class="text-center">
        <img class="p-0 w-100 max-w-1000" :src="imageUrl ? `${imageUrl}?t=${timestamp}` : '/pool-table.png'" />
      </div>
    </BAccordionItem>
    <BAccordionItem
      button-class="bg-gradient"
      :show="drillForm.step.length > 0"
      :title="$t(`${site}.drills.step_management.accordion.step_list.title`)"
    >
      <DrillStepTable
        v-model:drill-form="drillForm"
        :is-editable="isEditable"
        :site="site"
        :timestamp="timestamp"
        @confirm-delete-step="confirmDeleteStep"
        @edit-step="editStep"
      ></DrillStepTable>
    </BAccordionItem>
  </BAccordion>
</template>

<script lang="ts" setup>
  import { DrillInputInterface } from '@/utils/interface/drill/drill';

  import DrillStepTable from './DrillStepTable.vue';

  const emits = defineEmits(['confirmDeleteStep', 'editStep']);
  const drillForm = defineModel('drillForm', {
    type: Object as PropType<DrillInputInterface>,
    required: true
  });
  defineProps({
    imageUrl: {
      type: String,
      required: true
    },
    site: {
      type: String,
      default: 'admin'
    },
    timestamp: {
      type: Number,
      required: true
    },
    isEditable: {
      type: Boolean,
      required: true
    }
  });

  const confirmDeleteStep = (index: number) => {
    emits('confirmDeleteStep', index);
  };

  const editStep = (index: number) => {
    emits('editStep', index);
  };
</script>

<style lang="scss" scoped>
  :deep(.accordion-button) {
    &:not(.collapsed) {
      background-color: var(--bs-primary);
      color: var(--bs-white);
      &::after {
        filter: invert(100%) sepia(99%) saturate(2%) hue-rotate(31deg) brightness(105%) contrast(100%);
      }
    }
  }
</style>
