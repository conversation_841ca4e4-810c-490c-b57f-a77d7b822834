<template>
  <BCard no-body class="shadow-none m-0">
    <BCardBody class="p-0">
      <DrillDiagramEditor
        ref="drillStepDiagramEditor"
        id="step-canvas-container"
        :export-table-image="exportTableImage"
        :handle-delete-point="handleDeletePoint"
        :handleCanvasClick="handleCanvasClick"
        :handleDragging="handleDragging"
        :handleKeyUp="handleKeyUp"
        :handleMouseMove="handleMouseMove"
        :handleMouseUp="handleMouseUp"
        :image-url="imageUrl"
        :is-editable="isEditable"
        :load-flipped-layout="loadFlippedLayout"
        :load-rack-layout="loadRackLayout"
        :site="site"
        :table-data="tableData"
        :timestamp="timestamp"
      ></DrillDiagramEditor>

      <BForm>
        <BaseFormValidator
          :label="$t(`${site}.drills.step_management.form.description.label`)"
          required
          name="description"
        >
          <textarea
            v-model="drillStepForm.description"
            :placeholder="$t(`${site}.drills.step_management.form.description.placeholder`)"
            class="form-control min-h-100"
            id="description"
            name="description"
          />
        </BaseFormValidator>
      </BForm>
    </BCardBody>

    <BCardFooter v-if="isCourse" footer-bg-variant="light" class="px-0 pb-0 pt-4 text-end bg-transparent">
      <Button classes="me-2" icon="bx-x" variant="outline-primary" @click="handleCancel">
        {{ $t('common.cancel') }}
      </Button>

      <Button
        icon="bx-save"
        variant="success"
        :disabled="loading || !drillStepForm.description"
        :loading="loading"
        @click="handleSubmitStep"
      >
        {{ submitBtnText }}
      </Button>
    </BCardFooter>
  </BCard>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';

  import { DiagramInterface } from '@/utils/interface/diagram';
  import { DrillInputInterface } from '@/utils/interface/drill/drill';
  import { DrillStepInputInterface } from '@/utils/interface/drill/step';

  import { usePoolTable } from '@/composable/poolTableLayout/usePoolTable';

  import Button from '../../Button.vue';
  import DrillDiagramEditor from './DrillDiagramEditor.vue';

  const emits = defineEmits(['hidden', 'handleSubmitStep']);
  const drillStepForm = defineModel('drillStepForm', {
    type: Object as PropType<DrillStepInputInterface>,
    required: true
  });
  const drillForm = defineModel('drillForm', {
    type: Object as PropType<DrillInputInterface>,
    required: true
  });
  const props = defineProps({
    site: {
      type: String,
      default: 'admin'
    },
    loading: {
      type: Boolean,
      default: false
    },
    drillDiagrams: {
      type: Array as () => DiagramInterface[],
      required: true
    },
    isEditing: {
      type: Boolean,
      default: false
    },
    isEditable: {
      type: Boolean,
      required: true
    },
    imageUrl: {
      type: String,
      required: true
    },
    timestamp: {
      type: Number,
      required: true
    },
    isCourse: {
      type: Boolean,
      default: false
    }
  });

  const {
    activePoint,
    balls,
    canvas,
    speedBars,
    tableData,
    targetingBalls,
    targetingZones,
    textBoxes,
    cleanTableDataForStepping,
    exportTableImage,
    fetchSavedLayouts,
    generateTableImage,
    handleClick,
    handleDeletePoint,
    handleDragging,
    handleKeyUp,
    handleMouseMove,
    handleMouseUp,
    handlePathCreation,
    initializeCanvas,
    loadFlippedLayout,
    loadRackLayout
  } = usePoolTable();

  const drillStepDiagramEditor = ref<InstanceType<typeof DrillDiagramEditor> | null>(null);

  const submitBtnText = computed(() => {
    const translateKey = props.isEditing ? 'update_title' : 'create_title';

    return i18n.global.t(`${props.site}.drills.step_management.step_modal.${translateKey}`);
  });

  const handleCanvasClick = (event: MouseEvent) => {
    handleClick(event);
    handlePathCreation(event);
  };

  const initCanvas = () => {
    if (!drillStepDiagramEditor.value || !drillStepDiagramEditor.value.drillDiagramCanvas) return;

    canvas.value = drillStepDiagramEditor.value.drillDiagramCanvas.canvas;
    initializeCanvas('step-canvas-container');
  };

  const fetchCreateDiagramDetail = () => {
    const stepLength = drillForm.value.step.length;

    if (props.drillDiagrams.length != 0 && stepLength == 0) {
      const firstDiagram = cloneDeep(props.drillDiagrams[0].setting);
      balls.value = cloneDeep(firstDiagram.balls);
      targetingBalls.value = cloneDeep(firstDiagram.targetingBalls);
      targetingZones.value = cloneDeep(firstDiagram.targetingZones);
      textBoxes.value = cloneDeep(firstDiagram.textBoxes);
      speedBars.value = cloneDeep(firstDiagram.speedBars);
    } else {
      const lstStepDiagram = cloneDeep(drillForm.value.step[stepLength - 1].setting);
      if (!lstStepDiagram) return;

      balls.value = cloneDeep(lstStepDiagram.balls);
      targetingBalls.value = cloneDeep(lstStepDiagram.targetingBalls);
      targetingZones.value = cloneDeep(lstStepDiagram.targetingZones);
      textBoxes.value = cloneDeep(lstStepDiagram.textBoxes);
      speedBars.value = cloneDeep(lstStepDiagram.speedBars);

      cleanTableDataForStepping();
    }
  };

  const fetchUpdateDiagramDetail = () => {
    const diagramSetting = cloneDeep(drillStepForm.value.setting);
    balls.value = cloneDeep(diagramSetting.balls);
    targetingBalls.value = cloneDeep(diagramSetting.targetingBalls);
    targetingZones.value = cloneDeep(diagramSetting.targetingZones);
    textBoxes.value = cloneDeep(diagramSetting.textBoxes);
    speedBars.value = cloneDeep(diagramSetting.speedBars);
  };

  const fetchDrillDiagramDetail = () => {
    props.isEditing ? fetchUpdateDiagramDetail() : fetchCreateDiagramDetail();
  };

  const initialize = async () => {
    await fetchSavedLayouts();

    if (props.drillDiagrams.length > 0) {
      fetchDrillDiagramDetail();
    }

    initCanvas();
  };

  const handleCancel = () => {
    emits('hidden');
  };

  const handleSubmitStep = () => {
    emits('handleSubmitStep');
  };

  watch(
    () => props.drillDiagrams,
    async newDiagrams => {
      if (newDiagrams.length > 0) {
        fetchDrillDiagramDetail();
        initCanvas();
      }
    },
    { immediate: true }
  );

  onMounted(() => {
    initialize();
  });

  defineExpose({
    activePoint,
    tableData,
    generateTableImage,
    initialize
  });
</script>
