<template>
  <BRow>
    <BCard class="shadow-none">
      <BCardHeader header-bg-variant="light" class="bg-transparent">
        <div class="text-center">
          <h3 class="border-bottom text-break pb-2">
            {{ drillDetail.title }}
          </h3>
          <h5 class="my-3">
            <BadgeLevel :level="drillDetail.level" :value="drillDetail.levelI18n"></BadgeLevel>
          </h5>
          <div>
            <span v-for="item in drillDetail.skills" :key="item.id" class="badge-soft-primary badge rounded-pill mx-2">
              {{ item.nameI18n }}
            </span>
          </div>
        </div>
      </BCardHeader>

      <BCardBody class="p-0 mt-3">
        <BCol class="mx-auto">
          <PriceDisplay
            :position="POSITION_ENUMS.CENTER"
            :price="drillDetail.price"
            :salePrice="drillDetail.salePrice"
            show-label
          ></PriceDisplay>

          <template v-if="drillDetail.description">
            <div class="pt-5 text-center">
              <h4 class="border-bottom pb-2">
                {{ $t(`${site}.drills.form.fields.description`) }}
              </h4>
            </div>
            <pre class="pre-content">{{ drillDetail.description }}</pre>
          </template>
        </BCol>
      </BCardBody>

      <BCardBody class="p-0">
        <template v-if="drillDetail.step && drillDetail.step.length != 0">
          <div class="pt-5 text-center">
            <h4 class="border-bottom pb-2">
              {{ $t(`${site}.drills.form.fields.step`) }}
            </h4>
          </div>

          <DrillStepCarousel :drill-steps="carouselDrillSteps" :site="site"></DrillStepCarousel>
        </template>
      </BCardBody>
    </BCard>
  </BRow>
</template>

<script setup lang="ts">
  import BadgeLevel from '@/components/base/DrillBadgeLevel.vue';
  import DrillStepCarousel from './DrillStepCarousel.vue';
  import PriceDisplay from '@/components/base/PriceDisplay.vue';

  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { DrillStepInterface, createBlankDiagramItem } from '@/utils/interface/drill/step';

  import { POSITION_ENUMS } from '@/utils/constant';

  const props = defineProps({
    drillDetail: {
      type: Object as PropType<DrillInterface>,
      required: true
    },
    site: {
      type: String,
      required: true
    },
    imageUrl: {
      type: String,
      required: true
    },
    timestamp: {
      type: Number,
      required: true
    }
  });

  const carouselDrillSteps = computed(() => {
    const diagramStep: DrillStepInterface = {
      description: '',
      diagramImage: props.imageUrl,
      setting: createBlankDiagramItem()
    };
    const drillStepsArr: DrillStepInterface[] = [diagramStep];

    if (props.drillDetail.step) {
      drillStepsArr.push(...props.drillDetail.step);
    }

    return drillStepsArr;
  });
</script>
