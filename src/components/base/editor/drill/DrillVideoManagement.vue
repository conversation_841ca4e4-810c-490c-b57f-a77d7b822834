<template>
  <div>
    <div v-if="drillDetail.videos.length" class="row g-3">
      <div v-for="video in drillDetail.videos" :key="video.id" class="col-12 col-xxl-6">
        <div class="w-100">
          <VideoPlayer
            v-if="video.isPlayable"
            :video-id="String(video.id)"
            :thumbnail="video.thumbnailURL"
            :video-title="video.title"
          />
          <div v-else>
            <img class="btn w-100 video-thumbnail-img" src="/processing-video.png" alt="video processing" />
          </div>

          <div class="text-center d-flex justify-content-center align-items-center mt-2">
            <span class="font-size-14">{{ video.title }}</span>
            <span class="ms-2">
              <Button
                v-if="isEditable"
                classes="rounded-circle p-1 font-size-14 d-flex align-items-center justify-content-center"
                icon-only
                icon="bx-trash"
                variant="danger"
                :disabled="isDeletingVideo"
                :loading="isDeletingVideo"
                @click="handleDeleteVideo(video.id)"
              ></Button>
            </span>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="!isEditable" class="row g-3 mb-4">
      <DataEmpty :message="$t('video.data_empty')" height="150" />
    </div>

    <div v-if="isEditable" class="mt-4">
      <UploadVideo
        card-class="shadow-none"
        class="text-start"
        enable-default-title
        parent-type="Drill"
        :site="site"
        @upload-success="handleUploadedVideo"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import {
    selfVideoDelete as adminSelfVideoDelete,
    saveAndUploadDrillVideo as adminSaveAndUploadDrillVideo
  } from '@/services/admin';
  import {
    selfVideoDelete as teacherSelfVideoDelete,
    saveAndUploadDrillVideo as teacherSaveAndUploadDrillVideo
  } from '@/services/teacher';

  import Button from '@/components/base/Button.vue';
  import DataEmpty from '@/components/utility/DataEmpty.vue';
  import UploadVideo from '@/components/base/video/UploadVideo.vue';
  import VideoPlayer from '../../VideoPlayer.vue';

  import { DrillInterface } from '@/utils/interface/drill/drill';

  const props = defineProps({
    drillDetail: {
      type: Object as PropType<DrillInterface>,
      required: true
    },
    site: {
      type: String,
      required: true
    },
    drillId: {
      type: Number,
      required: true
    },
    isEditable: {
      type: Boolean,
      required: true
    }
  });

  const isDeletingVideo = ref(false);
  const videoID = ref(0);

  const selfVideoDelete = computed(() => {
    return props.site === 'teacher' ? teacherSelfVideoDelete : adminSelfVideoDelete;
  });

  const saveAndUploadDrillVideo = computed(() => {
    return props.site === 'teacher' ? teacherSaveAndUploadDrillVideo : adminSaveAndUploadDrillVideo;
  });

  const handleUploadedVideo = (videoName: string, videoId: number) => {
    saveAndUploadDrillVideo.value(videoId.toString(), String(props.drillId)).then(() => {
      props.drillDetail.videos.push({
        id: videoId,
        status: 'processing',
        title: videoName,
        thumbnailURL: '',
        isPlayable: false,
        duration: 0,
        currentPosition: 0,
        videoPlatforms: []
      });

      videoID.value = 0;
    });
  };

  const handleDeleteVideo = async (videoId: number) => {
    try {
      isDeletingVideo.value = true;
      await selfVideoDelete.value(videoId.toString());
      props.drillDetail.videos = props.drillDetail.videos.filter(v => v.id !== videoId);
    } catch (error) {
      console.error('Error deleting video:', error);
    } finally {
      isDeletingVideo.value = false;
    }
  };
</script>
