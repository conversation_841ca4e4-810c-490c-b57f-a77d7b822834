<template>
  <EditorStepper
    v-model:tab-index="tabIndex"
    is-drill
    :censor-history="drillDetail?.censorHistories || []"
    :censor-status-message="drillDetail?.censorI18n || ''"
    :censor-status="drillDetail?.censor || CENSOR_STATUS_ENUMS.DRAFT"
    :editor-title="editorTitle"
    :hide-footer="isCourse && !isCreate"
    :hide-header="isCourse"
    :is-admin="isAdmin"
    :is-course="isCourse"
    :is-create="isCreate"
    :is-submit-button-loading="isDrillSubmitButtonLoading"
    :nav-wrapper-class="navWrapperClass"
    :show-censor-status="!!drillDetail && !isAdmin && !isCourse"
    :site="site"
    :step-array="drillStepArray"
    :steps-config="drillSteps"
    @change-step="handleChangeStep"
    @make-change-in-step="makeChangeInStep"
    @request-censor="confirmRequestCensor"
  >
    <template #step-content-basic-info>
      <DrillBasicInfo
        v-model:drill-form="drillForm"
        :is-editable="isEditable"
        :level-options="levelOptions"
        :site="site"
        :skill-options="skillOptions"
      ></DrillBasicInfo>
    </template>

    <template #step-content-diagrams>
      <DrillDiagramEditor
        v-if="!isCreate"
        ref="drillDiagramEditor"
        id="canvas-container"
        :export-table-image="exportTableImage"
        :handle-delete-point="handleDeletePoint"
        :handleCanvasClick="handleCanvasClick"
        :handleDragging="handleDragging"
        :handleKeyUp="handleKeyUp"
        :handleMouseMove="handleMouseMove"
        :handleMouseUp="handleMouseUp"
        :image-url="imageUrl"
        :is-editable="isEditable"
        :load-flipped-layout="loadFlippedLayout"
        :load-rack-layout="loadRackLayout"
        :site="site"
        :table-data="tableData"
        :timestamp="timestamp"
      ></DrillDiagramEditor>
    </template>

    <template #append-section-steps>
      <Button
        v-if="drillStepTabIndex === 0"
        icon="bx-plus"
        :disabled="isDrillSubmitButtonLoading || !isEditable"
        @click="showDrillStepForm"
      >
        {{ $t(`${site}.drills.step_management.create_step`) }}
      </Button>
    </template>

    <template #step-content-steps>
      <DrillStepManagement
        v-if="!isCreate"
        ref="drillStepManagement"
        v-model:drill-form="drillForm"
        v-model:drill-step-form="drillStepForm"
        v-model:drill-step-tab-index="drillStepTabIndex"
        :drill-diagrams="drillDiagrams"
        :image-url="imageUrl"
        :is-admin="isAdmin"
        :is-course="isCourse"
        :is-editable="isEditable"
        :site="site"
        :timestamp="timestamp"
        @save-drill="saveDrill(false)"
      ></DrillStepManagement>
    </template>

    <template #step-content-videos>
      <DrillVideoManagement
        v-if="drillId && drillDetail"
        :drill-detail="drillDetail"
        :drill-id="drillId"
        :is-editable="isEditable"
        :site="site"
      >
      </DrillVideoManagement>
    </template>

    <template #step-content-preview>
      <DrillPreview
        v-if="drillDetail"
        :drill-detail="drillDetail"
        :image-url="imageUrl"
        :site="site"
        :timestamp="timestamp"
      ></DrillPreview>
    </template>

    <template #action-buttons>
      <template v-if="drillStepTabIndex === 1">
        <Button icon="bx-x" variant="outline-primary" @click="handleCancelMutateDrillStep">
          {{ $t('common.cancel') }}
        </Button>

        <Button
          icon="bx-save"
          variant="success"
          :disabled="isStepSubmitButtonLoading || !drillStepForm.description"
          :loading="isStepSubmitButtonLoading"
          @click="handleSubmitStep"
        >
          {{ submitBtnText }}
        </Button>
      </template>

      <template v-else>
        <Button
          v-if="!isCreate && !isCourse"
          classes="min-w-160"
          :disabled="isChangingPublicStatus"
          :icon="publishBtnIcon()"
          :loading="isChangingPublicStatus"
          :variant="publishBtnVariant()"
          @click="confirmChangePublicStatus"
        >
          {{ $t(`common.${publishStatusText}`) }}
        </Button>

        <Button
          classes="min-w-160"
          icon="bx-save"
          variant="success"
          :disabled="isDrillSubmitButtonLoading"
          :loading="isDrillSubmitButtonLoading"
          @click="handleSubmitDrill"
        >
          {{ submitButtonText }}
        </Button>
      </template>
    </template>
  </EditorStepper>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';

  import { DrillForm as AdminDrillForm, DrillStepForm as AdminDrillStepForm } from '@/forms/admin/drill';
  import { DrillForm as TeacherDrillForm, DrillStepForm as TeacherDrillStepForm } from '@/forms/teacher/drill';

  import Toast from '@/utils/toast';
  import { CENSOR_STATUS_ENUMS, COURSE_PUBLIC_STATUS_ENUMS, NAVIGATION_TYPE, SITES } from '@/utils/constant';
  import { DiagramInterface } from '@/utils/interface/diagram';
  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { OptionInterface } from '@/utils/interface/select-options';
  import { SwalIconOptions, SwalOptions } from '@/utils/swal-options';

  import useSwal from '@/composable/swal';
  import { usePoolTable } from '@/composable/poolTableLayout/usePoolTable';

  import {
    COURSE_DRILL_STEPS,
    DRILL_STEPS,
    getArraySteps,
    getNextStepByIndex,
    getPreviousStepByIndex,
    getStepByPosition,
    getStepIndexById
  } from '@/composable/useEditor';

  import {
    drillCreate as adminDrillCreate,
    drillPublish as adminDrillPublish,
    drillShow as adminDrillShow,
    drillUpdate as adminDrillUpdate
  } from '@/services/admin/repositories/drill';
  import {
    drillCreate as teacherDrillCreate,
    drillPublish as teacherDrillPublish,
    drillShow as teacherDrillShow,
    drillSubmit,
    drillUpdate as teacherDrillUpdate
  } from '@/services/teacher/repositories/drill';
  import { upload } from '@/services/base/repositories/file';

  import Button from '../../Button.vue';
  import DrillBasicInfo from './DrillBasicInfo.vue';
  import DrillDiagramEditor from './DrillDiagramEditor.vue';
  import DrillPreview from './DrillPreview.vue';
  import DrillStepManagement from './DrillStepManagement.vue';
  import DrillVideoManagement from './DrillVideoManagement.vue';
  import EditorStepper from '../shared/EditorStepper.vue';

  const route = useRoute();
  const router = useRouter();
  const { confirming } = useSwal();

  const {
    activePoint,
    balls,
    canvas,
    speedBars,
    tableData,
    targetingBalls,
    targetingZones,
    textBoxes,
    exportTableImage,
    fetchSavedLayouts,
    generateTableImage,
    handleClick,
    handleDeletePoint,
    handleDragging,
    handleKeyUp,
    handleMouseMove,
    handleMouseUp,
    handlePathCreation,
    initializeCanvas,
    loadFlippedLayout,
    loadRackLayout
  } = usePoolTable();

  const emits = defineEmits(['redirect', 'closeModal']);

  const props = defineProps({
    levelOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    skillOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    isAdmin: {
      type: Boolean,
      default: true
    },
    isCreate: {
      type: Boolean,
      default: false
    },
    isCourse: {
      type: Boolean,
      default: false
    },
    activeDrillSlug: {
      type: String,
      default: null
    },
    editorTitle: {
      type: String,
      default: ''
    },
    hideActionHeader: {
      type: Boolean,
      default: false
    }
  });

  const drillDetail = ref<DrillInterface>();
  const drillDiagramEditor = ref<InstanceType<typeof DrillDiagramEditor> | null>(null);
  const drillDiagrams = ref<DiagramInterface[]>([]);
  const drillId = ref<number>();
  const drillSlug = ref<string>();
  const drillStepManagement = ref<InstanceType<typeof DrillStepManagement> | null>(null);
  const drillStepTabIndex = ref<number>(0);
  const imageUrl = ref<string>('');
  const isChangingPublicStatus = ref<boolean>(false);
  const isDrillSubmitButtonLoading = ref<boolean>(false);
  const needUpdate = ref<boolean>(false);
  const needUpload = ref<boolean>(false);
  const publishStatusText = ref<string>(COURSE_PUBLIC_STATUS_ENUMS.PUBLIC);
  const tabIndex = ref<number>(0);
  const timestamp = ref<number>(Date.now());

  let drillForm: Ref<AdminDrillForm | TeacherDrillForm>;
  if (props.isAdmin) {
    drillForm = ref(new AdminDrillForm());
  } else {
    drillForm = ref(new TeacherDrillForm());
  }

  let drillStepForm: Ref<AdminDrillStepForm | TeacherDrillStepForm>;
  if (props.isAdmin) {
    drillStepForm = ref(new AdminDrillStepForm());
  } else {
    drillStepForm = ref(new TeacherDrillStepForm());
  }

  const isStepSubmitButtonLoading = computed(() => {
    return drillStepManagement.value?.isStepSubmitButtonLoading;
  });

  const submitBtnText = computed(() => {
    const translateKey = drillStepManagement.value?.isEditing ? 'update_title' : 'create_title';

    return i18n.global.t(`${site.value}.drills.step_management.step_modal.${translateKey}`);
  });

  const navWrapperClass = computed(() => {
    const baseClass = 'col-xl-3 col-md-4 col-sm-12 col-12';

    const getStickyClass = () => {
      const baseClass = 'position-sticky';
      const topPosition = props.isCourse ? 20 : 76;

      return baseClass + ' ' + 'top-' + topPosition + '-px';
    };

    return `${baseClass} ${getStickyClass()}`;
  });

  const drillSteps = computed(() => {
    return props.isCourse ? COURSE_DRILL_STEPS : DRILL_STEPS;
  });

  const drillStepArray = computed(() => {
    return getArraySteps(drillSteps.value);
  });

  const site = computed(() => (props.isAdmin ? SITES.ADMIN : SITES.TEACHER));
  const submitButtonText = computed(() => {
    const key = props.isCreate ? 'save_drill' : 'update_drill';
    const translationKey = `${site.value}.pool_table.actions.${key}`;

    return i18n.global.t(translationKey);
  });

  const isEditable = computed(() => {
    return (
      (drillDetail.value?.censor !== CENSOR_STATUS_ENUMS.APPROVED &&
        !props.isCreate &&
        !props.isCourse &&
        !props.isAdmin) ||
      props.isAdmin ||
      props.isCreate ||
      props.isCourse
    );
  });

  const saveDrill = async (needUpload = true) => {
    try {
      await handleSubmitDrillOperation(false, false, needUpload);
    } catch {
      tabIndex.value = 0;
      return;
    }
  };

  const changeStep = async (type: string) => {
    const currentIndex = tabIndex.value;
    const currentQuery = { ...route.query };

    switch (type) {
      case NAVIGATION_TYPE.PREV: {
        const prevStep = getPreviousStepByIndex(drillSteps.value, tabIndex.value);
        if (prevStep) {
          tabIndex.value = getStepIndexById(drillSteps.value, prevStep.id);
        }
        break;
      }
      case NAVIGATION_TYPE.NEXT: {
        const nextStep = getNextStepByIndex(drillSteps.value, tabIndex.value);
        if (nextStep) {
          tabIndex.value = getStepIndexById(drillSteps.value, nextStep.id);
        }
        break;
      }
    }

    router.replace({
      query: {
        ...currentQuery,
        tabIndex: tabIndex.value
      }
    });

    handleChangeStep(currentIndex, true, type);
  };

  async function uploadImage(file: File) {
    const hasImage = !!imageUrl.value;
    const formData = new FormData();
    const endpoint = hasImage ? `drill_uploads` : `uploads`;

    if (hasImage) {
      const key = imageUrl.value.split('/').pop();
      formData.append('key', key as string);
    }

    formData.append('files[]', file);
    const res = await upload(formData, endpoint, site.value);

    return hasImage ? res.data.url : res.data[0].url;
  }

  const getNewTimestamp = () => {
    timestamp.value = Date.now();
  };

  const handleSubmitDrillOperation = async (isRedirecting = false, toast = false, needUpload = true) => {
    activePoint.value = null;

    if (props.isCreate) {
      if (props.isCourse && !props.isAdmin) drillForm.value.isMaster = false;

      const drillCreateFunc = props.isAdmin ? adminDrillCreate : teacherDrillCreate;
      await drillCreateFunc(drillForm.value).then(res => {
        drillId.value = res.data.drillCreate.drill.id;
        drillSlug.value = res.data.drillCreate.drill.slug;

        if (isRedirecting)
          router.push(`/${site.value}/editor/drill/${props.isAdmin ? drillId.value : drillSlug.value}?tabIndex=1`);
      });
    } else {
      const diagramAttributes = [];
      const drillUpdateFunc = props.isAdmin ? adminDrillUpdate : teacherDrillUpdate;

      if (needUpload && isEditable.value) {
        for (const diagramItem of tableData.value) {
          const image = await generateTableImage();

          if (!image) {
            Toast.error({
              title: i18n.global.t(`${site.value}.pool_table.generate_image_error`)
            });
          }

          const drillImageUrl = image ? await uploadImage(image) : '';
          diagramAttributes.push({
            imageUrl: drillImageUrl,
            setting: JSON.stringify(diagramItem)
          });
        }

        drillForm.value.diagramAttributes = cloneDeep(diagramAttributes);
      }

      const { data } = await drillUpdateFunc(String(drillDetail.value?.id), drillForm.value, toast);

      drillDiagrams.value = cloneDeep(data.drillUpdate.drill.diagrams);
      drillId.value = data.drillUpdate.drill.id;
      drillSlug.value = data.drillUpdate.drill.slug;
      imageUrl.value = drillDiagrams.value.length > 0 ? drillDiagrams.value[0].imageUrl : '';

      if (!props.isAdmin && !props.isCourse)
        router.push(`/${site.value}/editor/drill/${data.drillUpdate.drill.slug}?tabIndex=${tabIndex.value}`);

      if (isRedirecting && props.isCourse) router.push(`/${site.value}/drills/${drillSlug.value}`);

      await fetchDrillDetail();

      getNewTimestamp();
    }
  };

  const publishBtnVariant = () => {
    return publishStatusText.value === COURSE_PUBLIC_STATUS_ENUMS.PUBLIC ? 'outline-primary' : 'outline-warning';
  };

  const publishBtnIcon = () => {
    return publishStatusText.value === COURSE_PUBLIC_STATUS_ENUMS.PUBLIC ? 'bx-lock-open' : 'bx-lock';
  };

  const handleSubmitDrill = async () => {
    isDrillSubmitButtonLoading.value = true;

    try {
      await handleSubmitDrillOperation(!props.isCourse, true);
    } catch {
      isDrillSubmitButtonLoading.value = false;
      tabIndex.value = 0;
      return;
    }

    isDrillSubmitButtonLoading.value = false;

    if (props.isCourse && props.isCreate) emits('redirect', drillId.value);
    if (props.isCourse && !props.isCreate) emits('closeModal');
  };

  const confirmRequestCensor = async () => {
    const message = i18n.global.t('teacher.drills.confirm_censor.message');

    const confirmed = await confirming(
      new SwalOptions({
        title: i18n.global.t('common.request_review'),
        html: message,
        icon: SwalIconOptions.Question
      })
    );

    if (!confirmed || !drillForm.value.isMaster) return;

    if (confirmed) {
      try {
        await drillSubmit(String(drillId.value));
      } catch (e) {
        console.error(e);
        return;
      }

      router.push(`/teacher/drills`);
    }
  };

  const handleCanvasClick = (event: MouseEvent) => {
    handleClick(event);
    handlePathCreation(event);
  };

  const fetchDrillDetail = async () => {
    const drillIdentifier = props.isCourse
      ? props.activeDrillSlug
      : props.isAdmin
        ? drillId.value
          ? drillId.value
          : route.params.id
        : drillSlug.value
          ? drillSlug.value
          : route.params.slug;
    const needFetchDetail = !!(!props.isCreate && !!drillIdentifier);

    if (needFetchDetail) {
      const drillShowFunc = props.isAdmin ? adminDrillShow : teacherDrillShow;

      const { data } = await drillShowFunc(String(drillIdentifier));
      drillDetail.value = cloneDeep(data.drill);
      publishStatusText.value =
        drillDetail.value?.status === COURSE_PUBLIC_STATUS_ENUMS.PUBLIC
          ? COURSE_PUBLIC_STATUS_ENUMS.PRIVATE
          : COURSE_PUBLIC_STATUS_ENUMS.PUBLIC;

      if (data.drill.diagrams.length) {
        drillDiagrams.value = cloneDeep(data.drill.diagrams);
        imageUrl.value = data.drill.diagrams[0].imageUrl;
        balls.value = cloneDeep(data.drill.diagrams[0].setting.balls);
        targetingBalls.value = cloneDeep(data.drill.diagrams[0].setting.targetingBalls);
        targetingZones.value = cloneDeep(data.drill.diagrams[0].setting.targetingZones);
        textBoxes.value = cloneDeep(data.drill.diagrams[0].setting.textBoxes);
        speedBars.value = cloneDeep(data.drill.diagrams[0].setting.speedBars);
      }

      drillId.value = data.drill.id;
      drillSlug.value = data.drill.slug;
      drillForm.value.assignAttributes(data.drill);
      drillForm.value.step = cloneDeep(data.drill.step) || [];
      drillForm.value.skillIds = cloneDeep((data.drill.skills || []).map((skill: { id: string }) => Number(skill.id)));

      const diagramAttributes = [];
      for (const drillDiagram of drillDiagrams.value) {
        const drillImageUrl = drillDiagram.imageUrl;

        diagramAttributes.push({
          imageUrl: drillImageUrl,
          setting: JSON.stringify(drillDiagram.setting)
        });
      }
      drillForm.value.diagramAttributes = cloneDeep(diagramAttributes);

      getNewTimestamp();
    }
  };

  const initCanvas = () => {
    if (!drillDiagramEditor.value || !drillDiagramEditor.value.drillDiagramCanvas) return;

    canvas.value = drillDiagramEditor.value.drillDiagramCanvas.canvas;
    initializeCanvas();
  };

  const showDrillStepForm = () => {
    if (!drillStepManagement.value) return;

    drillStepManagement.value.handleShowDrillStepModal();
  };

  const initialize = async () => {
    await fetchSavedLayouts();
    await fetchDrillDetail();
    initCanvas();
  };

  const handleChangeStep = async (currentIndex: number, btnEvent = false, btnEventType?: string) => {
    if (currentIndex === tabIndex.value || props.isCreate) return;

    if (needUpdate.value && isEditable.value) await saveDrill(needUpload.value);

    if (
      btnEvent &&
      btnEventType === 'next' &&
      currentIndex === getStepIndexById(drillSteps.value, drillSteps.value.VIDEOS.id)
    )
      await fetchDrillDetail();
    else if (
      btnEvent &&
      btnEventType === 'prev' &&
      currentIndex === getStepIndexById(DRILL_STEPS, DRILL_STEPS.PREVIEW.id)
    )
      return;
    else if (currentIndex === getStepIndexById(DRILL_STEPS, DRILL_STEPS.PREVIEW.id)) await fetchDrillDetail();

    needUpdate.value = false;
    needUpload.value = false;
    drillStepTabIndex.value = 0;
  };

  const makeChangeInStep = (position: number) => {
    const step = getStepByPosition(drillSteps.value, position);
    if (!step) return;

    switch (step.id) {
      case DRILL_STEPS.BASIC_INFO.id:
      case drillSteps.value.VIDEOS.id:
        needUpdate.value = true;
        needUpload.value = false;
        break;
      case drillSteps.value.DIAGRAMS.id:
        needUpdate.value = true;
        needUpload.value = true;
        break;
      default:
        needUpdate.value = false;
        needUpload.value = false;
        break;
    }
  };

  const handlePublish = async () => {
    const statusValue =
      drillDetail.value?.status === COURSE_PUBLIC_STATUS_ENUMS.PUBLIC
        ? COURSE_PUBLIC_STATUS_ENUMS.PRIVATE
        : COURSE_PUBLIC_STATUS_ENUMS.PUBLIC;
    const drillPublishFunc = props.isAdmin ? adminDrillPublish : teacherDrillPublish;

    if (drillDetail.value) {
      await drillPublishFunc(String(drillDetail.value?.id), statusValue).then(res => {
        if (res.data && drillDetail.value) {
          drillDetail.value.status = statusValue;
        }
      });
    }
  };

  const confirmChangePublicStatus = async () => {
    const message = i18n.global.t(`${site.value}.drills.confirm_${publishStatusText.value}.content`);

    const confirmed = await confirming(
      new SwalOptions({
        title: i18n.global.t(`${site.value}.drills.confirm_${publishStatusText.value}.title`),
        html: message,
        icon: SwalIconOptions.Question
      })
    );

    if (!confirmed) return;

    if (confirmed) {
      isChangingPublicStatus.value = true;
      try {
        await handlePublish();
      } catch {
        isChangingPublicStatus.value = false;
        return;
      }
      await fetchDrillDetail();
      isChangingPublicStatus.value = false;
    }
  };

  const resetFormData = async () => {
    if (props.isAdmin) {
      drillForm.value = new AdminDrillForm();
      drillStepForm.value = new AdminDrillStepForm();
    } else {
      drillForm.value = new TeacherDrillForm();
      drillStepForm.value = new TeacherDrillStepForm();
    }

    tabIndex.value = 0;
    needUpdate.value = false;
    needUpload.value = false;
    drillDiagrams.value = [];
    imageUrl.value = '';
    activePoint.value = null;

    balls.value = [];
    targetingBalls.value = [];
    targetingZones.value = [];
    textBoxes.value = [];
    speedBars.value = [];

    await initialize();
  };

  const handleCancelMutateDrillStep = () => {
    if (drillStepManagement.value) {
      drillStepManagement.value.handleHiddenStepForm();
    }
  };

  const handleSubmitStep = () => {
    if (drillStepManagement.value) {
      drillStepManagement.value.handleSubmitStep();
    }
  };

  watch(
    () => drillDetail.value?.status,
    newStatus => {
      publishStatusText.value =
        newStatus === COURSE_PUBLIC_STATUS_ENUMS.PUBLIC
          ? COURSE_PUBLIC_STATUS_ENUMS.PRIVATE
          : COURSE_PUBLIC_STATUS_ENUMS.PUBLIC;
    },
    { immediate: true }
  );

  watch(
    () => props.activeDrillSlug,
    newSlug => {
      drillSlug.value = newSlug;
    },
    { immediate: true }
  );

  onMounted(() => {
    tabIndex.value = props.isCourse ? 0 : Number(route.query.tabIndex || 0);
    initialize();
  });

  defineExpose({
    changeStep,
    handleSubmitDrill,
    initialize,
    resetFormData
  });
</script>
