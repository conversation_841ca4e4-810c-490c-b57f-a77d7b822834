<template>
  <BCard no-body class="shadow-none">
    <BCardBody class="p-3">
      <div v-if="tableData.length > 1" class="d-flex justify-content-center gap-3 align-items-center">
        <Button :disabled="true" icon="bx-left-arrow-alt">
          {{ $t(`${site}.pool_table.actions.previous_page`) }}
        </Button>
        <span>1 / {{ tableData.length }}</span>
        <Button :disabled="true" append-icon="bx-right-arrow-alt">
          {{ $t(`${site}.pool_table.actions.next_page`) }}
        </Button>
      </div>
      <div class="position-relative d-flex justify-content-center" :id="id">
        <canvas
          class="pool-canvas"
          ref="canvas"
          tabindex="1"
          @click="handleCanvasClick"
          @keyup="handleKeyUp"
          @mousedown="handleDragging"
          @mousemove="handleMouseMove"
          @mouseup="handleMouseUp"
        />
      </div>
    </BCardBody>
  </BCard>
</template>

<script lang="ts" setup>
  import { type DiagramItem } from '@/composable/poolTableLayout/config/poolTableConfig';

  import Button from '@/components/base/Button.vue';

  defineProps({
    tableData: {
      type: Array as () => DiagramItem[],
      required: true
    },
    id: {
      type: String,
      default: 'canvas-container'
    },
    site: {
      type: String,
      default: 'admin'
    },
    handleKeyUp: {
      type: Function as PropType<(event: KeyboardEvent) => void>,
      required: true
    },
    handleDragging: {
      type: Function as PropType<(event: MouseEvent) => void>,
      required: true
    },
    handleMouseMove: {
      type: Function as PropType<(event: MouseEvent) => void>,
      required: true
    },
    handleMouseUp: {
      type: Function as PropType<(event: MouseEvent) => void>,
      required: true
    },
    handleCanvasClick: {
      type: Function as PropType<(event: MouseEvent) => void>,
      required: true
    }
  });

  const canvas = ref<HTMLCanvasElement | null>(null);

  defineExpose({
    canvas
  });
</script>
