<template>
  <div class="table-responsive">
    <TableList
      :empty-text="`${site}.drills.step_management.no_data`"
      :fields="fields"
      :items="drillForm.step"
      :metadata="{}"
    >
      <template #cell(diagramImage)="{ value }">
        <img :src="value ? `${value}?t=${timestamp}` : '/pool-table.png'" alt="Diagram Image" class="step-image" />
      </template>
      <template #cell(description)="{ value }">
        <pre class="pre-content text-break line-clamp-8">{{ value }}</pre>
      </template>
      <template v-if="!isPreview" #cell(actions)="{ index }">
        <ul class="list-unstyled hstack gap-1 mb-0">
          <li data-bs-toggle="tooltip" data-bs-placement="top" aria-label="Edit">
            <Button
              icon="bxs-edit-alt"
              size="sm"
              variant="outline-warning"
              :disabled="!isEditable"
              :icon-only="true"
              @click="editStep(index)"
            ></Button>
          </li>
          <li data-bs-toggle="tooltip" data-bs-placement="top" aria-label="Edit">
            <Button
              icon="bxs-trash"
              size="sm"
              variant="outline-danger"
              :disabled="!isEditable"
              :icon-only="true"
              @click="confirmDeleteStep(index)"
            ></Button>
          </li>
        </ul>
      </template>
    </TableList>
  </div>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';

  import Button from '@/components/base/Button.vue';

  import { DrillInputInterface } from '@/utils/interface/drill/drill';

  const emits = defineEmits(['confirmDeleteStep', 'editStep']);

  const drillForm = defineModel('drillForm', {
    type: Object as PropType<DrillInputInterface>,
    required: true
  });

  const props = defineProps({
    site: {
      type: String,
      default: 'admin'
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    timestamp: {
      type: Number,
      default: () => new Date().getTime()
    },
    isEditable: {
      type: Boolean,
      required: true
    }
  });

  const fields = ref<{ key: string; label: string; class?: string }[]>([
    {
      key: 'diagramImage',
      label: i18n.global.t(`${props.site}.drills.step_management.form.diagram_image.label`),
      class: 'step-col'
    },
    {
      key: 'description',
      label: i18n.global.t(`${props.site}.drills.step_management.form.description.label`),
      class: 'step-col'
    },
    ...(!props.isPreview
      ? [{ key: 'actions', label: i18n.global.t('common.actions'), class: 'step-col action-col' }]
      : [])
  ]);

  const editStep = (index: number) => {
    emits('editStep', index);
  };

  const confirmDeleteStep = (index: number) => {
    emits('confirmDeleteStep', index);
  };
</script>

<style lang="scss" scoped>
  .step-image {
    width: 90%;
    height: auto;
  }
  :deep(.step-col) {
    min-width: 100px;
  }
  :deep(.action-col) {
    width: 100px;
  }
  :deep(td) {
    &:has(.step-image) {
      width: 30% !important;
    }
  }
</style>
