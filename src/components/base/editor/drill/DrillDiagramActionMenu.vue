<template>
  <BRow class="g-3">
    <BCol md="4" sm="6">
      <div class="position-relative">
        <BFormSelect v-model="rack" class="form-select" @change="handleRackSelection" :id="`rack-${id}`">
          <BFormSelectOption value="rack" disabled>{{ $t('admin.pool_table.rack.label') }}</BFormSelectOption>
          <BFormSelectOption value="8-ball">
            {{ $t('admin.pool_table.rack.rack_options.eight_ball.label') }}
          </BFormSelectOption>
          <BFormSelectOption value="9-ball-low">
            {{ $t('admin.pool_table.rack.rack_options.nine_ball_low.label') }}
          </BFormSelectOption>
          <BFormSelectOption value="9-ball-high">
            {{ $t('admin.pool_table.rack.rack_options.nine_ball_high.label') }}
          </BFormSelectOption>
          <BFormSelectOption value="10-ball">
            {{ $t('admin.pool_table.rack.rack_options.ten_ball.label') }}
          </BFormSelectOption>
        </BFormSelect>
      </div>
    </BCol>
    <BCol md="4" sm="6">
      <div class="position-relative">
        <BFormSelect v-model="layout" class="form-select" @change="handleLayoutSelection" :id="`layout-${id}`">
          <BFormSelectOption value="layout" disabled>
            {{ $t('admin.pool_table.layout.label') }}
          </BFormSelectOption>
          <BFormSelectOption value="vertically">
            {{ $t('admin.pool_table.layout.layout_options.vertically.label') }}
          </BFormSelectOption>
          <BFormSelectOption value="horizontally">
            {{ $t('admin.pool_table.layout.layout_options.horizontally.label') }}
          </BFormSelectOption>
        </BFormSelect>
      </div>
    </BCol>
    <!-- <BCol md="3" sm="6">
      <div class="position-relative">
        <BFormSelect disabled v-model="duplicate" class="form-select" id="duplicate" @change="handleDuplicateSelection">
          <BFormSelectOption value="duplicate" disabled>
            {{ $t('admin.pool_table.duplicate.label') }}
          </BFormSelectOption>
          <BFormSelectOption value="next_page">
            {{ $t('admin.pool_table.duplicate.duplicate_options.next_page.label') }}
          </BFormSelectOption>
          <BFormSelectOption value="last_page">
            {{ $t('admin.pool_table.duplicate.duplicate_options.last_page.label') }}
          </BFormSelectOption>
          <BFormSelectOption value="previous_page">
            {{ $t('admin.pool_table.duplicate.duplicate_options.previous_page.label') }}
          </BFormSelectOption>
        </BFormSelect>
      </div>
    </BCol> -->
    <BCol md="4" sm="12">
      <div class="position-relative h-100 hstack gap-3">
        <BButtonGroup class="w-100 h-100">
          <BDropdown
            class="w-100 h-100"
            menu-class="dropdown-menu-end"
            toggle-class="bg-gradient"
            variant="outline-primary"
          >
            <template v-slot:button-content>
              {{ $t('common.actions') }}
              <i class="mdi mdi-chevron-down"></i>
            </template>
            <BDropdownItem variant="warning" @click="handleDeletePoint">
              <i class="bx bxs-eraser align-middle"></i>
              {{ $t('admin.pool_table.actions.remove') }}
            </BDropdownItem>
            <!-- <BDropdownItem disabled variant="warning">
              <i class="bx bx-reset align-middle"></i>
              {{ $t('admin.pool_table.actions.clear_page') }}
            </BDropdownItem>
            <BDropdownItem disabled variant="danger">
              <i class="bx bxs-tag-x align-middle"></i>
              {{ $t('admin.pool_table.actions.delete_page') }}
            </BDropdownItem> -->
            <BDropdownDivider></BDropdownDivider>
            <BDropdownItem @click="exportTableImage">
              <i class="bx bx-export align-middle"></i>
              {{ $t('admin.pool_table.actions.export_table_image') }}
            </BDropdownItem>
            <!-- <BDropdownItem disabled variant="primary">
              <i class="bx bx-target-lock align-middle"></i>
              {{ $t('admin.pool_table.actions.targeting_ball_image') }}
            </BDropdownItem>
            <BDropdownItem disabled variant="primary">
              <i class="bx bx-mobile-landscape align-middle"></i>
              {{ $t('admin.pool_table.actions.speed_bar_image') }}
            </BDropdownItem> -->
            <!-- <BDropdownDivider></BDropdownDivider>
            <BDropdownItem disabled variant="primary">
              <i class="bx bx-layout align-middle"></i>
              {{ $t('admin.pool_table.actions.manage_layout') }}
            </BDropdownItem> -->
          </BDropdown>
        </BButtonGroup>
      </div>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  const emits = defineEmits(['exportTableImage', 'handleDeletePoint', 'loadFlippedLayout', 'loadRackLayout']);

  defineProps({
    id: {
      type: String,
      default: 'canvas-container'
    }
  });

  // const duplicate = ref<string>('duplicate');
  const layout = ref<string>('layout');
  const rack = ref<string>('rack');

  const handleRackSelection = () => {
    emits('loadRackLayout', rack.value);

    rack.value = 'rack';
  };

  const handleLayoutSelection = () => {
    emits('loadFlippedLayout', layout.value);

    layout.value = 'layout';
  };

  // const handleDuplicateSelection = () => {
  //   duplicate.value = 'duplicate';
  // };

  const handleDeletePoint = () => {
    emits('handleDeletePoint');
  };

  const exportTableImage = () => {
    emits('exportTableImage');
  };
</script>
