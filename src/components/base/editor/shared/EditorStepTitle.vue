<template>
  <div class="d-flex justify-content-between align-items-center gap-2 p-3 step-title-wrapper">
    <div class="d-flex justify-content-center align-items-center gap-2">
      <h5
        class="step-number-item m-0 border border-2 rounded-circle d-flex justify-content-center align-items-center"
        :class="titleClass"
      >
        {{ position }}
      </h5>
      <p class="fw-bold m-0">{{ label }}</p>
    </div>
    <i v-if="icon" class="bx ms-2" :class="icon"></i>
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps({
    tabIndex: {
      type: Number,
      required: true
    },
    position: {
      type: Number,
      required: true
    },
    label: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      default: null
    }
  });

  const titleClass = computed(() => {
    return props.tabIndex === props.position - 1
      ? 'border-white'
      : 'border-primary border-opacity-10 bg-primary bg-opacity-10';
  });
</script>

<style lang="scss" scoped>
  .step-number-item {
    width: 30px;
    height: 30px;
    h5 {
      line-height: normal;
    }
  }
</style>
