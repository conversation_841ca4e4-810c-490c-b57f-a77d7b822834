<template>
  <BModal size="lg" :title="$t('teacher.editor.censor.status.modal.title')" lazy no-footer scrollable>
    <div class="history-list">
      <BAccordion>
        <BAccordionItem v-for="history in censorHistory" :key="history.id" :title="getHistoryTitle(history)">
          <template #title>
            <div class="d-flex justify-content-between align-items-center w-100">
              <div class="min-w-0">
                <small>
                  <b>
                    {{ formatDate(history.createdAt) }}
                  </b>
                </small>

                <p v-if="history.feedback" class="mb-0 mt-1 line-clamp-1">
                  {{ history.feedback }}
                </p>
              </div>

              <BBadge :variant="getStatusClass(history.status)" class="me-2">
                {{ history.statusI18n }}
              </BBadge>
            </div>
          </template>

          <pre class="pre-content">{{ history.feedback }}</pre>
        </BAccordionItem>
      </BAccordion>
    </div>
  </BModal>
</template>

<script lang="ts" setup>
  import { CensorHistory } from '@/utils/interface/censorHistory';

  defineProps({
    censorHistory: {
      type: Array as PropType<CensorHistory[]>,
      required: true
    }
  });

  const formatDate = (date: string) => {
    return new Date(date).toLocaleString();
  };

  const getHistoryTitle = (history: any) => {
    return `${history.creator?.name} - ${formatDate(history.createdAt)}`;
  };

  const getStatusClass = (status: string) => {
    const classes = {
      feedback: 'danger',
      submitted: 'warning'
    };
    return classes[status as keyof typeof classes] || 'secondary';
  };
</script>

<style lang="scss" scoped>
  :deep(.accordion-button) {
    &:not(.collapsed) {
      background-color: var(--bs-primary);
      color: var(--bs-white);

      &::after {
        filter: invert(100%) sepia(99%) saturate(2%) hue-rotate(31deg) brightness(105%) contrast(100%);
      }
    }
  }

  :deep(.accordion-body) {
    padding: 0.5rem 1rem;
  }
</style>
