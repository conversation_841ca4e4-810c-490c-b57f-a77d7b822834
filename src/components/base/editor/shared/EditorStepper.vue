<template>
  <div class="checkout-tabs">
    <EditorHeader
      v-if="!hideHeader"
      :censor-history="censorHistory"
      :censor-status-message="censorStatusMessage"
      :censor-status="censorStatus"
      :editor-title="editorTitle"
      :is-admin="isAdmin"
      :is-course="isCourse"
      :is-create="isCreate"
      :is-submit-button-loading="isSubmitButtonLoading"
      :show-censor-status="showCensorStatus"
      :site="site"
      @request-censor="requestCensor"
    ></EditorHeader>

    <BTabs
      v-model="tabIndex"
      class="ecommerce-checkout-tabs editor-stepper"
      content-class="w-100"
      nav-class="p-0"
      nav-item-class="p-0 bg-gradient"
      pills
      vertical
      :nav-wrapper-class="navWrapperClass"
    >
      <EditorStep
        v-for="step in stepArray"
        :description="$t(step.description(site))"
        :icon="step.icon"
        :is-create="isCreate"
        :key="step.key"
        :label="$t(step.label(site))"
        :lazy="step.lazyLoad"
        :position="getStepPositionById(stepsConfig, step.id)"
        :steps-config="stepsConfig"
        :tab-index="tabIndex"
        :title="$t(step.title(site))"
        @change-step="handleChangeStep"
        @make-change-in-step="makeChangeInStep"
      >
        <template v-slot:append-section>
          <slot :name="`append-section-${step.key}`" />
        </template>

        <template v-slot:step-content>
          <slot :name="`step-content-${step.key}`" />
        </template>
      </EditorStep>

      <EditorStepperFooter v-if="!hideFooter" :show-preview="showPreview" @handle-preview="handlePreview">
        <slot name="action-buttons"></slot>
      </EditorStepperFooter>
    </BTabs>
  </div>
</template>

<script lang="ts" setup>
  import { CENSOR_STATUS_ENUMS } from '@/utils/constant';
  import { CensorHistory } from '@/utils/interface/censorHistory';
  import { SiteType } from '@/utils/interface/common';

  import { StepConfigInterface, StepSets, getStepPositionById } from '@/composable/useEditor';

  import EditorStep from './EditorStep.vue';
  import EditorHeader from './EditorHeader.vue';
  import EditorStepperFooter from './EditorStepperFooter.vue';

  type CensorStatusType = (typeof CENSOR_STATUS_ENUMS)[keyof typeof CENSOR_STATUS_ENUMS];

  const emits = defineEmits(['change-step', 'make-change-in-step', 'request-censor', 'handle-preview']);
  const tabIndex = defineModel<number>('tabIndex', {
    default: 0
  });
  defineProps({
    showCensorStatus: {
      type: Boolean,
      default: false
    },
    censorHistory: {
      type: Array as PropType<CensorHistory[]>,
      default: () => []
    },
    isCourse: {
      type: Boolean,
      default: false
    },
    site: {
      type: String as PropType<SiteType>,
      required: true
    },
    censorStatus: {
      type: String as PropType<CensorStatusType>,
      required: true
    },
    censorStatusMessage: {
      type: String,
      required: true
    },
    navWrapperClass: {
      type: String,
      default: 'col-xl-3 col-md-4 col-sm-12 col-12'
    },
    stepArray: {
      type: Array as PropType<StepConfigInterface[]>,
      required: true
    },
    isCreate: {
      type: Boolean,
      default: false
    },
    stepsConfig: {
      type: Object as PropType<StepSets>,
      required: true
    },
    isDrill: {
      type: Boolean,
      default: false
    },
    hideFooter: {
      type: Boolean,
      default: false
    },
    editorTitle: {
      type: String,
      default: ''
    },
    hideHeader: {
      type: Boolean,
      default: false
    },
    isSubmitButtonLoading: {
      type: Boolean,
      default: false
    },
    showPreview: {
      type: Boolean,
      default: false
    },
    isAdmin: {
      type: Boolean,
      default: true
    }
  });

  const handleChangeStep = (currentIndex: number) => {
    emits('change-step', currentIndex);
  };

  const makeChangeInStep = (position: number) => {
    emits('make-change-in-step', position);
  };

  const requestCensor = () => {
    emits('request-censor');
  };

  const handlePreview = () => {
    emits('handle-preview');
  };
</script>

<style lang="scss" scoped>
  .checkout-tabs {
    :deep(.nav-pills) {
      .nav-link {
        i {
          color: #555ee6;
          font-size: 20px;
        }
        &.active {
          i {
            color: #fff;
          }
        }
      }
    }
  }

  .editor-stepper {
    :deep(.nav-item) {
      .nav-link {
        &.disabled {
          cursor: not-allowed !important;
          pointer-events: unset;
        }
      }
    }
  }
</style>
