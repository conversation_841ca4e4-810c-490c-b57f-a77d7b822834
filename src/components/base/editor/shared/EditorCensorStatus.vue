<template>
  <div class="d-flex justify-content-between align-items-center gap-2">
    <h5 class="m-0">
      <BBadge pill :variant="badgeVariant">
        <i class="bx" :class="statusIcon"></i>
        {{ censorStatusMessage }}
      </BBadge>
    </h5>

    <Button
      v-if="censorHistory && censorHistory.length > 0"
      :variant="buttonVariant"
      icon="bx-history"
      @click="openHistoryModal"
    >
      {{ $t(`${site}.editor.censor.label`) }}
    </Button>

    <Button
      v-if="isSubmittable"
      icon="bx-send"
      classes="min-w-160"
      variant="outline-warning"
      :disabled="isSubmitButtonLoading"
      @click="confirmRequestCensor"
    >
      {{ $t('common.request_review') }}
    </Button>
  </div>

  <EditorCensorStatusHistoryModal
    v-model:show="showHistoryModal"
    :censor-history="censorHistory"
    @hidden="hideHistoryModal"
  />
</template>

<script setup lang="ts">
  import Button from '@/components/base/Button.vue';
  import EditorCensorStatusHistoryModal from './EditorCensorStatusHistoryModal.vue';

  import { CensorHistory } from '@/utils/interface/censorHistory';
  import { CENSOR_STATUS_ENUMS } from '@/utils/constant';

  type CensorStatusType = (typeof CENSOR_STATUS_ENUMS)[keyof typeof CENSOR_STATUS_ENUMS];

  const emits = defineEmits(['request-censor']);
  const props = defineProps({
    censorStatus: {
      type: String as PropType<CensorStatusType>,
      default: CENSOR_STATUS_ENUMS.DRAFT
    },
    censorStatusMessage: {
      type: String,
      required: true
    },
    censorHistory: {
      type: Array as PropType<CensorHistory[]>,
      required: true
    },
    isCourse: {
      type: Boolean,
      default: false
    },
    site: {
      type: String,
      required: true
    },
    isCreate: {
      type: Boolean,
      default: false
    },
    isSubmitButtonLoading: {
      type: Boolean,
      default: false
    }
  });

  const statusConfig = {
    [CENSOR_STATUS_ENUMS.REJECTED]: {
      badgeVariant: 'danger',
      buttonVariant: 'outline-danger',
      icon: 'bx-x-circle'
    },
    [CENSOR_STATUS_ENUMS.SUBMITTED]: {
      badgeVariant: 'warning',
      buttonVariant: 'outline-primary',
      icon: 'bx-time-five'
    },
    [CENSOR_STATUS_ENUMS.APPROVED]: {
      badgeVariant: 'success',
      buttonVariant: 'outline-primary',
      icon: 'bx-check-circle'
    },
    [CENSOR_STATUS_ENUMS.DRAFT]: {
      badgeVariant: 'secondary',
      buttonVariant: 'outline-primary',
      icon: 'bx-file-blank'
    },
    default: {
      badgeVariant: 'secondary',
      buttonVariant: 'outline-primary',
      icon: 'bx-file-blank'
    }
  } as const;

  const showHistoryModal = ref(false);

  const badgeVariant = computed(() => currentConfig.value.badgeVariant);
  const buttonVariant = computed(() => currentConfig.value.buttonVariant);
  const statusIcon = computed(() => currentConfig.value.icon);

  const currentConfig = computed(() => {
    return statusConfig[props.censorStatus as keyof typeof statusConfig] ?? statusConfig.default;
  });

  const isSubmittable = computed(() => {
    return (
      (props.censorStatus !== CENSOR_STATUS_ENUMS.APPROVED &&
        props.censorStatus !== CENSOR_STATUS_ENUMS.SUBMITTED &&
        !props.isCreate &&
        !props.isCourse) ||
      (props.censorStatus !== CENSOR_STATUS_ENUMS.APPROVED && props.censorStatus !== CENSOR_STATUS_ENUMS.SUBMITTED)
    );
  });

  const openHistoryModal = () => {
    showHistoryModal.value = true;
  };

  const hideHistoryModal = () => {
    showHistoryModal.value = false;
  };

  const confirmRequestCensor = () => {
    emits('request-censor');
  };
</script>

<style lang="scss" scoped>
  .censor-status-bg {
    background-color: var(--bs-body-bg);
    position: sticky;
    top: 76px;
    z-index: 1000;
  }

  .censor-status-overlay-bg {
    background-color: var(--bs-body-bg);
    position: absolute;
    height: 15px;
    width: 100%;
    top: -12px;
  }
</style>
