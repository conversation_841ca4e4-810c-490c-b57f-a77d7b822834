<template>
  <BRow
    class="position-fixed bottom-0 left-12-px end-0 w-100 height-60 d-flex align-items-center bg-white border-top border-1 shadow z-1"
  >
    <BCol>
      <div class="container-fluid">
        <div class="d-flex gap-2" :class="showPreview ? 'justify-content-between' : 'justify-content-end'">
          <div v-if="showPreview">
            <Button icon="bx-show" classes="min-w-160" variant="outline-primary" @click="handlePreview">
              {{ $t('common.preview') }}
            </Button>
          </div>

          <div class="d-flex gap-2">
            <slot></slot>
          </div>
        </div>
      </div>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import Button from '../../Button.vue';

  const emits = defineEmits(['handle-preview']);
  defineProps({
    showPreview: {
      type: Boolean,
      default: false
    }
  });

  const handlePreview = () => {
    emits('handle-preview');
  };
</script>
