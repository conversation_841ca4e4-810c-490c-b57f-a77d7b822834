<template>
  <BTab :disabled="isDisabled" :active="isActiveDefault" :lazy="lazy">
    <template v-slot:title>
      <div class="h-100 w-100" :class="stepClass" @click="changeStep">
        <EditorStepTitle :tab-index="tabIndex" :position="position" :label="label" :icon="icon" />
      </div>
    </template>

    <BCard no-body @click="makeChangeInStep">
      <BCardBody class="pb-0">
        <BRow>
          <BCol sm="9">
            <BCardTitle>
              {{ title }}
            </BCardTitle>
            <p class="card-title-desc">
              {{ description }}
            </p>
          </BCol>

          <BCol class="text-end" sm="3">
            <slot name="append-section"></slot>
          </BCol>
        </BRow>

        <slot name="step-content"></slot>
      </BCardBody>
    </BCard>
  </BTab>
</template>

<script lang="ts" setup>
  import EditorStepTitle from '../shared/EditorStepTitle.vue';

  import { StepSets, getMinStepId, getStepPositionById } from '@/composable/useEditor';

  const route = useRoute();
  const router = useRouter();

  const emits = defineEmits(['changeStep', 'makeChangeInStep']);
  const props = defineProps({
    isCreate: {
      type: Boolean,
      default: false
    },
    position: {
      type: Number,
      required: true
    },
    stepsConfig: {
      type: Object as PropType<StepSets>,
      required: true
    },
    tabIndex: {
      type: Number,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    label: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: null
    },
    description: {
      type: String,
      required: true
    },
    isCourse: {
      type: Boolean,
      default: false
    },
    lazy: {
      type: Boolean,
      default: false
    }
  });

  const isActiveDefault = computed(() => {
    const defaultStepId = getMinStepId(props.stepsConfig);
    return props.position === getStepPositionById(props.stepsConfig, defaultStepId);
  });

  const isDisabled = computed(() => {
    return props.isCreate && !isActiveDefault.value;
  });

  const stepClass = computed(() => {
    return isDisabled.value ? 'opacity-50 cursor-not-allowed' : '';
  });

  const changeStep = () => {
    if (!props.isCourse) {
      const currentQuery = { ...route.query };
      router.replace({
        query: {
          ...currentQuery,
          tabIndex: props.position - 1
        }
      });
    }
    emits('changeStep', props.position - 1);
  };

  const makeChangeInStep = () => {
    emits('makeChangeInStep', props.position);
  };
</script>
