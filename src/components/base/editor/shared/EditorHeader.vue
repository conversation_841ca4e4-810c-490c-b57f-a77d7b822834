<template>
  <BRow
    class="position-fixed top-0 left-12-px end-0 w-100 height-60 d-flex align-items-center bg-white border-top border-1 shadow z-2"
  >
    <BCol>
      <div class="container-fluid">
        <div class="d-flex align-items-center justify-content-between">
          <div class="d-flex align-items-center justify-content-start gap-4">
            <Button icon="bx-left-arrow-alt" icon-only variant="outline-primary" icon-size="3" @click="handleBack">
              {{ $t(`${site}.editor.back_to_drill`) }}
            </Button>

            <h5 class="mb-0">
              {{ editorTitle }}
            </h5>
          </div>

          <EditorCensorStatus
            v-if="showCensorStatus"
            :censor-history="censorHistory"
            :censor-status-message="censorStatusMessage"
            :censor-status="censorStatus"
            :is-course="isCourse"
            :is-create="isCreate"
            :is-submit-button-loading="isSubmitButtonLoading"
            :site="site"
            @request-censor="requestCensor"
          ></EditorCensorStatus>
        </div>
      </div>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import Button from '../../Button.vue';
  import EditorCensorStatus from './EditorCensorStatus.vue';

  import { CENSOR_STATUS_ENUMS } from '@/utils/constant';
  import { CensorHistory } from '@/utils/interface/censorHistory';
  import { SiteType } from '@/utils/interface/common';

  type CensorStatusType = (typeof CENSOR_STATUS_ENUMS)[keyof typeof CENSOR_STATUS_ENUMS];

  const route = useRoute();
  const router = useRouter();

  const emits = defineEmits(['request-censor']);
  const props = defineProps({
    site: {
      type: String as PropType<SiteType>,
      required: true
    },
    editorTitle: {
      type: String,
      default: ''
    },
    showCensorStatus: {
      type: Boolean,
      default: false
    },
    censorHistory: {
      type: Array as PropType<CensorHistory[]>,
      default: () => []
    },
    isCourse: {
      type: Boolean,
      default: false
    },
    censorStatus: {
      type: String as PropType<CensorStatusType>,
      default: CENSOR_STATUS_ENUMS.DRAFT
    },
    censorStatusMessage: {
      type: String,
      required: true
    },
    isCreate: {
      type: Boolean,
      default: false
    },
    isSubmitButtonLoading: {
      type: Boolean,
      default: false
    },
    isAdmin: {
      type: Boolean,
      default: true
    }
  });

  const handleBack = () => {
    const { id, slug } = route.params || {};
    const type = props.isCourse ? 'courses' : 'drills';
    const subPath = props.isCourse ? 'detail' : null;

    const getIdentifier = () => {
      if (props.isCreate) return null;
      return props.isAdmin ? id : slug;
    };

    const pathComponents = [props.site, type, getIdentifier(), subPath].filter(Boolean);
    const path = `/${pathComponents.join('/')}`;

    router.push(path);
  };

  const requestCensor = () => {
    emits('request-censor');
  };
</script>
