<template>
  <BCard class="text-center bg-white m-0 p-0 rounded-2 shadow-none" no-body>
    <Loader :loading="isGettingCourseSectionItemDetail">
      <BCardBody class="p-3">
        <CourseSectionItemContentText
          v-if="isEditingTextContent || hasTextContent"
          v-model:course-section-item-form="courseSectionItemForm"
          :course-section-id="courseSectionId"
          :course-section-item-id="courseSectionItemId"
          :is-approved="isApproved"
          :is-editing-text-content="isEditingTextContent"
          :loading="isUpdateTextContentBtnLoading"
          @cancel-edit-text-content="handleCancelEditTextContent"
          @remove-text-content="handleRemoveTextContent"
          @update-text-content="handleUpdateTextContent"
        ></CourseSectionItemContentText>

        <CourseSectionItemContentVideo
          v-if="isEditingVideoContent || hasVideoContent"
          :course-section-id="courseSectionId"
          :course-section-item-id="courseSectionItemId"
          :is-approved="isApproved"
          :is-deleting-video="isDeletingVideo"
          :is-editing-video-content="isEditingVideoContent"
          :is-uploaded-video-list-loading="isUploadedVideoListLoading"
          :uploaded-videos="courseSectionItem?.videos || []"
          :video-file-name="videoFileName"
          @cancel-edit-video-content="handleCancelEditVideoContent"
          @delete-video="handleDeleteVideo"
          @uploaded-video="handleUploadedVideo"
        ></CourseSectionItemContentVideo>

        <CourseSectionItemContentDrill
          v-if="isEditingDrillContent || hasDrillContent"
          v-model:is-create-drill-modal-open="isCreateDrillModalOpen"
          v-model:isSelectDrillModalOpen="isSelectDrillModalOpen"
          v-model:query="query"
          :active-drill-slug="activeDrillSlug"
          :course-section-id="courseSectionId"
          :course-section-item-id="courseSectionItemId"
          :drills="items"
          :is-approved="isApproved"
          :is-drill-submit-button-loading="isDrillSubmitButtonLoading"
          :is-editing-drill-content="isEditingDrillContent"
          :is-list-drill-loading="isListDrillLoading"
          :is-selected-drill-list-loading="isSelectedDrillListLoading"
          :level-options="levelOptions"
          :loading="isUpdateDrillContentBtnLoading"
          :metadata="metadata"
          :selected-drills="courseSectionItem?.drills || []"
          :skill-options="skillOptions"
          :timestamp="timestamp"
          @cancel-edit-drill-content="handleCancelEditDrillContent"
          @delete-selected-drill="handleDeleteSelectedDrill"
          @edit-selected-drill="handleEditDrill"
          @fetch-list="changePage"
          @handle-hidden-create-drill-modal="handleHiddenCreateDrillModal"
          @hidden="handleHiddenSelectDrillModal"
          @open-create-drill-modal="openCreateDrillModal"
          @open-select-drill-modal="openSelectDrillModal"
          @reset="handleReset"
          @search="handleSearch"
          @select-drill="selectDrill"
        ></CourseSectionItemContentDrill>

        <ul v-if="!isApproved" class="list-unstyled text-start gap-1 mb-0 action-btn-wrapper">
          <li v-if="!hasTextContent && !isEditingTextContent">
            <Button classes="width-140" icon="bx-plus" variant="outline-primary" @click="handleAddText">
              {{ $t('teacher.editor.course.steps.sections.action_btn.add_text') }}
            </Button>
          </li>
          <li v-if="!hasVideoContent && !isEditingVideoContent">
            <Button classes="width-140" icon="bx-plus" variant="outline-primary" @click="handleAddVideo">
              {{ $t('teacher.editor.course.steps.sections.action_btn.add_video') }}
            </Button>
          </li>
          <li v-if="!hasDrillContent && !isEditingDrillContent">
            <Button classes="width-140" icon="bx-plus" variant="outline-primary" @click="handleAddDrill">
              {{ $t('teacher.editor.course.steps.sections.action_btn.add_drill') }}
            </Button>
          </li>
        </ul>
      </BCardBody>
    </Loader>
  </BCard>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';
  import { useGoList } from '@bachdx/b-vuse';

  import useSwal from '@/composable/swal';

  import {
    courseSectionItemAddDrill,
    courseSectionItemDetail,
    courseSectionItemRemoveDrill,
    courseSectionItemUpdate
  } from '@/services/teacher/repositories/courseSectionItem';
  import {
    courseSectionItemAddVideo,
    courseSectionItemDeleteVideo
  } from '@/services/teacher/repositories/courseSectionItem';
  import { drillList, drillCreate } from '@/services/teacher/repositories/drill';

  import { CourseSectionItemForm } from '@/forms/teacher/courseSectionItem';
  import { DrillListQueryFormModel, DrillForm } from '@/forms/teacher/drill';

  import { CourseSectionItemInterface } from '@/utils/interface/teacher/courseSectionItem';
  import { OptionInterface } from '@/utils/interface/select-options';
  import { SwalIconOptions, SwalOptions } from '@/utils/swal-options';

  import Button from '../../Button.vue';
  import CourseSectionItemContentDrill from './CourseSectionItemContentDrill.vue';
  import CourseSectionItemContentText from './CourseSectionItemContentText.vue';
  import CourseSectionItemContentVideo from './CourseSectionItemContentVideo.vue';
  import Loader from '@/components/dashboard/Loader.vue';

  const route = useRoute();
  const router = useRouter();
  const { confirming } = useSwal();
  const { items, metadata, search, changePage, query, reset, parseQueryAndFetch } = useGoList({
    fetchListFnc: drillList,
    fetchKey: 'drills',
    route: route,
    queryFormModels: DrillListQueryFormModel,
    router: router,
    perPage: 10,
    reflectUrl: false
  });

  const emits = defineEmits(['getNewTimestamp', 'removeCourseSectionItemBlank']);
  const courseSectionItemForm = defineModel('courseSectionItemForm', {
    type: Object as PropType<CourseSectionItemForm>,
    required: true
  });
  const props = defineProps({
    courseId: {
      type: Number,
      required: true
    },
    courseSectionId: {
      type: Number,
      required: true
    },
    courseSectionItemId: {
      type: Number,
      required: true
    },
    courseSectionItemIndex: {
      type: Number,
      required: true
    },
    courseTitle: {
      type: String,
      required: true
    },
    courseSectionIndex: {
      type: Number,
      required: true
    },
    levelOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    skillOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    isApproved: {
      type: Boolean,
      required: true
    },
    timestamp: {
      type: Number,
      required: true
    }
  });

  const activeDrillSlug = ref<string>();
  const courseSectionItem = ref<CourseSectionItemInterface>();
  const drillForm = ref<DrillForm>(new DrillForm());
  const isCreateDrillModalOpen = ref(false);
  const isDeletingVideo = ref<boolean>(false);
  const isDrillSubmitButtonLoading = ref<boolean>(false);
  const isEditingDrillContent = ref<boolean>(false);
  const isEditingTextContent = ref<boolean>(false);
  const isEditingVideoContent = ref<boolean>(false);
  const isGettingCourseSectionItemDetail = ref<boolean>(false);
  const isListDrillLoading = ref<boolean>(false);
  const isSelectDrillModalOpen = ref<boolean>(false);
  const isSelectedDrillListLoading = ref<boolean>(false);
  const isUpdateDrillContentBtnLoading = ref<boolean>(false);
  const isUpdateTextContentBtnLoading = ref<boolean>(false);
  const isUploadedVideoListLoading = ref<boolean>(false);
  const videoFileName = ref<string>('');

  const hasTextContent = computed(() => {
    return !!courseSectionItem.value?.content;
  });

  const hasVideoContent = computed(() => {
    return !!courseSectionItem.value?.videos.length;
  });

  const hasDrillContent = computed(() => {
    return !!courseSectionItem.value?.drills.length;
  });

  const selectedDrillIds = computed(() => courseSectionItem.value?.drills?.map(drill => drill.id) || []);

  const getCourseSectionItemDetail = async () => {
    const res = await courseSectionItemDetail(
      String(props.courseSectionItemId),
      String(props.courseId),
      String(props.courseSectionId)
    );

    courseSectionItem.value = cloneDeep(res.data.courseSectionItem);
    courseSectionItemForm.value.assignAttributes(res.data.courseSectionItem);
  };

  const handleGetCourseSectionItemDetail = async () => {
    isGettingCourseSectionItemDetail.value = true;
    try {
      await getCourseSectionItemDetail();
    } catch {
      isGettingCourseSectionItemDetail.value = false;
      return;
    }
    isGettingCourseSectionItemDetail.value = false;
  };

  const handleAddText = () => {
    isEditingTextContent.value = true;
  };

  const handleCancelEditTextContent = () => {
    isEditingTextContent.value = false;
  };

  const handleAddVideo = () => {
    isEditingVideoContent.value = true;
  };

  const handleUpdateTextContent = async () => {
    isUpdateTextContentBtnLoading.value = true;
    try {
      await courseSectionItemUpdate(
        String(props.courseSectionItemId),
        String(props.courseId),
        String(props.courseSectionId),
        courseSectionItemForm.value
      );
    } catch {
      isUpdateTextContentBtnLoading.value = false;
      return;
    }
    isUpdateTextContentBtnLoading.value = false;
    isEditingTextContent.value = false;
    await handleGetCourseSectionItemDetail();
    emits('removeCourseSectionItemBlank', props.courseSectionId, props.courseSectionItemId);
  };

  const handleRemoveTextContent = async () => {
    const message = i18n.global.t('teacher.editor.course.steps.sections.content_type.text.remove_text_message');

    const confirmed = await confirming(
      new SwalOptions({
        html: message,
        icon: SwalIconOptions.Warning
      })
    );

    if (!confirmed) return;

    if (confirmed) {
      courseSectionItemForm.value.content = '';
      await handleUpdateTextContent();
    }
  };

  const handleAddDrill = () => {
    isEditingDrillContent.value = true;
  };

  const handleCancelEditDrillContent = () => {
    isEditingDrillContent.value = false;
  };

  const handleCancelEditVideoContent = () => {
    isEditingVideoContent.value = false;
  };

  const handleDeleteSelectedDrill = async (id: number) => {
    const message = i18n.global.t('teacher.drills.delete_modal.message');

    const confirmed = await confirming(
      new SwalOptions({
        html: message,
        icon: SwalIconOptions.Warning
      })
    );

    if (!confirmed) return;
    if (!id) return;

    isSelectedDrillListLoading.value = true;
    try {
      await courseSectionItemRemoveDrill(
        String(props.courseSectionItemId),
        String(props.courseId),
        String(props.courseSectionId),
        String(id)
      );

      await handleGetCourseSectionItemDetail();
    } catch {
      isSelectedDrillListLoading.value = false;
      return;
    }

    isSelectedDrillListLoading.value = false;
  };

  const fetchListDrill = async () => {
    isListDrillLoading.value = true;
    query.value = {
      ...query.value,
      idNotIn: selectedDrillIds.value
    };
    await parseQueryAndFetch(query.value);
    isListDrillLoading.value = false;
  };

  const openSelectDrillModal = async () => {
    isSelectDrillModalOpen.value = true;
    await fetchListDrill();
  };

  const handleHiddenSelectDrillModal = () => {
    activeDrillSlug.value = undefined;
    isSelectDrillModalOpen.value = false;
  };

  const handleReset = async () => {
    isListDrillLoading.value = true;
    query.value = {
      idNotIn: courseSectionItem.value?.drills ? courseSectionItem.value.drills.map(drill => drill.id) : []
    };
    await reset({ defaultQuery: query.value });
    isListDrillLoading.value = false;
  };

  const handleSearch = async () => {
    isListDrillLoading.value = true;
    query.value = {
      ...query.value,
      idNotIn: courseSectionItem.value?.drills ? courseSectionItem.value.drills.map(drill => drill.id) : []
    };
    await search();
    isListDrillLoading.value = false;
  };

  const selectDrill = async (id: number, isCreate = false) => {
    isListDrillLoading.value = true;
    try {
      await courseSectionItemAddDrill(
        String(props.courseSectionItemId),
        String(props.courseId),
        String(props.courseSectionId),
        String(id)
      );

      if (!isCreate) {
        isCreateDrillModalOpen.value = false;
      }

      isSelectDrillModalOpen.value = false;

      await handleGetCourseSectionItemDetail();
      emits('removeCourseSectionItemBlank', props.courseSectionId, props.courseSectionItemId);
    } catch {
      isListDrillLoading.value = false;
      return;
    }

    isListDrillLoading.value = false;
  };

  const handleCreateCourseDrill = async () => {
    drillForm.value.isMaster = false;
    drillForm.value.title = `${i18n.global.t('teacher.course_section_item.form.drill')} ${i18n.global.t(
      'teacher.course_section_item.form.title'
    )} ${props.courseSectionItemIndex + 1} ${i18n.global.t('teacher.course_section.form.title')} ${
      props.courseSectionIndex + 1
    } ${props.courseTitle}`;

    await drillCreate(drillForm.value).then(res => {
      activeDrillSlug.value = res.data.drillCreate.drill.slug;
      selectDrill(res.data.drillCreate.drill.id, true);
    });
  };

  const openCreateDrillModal = async () => {
    isDrillSubmitButtonLoading.value = true;
    try {
      await handleCreateCourseDrill();
    } catch {
      isDrillSubmitButtonLoading.value = false;
    }
    isCreateDrillModalOpen.value = true;
    isDrillSubmitButtonLoading.value = false;
  };

  const handleHiddenCreateDrillModal = async () => {
    await handleGetCourseSectionItemDetail();
    activeDrillSlug.value = undefined;
    isCreateDrillModalOpen.value = false;
    emits('getNewTimestamp');
    emits('removeCourseSectionItemBlank', props.courseSectionId, props.courseSectionItemId);
  };

  const handleEditDrill = async (slug: string) => {
    activeDrillSlug.value = slug;
    isCreateDrillModalOpen.value = true;
  };

  const handleDeleteVideo = async (videoId: number) => {
    const message = i18n.global.t('video.modal.delete_message');
    const confirmed = await confirming(
      new SwalOptions({
        html: message,
        icon: SwalIconOptions.Warning
      })
    );
    if (!confirmed) return;

    try {
      isDeletingVideo.value = true;
      await courseSectionItemDeleteVideo(String(videoId), String(props.courseId));
      await handleGetCourseSectionItemDetail();
    } catch {
      isDeletingVideo.value = false;
      return;
    } finally {
      isDeletingVideo.value = false;
    }
  };

  const handleUploadedVideo = async (videoName: string, videoId: number) => {
    await courseSectionItemAddVideo(String(props.courseSectionItemId), String(props.courseId), String(videoId)).then(
      () => {
        if (!courseSectionItem.value) return;
        courseSectionItem.value.videos.push({
          id: videoId,
          status: 'processing',
          thumbnailURL: '',
          videoPlatforms: [],
          title: videoName,
          isPlayable: false
        });
      }
    );
    emits('removeCourseSectionItemBlank', props.courseSectionId, props.courseSectionItemId);
  };

  onMounted(async () => {
    handleGetCourseSectionItemDetail();
  });
</script>

<style lang="scss" scoped>
  .action-btn-wrapper {
    li {
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
</style>
