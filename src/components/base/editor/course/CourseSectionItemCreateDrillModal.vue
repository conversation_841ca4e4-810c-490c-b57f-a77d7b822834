<template>
  <BModal
    body-class="p-0"
    dialog-class="drill-modal-dialog-wrapper"
    fullscreen="xxl"
    lazy
    ok-only
    scrollable
    size="xl"
    unmount-lazy
    ok-variant="success"
    :ok-title="$t('teacher.pool_table.actions.update_drill')"
    :title="$t('teacher.course.create_drill_modal.title')"
    @hidden="handleHidden"
    @ok="handleSubmitDrill"
  >
    <BCard class="bg-body mb-0 position-relative" no-body>
      <BCardBody class="border-bottom pb-0">
        <DrillEditorStepper
          ref="editorStepper"
          :active-drill-slug="activeDrillSlug"
          :is-admin="false"
          :is-course="true"
          :level-options="levelOptions"
          :skill-options="skillOptions"
          @close-modal="closeModal"
          @redirect="handleRedirect"
        />
      </BCardBody>
    </BCard>
  </BModal>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';

  import useDynamicSearch from '@/composable/dynamicSearch';

  import SearchField from '@/utils/search-fields';
  import { OptionInterface } from '@/utils/interface/select-options';

  import DrillEditorStepper from '@/components/base/editor/drill/DrillEditorStepper.vue';

  const emits = defineEmits(['hidden', 'selectDrill']);

  defineProps({
    levelOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    skillOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    activeDrillSlug: {
      type: String,
      default: null
    }
  });

  const router = useRouter();

  const { searchFieldsList, searchComponents } = useDynamicSearch();

  const editorStepper = ref<InstanceType<typeof DrillEditorStepper> | null>(null);

  const closeModal = () => {
    resetModal();
    emits('hidden');
  };

  searchFieldsList.value = [
    new SearchField(
      i18n.global.t('teacher.drills.search_form.fields.title'),
      'titleCont',
      'bx bx-search-alt',
      searchComponents.TextInputField,
      { xxl: 8, lg: 12 }
    )
  ];

  const handleRedirect = async () => {
    if (editorStepper.value) {
      await editorStepper.value.initialize();
      editorStepper.value.changeStep('next');
    }
  };

  const handleSubmitDrill = () => {
    if (editorStepper.value) {
      editorStepper.value.handleSubmitDrill();
    }
  };

  const handleHidden = () => {
    resetModal();
    emits('hidden');
  };

  const resetModal = () => {
    router.push({ query: {} });
    if (editorStepper.value) editorStepper.value.resetFormData();
  };
</script>
