<template>
  <BRow>
    <BCol lg="12" class="mb-4" v-if="availablePackages.length > 0">
      <div class="d-flex align-items-center justify-content-end">
        <Button variant="success" @click="showModal = true">
          <i class="mdi mdi-check"></i>
          {{ $t('teacher.course.basic_form.form.is_setting_package') }}
        </Button>
      </div>
    </BCol>
  </BRow>

  <BRow>
    <BCol
      v-for="(cpk, index) in courseForm.coursePackageAttributes?.filter(attr => !attr.isDeleted)"
      :key="cpk.id"
      :lg="getColumnSize(courseForm.coursePackageAttributes)"
      :md="getColumnSize(courseForm.coursePackageAttributes)"
      :sm="12"
      class="mb-4"
    >
      <div class="package-card-container">
        <CoursePackageFormCard
          :course-package="cpk"
          :index="index"
          :package-detail="getPackageDetail(cpk.packageDeal?.name as string)"
          :packageName="getPackageName(cpk.packageDeal?.name as string)"
          :is-approved="isApproved"
        />
      </div>
    </BCol>
  </BRow>

  <BRow>
    <BCol class="pb-4">
      <div class="alert alert-info">
        <div class="mb-2">
          <strong>
            <b>※ {{ $t('common.note') }}: </b>
          </strong>
        </div>

        <ul class="mb-0 ps-3">
          <li>{{ $t('teacher.editor.course.steps.pricing.form.sale_price.subtitle.1') }}</li>
          <li>
            {{ $t('teacher.editor.course.steps.pricing.form.sale_price.subtitle.2') }}
          </li>
          <li>
            {{ $t('teacher.editor.course.steps.pricing.form.sale_price.subtitle.3') }}
          </li>
        </ul>
      </div>
    </BCol>
  </BRow>

  <BModal
    v-model="showModal"
    :title="$t('teacher.editor.course.steps.pricing.add_package')"
    title-class="font-18 fw-bold"
    size="lg"
    lazy
    no-footer
    centered
    hide-header-close
    @ok="handleAddPackage"
  >
    <template #modal-header="{ close }">
      <div class="d-flex justify-content-between align-items-center w-100">
        <h4 class="modal-title">{{ $t('teacher.course.basic_form.form.is_setting_package') }}</h4>
        <button type="button" class="btn-close" @click="close()"></button>
      </div>
    </template>

    <div class="mb-4">
      <div class="row g-3">
        <PackageCard
          v-for="(cpk, index) in availablePackages"
          :key="index"
          :packageName="cpk.title"
          :packageDetail="cpk.detail"
          :column-size="getResponsiveColumnClass(availablePackages.length)"
          @selectPackage="handleSelectPackage(cpk.title, cpk.packageDealId as string)"
        />
      </div>
    </div>
  </BModal>
</template>

<script lang="ts" setup>
  import { CourseInputInterface } from '@/utils/interface/teacher/course';

  import { packageDetails } from '@/utils/config-data-info';

  import CoursePackageFormCard from './CoursePackageFormCard.vue';
  import { PACKAGES_NAMES } from '@/utils/constant';
  import { OptionInterface } from '@/utils/interface/select-options';

  import Button from '../../Button.vue';
  import PackageCard from '@/components/teacher/courses/details/PackageCard.vue';
  import { CoursePackageInterface } from '@/utils/interface/teacher/course';

  const courseForm = defineModel('courseForm', {
    type: Object as PropType<CourseInputInterface>,
    required: true
  });

  const props = defineProps({
    isApproved: {
      type: Boolean,
      required: true
    },
    packageDealOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    }
  });

  const showModal = ref(false);

  const availablePackages = computed(() => {
    const selectedPackageIds =
      courseForm.value.coursePackageAttributes
        ?.filter(attr => !attr.isDeleted)
        .map(attr => Number(attr.packageDealId)) || [];

    const availablePackageOptions = props.packageDealOptions.filter((option: OptionInterface) => {
      const optionValue = typeof option.value === 'number' ? option.value : Number(option.value);
      return !selectedPackageIds.includes(optionValue);
    });

    return availablePackageOptions.map((option: OptionInterface) => {
      return {
        title: option.label,
        detail: getPackageDetail(option.label),
        packageDealId: option.value
      };
    });
  });

  const getPackageDetail = (ck: string) => {
    switch (ck) {
      case PACKAGES_NAMES.BASIC:
        return packageDetails.basic;
      case PACKAGES_NAMES.ADVANCE:
        return packageDetails.advance;
      case PACKAGES_NAMES.OFFLINE:
        return packageDetails.offline;
      default:
        return packageDetails.basic;
    }
  };

  const getPackageName = (ck: string) => {
    return ck?.toCamelCase();
  };

  const handleAddPackage = () => {
    showModal.value = false;
  };

  const handleSelectPackage = (packageName: string, packageDealId: string) => {
    const existingPackage = courseForm.value.coursePackageAttributes?.find(
      attr => attr.packageDealId === packageDealId
    );

    if (existingPackage && existingPackage.isDeleted) {
      existingPackage.isDeleted = false;
    } else if (!existingPackage) {
      courseForm.value.coursePackageAttributes?.push({
        salePrice: 0,
        price: 0,
        packageDealId: packageDealId,
        packageDeal: {
          id: packageDealId,
          name: packageName
        }
      });
    }

    showModal.value = false;
  };

  const getColumnSize = (packageAttributes: CoursePackageInterface[] | undefined) => {
    if (!packageAttributes) return 12;

    const activePackagesCount = packageAttributes.filter(pkg => !pkg.isDeleted).length;

    if (activePackagesCount === 1) {
      return 12;
    } else if (activePackagesCount === 2) {
      return 6;
    } else {
      return 4;
    }
  };

  const getResponsiveColumnClass = (length: number) => {
    if (length === 1) {
      return 'col-12';
    } else if (length === 2) {
      return 'col-md-6';
    } else {
      return 'col-md-4';
    }
  };
</script>

<style scoped>
  .alert-info {
    background: #eff6ff;
    border: 1px solid #bfdbfe;
    border-radius: 8px;
    color: #1e40af;
  }

  .alert-info ul li {
    margin-bottom: 8px;
  }

  .custom-switch {
    transform: scale(1.2);
  }

  /* Responsive package cards layout */
  .row {
    display: flex;
    flex-wrap: wrap;
  }

  .row > [class*='col-'] {
    display: flex;
    flex-direction: column;
  }

  /* Ensure equal height for all package cards */
  .package-card-container {
    display: flex;
    flex: 1;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .row > [class*='col-'] {
      margin-bottom: 1rem;
    }
  }

  @media (min-width: 769px) {
    .row > [class*='col-'] {
      min-height: 100%;
    }
  }
</style>
