<template>
  <Loader :loading="isUploadedVideoListLoading">
    <BRow v-if="uploadedVideos.length">
      <BCol xxl="6" cols="12" v-for="video in uploadedVideos" :key="video.id">
        <div class="w-100 pb-4">
          <VideoPlayer
            v-if="video.isPlayable"
            :video-id="String(video.id)"
            :thumbnail="video.thumbnailURL"
            :video-title="video.title"
          />

          <div v-else>
            <img class="btn w-100 video-thumbnail-img" src="/processing-video.png" alt="video processing" />
          </div>

          <div class="text-center d-flex justify-content-center align-items-center mt-2">
            <span class="font-size-14">{{ video.title }}</span>
            <span v-if="!isApproved" class="ms-2">
              <Button
                classes="rounded-circle p-1 font-size-14 d-flex align-items-center justify-content-center"
                icon-only
                icon="bx-trash"
                variant="danger"
                :disabled="isDeletingVideo"
                :loading="isDeletingVideo"
                @click="handleDeleteVideo(video.id)"
              ></Button>
            </span>
          </div>
        </div>
      </BCol>
    </BRow>
  </Loader>
</template>

<script lang="ts" setup>
  import { VideoInterface } from '@/utils/interface/video';

  import Button from '../../Button.vue';
  import VideoPlayer from '@/components/base/VideoPlayer.vue';

  const emits = defineEmits(['deleteVideo']);
  defineProps({
    uploadedVideos: {
      type: Array as PropType<VideoInterface[]>,
      required: true
    },
    isUploadedVideoListLoading: {
      type: Boolean,
      default: false
    },
    isDeletingVideo: {
      type: Boolean,
      default: false
    },
    isApproved: {
      type: Boolean,
      required: true
    }
  });

  const handleDeleteVideo = (videoId: number) => {
    emits('deleteVideo', videoId);
  };
</script>
