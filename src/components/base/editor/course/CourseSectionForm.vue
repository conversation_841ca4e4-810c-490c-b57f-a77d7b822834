<template>
  <div class="course-section-form-wrapper">
    <div class="d-flex align-items-center w-100">
      <h5 class="pb-3">
        <strong class="pe-2">
          <template v-if="isCreate">{{ $t('teacher.course_section.form.new_title') }}:</template>
          <template v-else>{{ $t('teacher.course_section.form.title') }} {{ courseSectionIndex + 1 }} :</template>
        </strong>
      </h5>

      <BaseFormValidator name="title" required horizontal class="flex-grow-1">
        <input
          v-model="courseSectionForm.title"
          class="form-control"
          id="title"
          name="title"
          type="text"
          :placeholder="$t('teacher.course_section.form.fields.title')"
        />
      </BaseFormValidator>
    </div>

    <BCardBody class="p-0">
      <div class="d-flex justify-content-end gap-2">
        <Button variant="outline-primary" icon="bx-x" @click="handleCancel">
          {{ $t('teacher.course_section.form.cancel_btn') }}
        </Button>
        <Button icon="bx-save" variant="success" :disabled="loading" :loading="loading" @click="handleSubmit">
          {{ $t('teacher.course_section.form.save_btn') }}
        </Button>
      </div>
    </BCardBody>
  </div>
</template>

<script lang="ts" setup>
  import { CourseSectionForm } from '@/forms/teacher/courseSection';

  import Button from '../../Button.vue';

  const emits = defineEmits(['cancel', 'submit']);
  const courseSectionForm = defineModel('courseSectionForm', {
    type: Object as PropType<CourseSectionForm>,
    required: true
  });
  defineProps({
    loading: {
      type: Boolean,
      default: false
    },
    isCreate: {
      type: Boolean,
      default: false
    },
    courseSectionIndex: {
      type: Number,
      default: 0
    }
  });

  const handleCancel = () => {
    emits('cancel');
  };

  const handleSubmit = () => {
    emits('submit');
  };
</script>
