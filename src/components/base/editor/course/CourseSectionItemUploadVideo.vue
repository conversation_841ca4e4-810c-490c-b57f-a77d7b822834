<template>
  <div>
    <UploadVideo
      class="text-start"
      enable-default-title
      parent-type="CourseSectionItem"
      site="teacher"
      @upload-success="handleUploadedVideo"
    />
  </div>
</template>

<script lang="ts" setup>
  import UploadVideo from '@/components/base/video/UploadVideo.vue';

  const emits = defineEmits(['uploadedVideo', 'deleteUploadingVideo', 'saveAndPostVideo']);
  defineProps({
    videoFileName: {
      type: String,
      default: ''
    },
    isDeletingVideo: {
      type: Boolean,
      default: false
    }
  });

  const handleUploadedVideo = (videoName: string, videoId: number) => {
    emits('uploadedVideo', videoName, videoId);
  };
</script>
