<template>
  <div>
    <BCard
      class="border border-primary bg-white mb-2 rounded-2"
      no-body
      :class="{ 'border-danger': isCourseSectionItemBlank }"
    >
      <BCardHeader
        v-if="!isEditingSectionItem"
        class="cursor-pointer rounded-2 course-section-item-wrapper d-flex justify-content-between align-items-center gap-4"
        header-tag="div"
        @click="handleCollapseChange(!isExpanded)"
      >
        <div class="d-flex align-items-center">
          <h5 class="my-0 line-clamp-3">
            <i class="pe-2 mdi" :class="{ 'mdi-chevron-up': isExpanded, 'mdi-chevron-down': !isExpanded }"></i>
            <strong>{{ $t('teacher.course_section_item.form.title') }} {{ courseSectionItemIndex + 1 }}: </strong>
            <span>
              {{ courseSectionItemTitle }}
            </span>
          </h5>
          <span v-if="courseSectionItemIsFree" class="mx-2 px-2 rounded text-success course-section-free">
            {{ $t('teacher.course_section_item.form.fields.is_free') }}
          </span>
          <span v-else class="mx-2 px-2 rounded text-secondary course-section-private">
            {{ $t('teacher.course_section_item.form.fields.is_private') }}
          </span>
        </div>

        <ul v-if="!isApproved" class="list-unstyled hstack gap-1 mb-0">
          <li @click.stop="handleEditCourseSectionItem(courseSectionItemId)">
            <Button icon="bx-pencil" icon-only variant="outline-info"></Button>
          </li>
          <li @click.stop="handleDeleteCourseSectionItem(courseSectionItemId)">
            <Button icon="bx-trash" icon-only variant="outline-danger"></Button>
          </li>
          <li>
            <Button class="handle cursor-move" icon="bx-grid-vertical" icon-only variant="outline-secondary"></Button>
          </li>
        </ul>
      </BCardHeader>

      <BCardBody
        v-if="isEditingSectionItem"
        class="p-3"
        :class="{ 'border-bottom border-primary': isExpanded, 'border-danger': isCourseSectionItemBlank }"
      >
        <CourseSectionItemFormView
          v-model:course-section-item-form="courseSectionItemForm"
          :course-section-item-index="courseSectionItemIndex"
          :loading="isUpdateSectionItemBtnLoading"
          @cancel="handleCancelUpdateCourseSectionItem"
          @submit="handleUpdateCourseSectionItem"
        ></CourseSectionItemFormView>
      </BCardBody>

      <BCollapse
        lazy
        unmount-lazy
        :id="collapseToggleId"
        :modelValue="isExpanded"
        @update:modelValue="handleCollapseChange"
      >
        <BCardBody
          class="p-0"
          :class="{
            'border-primary border-top': !isEditingSectionItem,
            'border-danger': isCourseSectionItemBlank
          }"
        >
          <CourseSectionItemContent
            v-model:course-section-item-form="courseSectionItemForm"
            :course-id="courseId"
            :course-section-id="courseSectionId"
            :course-section-index="courseSectionIndex"
            :course-section-item-id="courseSectionItemId"
            :course-section-item-index="courseSectionItemIndex"
            :course-title="courseTitle"
            :is-approved="isApproved"
            :level-options="levelOptions"
            :skill-options="skillOptions"
            :timestamp="timestamp"
            @get-new-timestamp="getNewTimestamp"
            @remove-course-section-item-blank="removeCourseSectionItemBlank"
          ></CourseSectionItemContent>
        </BCardBody>
      </BCollapse>
    </BCard>

    <div v-if="isCourseSectionItemBlank" class="mb-3 d-block text-danger text-start">
      <span>{{ getCourseSectionItemError() }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { OptionInterface } from '@/utils/interface/select-options';

  import { CourseSectionItemForm } from '@/forms/teacher/courseSectionItem';

  import Button from '../../Button.vue';
  import CourseSectionItemContent from './CourseSectionItemContent.vue';
  import CourseSectionItemFormView from './CourseSectionItemForm.vue';

  export interface CourseSectionItemCardExpose {
    showUpdateSectionItemBtnLoading: () => void;
    hideUpdateSectionItemBtnLoading: () => void;
  }

  const emits = defineEmits([
    'cancelEditCourseSectionItem',
    'collapse-change',
    'deleteCourseSectionItem',
    'editCourseSectionItem',
    'getNewTimestamp',
    'removeCourseSectionItemBlank',
    'updateCourseSectionItem'
  ]);
  const courseSectionItemForm = defineModel('courseSectionItemForm', {
    type: Object as PropType<CourseSectionItemForm>,
    required: true
  });
  const props = defineProps({
    courseId: {
      type: Number,
      required: true
    },
    isExpanded: {
      type: Boolean,
      default: false
    },
    courseSectionId: {
      type: Number,
      required: true
    },
    courseSectionItemTitle: {
      type: String,
      required: true
    },
    courseSectionItemIsFree: {
      type: Boolean,
      required: true
    },
    courseSectionItemId: {
      type: Number,
      required: true
    },
    courseSectionItemIndex: {
      type: Number,
      required: true
    },
    isEditingSectionItem: {
      type: Boolean,
      default: false
    },
    courseTitle: {
      type: String,
      required: true
    },
    courseSectionIndex: {
      type: Number,
      required: true
    },
    levelOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    skillOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    isApproved: {
      type: Boolean,
      required: true
    },
    timestamp: {
      type: Number,
      required: true
    },
    courseSectionErrors: {
      type: Object as PropType<Record<string, string[]>>,
      required: true
    }
  });

  const isExpanded = ref<boolean>(false);
  const isUpdateSectionItemBtnLoading = ref<boolean>(false);

  const collapseToggleId = computed(
    () => `collapse-section-item-${props.courseSectionId}-${props.courseSectionItemId}`
  );

  const errorKeyPrefix = computed(() => {
    return 'courseSection.' + String(props.courseSectionId) + '.courseSectionItem.' + String(props.courseSectionItemId);
  });

  const isCourseSectionItemBlank = computed(() => {
    if (Object.keys(props.courseSectionErrors).length <= 0) return false;

    return Object.keys(props.courseSectionErrors).some(key => key == errorKeyPrefix.value);
  });

  const getCourseSectionItemError = () => {
    return props.courseSectionErrors[errorKeyPrefix.value][0];
  };

  const handleCollapseChange = (val: boolean) => {
    isExpanded.value = val;
    emits('collapse-change', props.courseSectionItemId, val);
  };

  const handleDeleteCourseSectionItem = (courseSectionItemId: number) => {
    emits('deleteCourseSectionItem', courseSectionItemId);
  };

  const handleEditCourseSectionItem = (courseSectionItemId: number) => {
    emits('editCourseSectionItem', courseSectionItemId);
  };

  const handleCancelUpdateCourseSectionItem = () => {
    emits('cancelEditCourseSectionItem');
  };

  const handleUpdateCourseSectionItem = () => {
    emits('updateCourseSectionItem');
  };

  const showUpdateSectionItemBtnLoading = () => {
    isUpdateSectionItemBtnLoading.value = true;
  };

  const hideUpdateSectionItemBtnLoading = () => {
    isUpdateSectionItemBtnLoading.value = false;
  };

  const getNewTimestamp = () => {
    emits('getNewTimestamp');
  };

  const removeCourseSectionItemBlank = (courseSectionId: number, courseSectionItemId: number) => {
    emits('removeCourseSectionItemBlank', courseSectionId, courseSectionItemId);
  };

  watch(
    () => props.isExpanded,
    val => {
      if (isExpanded.value !== val) {
        isExpanded.value = val;
      }
    }
  );

  defineExpose({
    hideUpdateSectionItemBtnLoading,
    showUpdateSectionItemBtnLoading
  });
</script>

<style lang="scss" scoped>
  .course-section-free {
    background-color: #d4edda;
    border-radius: 50px !important;
    font-size: 10px !important;
  }

  .course-section-private {
    background-color: #e6e6e6;
    border-radius: 50px !important;
    font-size: 10px !important;
  }
</style>
