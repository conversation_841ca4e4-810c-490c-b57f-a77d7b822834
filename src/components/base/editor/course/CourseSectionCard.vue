<template>
  <div>
    <BCard
      class="course-section-card border border-primary bg-white"
      header-class="rounded-2"
      no-body
      :class="{ 'no-header': isEditingSection, 'border-danger mb-1': isCourseSectionBlank }"
      :id="errorKeyPrefix"
    >
      <template #header>
        <div v-if="!isEditingSection" class="d-flex justify-content-between align-items-center min-h-36 gap-4">
          <h5 class="my-0 line-clamp-3">
            <strong>{{ $t('teacher.course_section.form.title') }} {{ courseSectionIndex + 1 }}: </strong>
            <span>
              {{ courseSectionDetail.title }}
            </span>
          </h5>

          <ul v-if="!isApproved" class="list-unstyled hstack gap-1 mb-0">
            <li @click="handleEditCourseSection(courseSectionDetail.id)">
              <Button icon="bx-pencil" icon-only variant="outline-info"></Button>
            </li>
            <li @click="handleDeleteCourseSection(courseSectionDetail.id)">
              <Button icon="bx-trash" icon-only variant="outline-danger"></Button>
            </li>
            <li>
              <Button class="handle cursor-move" icon="bx-grid-vertical" icon-only variant="outline-secondary"></Button>
            </li>
          </ul>
        </div>
      </template>

      <BCardBody
        class="p-0"
        :class="{
          'border-primary border-top': !isEditingSection,
          'border-danger': isCourseSectionBlank
        }"
      >
        <CourseSectionFormView
          v-if="isEditingSection"
          v-model:course-section-form="courseSectionForm"
          :class="{ 'mb-3 p-3 border-primary border-bottom': isEditingSection, 'border-danger': isCourseSectionBlank }"
          :course-section-index="courseSectionIndex"
          :loading="isUpdateSectionBtnLoading"
          @cancel="handleCancelUpdateCourseSection"
          @submit="handleUpdateCourseSection"
        ></CourseSectionFormView>

        <draggable
          animation="200"
          class="m-3"
          handle=".handle"
          itemKey="id"
          tag="div"
          :group="draggableGroupConfig"
          :list="courseSectionDetail.courseSectionItems"
          @change="swapPosition"
        >
          <template #item="{ element, index }">
            <CourseSectionItemCard
              v-model:course-section-item-form="courseSectionItemForm"
              :course-id="courseId"
              :course-section-errors="courseSectionErrors"
              :course-section-id="courseSectionId"
              :course-section-index="courseSectionIndex"
              :course-section-item-id="element.id"
              :course-section-item-index="index"
              :course-section-item-title="element.title"
              :course-section-item-is-free="element.isFree"
              :course-title="courseTitle"
              :is-approved="isApproved"
              :is-editing-section-item="isEditingSectionItem(element.id)"
              :is-expanded="activeSectionItemId === element.id"
              :ref="el => registerRef(el, element.id)"
              :timestamp="timestamp"
              @cancel-edit-course-section-item="handleCancelEditCourseSectionItem"
              @collapse-change="handleCollapseChange"
              @delete-course-section-item="handleDeleteCourseSectionItem"
              @edit-course-section-item="handleEditCourseSectionItem"
              @get-new-timestamp="getNewTimestamp"
              @update-course-section-item="handleUpdateCourseSectionItem"
              @remove-course-section-item-blank="removeCourseSectionItemBlank"
            ></CourseSectionItemCard>
          </template>
        </draggable>

        <BCard v-if="isAddingSectionItem" no-body class="bg-white m-3 p-3 border border-primary">
          <CourseSectionItemFormView
            v-model:course-section-item-form="courseSectionItemForm"
            isCreate
            :loading="isCreateSectionItemBtnLoading"
            @cancel="handleCancelCreateSectionItem"
            @submit="handleCreateSectionItem"
          ></CourseSectionItemFormView>
        </BCard>

        <div v-if="!isAddingSectionItem && !isApproved" class="d-flex align-items-start m-3">
          <Button icon="bx-plus" @click="handleAddingSectionItem">
            {{ $t('teacher.course_section_item.add_btn') }}
          </Button>
        </div>
      </BCardBody>
    </BCard>

    <div v-if="isCourseSectionBlank" class="mb-3 d-block text-danger text-start">
      <span>{{ getCourseSectionError() }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import draggable from 'vuedraggable';
  import i18n from '@/plugin/i18n';

  import { CourseSectionForm } from '@/forms/teacher/courseSection';
  import { CourseSectionItemForm } from '@/forms/teacher/courseSectionItem';

  import { CourseSectionInterface, CourseSectionItemDraggablePayload } from '@/utils/interface/teacher/courseSection';
  import { OptionInterface } from '@/utils/interface/select-options';
  import { SwalIconOptions, SwalOptions } from '@/utils/swal-options';

  import {
    courseSectionItemCreate,
    courseSectionItemDelete,
    courseSectionItemSwapPosition,
    courseSectionItemUpdate
  } from '@/services/teacher/repositories/courseSectionItem';

  import useSwal from '@/composable/swal';

  import Button from '@/components/base/Button.vue';
  import CourseSectionFormView from './CourseSectionForm.vue';
  import CourseSectionItemCard, { CourseSectionItemCardExpose } from './CourseSectionItemCard.vue';
  import CourseSectionItemFormView from './CourseSectionItemForm.vue';

  export interface CourseSectionCardExpose {
    showUpdateSectionBtnLoading: () => void;
    hideUpdateSectionBtnLoading: () => void;
  }

  const courseSectionForm = defineModel('courseSectionForm', {
    type: Object as PropType<CourseSectionForm>,
    required: true
  });
  const emits = defineEmits([
    'cancelEditCourseSection',
    'deleteCourseSection',
    'editCourseSection',
    'getNewTimestamp',
    'refreshCourseDetail',
    'removeCourseSectionBlank',
    'removeCourseSectionItemBlank',
    'resetErrors',
    'updateCourseSection'
  ]);
  const props = defineProps({
    courseId: {
      type: Number,
      required: true
    },
    courseSectionId: {
      type: Number,
      required: true
    },
    courseSectionDetail: {
      type: Object as PropType<CourseSectionInterface>,
      required: true
    },
    courseSectionIndex: {
      type: Number,
      required: true
    },
    isEditingSection: {
      type: Boolean,
      default: false
    },
    courseTitle: {
      type: String,
      required: true
    },
    levelOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    skillOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    isApproved: {
      type: Boolean,
      required: true
    },
    timestamp: {
      type: Number,
      required: true
    },
    courseSectionErrors: {
      type: Object as PropType<Record<string, string[]>>,
      required: true
    }
  });

  const draggableGroupConfig = {
    name: 'course-section-items',
    pull: false,
    put: false
  };

  const { confirming } = useSwal();

  const activeSectionItemId = ref<number>();
  const courseSectionItemForm = ref<CourseSectionItemForm>(new CourseSectionItemForm());
  const courseSectionItemRefs = ref<Record<number, CourseSectionItemCardExpose>>({});
  const isAddingSectionItem = ref<boolean>(false);
  const isCreateSectionItemBtnLoading = ref<boolean>(false);
  const isUpdateSectionBtnLoading = ref<boolean>(false);
  const modifyingCourseSectionItemId = ref<number>();

  const errorKeyPrefix = computed(() => {
    return 'courseSection.' + String(props.courseSectionId);
  });

  const isCourseSectionBlank = computed(() => {
    if (Object.keys(props.courseSectionErrors).length <= 0) return false;

    return Object.keys(props.courseSectionErrors).some(key => key == errorKeyPrefix.value);
  });

  const getCourseSectionError = () => {
    return props.courseSectionErrors[errorKeyPrefix.value][0];
  };

  const isEditingSectionItem = (courseSectionItemId: number) => {
    return courseSectionItemId == modifyingCourseSectionItemId.value;
  };

  const handleDeleteCourseSection = (courseSectionId: number) => {
    emits('deleteCourseSection', courseSectionId);
  };

  const handleEditCourseSection = (courseSectionId: number) => {
    emits('editCourseSection', courseSectionId);
  };

  const handleCancelUpdateCourseSection = () => {
    emits('cancelEditCourseSection');
  };

  const handleUpdateCourseSection = () => {
    emits('updateCourseSection');
  };

  const showUpdateSectionBtnLoading = () => {
    isUpdateSectionBtnLoading.value = true;
  };

  const hideUpdateSectionBtnLoading = () => {
    isUpdateSectionBtnLoading.value = false;
  };

  const swapPosition = async (payload: CourseSectionItemDraggablePayload) => {
    await courseSectionItemSwapPosition(
      String(props.courseId),
      String(props.courseSectionId),
      String(payload.moved.element.id),
      Number(payload.moved.newIndex)
    );
  };

  const refreshCourseDetail = () => {
    emits('refreshCourseDetail');
  };

  const handleAddingSectionItem = () => {
    isAddingSectionItem.value = true;
    isCreateSectionItemBtnLoading.value = false;
    modifyingCourseSectionItemId.value = undefined;
    courseSectionItemForm.value = new CourseSectionItemForm();
    activeSectionItemId.value = undefined;
    emits('resetErrors');
  };

  const handleCollapseChange = (sectionItemId: number, isOpen: boolean) => {
    if (isOpen) {
      if (activeSectionItemId.value !== sectionItemId) {
        activeSectionItemId.value = sectionItemId;
        isAddingSectionItem.value = false;
      }
    } else if (activeSectionItemId.value === sectionItemId) {
      activeSectionItemId.value = undefined;
    }
  };

  const handleCancelCreateSectionItem = () => {
    isAddingSectionItem.value = false;
  };

  const handleCreateSectionItem = async () => {
    if (!props.courseId || !props.courseSectionId) return;

    isCreateSectionItemBtnLoading.value = true;
    try {
      await courseSectionItemCreate(String(props.courseId), String(props.courseSectionId), courseSectionItemForm.value);
    } catch {
      isCreateSectionItemBtnLoading.value = false;
      return;
    }
    isAddingSectionItem.value = false;
    isCreateSectionItemBtnLoading.value = false;
    await refreshCourseDetail();
    emits('removeCourseSectionBlank', props.courseSectionId);
  };

  const registerRef = (el: Element | ComponentPublicInstance | null, sectionId: number) => {
    const instance = el as CourseSectionItemCardExpose | null;
    if (
      instance &&
      typeof instance.showUpdateSectionItemBtnLoading === 'function' &&
      typeof instance.hideUpdateSectionItemBtnLoading === 'function'
    ) {
      courseSectionItemRefs.value[sectionId] = instance;
    } else {
      delete courseSectionItemRefs.value[sectionId];
    }
  };

  const handleDeleteCourseSectionItem = async (courseSectionItemId: number) => {
    const message = i18n.global.t('teacher.course_section_item.delete_modal.content');

    const confirmed = await confirming(
      new SwalOptions({
        html: message,
        icon: SwalIconOptions.Warning
      })
    );

    if (!confirmed) return;

    if (confirmed) {
      await courseSectionItemDelete(String(courseSectionItemId), String(props.courseId), String(props.courseSectionId));
      refreshCourseDetail();
    }
  };

  const handleEditCourseSectionItem = (courseSectionItemId: number) => {
    const courseSectionItem = props.courseSectionDetail.courseSectionItems.find(
      sectionItem => sectionItem.id == String(courseSectionItemId)
    );
    isAddingSectionItem.value = false;
    modifyingCourseSectionItemId.value = courseSectionItemId;
    courseSectionItemForm.value.assignAttributes(courseSectionItem);
    emits('resetErrors');
  };

  const handleCancelEditCourseSectionItem = () => {
    modifyingCourseSectionItemId.value = undefined;
  };

  const handleUpdateCourseSectionItem = async () => {
    if (!modifyingCourseSectionItemId.value) return;

    const courseSectionItem = courseSectionItemRefs.value[modifyingCourseSectionItemId.value];
    if (!courseSectionItem) return;

    courseSectionItem.showUpdateSectionItemBtnLoading();

    try {
      await courseSectionItemUpdate(
        String(modifyingCourseSectionItemId.value),
        String(props.courseId),
        String(props.courseSectionId),
        courseSectionItemForm.value
      );
    } catch {
      courseSectionItem.hideUpdateSectionItemBtnLoading();
      return;
    }
    courseSectionItem.hideUpdateSectionItemBtnLoading();
    modifyingCourseSectionItemId.value = undefined;
    refreshCourseDetail();
  };

  const getNewTimestamp = () => {
    emits('getNewTimestamp');
  };

  const removeCourseSectionItemBlank = (courseSectionId: number, courseSectionItemId: number) => {
    emits('removeCourseSectionItemBlank', courseSectionId, courseSectionItemId);
  };

  defineExpose({
    hideUpdateSectionBtnLoading,
    showUpdateSectionBtnLoading
  });
</script>

<style lang="scss" scoped>
  .course-section-card {
    &.no-header {
      .card-header {
        display: none;
      }
    }
  }
</style>
