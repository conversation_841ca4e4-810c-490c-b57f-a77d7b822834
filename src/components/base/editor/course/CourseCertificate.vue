<template>
  <BRow>
    <BCol lg="12">
      <BCard no-body class="shadow-none border-top">
        <BCardBody class="pb-0">
          <BForm>
            <BaseFormValidator
              name="isActive"
              horizontal
              :disabled="isApproved"
              :label="$t('teacher.editor.course.steps.certificate.form.is_active.title')"
              :tooltip="$t('teacher.editor.course.steps.certificate.form.is_active.tooltip')"
            >
              <BFormCheckbox v-model="certificate.isActive" name="lockingItem" class="custom-switch" switch size="lg" />
            </BaseFormValidator>

            <BaseFormValidator
              horizontal
              :disabled="isApproved"
              name="title"
              required
              :label="$t('teacher.editor.course.steps.certificate.form.title.title')"
              :subtitle="$t('teacher.editor.course.steps.certificate.form.title.subtitle')"
            >
              <input
                v-model="certificate.title"
                class="form-control"
                id="title"
                name="title"
                type="text"
                :placeholder="$t('teacher.editor.course.steps.certificate.form.title.placeholder')"
              />
            </BaseFormValidator>
            <BaseFormValidator
              horizontal
              :disabled="isApproved"
              name="description"
              :label="$t('teacher.editor.course.steps.certificate.form.description.title')"
              :subtitle="$t('teacher.editor.course.steps.certificate.form.description.subtitle')"
            >
              <textarea
                v-model="certificate.description"
                class="form-control min-h-200"
                id="description"
                name="description"
                :placeholder="$t('teacher.editor.course.steps.certificate.form.description.placeholder')"
              />
            </BaseFormValidator>
          </BForm>
        </BCardBody>
        <BCardFooter class="d-flex justify-content-end" v-if="!isApproved">
          <BButton variant="primary" class="me-2" :disabled="!certificate.title" @click="onSave"> Lưu </BButton>
        </BCardFooter>
      </BCard>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import { CourseInterface } from '@/utils/interface/teacher/course';
  import { courseCertificateSetting } from '@/services/teacher/repositories/certificate';
  import { CourseSectionForm } from '@/forms/teacher/certificate';

  const certificate = ref(new CourseSectionForm());

  const props = defineProps({
    courseDetail: {
      type: Object as PropType<CourseInterface>,
      required: true
    },
    courseId: {
      type: Number,
      required: true
    },
    isApproved: {
      type: Boolean,
      required: true
    }
  });

  function onSave() {
    courseCertificateSetting(String(props.courseId), certificate.value);
  }

  onMounted(() => {
    if (!props.courseDetail?.certificate) return;
    certificate.value = cloneDeep(props.courseDetail.certificate);
  });
</script>
