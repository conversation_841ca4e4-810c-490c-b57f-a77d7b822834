<template>
  <EditorStepper
    v-model:tab-index="tabIndex"
    is-course
    show-preview
    nav-wrapper-class="col-xl-3 col-md-4 col-sm-12 col-12 position-sticky top-76-px"
    :censor-history="courseDetail?.courseCensorHistories || []"
    :censor-status-message="courseDetail?.statusI18n || ''"
    :censor-status="courseDetail?.status || CENSOR_STATUS_ENUMS.DRAFT"
    :editor-title="editorTitle"
    :is-admin="false"
    :is-submit-button-loading="isCourseSubmitButtonLoading"
    :show-censor-status="!!courseDetail"
    :site="site"
    :step-array="courseStepArray"
    :steps-config="COURSE_STEPS"
    @change-step="handleChangeStep"
    @handle-preview="handlePreview"
    @make-change-in-step="makeChangeInStep"
    @request-censor="confirmRequestReview"
  >
    <template #step-content-planning-tip>
      <CoursePlanningTip />
    </template>

    <template #step-content-sections>
      <CourseSection
        v-if="courseDetail && courseId"
        :course-detail="courseDetail"
        :course-id="courseId"
        :course-section-errors="courseSectionErrors"
        :is-approved="isApproved"
        :level-options="levelOptions"
        :skill-options="skillOptions"
        :timestamp="timestamp"
        @get-new-timestamp="getNewTimestamp"
        @refresh-course-detail="fetchCourseDetail"
        @remove-course-section-blank="removeCourseSectionBlank"
        @remove-course-section-item-blank="removeCourseSectionItemBlank"
        @reset-errors="resetErrors"
      ></CourseSection>
    </template>

    <template #step-content-certificate>
      <CourseCertificate
        v-if="courseDetail && courseId"
        :course-detail="courseDetail"
        :course-id="courseId"
        :is-approved="isApproved"
      ></CourseCertificate>
    </template>

    <template #step-content-landing-page>
      <CourseLandingPage
        v-model:course-form="courseForm"
        :course-instructional-level-options="courseInstructionalLevelOptions"
        :is-approved="isApproved"
        :site="site"
        @save-course="saveCourse(true)"
      ></CourseLandingPage>
    </template>

    <template #step-content-pricing>
      <CoursePricing
        v-model:course-form="courseForm"
        :is-approved="isApproved"
        :package-deal-options="packageDealOptions"
      />
    </template>

    <template #step-content-preview>
      <CoursePreview v-if="courseDetail" :course-detail="courseDetail" />
    </template>

    <template #action-buttons>
      <Button
        v-if="courseDetail"
        classes="min-w-160"
        :disabled="isChangingPublicStatus"
        :icon="publishBtnIcon()"
        :loading="isChangingPublicStatus"
        :variant="publishBtnVariant()"
        @click="confirmChangePublicStatus"
      >
        {{ $t(`common.${publicText}`) }}
      </Button>

      <Button
        classes="min-w-160"
        icon="bx-save"
        variant="success"
        :disabled="isCourseSaveButtonLoading"
        :loading="isCourseSaveButtonLoading"
        @click="handleSaveCourse"
      >
        {{ $t('teacher.editor.course.actions.save') }}
      </Button>
    </template>
  </EditorStepper>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';

  import { CourseForm } from '@/forms/teacher/course';

  import { CourseInterface } from '@/utils/interface/teacher/course';
  import { ErrorObjectInterface } from '@/utils/interface/common';
  import { OptionInterface } from '@/utils/interface/select-options';
  import { SiteType } from '@/utils/interface/common';
  import { SwalIconOptions, SwalOptions } from '@/utils/swal-options';
  import {
    CENSOR_STATUS_ENUMS,
    COURSE_ELEMENT_TYPE,
    COURSE_PUBLIC_STATUS_ENUMS,
    NAVIGATION_TYPE,
    SITES
  } from '@/utils/constant';

  import { useGlobalStore } from '@/store/global';

  import useSwal from '@/composable/swal';
  import { COURSE_STEPS, getArraySteps, getStepByPosition, getStepIndexById } from '@/composable/useEditor';

  import { showCourse, courseSubmit, courseUpdate, coursePublic } from '@/services/teacher';

  import Button from '../../Button.vue';
  import CourseLandingPage from './CourseLandingPage.vue';
  import CoursePlanningTip from './CoursePlanningTip.vue';
  import CoursePreview from './CoursePreview.vue';
  import CoursePricing from './CoursePricing.vue';
  import CourseSection from './CourseSection.vue';
  import CourseCertificate from './CourseCertificate.vue';
  import EditorStepper from '../shared/EditorStepper.vue';

  const globalStore = useGlobalStore();
  const route = useRoute();
  const router = useRouter();
  const { confirming } = useSwal();

  defineProps({
    courseInstructionalLevelOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    levelOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    skillOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    packageDealOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    editorTitle: {
      type: String,
      default: ''
    }
  });

  const courseDetail = ref<CourseInterface>();
  const courseForm = ref<CourseForm>(new CourseForm());
  const courseId = ref<number>();
  const courseSectionErrors = ref<Record<string, string[]>>({});
  const courseSlug = ref<string>();
  const isChangingPublicStatus = ref<boolean>(false);
  const isCourseSaveButtonLoading = ref<boolean>(false);
  const isCourseSubmitButtonLoading = ref<boolean>(false);
  const needUpdate = ref<boolean>(false);
  const site = ref<SiteType>(SITES.TEACHER);
  const tabIndex = ref<number>(0);
  const timestamp = ref<number>(Date.now());

  const courseStepArray = computed(() => {
    return getArraySteps(COURSE_STEPS);
  });

  const publicText = computed(() => {
    return courseDetail.value?.isPublic ? COURSE_PUBLIC_STATUS_ENUMS.PRIVATE : COURSE_PUBLIC_STATUS_ENUMS.PUBLIC;
  });

  const isApproved = computed(() => {
    return courseDetail.value?.status === CENSOR_STATUS_ENUMS.APPROVED;
  });

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const scrollToErrorCourseSection = (courseSectionId: string) => {
    const element = document.getElementById(courseSectionId);

    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  };

  const resetErrors = () => {
    globalStore.setErrors([]);
  };

  const handleError = (type: string, errorObject: ErrorObjectInterface) => {
    const firstErrorKey = Object.keys(errorObject.errors)[0];
    const errorCourseSectionIds: number[] = [];
    let firstErrorCourseSectionId = '';

    Object.keys(errorObject.errors).forEach(key => {
      const match = key.match(/^courseSection\.(\d+)/);

      if (match) {
        const id = parseInt(match[1], 10);
        errorCourseSectionIds.push(id);
      }
    });

    if (errorCourseSectionIds.length !== 0) {
      const smallestId = Math.min(...errorCourseSectionIds);
      firstErrorCourseSectionId = `courseSection.${smallestId}`;
    }

    switch (type) {
      case COURSE_ELEMENT_TYPE.COURSE:
        switch (firstErrorKey) {
          case 'title':
          case 'description':
          case 'instructionalLevel':
          case 'banner':
            tabIndex.value = 2;
            break;
          case 'price':
          case 'salePrice':
            tabIndex.value = 3;
            break;
          default:
            tabIndex.value = 2;
            break;
        }
        break;
      case COURSE_ELEMENT_TYPE.COURSE_SECTION:
      case COURSE_ELEMENT_TYPE.COURSE_SECTION_ITEM:
        tabIndex.value = 1;

        if (typeof errorObject.errors !== 'string') courseSectionErrors.value = errorObject.errors;

        setTimeout(() => {
          scrollToErrorCourseSection(firstErrorCourseSectionId);
        }, 500);
        break;
      default:
        tabIndex.value = 4;
        break;
    }
  };

  const makeChangeInStep = (position: number) => {
    const step = getStepByPosition(COURSE_STEPS, position);
    if (!step) return;

    switch (step.id) {
      case COURSE_STEPS.LANDING_PAGE.id:
      case COURSE_STEPS.PRICING.id:
        needUpdate.value = true;
        break;
      default:
        needUpdate.value = false;
        break;
    }
  };

  const handleSubmitCourseOperation = async (toast = false) => {
    if (!courseId.value) return;

    const { data } = await courseUpdate(String(courseId.value), courseForm.value.updateInput(), toast);

    courseId.value = data.courseUpdate.course.id;
    courseSlug.value = data.courseUpdate.course.slug;

    if (route.params.slug !== courseSlug.value) {
      router.push(`/teacher/editor/course/${courseSlug.value}?tabIndex=${tabIndex.value}`);
    }

    await fetchCourseDetail();

    getNewTimestamp();
  };

  const saveCourse = async (toast = false) => {
    try {
      await handleSubmitCourseOperation(toast);
    } catch (e) {
      handleError(COURSE_ELEMENT_TYPE.COURSE, e as ErrorObjectInterface);
      return;
    }
  };

  const handleChangeStep = async (currentIndex: number, btnEvent = false, btnEventType?: string) => {
    if (currentIndex === tabIndex.value) return;

    resetErrors();

    if (needUpdate.value) await saveCourse();

    if (
      btnEvent &&
      btnEventType === NAVIGATION_TYPE.NEXT &&
      currentIndex === getStepIndexById(COURSE_STEPS, COURSE_STEPS.PRICING.id)
    )
      await fetchCourseDetail();
    else if (
      btnEvent &&
      btnEventType === 'prev' &&
      currentIndex === getStepIndexById(COURSE_STEPS, COURSE_STEPS.PREVIEW.id)
    )
      return;
    else if (currentIndex === getStepIndexById(COURSE_STEPS, COURSE_STEPS.PREVIEW.id)) await fetchCourseDetail();

    needUpdate.value = false;
    scrollToTop();
  };

  const confirmChangePublicStatus = async () => {
    const message = i18n.global.t(`teacher.course.confirm_${publicText.value}.content`);

    const confirmed = await confirming(
      new SwalOptions({
        title: i18n.global.t(`teacher.course.confirm_${publicText.value}.title`),
        html: message,
        icon: SwalIconOptions.Question
      })
    );

    if (!confirmed) return;

    if (confirmed) {
      isChangingPublicStatus.value = true;
      try {
        await coursePublic(String(courseId.value));
      } catch {
        isChangingPublicStatus.value = false;
        return;
      }
      await fetchCourseDetail();
      isChangingPublicStatus.value = false;
    }
  };

  const confirmRequestReview = async () => {
    const message = i18n.global.t('teacher.course.confirm_submit.content');

    const confirmed = await confirming(
      new SwalOptions({
        title: i18n.global.t('common.request_review'),
        html: message,
        icon: SwalIconOptions.Question
      })
    );

    if (!confirmed || !courseDetail.value) return;

    if (confirmed) {
      isCourseSubmitButtonLoading.value = true;

      try {
        await courseSubmit(String(courseId.value));
      } catch (e) {
        handleError(COURSE_ELEMENT_TYPE.COURSE_SECTION, e as ErrorObjectInterface);
        isCourseSubmitButtonLoading.value = false;
        return;
      }

      await fetchCourseDetail();
      isCourseSubmitButtonLoading.value = false;
    }
  };

  const fetchCourseDetail = async () => {
    const courseIdentifier = courseSlug.value ? courseSlug.value : (route.params.slug as string);

    const { data } = await showCourse(courseIdentifier);

    courseDetail.value = cloneDeep(data.course);
    courseForm.value.assignAttributes(data.course);
    courseForm.value.highlightIds = data.course?.courseHighlights
      ? data.course?.courseHighlights.map((item: { highlight: { id: string } }) => String(item.highlight.id))
      : [];

    courseForm.value.coursePackageAttributes = cloneDeep(data.course.coursePackages);
    courseId.value = data.course.id;
    courseSlug.value = data.course.slug;

    getNewTimestamp();
  };

  const publishBtnVariant = () => {
    return publicText.value === COURSE_PUBLIC_STATUS_ENUMS.PUBLIC ? 'outline-primary' : 'outline-warning';
  };

  const publishBtnIcon = () => {
    return publicText.value === COURSE_PUBLIC_STATUS_ENUMS.PUBLIC ? 'bx-lock-open' : 'bx-lock';
  };

  const handlePreview = () => {
    const previewUrl = `/teacher/courses/${courseSlug.value}/detail?preview=true`;
    window.open(previewUrl, '_blank');
  };

  const getNewTimestamp = () => {
    timestamp.value = Date.now();
  };

  const handleSaveCourse = async () => {
    isCourseSaveButtonLoading.value = true;
    try {
      await handleSubmitCourseOperation(true);
      await fetchCourseDetail();
    } catch {
      isCourseSaveButtonLoading.value = false;
      return;
    }
    isCourseSaveButtonLoading.value = false;
  };

  const initialize = async () => {
    tabIndex.value = Number(route.query.tabIndex || 0);
    courseSlug.value = route.params.slug as string;
    await fetchCourseDetail();
  };

  const removeCourseSectionBlank = (courseSectionId: number) => {
    Object.keys(courseSectionErrors.value).forEach(key => {
      const match = key.match(/^courseSection\.(\d+)/);
      if (match && parseInt(match[1], 10) === courseSectionId) {
        delete courseSectionErrors.value[key];
      }
    });
  };

  const removeCourseSectionItemBlank = (courseSectionId: number, courseSectionItemId: number) => {
    Object.keys(courseSectionErrors.value).forEach(key => {
      const match = key.match(/^courseSection\.(\d+)\.courseSectionItem\.(\d+)$/);
      if (match && parseInt(match[1], 10) === courseSectionId && parseInt(match[2], 10) === courseSectionItemId) {
        delete courseSectionErrors.value[key];
      }
    });
  };

  onMounted(async () => {
    await initialize();
  });
</script>
