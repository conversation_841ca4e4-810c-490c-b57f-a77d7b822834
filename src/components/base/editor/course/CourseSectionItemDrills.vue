<template>
  <BRow>
    <BCol>
      <BCard no-body class="mb-0 shadow-none">
        <BCardBody class="border-bottom p-0 pb-4">
          <SearchForm
            v-model:query="query"
            :disabled="loading"
            :loading="loading"
            :search-fields-list="searchFieldsList"
            @reset="reset"
            @search="search"
          >
          </SearchForm>
        </BCardBody>

        <BCardBody v-if="drills.length" class="p-0 pt-3">
          <CourseSectionItemDrillList
            on-section-item
            :drills="drills"
            :metadata="metadata"
            :loading="loading"
            @select="select"
            @fetch-list="fetchList"
          ></CourseSectionItemDrillList>
        </BCardBody>

        <BRow v-else class="pb-5">
          <DataEmpty :message="$t('teacher.drills.data_empty')" height="150" />
        </BRow>
      </BCard>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';

  import CourseSectionItemDrillList from './CourseSectionItemDrillList.vue';
  import DataEmpty from '@/components/utility/DataEmpty.vue';
  import SearchForm from '@/components/base/SearchForm.vue';

  import useDynamicSearch from '@/composable/dynamicSearch';

  import SearchField from '@/utils/search-fields';
  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { MetaDataInterface } from '@/utils/interface/common';

  const emits = defineEmits(['reset', 'search', 'select-drill', 'fetchList']);
  const query = defineModel('query', {
    type: Object,
    default: {}
  });
  defineProps({
    loading: {
      type: Boolean,
      default: false
    },
    drills: {
      type: Array as PropType<DrillInterface[]>,
      required: true
    },
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    }
  });

  const { searchFieldsList, searchComponents } = useDynamicSearch();

  searchFieldsList.value = [
    new SearchField(
      i18n.global.t('teacher.drills.search_form.fields.title'),
      'titleCont',
      'bx bx-search-alt',
      searchComponents.TextInputField,
      { xxl: 8, lg: 12 }
    )
  ];

  const reset = () => {
    emits('reset');
  };

  const search = () => {
    emits('search');
  };

  const select = (id: number) => {
    emits('select-drill', id);
  };

  const fetchList = (e: { page: number }) => {
    emits('fetchList', e);
  };
</script>
