<template>
  <div class="course-detail mt-0">
    <div class="banner rounded-4 text-white py-2 d-none d-lg-block overflow-hidden">
      <BRow class="w-100 h-100">
        <BCol xxl="7" class="position-relative h-100 mx-auto">
          <div class="course-detail-banner d-flex flex-column gap-2 gap-lg-4 px-0 px-lg-4 py-2">
            <h2 class="text-break">
              {{ courseDetail.title }}
            </h2>

            <div class="d-flex flex-column flex-lg-row align-items-start align-items-lg-center gap-1 gap-lg-3">
              <div class="d-none d-lg-flex align-items-center gap-1">
                <i class="mdi mdi-book-open-page-variant font-size-18"></i>
                <span>{{ courseDetail.sectionCount }} {{ $t('public.course.course_section') }}</span>
              </div>
              <div class="d-flex align-items-center gap-1">
                <i class="mdi mdi-web font-size-18"></i>
                <span>{{ $t('public.course.vietnamese') }}</span>
              </div>
            </div>
          </div>
        </BCol>
      </BRow>
      <img src="@/assets/images/vector-billiard-1.png" alt="vector billiard" class="vector-billiard left" />
      <img src="@/assets/images/vector-billiard-2.png" alt="vector billiard" class="vector-billiard right" />
      <img src="@/assets/images/vector-billiard-3.png" alt="vector billiard" class="vector-billiard right sp" />
    </div>

    <BRow class="w-100 mt-4">
      <BCol lg="8" class="order-2 order-lg-1 px-0 px-lg-2">
        <BCard class="course-intro-content text-black">
          <div class="d-flex flex-column gap-4">
            <div class="course-introduction border-bottom pb-4">
              <h4 class="fw-bold">{{ $t('public.course.introduction') }}</h4>
              <pre class="pt-2 pre-content">{{ courseDetail.description }}</pre>
            </div>

            <div class="course-content border-bottom pb-4">
              <h4 class="fw-bold">{{ $t('public.course.content') }}</h4>
              <p class="pt-2">
                {{ $t('public.course.intro') }} {{ courseDetail.sectionCount }}
                <span class="text-lowercase">{{ $t('public.course.course_section') }}</span>
              </p>
              <CourseSectionItem :course="courseDetail" />
            </div>
          </div>
        </BCard>
      </BCol>
      <BCol lg="4" class="order-1 order-lg-2 px-0 px-lg-2">
        <BCard class="course-intro-sidebar">
          <template #img>
            <div class="ratio ratio-4x3">
              <img
                :src="courseDetail.banner ? courseDetail.banner : dummyBanner"
                alt="Course banner"
                class="course-image object-cover rounded-4"
              />
            </div>
          </template>
          <div class="d-flex flex-column gap-3">
            <div class="d-block d-lg-none">
              <div class="course-detail-banner d-flex flex-column gap-2 gap-lg-4 px-0 px-lg-4 py-2">
                <h2>
                  {{ courseDetail.title }}
                </h2>

                <div class="d-flex flex-column flex-lg-row align-items-start align-items-lg-center gap-1 gap-lg-3">
                  <div class="d-none d-lg-flex align-items-center gap-1">
                    <i class="mdi mdi-play-circle-outline font-size-18"></i>
                    <span>{{ courseDetail.sectionCount }} {{ $t('public.course.section_item') }}</span>
                  </div>
                  <div class="d-flex align-items-center gap-1">
                    <i class="mdi mdi-web font-size-18"></i>
                    <span>{{ $t('public.course.vietnamese') }}</span>
                  </div>
                </div>
              </div>
            </div>

            <PriceDisplay :price="courseDetail.price" :salePrice="courseDetail.salePrice" show-label></PriceDisplay>

            <div>
              <h6>{{ $t('public.course.intro') }}:</h6>
              <div v-for="(item, index) in courseIncludes" :key="index" class="d-flex align-items-center gap-2 pb-1">
                <template v-if="!(index === 2 && !courseDetail.certificate?.isActive)">
                  <i class="mdi font-size-20 text-warning" :class="item.icon"></i>
                  <span>
                    <span v-if="index == 0">
                      {{ courseDetail.sectionCount }}
                    </span>
                    <span v-if="index == 1">
                      {{ courseDetail.sectionItemCount }}
                    </span>
                    <span v-if="index == 2">
                      {{ courseDetail.certificate.title }}
                    </span>
                    {{ index != 2 ? $t(`public.course.${item.text}`) : '' }}
                  </span>
                </template>
              </div>
            </div>
          </div>
        </BCard>
      </BCol>
    </BRow>
  </div>
</template>
<script lang="ts" setup>
  import { CourseInterface } from '@/utils/interface/teacher/course';

  import PriceDisplay from '@/components/base/PriceDisplay.vue';
  import dummyBanner from '@/assets/images/dummy_banner.png';
  import CourseSectionItem from '@/components/teacher/courses/CourseSectionItem.vue';

  defineProps({
    courseDetail: {
      type: Object as PropType<CourseInterface>,
      required: true
    }
  });

  const courseIncludes = [
    { icon: 'mdi-book-open-page-variant', text: 'course_section' },
    { icon: 'mdi-play-circle-outline', text: 'section_item' },
    { icon: 'mdi-certificate-outline', text: 'certificate' },
    { icon: 'mdi-tablet-cellphone', text: 'access' },
    { icon: 'mdi-infinity', text: 'timeline' }
  ];
</script>

<style lang="scss" scoped>
  @media (max-width: 2100px) {
    .course-detail {
      .banner {
        .vector-billiard.left {
          left: -45px;
        }
      }
    }
  }

  @media (max-width: 1700px) {
    .course-detail {
      .banner {
        .vector-billiard.left {
          width: 25%;
          object-fit: contain;
          bottom: 0;
          top: auto;
          transform: none;
          height: auto;
        }
      }
    }
  }
</style>
