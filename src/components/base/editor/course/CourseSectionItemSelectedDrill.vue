<template>
  <Loader :loading="isSelectedDrillListLoading">
    <BRow v-if="selectedDrills.length">
      <BCol xl="4" lg="6" sm="12" v-for="drill in selectedDrills" :key="drill.id">
        <BCard no-body class="shadow-none">
          <BCardHeader header-bg-variant="light" class="bg-transparent">
            <div class="text-center">
              <h5>
                <BadgeLevel :level="drill.level!" :value="drill.levelI18n!"></BadgeLevel>
              </h5>

              <h5 class="text-black text-truncate d-block">
                {{ drill.title }}
              </h5>
            </div>
          </BCardHeader>

          <BCardBody>
            <img
              class="p-0 w-100"
              :src="
                drill.diagrams && drill.diagrams.length && drill.diagrams[0].imageUrl
                  ? drill.diagrams[0].imageUrl + '?t=' + timestamp
                  : '/pool-table.png'
              "
            />
          </BCardBody>

          <BCardFooter v-if="!isApproved" footer-bg-variant="light" class="text-center bg-transparent">
            <BButtonGroup class="action-btn-group">
              <Button icon="bx-edit-alt" variant="white" @click="handleEditSelectedDrill(drill.slug)">
                {{ $t('common.edit') }}
              </Button>
              <Button icon="bx-x" variant="white" @click="handleDeleteSelectedDrill(drill.id)">
                {{ $t('common.delete') }}
              </Button>
            </BButtonGroup>
          </BCardFooter>
        </BCard>
      </BCol>
    </BRow>

    <BRow v-else class="pb-5">
      <DataEmpty :message="$t('teacher.drills.data_empty')" height="150" />
    </BRow>
  </Loader>
</template>

<script lang="ts" setup>
  import { DrillInterface } from '@/utils/interface/drill/drill';

  import BadgeLevel from '@/components/base/DrillBadgeLevel.vue';
  import Button from '../../Button.vue';
  import DataEmpty from '@/components/utility/DataEmpty.vue';

  const emits = defineEmits(['editSelectedDrill', 'deleteSelectedDrill']);
  defineProps({
    selectedDrills: {
      type: Array as PropType<DrillInterface[]>,
      required: true
    },
    isSelectedDrillListLoading: {
      type: Boolean,
      default: false
    },
    isApproved: {
      type: Boolean,
      required: true
    },
    timestamp: {
      type: Number,
      required: true
    }
  });

  function handleEditSelectedDrill(slug: string) {
    emits('editSelectedDrill', slug);
  }

  function handleDeleteSelectedDrill(id: number) {
    emits('deleteSelectedDrill', id);
  }
</script>

<style lang="scss" scoped>
  .action-btn-group {
    .btn {
      &:hover {
        color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity));
      }
    }
  }
</style>
