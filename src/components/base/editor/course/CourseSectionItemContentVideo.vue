<template>
  <BCard class="text-center border border-primary bg-white mb-2 rounded-2" no-body>
    <BCardHeader
      class="cursor-pointer rounded-2 course-section-item-wrapper d-flex justify-content-between align-items-center cursor-pointer"
      header-tag="div"
      v-b-toggle="[collapseToggleId]"
    >
      <h5 class="my-0">
        <span class="btn p-0 cursor-pointer border-0">
          <i
            class="font-size-24 pe-2 mdi"
            :class="{ 'mdi-chevron-up': isExpanded, 'mdi-chevron-down': !isExpanded }"
          ></i>
        </span>
        <span>
          {{ $t('teacher.editor.course.steps.sections.content_type.video.title') }}
        </span>
      </h5>
    </BCardHeader>

    <BCollapse :id="collapseToggleId" lazy :visible="isEditingVideoContent" unmount-lazy v-model="isExpanded">
      <BCardBody class="p-3 border-primary border-top border-bottom">
        <CourseSectionItemUploadedVideo
          :is-approved="isApproved"
          :is-deleting-video="isDeletingVideo"
          :uploaded-videos="uploadedVideos"
          @delete-video="handleDeleteVideo"
        ></CourseSectionItemUploadedVideo>

        <CourseSectionItemUploadVideo
          v-if="!isApproved"
          :is-deleting-video="isDeletingVideo"
          :video-file-name="videoFileName"
          @uploaded-video="handleUploadedVideo"
        ></CourseSectionItemUploadVideo>
      </BCardBody>

      <BCardFooter class="rounded-2" v-if="isEditingVideoContent">
        <div class="d-flex justify-content-end gap-2">
          <Button icon="bx-x" variant="outline-primary" @click="handleCancelEditVideoContent">
            {{ $t('common.cancel') }}
          </Button>
        </div>
      </BCardFooter>
    </BCollapse>
  </BCard>
</template>

<script lang="ts" setup>
  import { vBToggle } from 'bootstrap-vue-next';

  import { VideoInterface } from '@/utils/interface/video';

  import Button from '../../Button.vue';
  import CourseSectionItemUploadedVideo from './CourseSectionItemUploadedVideo.vue';
  import CourseSectionItemUploadVideo from './CourseSectionItemUploadVideo.vue';

  const emits = defineEmits([
    'cancelEditVideoContent',
    'deleteUploadingVideo',
    'deleteVideo',
    'saveAndPostVideo',
    'uploadedVideo'
  ]);
  const props = defineProps({
    courseSectionId: {
      type: Number,
      required: true
    },
    courseSectionItemId: {
      type: Number,
      required: true
    },
    isEditingVideoContent: {
      type: Boolean,
      default: false
    },
    isUploadedVideoListLoading: {
      type: Boolean,
      default: false
    },
    uploadedVideos: {
      type: Array as PropType<VideoInterface[]>,
      required: true
    },
    isDeletingVideo: {
      type: Boolean,
      default: false
    },
    videoFileName: {
      type: String,
      default: ''
    },
    isApproved: {
      type: Boolean,
      required: true
    }
  });

  const isExpanded = ref<boolean>(false);

  const collapseToggleId = computed(
    () => `collapse-section-item-video-${props.courseSectionId}-${props.courseSectionItemId}`
  );

  const handleCancelEditVideoContent = () => {
    emits('cancelEditVideoContent');
  };

  const handleDeleteVideo = (videoId: number) => {
    emits('deleteVideo', videoId);
  };

  const handleUploadedVideo = (videoName: string, videoId: number) => {
    emits('uploadedVideo', videoName, videoId);
  };
</script>
