<template>
  <BCard class="text-center border border-primary bg-white mb-2 rounded-2" no-body>
    <BCardHeader
      class="cursor-pointer rounded-2 course-section-item-wrapper d-flex justify-content-between align-items-center cursor-pointer"
      header-tag="div"
      v-b-toggle="[collapseToggleId]"
    >
      <h5 class="my-0">
        <span class="btn p-0 cursor-pointer border-0">
          <i
            class="font-size-24 pe-2 mdi"
            :class="{ 'mdi-chevron-up': isExpanded, 'mdi-chevron-down': !isExpanded }"
          ></i>
        </span>
        <span>
          {{ $t('teacher.editor.course.steps.sections.content_type.text.title') }}
        </span>
      </h5>
    </BCardHeader>

    <BCollapse :id="collapseToggleId" lazy :visible="isEditingTextContent" unmount-lazy v-model="isExpanded">
      <BCardBody class="p-3 border-primary border-top border-bottom">
        <BaseFormValidator
          class="flex-grow-1 m-2 text-start"
          name="content"
          required
          :hide-label="isApproved"
          :label="$t('teacher.course_section_item.form.fields.content')"
        >
          <QuillEditor
            v-model="courseSectionItemForm.content"
            :read-only="isApproved"
            :site="ROUTE_PREFIX_ENUMS.TEACHER"
          />
        </BaseFormValidator>
      </BCardBody>

      <BCardFooter class="rounded-2" v-if="!isApproved">
        <div class="d-flex justify-content-end gap-2">
          <Button
            v-if="isEditingTextContent"
            icon="bx-x"
            variant="outline-primary"
            @click="handleCancelEditTextContent"
          >
            {{ $t('common.cancel') }}
          </Button>
          <Button
            v-if="!isEditingTextContent"
            icon="bx-trash"
            variant="outline-danger"
            :disabled="loading"
            :loading="loading"
            @click="handleRemoveTextContent"
          >
            {{ $t('common.delete') }}
          </Button>
          <Button
            icon="bx-save"
            variant="success"
            :disabled="loading || courseSectionItemForm.content == ''"
            :loading="loading"
            @click="handleUpdateTextContent"
          >
            {{ $t('common.save') }}
          </Button>
        </div>
      </BCardFooter>
    </BCollapse>
  </BCard>
</template>

<script lang="ts" setup>
  import { vBToggle } from 'bootstrap-vue-next';

  import { CourseSectionItemForm } from '@/forms/teacher/courseSectionItem';

  import { ROUTE_PREFIX_ENUMS } from '@/utils/constant';

  import Button from '../../Button.vue';
  import QuillEditor from '../../quill/QuillEditor.vue';

  const emits = defineEmits(['cancelEditTextContent', 'removeTextContent', 'updateTextContent']);
  const courseSectionItemForm = defineModel('courseSectionItemForm', {
    type: Object as PropType<CourseSectionItemForm>,
    required: true
  });
  const props = defineProps({
    isEditingTextContent: {
      type: Boolean,
      default: false
    },
    courseSectionId: {
      type: Number,
      required: true
    },
    courseSectionItemId: {
      type: Number,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    isApproved: {
      type: Boolean,
      required: true
    }
  });

  const isExpanded = ref<boolean>(false);

  const collapseToggleId = computed(
    () => `collapse-section-item-text-${props.courseSectionId}-${props.courseSectionItemId}`
  );

  const handleCancelEditTextContent = () => {
    emits('cancelEditTextContent');
  };

  const handleRemoveTextContent = () => {
    emits('removeTextContent');
  };

  const handleUpdateTextContent = () => {
    emits('updateTextContent');
  };
</script>
