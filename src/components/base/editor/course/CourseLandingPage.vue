<template>
  <BRow>
    <BCol lg="12">
      <BCard no-body class="shadow-none border-top">
        <BCardBody class="pb-0">
          <BForm>
            <BaseFormValidator
              horizontal
              name="title"
              required
              :disabled="isApproved"
              :label="$t('teacher.editor.course.steps.landing_page.form.title.title')"
              :subtitle="$t('teacher.editor.course.steps.landing_page.form.title.subtitle')"
            >
              <input
                v-model="courseForm.title"
                class="form-control"
                id="title"
                name="title"
                type="text"
                :disabled="isApproved"
                :placeholder="$t('teacher.editor.course.steps.landing_page.form.title.placeholder')"
              />
            </BaseFormValidator>
            <BaseFormValidator
              horizontal
              name="description"
              :disabled="isApproved"
              :label="$t('teacher.editor.course.steps.landing_page.form.description.title')"
              :subtitle="$t('teacher.editor.course.steps.landing_page.form.description.subtitle')"
            >
              <textarea
                v-model="courseForm.description"
                class="form-control min-h-200"
                id="description"
                name="description"
                :disabled="isApproved"
                :placeholder="$t('teacher.editor.course.steps.landing_page.form.description.placeholder')"
              />
            </BaseFormValidator>
            <BaseFormValidator
              horizontal
              name="instructionalLevel"
              :disabled="isApproved"
              :label="$t('teacher.editor.course.steps.landing_page.form.instructional_level.title')"
              :subtitle="$t('teacher.editor.course.steps.landing_page.form.instructional_level.subtitle')"
            >
              <Multiselect
                v-model="courseForm.instructionalLevel"
                :disabled="isApproved"
                :options="courseInstructionalLevelOptions"
                :placeholder="$t('teacher.editor.course.steps.landing_page.form.instructional_level.placeholder')"
              />
            </BaseFormValidator>

            <BaseFormValidator
              horizontal
              name="highlightIds"
              :label="$t('teacher.editor.course.steps.landing_page.form.highlight.title')"
            >
              <Multiselect
                id="highlight-select"
                v-model="courseForm.highlightIds"
                :options="highlightIdsOptions"
                mode="tags"
                :close-on-select="false"
                :searchable="true"
                placeholder="Select highlight"
                :no-results-text="$t('teacher.editor.course.steps.landing_page.form.highlight.no_results')"
                class="multiselect-custom"
              >
                <template v-slot:tag="{ option }">
                  <div class="multiselect-tag">
                    <i :class="option.icon" class="font-size-24 text-warning" />
                    <span class="mx-2">{{ option.label }}</span>
                    <i class="mdi mdi-close font-size-16 text-danger" @click="removeHighlight(option)" />
                  </div>
                </template>

                <template v-slot:option="{ option }">
                  <div class="d-flex align-items-center gap-2">
                    <i :class="option.icon" class="font-size-24 text-warning" />
                    <span>{{ option.label }}</span>
                  </div>
                </template>
              </Multiselect>
              <a href="/teacher/highlights" target="_blank" class="small">
                {{ $t('teacher.editor.course.steps.landing_page.form.highlight.add_highlight') }}</a
              >
            </BaseFormValidator>
            <!-- <div class="mb-3">
              {{ $t('teacher.editor.course.steps.landing_page.form.course_setting.title') }}
            </div> -->

            <BaseFormValidator
              v-if="courseForm.courseSetting"
              name="courseSetting"
              horizontal
              :disabled="isApproved"
              :label="$t('teacher.editor.course.steps.landing_page.form.course_setting.locking_item.title')"
              :tooltip="$t('teacher.editor.course.steps.landing_page.form.course_setting.locking_item.tooltip')"
            >
              <BFormCheckbox
                v-model="courseForm.courseSetting.lockingItem"
                name="lockingItem"
                class="custom-switch"
                switch
              />
            </BaseFormValidator>

            <BaseFormValidator
              horizontal
              name="banner"
              :label="$t('teacher.editor.course.steps.landing_page.form.banner.title')"
            >
              <img
                v-if="courseForm.banner"
                class="w-100 max-w-500 mb-4"
                :src="courseForm.banner"
                @click="handleChangeBanner"
              />
              <ImageUploader
                v-if="!isApproved"
                v-model:url="courseForm.banner"
                ref="bannerUploader"
                show-cropper
                :site="site"
                :default-aspect-ratio="ASPECT_RATIO_ENUMS['4:3']"
                @upload-success="saveCourse"
              >
              </ImageUploader>
            </BaseFormValidator>
          </BForm>
        </BCardBody>
      </BCard>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import { CourseInputInterface } from '@/utils/interface/teacher/course';
  import { OptionInterface } from '@/utils/interface/select-options';

  import { ASPECT_RATIO_ENUMS } from '@/utils/constant';
  import useTeacherSelectOptions from '@/composable/useTeacherSelectOptions';
  import ImageUploader from '../../ImageUploader.vue';
  const courseForm = defineModel('courseForm', {
    type: Object as PropType<CourseInputInterface>,
    required: true
  });
  const emits = defineEmits(['saveCourse']);
  defineProps({
    courseInstructionalLevelOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    isApproved: {
      type: Boolean,
      required: true
    },
    site: {
      type: String,
      required: true
    }
  });

  const { selectOptions, fetchTeacherSelectOptions } = useTeacherSelectOptions();

  const highlightIdsOptions = computed(() => {
    return (
      selectOptions.value.highlightOptions?.map(item => ({
        value: String(item.value),
        label: item.label,
        icon: item.icon
      })) || []
    );
  });

  const bannerUploader = ref<InstanceType<typeof ImageUploader> | null>(null);

  const handleChangeBanner = () => {
    if (bannerUploader.value) {
      bannerUploader.value.triggerFileInput();
    }
  };

  const removeHighlight = (option: OptionInterface) => {
    courseForm.value.highlightIds = courseForm.value?.highlightIds?.filter(id => id !== option.value);
  };

  onMounted(async () => {
    await fetchTeacherSelectOptions(['highlightOptions']);
  });

  const saveCourse = () => {
    emits('saveCourse');
  };
</script>
<style scoped>
  .multiselect-custom .multiselect-tag {
    background-color: var(--bs-gray-100) !important;
    color: var(--bs-gray-800) !important;
    border-radius: 5px;
    padding: 5px 10px;
    margin: 5px;
  }
</style>
