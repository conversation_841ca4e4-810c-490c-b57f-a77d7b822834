<template>
  <BCard class="package-card h-100 w-100 position-relative">
    <!-- <div v-if="!isBasicPackage && !isApproved" class="d-flex justify-content-end">
      <Button @click="handleDeletePackage" icon="bx-trash" icon-only variant="outline-danger"></Button>
    </div> -->

    <BCardBody class="p-4">
      <div class="d-flex justify-content-between align-items-start mb-3">
        <div>
          <h4 class="package-title mb-1">{{ packageDetail.title }}</h4>
        </div>
      </div>

      <p class="text-muted mb-4">{{ packageDetail?.description }}</p>

      <BForm>
        <BaseFormValidator
          :name="`coursePackageAttributes.${index}.price`"
          :label="$t('teacher.editor.course.steps.pricing.form.price.title')"
          class="pb-4"
        >
          <input
            v-model="coursePackage.price"
            class="form-control"
            id="price"
            name="price"
            type="number"
            :placeholder="$t('teacher.editor.course.steps.pricing.form.price.placeholder')"
            @input="onNumberChange($event as InputEvent)"
          />
        </BaseFormValidator>

        <BaseFormValidator
          :name="`coursePackageAttributes.${index}.salePrice`"
          :label="$t('teacher.editor.course.steps.pricing.form.sale_price.title')"
          class="pb-4"
        >
          <input
            v-model="coursePackage.salePrice"
            class="form-control"
            id="salePrice"
            name="salePrice"
            type="number"
            :placeholder="$t('teacher.editor.course.steps.pricing.form.sale_price.placeholder')"
            @input="onNumberChange($event as InputEvent, true)"
          />

          <template v-slot:custom-subtitle> </template>
        </BaseFormValidator>
      </BForm>
      <div class="features-section">
        <h6 class="mb-3">{{ $t('teacher.course_package.feature_list_text') }}</h6>

        <ul class="features-list">
          <li v-for="feature in packageDetail.features" :key="feature">• {{ feature.content }}</li>
        </ul>
      </div>
    </BCardBody>
  </BCard>
</template>

<script setup lang="ts">
  import { CoursePackageInterface } from '@/utils/interface/teacher/course';
  // import Button from '@/components/base/Button.vue';
  // import { PACKAGES_NAMES } from '@/utils/constant';

  const props = defineProps({
    coursePackage: {
      type: Object as PropType<CoursePackageInterface>,
      required: true
    },
    packageName: {
      type: String,
      default: ''
    },
    packageDetail: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      required: true,
      default: 0
    },
    isApproved: {
      type: Boolean,
      required: true,
      default: false
    }
  });

  // const isBasicPackage = computed(() => {
  //   return props.coursePackage.packageDeal?.name === PACKAGES_NAMES.BASIC;
  // });

  const onNumberChange = (event: InputEvent, isSalePrice = false) => {
    const target = event.target as HTMLInputElement;
    let raw = target.value;

    if (raw.length > 9) {
      raw = raw.slice(0, 9);
      target.value = raw;
    }

    if (raw === '') {
      if (isSalePrice) {
        props.coursePackage.salePrice = 0;
      } else {
        props.coursePackage.price = 0;
      }
    } else {
      const asNumber = Number(raw);
      if (isSalePrice) {
        props.coursePackage.salePrice = isNaN(asNumber) ? 0 : asNumber;
      } else {
        props.coursePackage.price = isNaN(asNumber) ? 0 : asNumber;
      }
    }
  };

  // const handleDeletePackage = () => {
  //   props.coursePackage.isDeleted = true;
  // };
</script>

<style scoped>
  .package-card {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.3s ease;
    overflow: hidden;
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .package-card .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .features-section {
    margin-top: auto;
  }

  /* .package-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  } */

  /* .package-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  } */

  .package-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 4px;
  }

  .features-section h6 {
    color: #374151;
    font-weight: 600;
  }

  .features-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .features-list li {
    padding: 4px 0;
    color: #6b7280;
    font-size: 0.875rem;
  }
</style>
