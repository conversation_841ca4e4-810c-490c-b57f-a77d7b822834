<template>
  <BRow>
    <BCol lg="12">
      <BCard no-body class="shadow-none border-top">
        <BCardBody>
          <draggable
            animation="200"
            group="course-sections"
            handle=".handle"
            itemKey="id"
            tag="div"
            :list="courseDetail.courseSections"
            @change="swapPosition"
          >
            <template #item="{ element, index }">
              <CourseSectionCard
                v-model:course-section-form="courseSectionForm"
                :course-id="courseId"
                :course-section-detail="element"
                :course-section-errors="courseSectionErrors"
                :course-section-id="element.id"
                :course-section-index="index"
                :course-title="courseDetail.title"
                :is-approved="isApproved"
                :is-editing-section="isEditingSection(element.id)"
                :level-options="levelOptions"
                :ref="el => registerRef(el, element.id)"
                :skill-options="skillOptions"
                :timestamp="timestamp"
                @cancel-edit-course-section="handleCancelEditCourseSection"
                @delete-course-section="handleDeleteCourseSection"
                @edit-course-section="handleEditCourseSection"
                @get-new-timestamp="getNewTimestamp"
                @refresh-course-detail="refreshCourseDetail"
                @remove-course-section-blank="removeCourseSectionBlank"
                @remove-course-section-item-blank="removeCourseSectionItemBlank"
                @reset-errors="resetErrors"
                @update-course-section="handleUpdateCourseSection"
              ></CourseSectionCard>
            </template>
          </draggable>

          <BCard v-if="isAddingSection" no-body class="bg-white p-3 border border-primary">
            <CourseSectionFormView
              v-model:course-section-form="courseSectionForm"
              isCreate
              :loading="isCreateSectionBtnLoading"
              @cancel="handleCancelCreateSection"
              @submit="handleCreateSection"
            ></CourseSectionFormView>
          </BCard>

          <Button v-if="!isAddingSection && !isApproved" icon="bx-plus" @click="handleAddingSection">
            {{ $t('teacher.course_section.add_btn') }}
          </Button>
        </BCardBody>
      </BCard>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import draggable from 'vuedraggable';

  import i18n from '@/plugin/i18n';

  import useSwal from '@/composable/swal';

  import { CourseInterface } from '@/utils/interface/teacher/course';
  import { CourseSectionDraggablePayload } from '@/utils/interface/teacher/courseSection';
  import { OptionInterface } from '@/utils/interface/select-options';
  import { SwalIconOptions, SwalOptions } from '@/utils/swal-options';

  import { CourseSectionForm } from '@/forms/teacher/courseSection';

  import {
    courseSectionCreate,
    courseSectionDelete,
    courseSectionSwapPosition,
    courseSectionUpdate
  } from '@/services/teacher/repositories/courseSection';

  import Button from '../../Button.vue';
  import CourseSectionCard from './CourseSectionCard.vue';
  import CourseSectionFormView from './CourseSectionForm.vue';
  import type { CourseSectionCardExpose } from './CourseSectionCard.vue';

  const { confirming } = useSwal();

  const emits = defineEmits([
    'getNewTimestamp',
    'refreshCourseDetail',
    'removeCourseSectionBlank',
    'removeCourseSectionItemBlank',
    'resetErrors'
  ]);
  const props = defineProps({
    courseDetail: {
      type: Object as PropType<CourseInterface>,
      required: true
    },
    courseId: {
      type: Number,
      required: true
    },
    levelOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    skillOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    isApproved: {
      type: Boolean,
      required: true
    },
    timestamp: {
      type: Number,
      required: true
    },
    courseSectionErrors: {
      type: Object as PropType<Record<string, string[]>>,
      required: true
    }
  });

  const courseSectionForm = ref<CourseSectionForm>(new CourseSectionForm());
  const courseSectionRefs = ref<Record<number, CourseSectionCardExpose>>({});
  const isAddingSection = ref<boolean>(false);
  const isCreateSectionBtnLoading = ref<boolean>(false);
  const modifyingCourseSectionId = ref<number>();

  const resetErrors = () => {
    emits('resetErrors');
  };

  const isEditingSection = (courseSectionId: number) => {
    return courseSectionId == modifyingCourseSectionId.value;
  };

  const swapPosition = async (payload: CourseSectionDraggablePayload) => {
    await courseSectionSwapPosition(
      String(props.courseId),
      String(payload.moved.element.id),
      Number(payload.moved.newIndex)
    );
  };

  const handleAddingSection = () => {
    isAddingSection.value = true;
    isCreateSectionBtnLoading.value = false;
    modifyingCourseSectionId.value = undefined;
    courseSectionForm.value = new CourseSectionForm();
    emits('resetErrors');
  };

  const handleCancelCreateSection = () => {
    isAddingSection.value = false;
  };

  const refreshCourseDetail = () => {
    emits('refreshCourseDetail');
  };

  const handleCreateSection = async () => {
    if (!props.courseId) return;

    isCreateSectionBtnLoading.value = true;

    try {
      await courseSectionCreate(String(props.courseId), courseSectionForm.value);
    } catch {
      isCreateSectionBtnLoading.value = false;
      return;
    }

    isAddingSection.value = false;
    isCreateSectionBtnLoading.value = false;
    refreshCourseDetail();
  };

  const deleteCourseSection = async (courseSectionId: number) => {
    if (!courseSectionId) return;

    await courseSectionDelete(String(courseSectionId), String(props.courseId));
  };

  const handleDeleteCourseSection = async (courseSectionId: number) => {
    const message = i18n.global.t('teacher.course_section.delete_modal.content');

    const confirmed = await confirming(
      new SwalOptions({
        html: message,
        icon: SwalIconOptions.Warning
      })
    );

    if (!confirmed) return;

    if (confirmed) {
      await deleteCourseSection(courseSectionId);
      refreshCourseDetail();
    }
  };

  const handleEditCourseSection = (courseSectionId: number) => {
    const courseSection = props.courseDetail.courseSections.find(section => section.id == courseSectionId);
    isAddingSection.value = false;
    modifyingCourseSectionId.value = courseSectionId;
    courseSectionForm.value.assignAttributes(courseSection);
    emits('resetErrors');
  };

  const handleCancelEditCourseSection = () => {
    modifyingCourseSectionId.value = undefined;
  };

  const registerRef = (el: Element | ComponentPublicInstance | null, sectionId: number) => {
    const instance = el as CourseSectionCardExpose | null;
    if (
      instance &&
      typeof instance.showUpdateSectionBtnLoading === 'function' &&
      typeof instance.hideUpdateSectionBtnLoading === 'function'
    ) {
      courseSectionRefs.value[sectionId] = instance;
    } else {
      delete courseSectionRefs.value[sectionId];
    }
  };

  const handleUpdateCourseSection = async () => {
    if (!modifyingCourseSectionId.value) return;

    const courseSection = courseSectionRefs.value[modifyingCourseSectionId.value];
    if (!courseSection) return;

    courseSection.showUpdateSectionBtnLoading();
    try {
      await courseSectionUpdate(
        String(modifyingCourseSectionId.value),
        String(props.courseId),
        courseSectionForm.value
      );
    } catch {
      courseSection.hideUpdateSectionBtnLoading();
      return;
    }
    courseSection.hideUpdateSectionBtnLoading();
    modifyingCourseSectionId.value = undefined;
    refreshCourseDetail();
  };

  const getNewTimestamp = () => {
    emits('getNewTimestamp');
  };

  const removeCourseSectionBlank = (courseSectionId: number) => {
    emits('removeCourseSectionBlank', courseSectionId);
  };

  const removeCourseSectionItemBlank = (courseSectionId: number, courseSectionItemId: number) => {
    emits('removeCourseSectionItemBlank', courseSectionId, courseSectionItemId);
  };
</script>
