<template>
  <BCard class="text-center border border-primary bg-white mb-2 rounded-2" no-body>
    <BCardHeader
      class="cursor-pointer rounded-2 course-section-item-wrapper d-flex justify-content-between align-items-center cursor-pointer"
      header-tag="div"
      v-b-toggle="[collapseToggleId]"
    >
      <h5 class="my-0">
        <span class="btn p-0 cursor-pointer border-0">
          <i
            class="font-size-24 pe-2 mdi"
            :class="{ 'mdi-chevron-up': isExpanded, 'mdi-chevron-down': !isExpanded }"
          ></i>
        </span>
        <span>
          {{ $t('teacher.editor.course.steps.sections.content_type.drill.title') }}
        </span>
      </h5>
    </BCardHeader>

    <BCollapse :id="collapseToggleId" lazy :visible="isEditingDrillContent" unmount-lazy v-model="isExpanded">
      <BCardBody class="p-3 border-primary border-top">
        <CourseSectionItemSelectedDrill
          :is-approved="isApproved"
          :is-selected-drill-list-loading="isSelectedDrillListLoading"
          :selected-drills="selectedDrills"
          :timestamp="timestamp"
          @edit-selected-drill="handleEditSelectedDrill"
          @delete-selected-drill="handleDeleteSelectedDrill"
        ></CourseSectionItemSelectedDrill>

        <div v-if="!isApproved" class="d-flex justify-content-center align-items-center gap-2">
          <Button icon="bx-select-multiple" variant="outline-primary" @click="openSelectDrillModal">
            {{ $t('teacher.course_section_item.form.select_drill') }}
          </Button>
          <Button
            icon="bx-plus"
            variant="outline-primary"
            :disabled="isDrillSubmitButtonLoading"
            :loading="isDrillSubmitButtonLoading"
            @click="openCreateDrillModal"
          >
            {{ $t('teacher.course_section_item.form.create_drill') }}
          </Button>
        </div>
      </BCardBody>

      <BCardFooter class="rounded-2 border-primary border-top" v-if="isEditingDrillContent">
        <div class="d-flex justify-content-end gap-2">
          <Button icon="bx-x" variant="outline-primary" @click="handleCancelEditDrillContent">
            {{ $t('common.cancel') }}
          </Button>
        </div>
      </BCardFooter>
    </BCollapse>
  </BCard>

  <CourseSectionItemSelectDrillModal
    v-model:query="query"
    v-model="isSelectDrillModalOpen"
    :drills="drills"
    :loading="isListDrillLoading"
    :metadata="metadata"
    @fetch-list="changePage"
    @hidden="handleHiddenSelectDrillModal"
    @reset="handleReset"
    @search="handleSearch"
    @select-drill="selectDrill"
  ></CourseSectionItemSelectDrillModal>

  <CourseSectionItemCreateDrillModal
    v-model="isCreateDrillModalOpen"
    :active-drill-slug="activeDrillSlug"
    :level-options="levelOptions"
    :skill-options="skillOptions"
    @hidden="handleHiddenCreateDrillModal"
    @select-drill="selectDrill"
  />
</template>

<script lang="ts" setup>
  import { vBToggle } from 'bootstrap-vue-next';

  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { MetaDataInterface } from '@/utils/interface/common';
  import { OptionInterface } from '@/utils/interface/select-options';

  import Button from '../../Button.vue';
  import CourseSectionItemCreateDrillModal from './CourseSectionItemCreateDrillModal.vue';
  import CourseSectionItemSelectDrillModal from './CourseSectionItemSelectDrillModal.vue';
  import CourseSectionItemSelectedDrill from './CourseSectionItemSelectedDrill.vue';

  const query = defineModel('query', {
    type: Object,
    default: {}
  });
  const isSelectDrillModalOpen = defineModel('isSelectDrillModalOpen', {
    type: Boolean,
    default: false
  });
  const isCreateDrillModalOpen = defineModel('isCreateDrillModalOpen', {
    type: Boolean,
    default: false
  });
  const emits = defineEmits([
    'cancelEditDrillContent',
    'deleteSelectedDrill',
    'editSelectedDrill',
    'fetchList',
    'handleHiddenCreateDrillModal',
    'hidden',
    'openCreateDrillModal',
    'openSelectDrillModal',
    'reset',
    'search',
    'selectDrill'
  ]);
  const props = defineProps({
    selectedDrills: {
      type: Array as PropType<DrillInterface[]>,
      required: true
    },
    isSelectedDrillListLoading: {
      type: Boolean,
      default: false
    },
    isEditingDrillContent: {
      type: Boolean,
      default: false
    },
    courseSectionId: {
      type: Number,
      required: true
    },
    courseSectionItemId: {
      type: Number,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    drills: {
      type: Array as PropType<DrillInterface[]>,
      required: true
    },
    isListDrillLoading: {
      type: Boolean,
      default: false
    },
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    },
    isDrillSubmitButtonLoading: {
      type: Boolean,
      default: false
    },
    activeDrillSlug: {
      type: String,
      default: null
    },
    levelOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    skillOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    isApproved: {
      type: Boolean,
      required: true
    },
    timestamp: {
      type: Number,
      required: true
    }
  });

  const isExpanded = ref<boolean>(false);

  const collapseToggleId = computed(
    () => `collapse-section-item-drill-${props.courseSectionId}-${props.courseSectionItemId}`
  );

  const handleCancelEditDrillContent = () => {
    emits('cancelEditDrillContent');
  };

  const handleEditSelectedDrill = (slug: string) => {
    emits('editSelectedDrill', slug);
  };

  const handleDeleteSelectedDrill = (id: number) => {
    emits('deleteSelectedDrill', id);
  };

  const openSelectDrillModal = () => {
    emits('openSelectDrillModal');
  };

  const handleReset = () => {
    emits('reset');
  };

  const handleSearch = () => {
    emits('search');
  };

  const selectDrill = (id: number) => {
    emits('selectDrill', id);
  };

  const changePage = (e: { page: number }) => {
    emits('fetchList', e);
  };

  const handleHiddenSelectDrillModal = () => {
    emits('hidden');
  };

  const handleHiddenCreateDrillModal = () => {
    emits('handleHiddenCreateDrillModal');
  };

  const openCreateDrillModal = () => {
    emits('openCreateDrillModal');
  };
</script>
