<template>
  <BRow>
    <BCol lg="12">
      <BCard no-body class="shadow-none">
        <section class="border-top border-bottom">
          <h2 class="mt-5 border-bottom border-black pb-2 width-fit-content">
            {{ $t('teacher.editor.course.steps.planning_tip.content.tip.title') }}
          </h2>
          <div v-for="i in 3" :key="i">
            <h3 class="mt-4 mb-2">
              {{ $t(`teacher.editor.course.steps.planning_tip.content.tip.list.${i}.title`) }}
            </h3>
            <p class="pb-2">
              {{ $t(`teacher.editor.course.steps.planning_tip.content.tip.list.${i}.description`) }}
            </p>

            <ul>
              <div v-for="k in 8 - i" :key="k" class="mb-4">
                <li>
                  <h5>
                    <strong>
                      {{ $t(`teacher.editor.course.steps.planning_tip.content.tip.list.${i}.sub_list.${k}.label`) }}
                    </strong>
                  </h5>
                  <p class="mb-0">
                    {{ $t(`teacher.editor.course.steps.planning_tip.content.tip.list.${i}.sub_list.${k}.description`) }}
                  </p>
                </li>
              </div>
            </ul>
          </div>
        </section>

        <section>
          <h2 class="mt-5 mb-4 border-bottom border-black pb-2 width-fit-content">
            {{ $t('teacher.editor.course.steps.planning_tip.content.requirement.title') }}
          </h2>

          <ul v-for="i in 6" :key="i">
            <li class="mb-2">
              {{ $t(`teacher.editor.course.steps.planning_tip.content.requirement.list.${i}.description`) }}
            </li>
          </ul>
        </section>
      </BCard>
    </BCol>
  </BRow>
</template>
