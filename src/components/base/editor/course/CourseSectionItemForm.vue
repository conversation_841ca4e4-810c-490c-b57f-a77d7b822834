<template>
  <div class="course-section-item-form-wrapper">
    <div class="d-flex align-items-center w-100">
      <h5 class="pb-3">
        <strong class="pe-2">
          <template v-if="isCreate">{{ $t('teacher.course_section_item.form.new_title') }}:</template>
          <template v-else>
            {{ $t('teacher.course_section_item.form.title') }} {{ courseSectionItemIndex + 1 }} :
          </template>
        </strong>
      </h5>

      <div class="flex-grow-1">
        <BaseFormValidator name="title" required class="mb-2">
          <input
            v-model="courseSectionItemForm.title"
            class="form-control"
            id="title"
            name="title"
            type="text"
            :placeholder="$t('teacher.course_section_item.form.fields.title')"
          />
        </BaseFormValidator>

        <BaseFormValidator name="is_free" required>
          <div class="form-check form-switch form-switch-lg">
            <input
              v-model="courseSectionItemForm.isFree"
              class="form-check-input"
              type="checkbox"
              id="isFree"
              checked
            />
            <label class="form-check-label ms-2" for="isFree">
              {{ $t('teacher.course_section_item.form.fields.is_free') }}
            </label>
          </div>
        </BaseFormValidator>
      </div>
    </div>

    <BCardBody class="p-0">
      <div class="d-flex justify-content-end gap-2">
        <Button variant="outline-primary" icon="bx-x" @click="handleCancel">
          {{ $t('teacher.course_section_item.form.cancel_btn') }}
        </Button>
        <Button icon="bx-save" variant="success" :disabled="loading" :loading="loading" @click="handleSubmit">
          {{ $t('teacher.course_section_item.form.save_btn') }}
        </Button>
      </div>
    </BCardBody>
  </div>
</template>

<script lang="ts" setup>
  import { CourseSectionItemForm } from '@/forms/teacher/courseSectionItem';

  import Button from '../../Button.vue';

  const emits = defineEmits(['cancel', 'submit']);
  const courseSectionItemForm = defineModel('courseSectionItemForm', {
    type: Object as PropType<CourseSectionItemForm>,
    required: true
  });
  defineProps({
    loading: {
      type: Boolean,
      default: false
    },
    isCreate: {
      type: Boolean,
      default: false
    },
    courseSectionItemIndex: {
      type: Number,
      default: 0
    }
  });

  const handleCancel = () => {
    emits('cancel');
  };

  const handleSubmit = () => {
    emits('submit');
  };
</script>
