<template>
  <BModal
    body-class="p-0"
    fullscreen="lg"
    lazy
    no-footer
    scrollable
    size="xl"
    :title="$t('teacher.course.select_drill_modal.title')"
  >
    <BCard no-body class="mb-0">
      <BCardBody class="border-bottom pt-0 px-3">
        <CourseSectionItemDrills
          v-model:query="query"
          :drills="drills"
          :loading="loading"
          :metadata="metadata"
          @fetch-list="fetchList"
          @reset="reset"
          @search="search"
          @select-drill="select"
        ></CourseSectionItemDrills>
      </BCardBody>
    </BCard>
  </BModal>
</template>

<script lang="ts" setup>
  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { MetaDataInterface } from '@/utils/interface/common';

  import CourseSectionItemDrills from './CourseSectionItemDrills.vue';

  const query = defineModel('query', {
    type: Object,
    default: {}
  });
  const emits = defineEmits(['reset', 'search', 'select-drill', 'fetchList']);
  defineProps({
    loading: {
      type: Boolean,
      default: false
    },
    drills: {
      type: Array as PropType<DrillInterface[]>,
      required: true
    },
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    }
  });

  const reset = () => {
    emits('reset');
  };

  const search = () => {
    emits('search');
  };

  const select = (id: number) => {
    emits('select-drill', id);
  };

  const fetchList = (e: { page: number }) => {
    emits('fetchList', e);
  };
</script>
