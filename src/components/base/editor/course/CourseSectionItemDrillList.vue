<template>
  <Loader :loading="loading">
    <BRow v-for="drill in drills" :key="drill.slug">
      <BCard no-body class="drill-item-wrapper shadow-none mb-0" @click="handleAddDrill(drill.id)">
        <BCardBody>
          <BRow>
            <BCol md="3">
              <div class="pb-2 h-100 d-flex justify-content-center align-items-center">
                <img
                  class="btn p-0 w-100"
                  :src="
                    drill.diagrams && drill.diagrams.length && drill.diagrams[0].imageUrl
                      ? drill.diagrams[0].imageUrl
                      : '/pool-table.png'
                  "
                />
              </div>
            </BCol>
            <BCol md="7">
              <h4>
                {{ drill.title }}
              </h4>
              <pre v-if="drill.description" class="pre-content line-clamp-5">{{ drill.description }}</pre>
            </BCol>
            <BCol md="2">
              <div class="d-flex justify-content-center align-items-center h-100">
                <Button v-if="onSectionItem" classes="select-btn" icon="bx-down-arrow-alt" variant="white">
                  {{ $t('common.select') }}
                </Button>
              </div>
            </BCol>
          </BRow>
        </BCardBody>
      </BCard>
    </BRow>

    <Pagination :metadata="metadata" @change="$emit('fetchList', $event)"></Pagination>
  </Loader>
</template>

<script lang="ts" setup>
  import Button from '@/components/base/Button.vue';

  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { MetaDataInterface } from '@/utils/interface/common';

  const emit = defineEmits(['fetchList', 'select']);
  defineProps({
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    drills: {
      type: Array as PropType<DrillInterface[]>,
      required: true
    },
    onSectionItem: {
      type: Boolean,
      default: false
    }
  });

  function handleAddDrill(id: number) {
    emit('select', id);
  }
</script>

<style lang="scss" scoped>
  .drill-item-wrapper {
    cursor: pointer;
    transition: all 0.1s ease-in-out;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);

      .select-btn {
        color: var(--bs-primary);
      }
    }
  }
</style>
