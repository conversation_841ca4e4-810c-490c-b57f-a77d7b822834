<template>
  <div v-if="items?.length" class="courses-section">
    <h2 class="h2 fw-bold mb-4">{{ i18n.global.t('public.course.my_courses') }} ({{ metadata.total }})</h2>
    <BRow>
      <BCol v-for="course in items" :key="course.id" xl="3" lg="4" md="6" cols="12" class="mt-4">
        <CourseCard :course="course" />
      </BCol>
    </BRow>
  </div>
</template>

<script setup lang="ts">
  import i18n from '@/plugin/i18n';
  import CourseCard from '@/components/public/course/CourseCard.vue';

  import { MetaDataInterface } from '@/utils/interface/common';
  import { CourseInterface } from '@/utils/interface/public/course';

  defineProps({
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    },
    items: {
      type: Array as PropType<CourseInterface[]>,
      required: true
    }
  });
</script>

<style scoped lang="scss">
  $primary: #f1b44c;

  .primary-color {
    color: $primary;
  }

  .teacher-avatar {
    border: 4px solid white;
    object-fit: cover;
  }

  .course-card {
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 32px rgba($primary, 0.15);
    }

    .course-banner {
      width: 100%;
      height: 200px;
      object-fit: cover;
    }

    .bestseller-badge {
      position: absolute;
      top: 12px;
      left: 12px;
      background: $primary;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 600;
    }
  }

  .badge {
    &.bg-primary {
      background-color: $primary !important;
    }
  }

  @media (max-width: 768px) {
    .teacher-avatar {
      width: 150px !important;
      height: 150px !important;
    }
  }

  .gradient-background {
    background: linear-gradient(204deg, #f5b44c, #f9e7c6);
  }
</style>
