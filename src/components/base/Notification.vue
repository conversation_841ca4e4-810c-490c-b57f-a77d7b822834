<template>
  <!-- Notification Dropdown -->
  <BDropdown
    v-if="isLoggedIn"
    class="notification-dropdown"
    right
    :toggle-class="['notify-toggle', hoverBgClass]"
    menu-class="notification-dropdown-menu"
    @show="handleDropdownShow"
  >
    <template #button-content>
      <div class="position-relative notification-bell">
        <i class="bx bx-bell bx-sm" :class="{ 'text-white': isWhite }"></i>
        <span
          v-if="unreadCount > 0"
          class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge"
        >
          {{ unreadCount > 99 ? '99+' : unreadCount }}
        </span>
      </div>
    </template>

    <div class="px-3 py-2 d-flex align-items-center justify-content-between" @click.stop>
      <h4 class="fw-semibold">{{ $t('notification.title') }}</h4>
      <!-- <router-link to="/notifications" class="font-size-12 text-primary">{{ $t('notification.view_all') }}</router-link> -->
    </div>
    <div class="notification-tabs d-flex align-items-center gap-2 px-3 pb-2" @click.stop>
      <span
        v-for="tab in tabs"
        :key="tab.value"
        class="tab-item"
        :class="{ active: activeTab === tab.value }"
        tabindex="0"
        @click="setActiveTab(tab.value as 'all' | 'unread')"
      >
        {{ $t(tab.label) }}
      </span>
    </div>

    <div v-if="isLoading" class="d-flex justify-content-center align-items-center py-3">
      <div class="spinner-border spinner-border-sm text-primary" role="status">
        <span class="visually-hidden">{{ $t('common.loading') }}</span>
      </div>
      <span class="ms-2">{{ $t('common.loading') }}</span>
    </div>
    <div
      v-else-if="displayedNotifications.length"
      class="notification-list"
      @scroll.passive="handleScroll"
      style="max-height: 600px; overflow-y: auto"
    >
      <BDropdownItem
        v-for="(notification, idx) in displayedNotifications"
        :key="idx"
        class="notification-item py-2 border-0"
        :class="{ unread: !notification.isRead }"
        @click="onMakeNotificationRead(notification)"
      >
        <div class="d-flex align-items-center gap-2">
          <div class="notification-avatar">
            <BAvatar variant="warning" :src="notification.sender?.imageUrl || dummyAvatar" size="60"></BAvatar>
            <!-- TODO: add notification type icon with noticeKind -->
            <div class="notification-type-icon" :class="getNotificationIcon(notification)">
              <i :class="getNotificationIconClass(notification)"></i>
            </div>
          </div>
          <div class="notification-content">
            <div v-html="getNotificationContent(notification)"></div>

            <div class="notification-time">
              {{ formatTimeAgo(notification.createdAt) }}
            </div>
          </div>

          <div v-if="!notification.isRead">
            <div class="notification-red-dot"></div>
          </div>
        </div>
      </BDropdownItem>
    </div>
    <div v-else class="px-3 py-2 text-muted font-size-12 text-center">
      {{ activeTab === 'unread' ? $t('notification.no_unread') : $t('notification.no_notification') }}
    </div>
    <div v-if="isLoadingMore" class="d-flex justify-content-center py-2">
      <div class="spinner-border spinner-border-sm text-primary" role="status">
        <span class="visually-hidden"> {{ $t('common.loading') }}</span>
      </div>
      <span class="ms-2">{{ $t('notification.loading_more') }}</span>
    </div>
    <!-- <div v-else-if="hasMoreNotifications && !isLoading" class="text-center py-2" @click.stop>
      <a href="#" class="text-primary font-size-12" @click.prevent="loadMoreNotifications">
        {{ $t('notification.view_more') }}
      </a>
    </div> -->
  </BDropdown>
</template>

<script lang="ts" setup>
  import { useUserAuthStore } from '@/store/user/auth';
  import i18n from '@/plugin/i18n';
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';
  import { makeNotificationRead, notificationList as notificationUserList } from '@/services/user';
  import { NotificationInterface } from '@/utils/interface/public/notification';
  import { MetaDataInterface } from '@/utils/interface/common';
  import { SITES } from '@/utils/constant';
  import { notificationList as notificationTeacherList } from '@/services/teacher/repositories/notification';
  import { notificationList as notificationAdminList } from '@/services/admin/repositories/notification';

  const props = defineProps({
    isLoggedIn: {
      type: Boolean,
      default: false
    },
    site: {
      type: String,
      required: true
    }
  });
  const authUserStore = useUserAuthStore();
  const { userProfile } = storeToRefs(authUserStore);
  const router = useRouter();

  const activeTab = ref<'all' | 'unread'>('all');
  const isLoading = ref(false);
  const pageSize = 10;
  const isLoadingMore = ref(false);
  const notificationsList = ref<NotificationInterface[]>([]);
  const metadata = ref<MetaDataInterface>({
    total: 0,
    perPage: pageSize,
    page: 1,
    pages: 0,
    count: 0,
    next: 0,
    prev: 0,
    from: 0,
    to: 0
  });

  const tabs = [
    { value: 'all', label: 'notification.all' },
    { value: 'unread', label: 'notification.unread' }
  ];

  const hoverBgClass = computed(() => {
    switch (props.site) {
      case SITES.TEACHER:
        return 'hover-bg-teacher';
      case SITES.ADMIN:
        return 'hover-bg-admin';
      case SITES.USER:
        return 'hover-bg-user';
      default:
        return '';
    }
  });
  const isWhite = computed(() => {
    return props.site === SITES.TEACHER || props.site === SITES.ADMIN;
  });

  const unreadCount = computed(() => {
    return userProfile.value?.unreadNotificationCount || 0;
  });

  const displayedNotifications = computed(() => {
    return notificationsList.value;
  });

  const hasMoreNotifications = computed(() => {
    return metadata.value.next !== null;
  });

  const notificationList = async (page: number, pageSize: number, query: any) => {
    switch (props.site) {
      case SITES.USER:
        return await notificationUserList({ page, perPage: pageSize }, query);
      case SITES.TEACHER:
        return await notificationTeacherList({ page, perPage: pageSize }, query);
      case SITES.ADMIN:
        return await notificationAdminList({ page, perPage: pageSize }, query);
      default:
        throw new Error('Unknown site type');
    }
  };

  // Methods
  const fetchNotifications = async (page = 1, isLoadMore = false) => {
    try {
      if (isLoadMore) {
        isLoadingMore.value = true;
      } else {
        isLoading.value = true;
        // Reset danh sách khi load mới (không phải load more)
        if (!isLoadMore) {
          notificationsList.value = [];
        }
      }

      const query: any = {};
      console.log('activeTab.value', activeTab.value);
      if (activeTab.value === 'unread') {
        query.isReadEq = false;
      }

      const result = await notificationList(page, pageSize, query);

      const { collection, metadata: meta } = result.notifications;

      if (isLoadMore) {
        notificationsList.value.push(...collection);
      } else {
        notificationsList.value = collection;
      }

      metadata.value = {
        ...meta,
        page: page,
        next: meta.next || null,
        prev: meta.prev || null
      };
    } catch (error) {
      console.error('Lỗi khi tải thông báo:', error);
    } finally {
      setTimeout(() => {
        isLoading.value = false;
      }, 500);
      isLoadingMore.value = false;
    }
  };

  const handleDropdownShow = async () => {
    await fetchNotifications();
  };

  const setActiveTab = async (tab: 'all' | 'unread') => {
    if (activeTab.value === tab) return;

    activeTab.value = tab;
    await fetchNotifications();
  };

  const formatTimeAgo = (dateString: string): string => {
    if (!dateString) return i18n.global.t('notification.just_now');
    const now = new Date();
    const date = new Date(dateString);
    const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    const intervals = [
      { threshold: 31536000, key: 'notification.year_time_ago' },
      { threshold: 2592000, key: 'notification.month_time_ago' },
      { threshold: 604800, key: 'notification.week_time_ago' },
      { threshold: 86400, key: 'notification.day_time_ago' },
      { threshold: 3600, key: 'notification.hour_time_ago' },
      { threshold: 60, key: 'notification.minute_time_ago' }
    ];

    for (const { threshold, key } of intervals) {
      const interval = Math.floor(seconds / threshold);
      if (interval >= 1) return i18n.global.t(key, { interval });
    }
    return i18n.global.t('notification.just_now');
  };

  const loadMoreNotifications = async () => {
    if (isLoadingMore.value || !hasMoreNotifications.value) return;

    const nextPage = (metadata.value.page || 1) + 1;
    if (nextPage <= (metadata.value.pages || 1)) {
      await fetchNotifications(nextPage, true);
    }
  };

  const handleScroll = (event: Event) => {
    const container = event.target as HTMLElement;
    // Add a small threshold to trigger loading before reaching the absolute bottom
    const threshold = 50; // pixels from bottom
    const scrollPosition = container.scrollTop + container.offsetHeight;
    const scrollThreshold = container.scrollHeight - threshold;

    // Only trigger if we're near the bottom and not already loading
    if (scrollPosition >= scrollThreshold && hasMoreNotifications.value && !isLoadingMore.value) {
      loadMoreNotifications();
    }
  };

  const getNotificationIcon = (notification: NotificationInterface) => {
    switch (notification.noticeKind) {
      case 'InviteCourse':
        return 'InviteCourse';
      default:
        return '';
    }
  };

  const getNotificationIconClass = (notification: NotificationInterface) => {
    switch (notification.noticeKind) {
      case 'InviteCourse':
        return 'mdi mdi-account-plus';
      default:
        return '';
    }
  };

  const getNotificationContent = (notification: NotificationInterface) => {
    switch (notification.noticeKind) {
      case 'InviteCourse':
        return i18n.global.t(`notification.teacher_invite_course`, {
          senderName: `<strong>${notification.sender?.name}</strong>`,
          courseName: `<strong>${notification.entity?.title}</strong>`
        });
      case 'VideoProgress':
        return i18n.global.t(``);
      default:
        return '';
    }
  };
  const getNotificationUrl = (notification: NotificationInterface) => {
    switch (notification.noticeKind) {
      case 'InviteCourse':
        return `/courses/${notification.entity?.slug}`;
      default:
        return '';
    }
  };

  const onMakeNotificationRead = async (notification: NotificationInterface) => {
    if (!notification.isRead) {
      await makeNotificationRead(String(notification.id));
      await authUserStore.getProfile();
    }
    const url = getNotificationUrl(notification);
    if (url) router.push(url);
  };
</script>

<style lang="scss" scoped>
  .notification-dropdown {
    display: flex;

    :deep(.notify-toggle) {
      background: transparent;
      border: none;
      color: #495057;
      display: flex;
      align-items: center;
      font-size: 18px;
      box-shadow: none;

      &.hover-bg-user:hover {
        background-color: white;
      }

      &:after {
        margin-left: 6px;
      }
    }

    :deep(.notification-dropdown-menu) {
      width: 400px;
      max-height: 1000px;
      padding: 10px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      border-radius: 8px;

      .dropdown-header {
        border-bottom: 1px solid #e9ecef;
        background-color: #f8f9fa;
        margin: -10px -10px 10px -10px;
        padding: 10px;
      }

      .notification-item {
        width: calc(100% - 20px);
        margin: 5px;
        padding: 5px;
        border-radius: 8px;
        cursor: pointer;

        &:hover {
          background-color: #f8f9fa;
        }
        .dropdown-item {
          padding: 2px !important;
        }
        .notification-content {
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: normal;
          word-wrap: break-word;
          max-width: 300px;
          position: relative;
          padding-left: 5px;
        }

        .notification-time {
          font-size: 11px;
          color: #6c757d;
          margin-top: 4px;
          white-space: nowrap;
        }
      }
    }

    .notification-avatar {
      position: relative;
      .notification-type-icon {
        position: absolute;
        bottom: -4px;
        right: -4px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        color: white;

        &.invite_course {
          background-color: #28a745; /* Màu xanh lá */
        }

        &.message {
          background-color: #17a2b8; /* Màu xanh dương nhạt */
        }

        &.system {
          background-color: #6c757d; /* Màu xám */
        }
      }
    }

    .notification-bell {
      display: flex;
      align-items: center;
      justify-content: center;

      .notification-badge {
        font-size: 10px;
        min-width: 18px;
        height: 18px;
        line-height: 18px;
        padding: 0;
      }
    }

    .notification-tabs {
      border-bottom: 1px solid #e9ecef;

      .tab-item {
        font-size: 12px;
        font-weight: 600;
        color: #6c757d;
        padding: 8px 10px;
        border-radius: 14px;
        cursor: pointer;
        transition:
          background-color 0.2s,
          color 0.2s;

        &:hover {
          background-color: #f8f9fa;
          color: #0056b3;
        }

        &:focus {
          outline: none;
          background-color: #e9ecef;
          color: #0056b3;
        }

        &.active {
          color: #0056b3;
          background-color: #e9ecef;
        }
      }
    }
  }

  .notification-red-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #0056b3;
  }
</style>
