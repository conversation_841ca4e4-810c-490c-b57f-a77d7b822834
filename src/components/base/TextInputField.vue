<template>
  <BFormInput
    v-model="inputValue"
    :placeholder="placeHolder"
    :class="usingClasses"
    type="text"
    @input="$emit('updated', inputValue)"
  />
</template>

<script lang="ts" setup>
  const props = defineProps({
    classes: {
      type: String,
      required: false,
      default: 'form-control'
    },
    title: {
      type: String,
      required: false,
      default: ''
    }
  });

  const usingClasses = computed(() => {
    if (props.classes) return props.classes;
    else return 'form-control';
  });

  const placeHolder = computed(() => {
    if (props.title) return props.title;
    else return 'Search...';
  });

  const inputValue = ref(null);

  const emit = defineEmits(['updated']);

  defineExpose({
    clear
  });

  function clear() {
    inputValue.value = null;
    emit('updated', null);
  }
</script>
