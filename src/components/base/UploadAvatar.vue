<template>
  <div class="position-relative d-inline-block">
    <div class="position-absolute bottom-0 end-0" @click="handleChangeBanner">
      <div class="avatar-xs">
        <div class="avatar-title bg-light border rounded-circle text-muted cursor-pointer shadow font-size-16">
          <i class="mdi mdi-camera-outline"></i>
        </div>
      </div>
    </div>

    <div class="avatar-lg">
      <div class="avatar-title bg-light rounded-circle">
        <img v-if="url" :src="url" id="projectlogo-img" class="avatar-md rounded-circle" />
        <img
          v-else
          src="@/assets/images/users/user-dummy-img.png"
          id="projectlogo-img"
          class="avatar-md rounded-circle"
        />
      </div>
    </div>
  </div>

  <ImageUploader
    v-model:url="url"
    hide-drop-zone
    ref="avatarUploader"
    show-cropper
    :default-aspect-ratio="ASPECT_RATIO_ENUMS['1:1']"
    :site="site"
    :stencil-component="CircleStencil"
    @upload-success="uploadSuccess"
  ></ImageUploader>
</template>

<script lang="ts" setup>
  import { CircleStencil } from 'vue-advanced-cropper';

  import { ASPECT_RATIO_ENUMS } from '@/utils/constant';

  import ImageUploader from './ImageUploader.vue';

  const emits = defineEmits(['uploadSuccess']);
  defineProps({
    site: {
      type: String,
      required: true
    }
  });

  const url = defineModel<string>('url', {
    default: ''
  });

  const avatarUploader = ref<InstanceType<typeof ImageUploader> | null>(null);

  const handleChangeBanner = () => {
    if (avatarUploader.value) {
      avatarUploader.value.triggerFileInput();
    }
  };

  const uploadSuccess = () => {
    emits('uploadSuccess');
  };
</script>
