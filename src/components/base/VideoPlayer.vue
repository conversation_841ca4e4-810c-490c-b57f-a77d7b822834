<template>
  <div class="video-container">
    <!-- Loading UI -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <p class="loading-text">Loading video...</p>
      </div>
    </div>

    <!-- Error UI -->
    <div v-else-if="hasError" class="error-overlay">
      <div class="error-content">
        <div class="error-icon">⚠️</div>
        <p class="error-text">Failed to load video</p>
        <button @click="retryLoad" class="retry-button">Retry</button>
      </div>
    </div>

    <!-- Video Player - Always show when data is available -->
    <div v-else-if="videoPlaybackData" class="video-wrapper">
      <div v-if="isYouTube" class="video-wrapper-inner">
        <div
          ref="mediaRef"
          data-plyr-provider="youtube"
          :poster="thumbnail"
          :data-plyr-embed-id="videoPlaybackData.playbackId"
        />
      </div>
      <div v-else class="video-wrapper-inner">
        <video
          ref="mediaRef"
          controls
          crossorigin="anonymous"
          :poster="thumbnail"
          playsinline
          webkit-playsinline
          preload="metadata"
        />
      </div>
    </div>

    <!-- Resume Modal - Overlay on top of video -->
    <div v-if="showResumeModal" class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">Tiếp tục xem</h3>
        </div>
        <div class="modal-body">
          <p class="modal-text">Lần trước bạn đang dừng video ở đoạn {{ formatTime(currentPosition || 0) }}</p>
        </div>
        <div class="modal-footer">
          <button @click="resumeFromPosition" class="modal-button modal-button-primary">Xem tiếp</button>
          <button @click="startFromBeginning" class="modal-button modal-button-secondary">Xem từ đầu</button>
        </div>
      </div>
    </div>

    <!-- Purchase Modal - Overlay on top of video -->
    <div v-if="showPurchaseModal" class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title"><i class="mdi mdi-lock-open-remove-outline"></i> Mở khoá toàn bộ bài học</h3>
        </div>
        <div class="modal-body">
          <p class="modal-text">
            Bạn đã xem hết phần preview. Để tiếp tục xem toàn bộ nội dung, vui lòng mua khóa học.
          </p>
        </div>
        <div class="modal-footer">
          <button @click="handlePurchaseClick" class="modal-button modal-button-primary">Mua ngay</button>
          <button @click="closePurchaseModal" class="modal-button modal-button-secondary">Đóng</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';
  import Plyr from 'plyr';
  import 'plyr/dist/plyr.css';
  import Hls from 'hls.js';
  import { videoPlayBack } from '@/services/public/repositories/video';
  import type { VideoPlayback } from '@/utils/interface/public/video';
  import { useSiteToken } from '@/composable/useSiteTokens';

  // =================== PROPS & EMITS ===================
  const props = defineProps({
    videoId: String,
    thumbnail: String,
    currentPosition: Number,
    blockAccess: Boolean
  });

  const emits = defineEmits(['updateWatchProgress', 'videoEnded', 'purchaseCourse']);

  // =================== REACTIVE STATE ===================
  const mediaRef = ref<HTMLVideoElement | HTMLElement | null>(null);
  const videoPlaybackData = ref<VideoPlayback | null>(null);
  const isLoading = ref(false);
  const hasError = ref(false);
  const showResumeModal = ref(false);
  const showPurchaseModal = ref(false);
  const progressInterval = ref<number | null>(null);

  // =================== REFS & INSTANCES ===================
  let player: Plyr | null = null;
  let hls: Hls | null = null;
  let playerInitTimeout: ReturnType<typeof setTimeout> | null = null;
  let iosSeekAttempted = false;

  // =================== COMPUTED PROPERTIES ===================
  const route = useRoute();
  const site = computed(() => route.meta.guard || 'user');

  const isYouTube = computed(() => videoPlaybackData.value?.isFree);

  const isRestrictedDevice = computed(() => {
    const userAgent = navigator.userAgent.toLowerCase();
    return (
      /iphone|ipad|ipod/.test(userAgent) ||
      (/safari/.test(userAgent) && !/chrome/.test(userAgent)) ||
      /smart-tv|tizen|webos/.test(userAgent)
    );
  });

  const currentVideoUrl = computed(() => {
    if (!videoPlaybackData.value) return '';

    const { isFree, playbackId } = videoPlaybackData.value;
    if (isFree) {
      return `https://www.youtube.com/watch?v=${playbackId}`;
    }

    const baseUrl = import.meta.env.VITE_APP_BASE_API;
    if (isRestrictedDevice.value) {
      const { siteToken } = useSiteToken(site.value as 'user' | 'admin');
      return `${baseUrl}/rest/${site.value}/stream/${playbackId}.m3u8?token=${siteToken}?preview=${props.blockAccess}`;
    }

    return `${baseUrl}/rest/${site.value}/stream/${playbackId}.m3u8?preview=${props.blockAccess}`;
  });

  const currentTime = computed(() => player?.currentTime || 0);

  // =================== CONSTANTS ===================
  const PLYR_CONTROLS: Plyr.Options['controls'] = [
    'play-large',
    'play',
    'progress',
    'current-time',
    'duration',
    'mute',
    'volume',
    'captions',
    'settings',
    'airplay',
    'fullscreen'
  ];

  const PROGRESS_UPDATE_INTERVAL = 30000;
  const PLAYER_INIT_DELAY = 100;
  const PLAYER_SETUP_RETRY_DELAY = 500;
  const PLAYER_READY_TIMEOUT = 15000;

  // =================== UTILITY FUNCTIONS ===================
  function formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  // =================== CLEANUP FUNCTIONS ===================
  function cleanup() {
    clearProgressInterval();
    clearPlayerInitTimeout();
    destroyPlayer();
    destroyHls();
    iosSeekAttempted = false;
  }

  function clearProgressInterval() {
    if (progressInterval.value) {
      clearInterval(progressInterval.value);
      progressInterval.value = null;
    }
  }

  function clearPlayerInitTimeout() {
    if (playerInitTimeout) {
      clearTimeout(playerInitTimeout);
      playerInitTimeout = null;
    }
  }

  function destroyPlayer() {
    if (player) {
      player.destroy();
      player = null;
    }
  }

  function destroyHls() {
    if (hls) {
      hls.destroy();
      hls = null;
    }
  }

  // =================== RESUME MODAL FUNCTIONS ===================
  function checkResumeModal() {
    if (!props.currentPosition || props.currentPosition <= 0) {
      return;
    }

    showResumeModal.value = true;
  }

  function resumeFromPosition() {
    showResumeModal.value = false;
    player?.play();
  }

  function startFromBeginning() {
    showResumeModal.value = false;
    if (player) {
      player.currentTime = 0;
      player.play();
    }
  }

  // =================== PROGRESS TRACKING ===================
  function startProgressTracking() {
    clearProgressInterval();
    progressInterval.value = setInterval(() => {
      emits('updateWatchProgress', props.videoId, Math.round(currentTime.value));
    }, PROGRESS_UPDATE_INTERVAL);
  }

  function stopProgressTracking() {
    clearProgressInterval();
  }

  // =================== VIDEO PLAYBACK API ===================
  async function fetchVideoPlayback(videoId: string) {
    isLoading.value = true;
    hasError.value = false;

    try {
      const { data } = await videoPlayBack(videoId);
      videoPlaybackData.value = data.videoPlayback;
      await nextTick();

      playerInitTimeout = setTimeout(() => {
        setupPlayer();
      }, PLAYER_INIT_DELAY);
    } catch (err) {
      console.error('Error fetching video playback:', err);
      hasError.value = true;
    } finally {
      isLoading.value = false;
    }
  }

  function retryLoad() {
    if (props.videoId) {
      fetchVideoPlayback(props.videoId);
    }
  }

  // =================== PLAYER SETUP ===================
  async function setupPlayer() {
    const el = mediaRef.value;
    if (!el || !videoPlaybackData.value) {
      retryPlayerSetup();
      return;
    }

    cleanup();

    try {
      if (isYouTube.value) {
        await initPlayer(el as HTMLElement);
      } else {
        await setupHlsPlayer(el as HTMLVideoElement);
      }
    } catch (err) {
      console.error('Error setting up player:', err);
      hasError.value = true;
    }
  }

  function retryPlayerSetup() {
    playerInitTimeout = setTimeout(() => {
      if (mediaRef.value && videoPlaybackData.value) {
        setupPlayer();
      } else {
        console.error('Failed to get media element');
        hasError.value = true;
      }
    }, PLAYER_SETUP_RETRY_DELAY);
  }

  // =================== HLS PLAYER SETUP ===================
  async function setupHlsPlayer(videoEl: HTMLVideoElement) {
    const url = currentVideoUrl.value;

    if (videoEl.canPlayType('application/vnd.apple.mpegurl')) {
      videoEl.src = url;
      await initPlayer(videoEl);
    } else if (Hls.isSupported()) {
      await setupHlsInstance(videoEl, url);
    } else {
      console.error('HLS is not supported in this browser');
      hasError.value = true;
    }
  }

  async function setupHlsInstance(videoEl: HTMLVideoElement, url: string) {
    const { siteToken } = useSiteToken(site.value as 'user' | 'admin');
    const startPosition = props.currentPosition && props.currentPosition > 0 ? props.currentPosition : 0;

    hls = new Hls({
      xhrSetup: xhr => {
        xhr.withCredentials = false;
        xhr.setRequestHeader('Education-Authorization', `Bearer ${siteToken}`);
      },
      enableWorker: true,
      lowLatencyMode: false,
      maxBufferLength: 60,
      maxMaxBufferLength: 600,
      startPosition: startPosition
    });

    hls.loadSource(url);
    hls.attachMedia(videoEl);

    setupHlsEventHandlers(videoEl);
  }

  function setupHlsEventHandlers(videoEl: HTMLVideoElement) {
    if (!hls) return;

    hls.on(Hls.Events.MANIFEST_PARSED, async () => {
      await initPlayer(videoEl);
    });

    hls.on(Hls.Events.ERROR, handleHlsError);
  }

  function handleHlsError(_event: any, data: any) {
    console.error('HLS Error:', data);
    if (!data.fatal) return;

    switch (data.type) {
      case Hls.ErrorTypes.NETWORK_ERROR:
        console.log('Fatal network error encountered, try to recover');
        hls?.startLoad();
        break;
      case Hls.ErrorTypes.MEDIA_ERROR:
        console.log('Fatal media error encountered, try to recover');
        hls?.recoverMediaError();
        break;
      default:
        hasError.value = true;
        hls?.destroy();
        break;
    }
  }

  // =================== PLYR PLAYER INITIALIZATION ===================
  async function initPlayer(target: HTMLElement | HTMLVideoElement): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const options = createPlayerOptions();
        player = new Plyr(target, options);

        let playerReady = false;
        let hasSetInitialPosition = false;

        setupPlayerEventHandlers(resolve, playerReady, hasSetInitialPosition);
        setupPlayerTimeout(resolve, playerReady);
      } catch (e) {
        reject(e);
      }
    });
  }

  function createPlayerOptions(): Plyr.Options {
    const options: Plyr.Options = {
      controls: PLYR_CONTROLS,
      settings: ['quality', 'speed', 'loop'],
      captions: { active: true, update: true },
      autoplay: false,
      muted: false,
      hideControls: false,
      fullscreen: {
        enabled: true,
        fallback: true,
        iosNative: true
      }
    };

    if (isYouTube.value) {
      options.youtube = {
        modestbranding: 1,
        rel: 0,
        disablekb: 1,
        iv_load_policy: 3,
        playsinline: 0,
        fs: 1,
        controls: 0,
        showinfo: 0,
        cc_load_policy: 0,
        enablejsapi: 1,
        origin: window.location.origin,
        suggestedQuality: 'hd1080',
        vq: 'hd1080'
      };
    }

    return options;
  }

  function setupPlayerEventHandlers(resolve: () => void, playerReady: boolean, hasSetInitialPosition: boolean) {
    if (!player) return;

    player.on('play', startProgressTracking);
    player.on('pause', stopProgressTracking);
    player.on('ended', () => {
      emits('videoEnded');
      stopProgressTracking();

      if (props.blockAccess) {
        showPurchaseModal.value = true;
      }
    });

    player.on('ready', () => {
      moveProgressRow();
      playerReady = true;

      setInitialPosition(hasSetInitialPosition);
      checkResumeModal();
      resolve();
    });

    player.on('canplay', () => {
      if (!playerReady) {
        playerReady = true;
        resolve();
      }
    });

    if (isRestrictedDevice.value) {
      player.on('loadedmetadata', () => {
        if (props.currentPosition && props.currentPosition > 0 && !iosSeekAttempted) {
          setTimeout(() => {
            attemptIOSSeek();
          }, 0);
        }
      });

      player.on('loadeddata', () => {
        if (props.currentPosition && props.currentPosition > 0 && !iosSeekAttempted) {
          setTimeout(() => {
            attemptIOSSeek();
          }, 0);
        }
      });

      player.on('canplaythrough', () => {
        if (props.currentPosition && props.currentPosition > 0 && !iosSeekAttempted) {
          setTimeout(() => {
            attemptIOSSeek();
          }, 0);
        }
      });
    }
  }

  function attemptIOSSeek() {
    if (iosSeekAttempted || !player || !props.currentPosition) return;

    if (player.duration && player.duration > props.currentPosition) {
      console.log(`iOS: Attempting to seek to ${props.currentPosition}s`);
      iosSeekAttempted = true;
      player.currentTime = props.currentPosition;

      // Verify the seek worked
      setTimeout(() => {
        if (player && Math.abs(player.currentTime - (props.currentPosition || 0)) > 2) {
          console.log(
            `iOS: Seek verification failed, retrying. Current: ${player.currentTime}, Target: ${props.currentPosition}`
          );
          player.currentTime = props.currentPosition || 0;
        } else {
          console.log(`iOS: Seek successful to ${player?.currentTime}s`);
        }
      }, 300);
    }
  }

  function setInitialPosition(hasSetInitialPosition: boolean) {
    if (props.currentPosition && props.currentPosition > 0 && !hasSetInitialPosition) {
      if (player && player.duration > props.currentPosition) {
        player.currentTime = props.currentPosition;
        hasSetInitialPosition = true;
      }
    }
  }

  function setupPlayerTimeout(resolve: () => void, playerReady: boolean) {
    setTimeout(() => {
      if (!playerReady) {
        playerReady = true;
        resolve();
      }
    }, PLAYER_READY_TIMEOUT);
  }

  // =================== UI CONTROLS CUSTOMIZATION ===================
  function moveProgressRow() {
    if (!player) return;

    const controlsEl = player.elements.controls as HTMLElement;
    if (!controlsEl) return;

    const progressEl = controlsEl.querySelector('.plyr__progress') as HTMLElement;
    if (!progressEl) return;

    repositionProgressBar(controlsEl, progressEl);
    styleControlsLayout(controlsEl, progressEl);
  }

  function repositionProgressBar(controlsEl: HTMLElement, progressEl: HTMLElement) {
    if (controlsEl.firstElementChild !== progressEl) {
      controlsEl.insertBefore(progressEl, controlsEl.firstElementChild);
    }
    progressEl.style.flex = '1 1 100%';
  }

  function styleControlsLayout(controlsEl: HTMLElement, progressEl: HTMLElement) {
    controlsEl.style.flexWrap = 'nowrap';
    controlsEl.style.flexDirection = 'column';

    const otherControls = Array.from(controlsEl.children).filter(child => child !== progressEl);
    if (otherControls.length > 0 && !controlsEl.querySelector('.plyr-controls-row')) {
      createControlsRow(controlsEl, otherControls);
    }
  }

  function createControlsRow(controlsEl: HTMLElement, otherControls: Element[]) {
    const controlsRow = document.createElement('div');
    controlsRow.className = 'plyr-controls-row';
    controlsRow.style.display = 'flex';
    controlsRow.style.alignItems = 'center';
    controlsRow.style.justifyContent = 'space-between';
    controlsRow.style.flexWrap = 'nowrap';

    otherControls.forEach(control => controlsRow.appendChild(control));
    controlsEl.appendChild(controlsRow);
  }

  // =================== PURCHASE MODAL FUNCTIONS ===================
  function handlePurchaseClick() {
    emits('purchaseCourse');
  }

  function closePurchaseModal() {
    showPurchaseModal.value = false;
  }

  // =================== PUBLIC METHODS ===================
  async function initializeVideo() {
    if (props.videoId) {
      await fetchVideoPlayback(props.videoId);
    }
  }

  function pause() {
    player?.pause();
  }

  // =================== LIFECYCLE HOOKS ===================
  onMounted(() => {
    initializeVideo();
  });

  onBeforeUnmount(() => {
    cleanup();
  });

  // =================== COMPONENT EXPOSE ===================
  defineExpose({
    videoId: props.videoId,
    getCurrentTime: () => currentTime.value,
    pause
  });
</script>

<style scoped>
  .video-container {
    position: relative;
    width: 100%;
    aspect-ratio: 16 / 9;
  }

  .video-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .video-wrapper-inner,
  .video-wrapper-inner video,
  .video-wrapper-inner div {
    position: absolute;
    top: 0;
    left: 0;
    object-fit: contain;
    width: 100%;
    height: 100%;
  }

  /* Make YouTube embed mostly unclickable but allow center clicks */
  .video-wrapper :deep(.plyr--youtube) {
    pointer-events: none;
  }

  .video-wrapper :deep(.plyr--youtube iframe) {
    pointer-events: none;
  }

  /* Re-enable pointer events for Plyr controls */
  .video-wrapper :deep(.plyr__controls) {
    pointer-events: auto;
  }

  .video-wrapper :deep(.plyr__control) {
    pointer-events: auto;
  }

  /* Allow clicking in the center area for play/pause */
  .video-wrapper :deep(.plyr__poster),
  .video-wrapper :deep(.plyr--playing .plyr__video-wrapper),
  .video-wrapper :deep(.plyr--paused .plyr__video-wrapper) {
    pointer-events: auto;
  }

  /* Specifically enable the play button overlay */
  .video-wrapper :deep(.plyr__control--overlaid) {
    pointer-events: auto;
  }

  /* Modal Styles - Now overlays on video */
  .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 16px;
    box-sizing: border-box;
  }

  .modal-content {
    background: white;
    border-radius: 12px;
    padding: 0;
    max-width: min(400px, 100%);
    width: 100%;
    max-height: 90%;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .modal-header {
    padding: 16px 20px 12px;
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0;
  }

  .modal-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #111827;
  }

  .modal-body {
    padding: 12px 20px;
    flex: 1;
    overflow-y: auto;
  }

  .modal-text {
    margin: 0;
    color: #374151;
    font-size: 14px;
    line-height: 1.5;
  }

  .modal-footer {
    padding: 16px 20px;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    flex-shrink: 0;
    flex-wrap: wrap;
  }

  .modal-button {
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    min-width: 0;
    white-space: nowrap;
    flex: 0 0 auto;
  }

  .modal-button-primary {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
  }

  .modal-button-primary:hover {
    background: #2563eb;
    border-color: #2563eb;
  }

  .modal-button-secondary {
    background: white;
    color: #374151;
    border-color: #d1d5db;
  }

  .modal-button-secondary:hover {
    background: #f9fafb;
    border-color: #9ca3af;
  }

  /* Additional responsive styles */
  @media (max-width: 480px) {
    .modal-overlay {
      padding: 8px;
    }

    .modal-header {
      padding: 10px 12px 6px;
    }

    .modal-title {
      font-size: 14px;
    }

    .modal-body {
      padding: 6px 12px;
    }

    .modal-text {
      font-size: 12px;
    }

    .modal-footer {
      padding: 6px 12px 8px;
      gap: 6px;
    }

    .modal-button {
      padding: 6px 10px;
      font-size: 12px;
    }
  }

  @media (max-width: 320px) {
    .modal-footer {
      gap: 4px;
    }

    .modal-button {
      padding: 5px 8px;
      font-size: 11px;
    }
  }

  /* Loading UI Styles */
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10;
  }

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    color: white;
    font-size: 16px;
    margin: 0;
    font-weight: 500;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /* Error UI Styles */
  .error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10;
  }

  .error-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    text-align: center;
    padding: 24px;
  }

  .error-icon {
    font-size: 48px;
  }

  .error-text {
    color: white;
    font-size: 16px;
    margin: 0;
    font-weight: 500;
  }

  .retry-button {
    padding: 10px 20px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .retry-button:hover {
    background: #0056b3;
  }

  .retry-button:active {
    transform: translateY(1px);
  }

  /* Scoped override under .video-wrapper */
  .video-wrapper :deep(.plyr__controls) {
    display: flex !important;
    flex-wrap: nowrap !important;
    flex-direction: column !important;
    align-items: stretch !important;
    padding: 5px;
  }
</style>
