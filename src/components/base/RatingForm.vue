<template>
  <div class="rating lh-1">
    <div v-for="i in max" :key="i" class="star-container">
      <span
        v-if="halfStar"
        class="star half"
        :class="{ filled: rating >= i - 0.5 }"
        @click="!readonly && (rating = i - 0.5)"
      >
        ★
      </span>

      <span class="star full" :class="{ filled: rating >= i }" @click="!readonly && (rating = i)"> ★ </span>
    </div>
    <span v-if="!hiddenValue" class="value">({{ rating }})</span>
  </div>
</template>

<script setup lang="ts">
  defineProps({
    max: { type: Number, default: 5 },
    readonly: { type: Boolean, default: false },
    hiddenValue: { type: Boolean, default: false },
    halfStar: { type: Boolean, default: false }
  });

  const rating = defineModel<number>('modelValue', {
    default: 5
  });
</script>

<style scoped>
  .rating {
    display: flex;
    align-items: center;
  }

  .star-container {
    position: relative;
    width: 1.5rem;
    height: 1.5rem;
    display: inline-block;
    font-size: 1.5rem;
    line-height: 1;
    cursor: pointer;
  }

  .star {
    position: absolute;
    top: 0;
    height: 100%;
    overflow: hidden;
    color: #ccc;
    transition: color 0.2s;
  }

  .star.half {
    width: 50%;
    left: 0;
    z-index: 2;
  }

  .star.full {
    width: 100%;
    left: 0;
    z-index: 1;
  }

  .star.filled {
    color: #f5b301;
  }

  .value {
    text-align: center;
    /* margin-left: 0.5rem; */
    font-size: 1rem;
    color: #555;
    width: 3rem;
  }
</style>
