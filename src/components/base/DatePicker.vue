<template>
  <date-picker :lang="locale" v-bind="$attrs"></date-picker>
</template>

<script setup lang="ts">
  import DatePicker from 'vue-datepicker-next';

  import 'vue-datepicker-next/locale/vi.es';

  const locale = computed(() => {
    try {
      const cookieLang = document.cookie
        .split('; ')
        .find(row => row.startsWith('language='))
        ?.split('=')[1];

      return cookieLang === 'vi' ? 'vi' : 'en';
    } catch (e) {
      return 'vi';
    }
  });
</script>
