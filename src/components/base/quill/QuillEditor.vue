<template>
  <div>
    <div ref="quillEditorRef" :id="editorId"></div>

    <ImageUploader
      v-model:url="imageUrl"
      hide-drop-zone
      ref="fileInputRef"
      require-depended-ref
      show-cropper
      :site="site"
      @upload-error="onUploadError"
      @upload-start="onUploadStart"
      @upload-success="onUploadSuccess"
    ></ImageUploader>
  </div>
</template>

<script lang="ts" setup>
  // https://quilljs.com/docs/configuration
  import Quill from 'quill';
  import type { QuillOptions, Range } from 'quill';

  import i18n from '@/plugin/i18n';

  import Toast from '@/utils/toast';
  import { QUILL_EMBED_TYPE_ENUMS, QUILL_EMITTER_SOURCE_ENUMS, QUILL_THEME_ENUMS } from '@/utils/constant';

  import ImageUploader from '../ImageUploader.vue';

  const emits = defineEmits(['update:modelValue']);
  const props = defineProps({
    modelValue: {
      type: String,
      required: true
    },
    editorId: {
      type: String,
      default: 'quill-editor-' + Math.random().toString(36).slice(2)
    },
    modules: {
      type: Object as PropType<QuillOptions['modules']>,
      default: () => ({
        toolbar: [
          [{ header: [1, 2, 3, 4, 5, 6, false] }],
          ['bold', 'italic', 'underline', 'strike', 'blockquote'],
          [{ color: [] }, { background: [] }],
          [{ list: 'ordered' }, { list: 'bullet' }, { indent: '-1' }, { indent: '+1' }, { align: [] }],
          ['link', 'image']
        ]
      })
    },
    placeholder: {
      type: String,
      required: false
    },
    readOnly: {
      type: Boolean,
      required: false
    },
    theme: {
      type: String,
      default: QUILL_THEME_ENUMS.SNOW
    },
    site: {
      type: String,
      required: true
    }
  });

  const cursorIndex = ref<number>(0);
  const fileInputRef = ref<InstanceType<typeof ImageUploader> | null>(null);
  const imageUrl = ref<string>('');
  const quillEditorRef = ref<HTMLElement>();
  const quillInstance = ref<Quill | null>(null);
  const selectionRange = ref<Range | null>(null);

  const options = computed<QuillOptions>(() => {
    const readOnlyModules = { toolbar: null };
    const normalModules = {
      ...props.modules,
      toolbar: {
        container: props.modules?.toolbar,
        handlers: {
          image: imageHandler
        }
      }
    };
    const moduleOptions = props.readOnly ? readOnlyModules : normalModules;

    return {
      modules: moduleOptions,
      placeholder: props.placeholder,
      readOnly: props.readOnly,
      theme: props.theme
    };
  });

  const defaultEditorMinHeight = computed(() => {
    return props.readOnly ? 0 : 350;
  });

  const imageHandler = () => {
    if (fileInputRef.value) {
      fileInputRef.value.triggerFileInput();
    }
  };

  const initializeQuill = () => {
    if (!quillEditorRef.value) return;

    // https://github.com/slab/quill/issues/4375#issuecomment-2504200748
    quillInstance.value = markRaw(new Quill(quillEditorRef.value, options.value));

    if (!fileInputRef.value) return;

    fileInputRef.value.dependedRef = quillInstance.value;

    if (props.modelValue) {
      quillInstance.value.root.innerHTML = props.modelValue;
    }

    quillInstance.value.on('text-change', () => {
      const html = quillInstance.value?.root.innerHTML || '';
      const isEmpty = quillInstance.value?.getText().trim().length === 0;

      emits('update:modelValue', isEmpty ? '' : html);
    });
  };

  const onUploadStart = () => {
    if (!quillInstance.value) return;

    selectionRange.value = quillInstance.value.getSelection(false);
    cursorIndex.value = selectionRange.value ? selectionRange.value.index : 0;

    quillInstance.value.insertText(
      cursorIndex.value,
      i18n.global.t('quill_editor.image_uploading'),
      QUILL_EMITTER_SOURCE_ENUMS.USER
    );
  };

  const onUploadSuccess = () => {
    if (!quillInstance.value) return;

    quillInstance.value.deleteText(cursorIndex.value, i18n.global.t('quill_editor.image_uploading').length);
    quillInstance.value.insertEmbed(
      cursorIndex.value,
      QUILL_EMBED_TYPE_ENUMS.IMAGE,
      imageUrl.value,
      QUILL_EMITTER_SOURCE_ENUMS.USER
    );
    quillInstance.value.setSelection(cursorIndex.value + 1);
  };

  const onUploadError = () => {
    if (!quillInstance.value) return;

    quillInstance.value.deleteText(cursorIndex.value, i18n.global.t('quill_editor.image_uploading').length);

    Toast.error({
      title: i18n.global.t('quill_editor.image_upload_failed')
    });
  };

  watch(
    () => props.modelValue,
    newValue => {
      if (quillInstance.value && newValue !== quillInstance.value.root.innerHTML) {
        quillInstance.value.root.innerHTML = newValue || '';
      }
    }
  );

  watch(
    () => props.readOnly,
    newValue => {
      if (quillInstance.value) {
        quillInstance.value.enable(!newValue);
      }
    }
  );

  onMounted(async () => {
    await nextTick();
    initializeQuill();
  });

  onBeforeUnmount(() => {
    if (quillInstance.value) {
      quillInstance.value.off('text-change');
      quillInstance.value = null;
    }
  });
</script>

<style lang="scss" scoped>
  :deep(.ql-container) {
    &.ql-snow {
      .ql-editor {
        min-height: v-bind('defaultEditorMinHeight + "px"');
      }
      &.ql-disabled {
        border: none;
        .ql-blank {
          &.ql-editor {
            padding: 0;
          }
        }
      }
    }
  }
</style>
