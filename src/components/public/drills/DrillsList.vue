<template>
  <Loader :loading="loading">
    <BRow class="drill-list g-4">
      <BCol xl="4" md="6" v-for="drill in drills" :key="drill.id">
        <BaseDrillCard
          :drill="drill"
          :site="SITES.USER"
          @on-skill-click="onSkillClick"
          @on-teacher-click="onViewTeacherClick(drill)"
          @on-view-detail-click="onViewDetailDrill(drill)"
        />
      </BCol>
    </BRow>

    <Pagination :metadata="metadata" @change="$emit('fetch-list', $event)" site="user"></Pagination>
  </Loader>
</template>

<script lang="ts" setup>
  import BaseDrillCard from '@/components/base/drill/BaseDrillCard.vue';
  import Pagination from '@/components/base/Pagination.vue';

  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { MetaDataInterface } from '@/utils/interface/common';
  import { OWNER_TYPE_ENUMS, SITES } from '@/utils/constant';
  import { SkillInterface } from '@/utils/interface/skill';

  const router = useRouter();

  const emits = defineEmits(['fetch-list', 'edit', 'delete', 'on-skill-click']);
  defineProps({
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    drills: {
      type: Array as PropType<DrillInterface[]>,
      required: true
    }
  });

  const onViewDetailDrill = (drill: DrillInterface) => {
    if (!drill.slug) {
      return;
    }
    router.push(`/drills/${drill.slug}`);
  };

  const onViewTeacherClick = (drill: DrillInterface) => {
    if (!drill || drill.ownerType !== OWNER_TYPE_ENUMS.TEACHER) {
      return;
    }
    router.push(`/teachers/${drill.owner.slug}`);
  };

  const onSkillClick = (skill: SkillInterface) => {
    emits('on-skill-click', skill);
  };
</script>

<style lang="scss" scoped>
  .action-btn-group {
    .btn {
      &:hover {
        color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity));
      }
    }
  }
</style>
