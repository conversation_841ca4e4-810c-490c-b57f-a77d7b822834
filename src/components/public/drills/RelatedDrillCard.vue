<template>
  <BRow class="related-course-card px-2 px-lg-0 pt-2 pt-lg-5">
    <h4>{{ $t('public.drills.related_drills') }}</h4>

    <BaseCarousel :items="relatedDrills" :auto-height="false">
      <template #default="{ item }">
        <FadeInUp class="drill-list g-4 w-100">
          <DrillCard :drill="item" />
        </FadeInUp>
      </template>
    </BaseCarousel>
  </BRow>
</template>

<script lang="ts" setup>
  import BaseCarousel from '@/components/base/BaseCarousel.vue';
  import DrillCard from './DrillCard.vue';
  defineProps({
    relatedDrills: {
      type: Array,
      required: true
    }
  });
</script>
