<template>
  <section class="hero-section position-relative mb-3 w-100 overflow-hidden">
    <div class="hero-background position-absolute top-0 start-0 w-100 h-100"></div>
    <BContainer>
      <div class="row align-items-center">
        <BCol lg="6">
          <div class="hero-content position-relative z-2 pe-3 text-white">
            <div class="hero-badge d-inline-block text-white fs-6 mb-4 rounded-5 bg-gradient px-3 py-2">
              {{ $t('public.drills.hero.label') }}
            </div>

            <h1 class="hero-title text-white mb-4">
              {{ $t('public.drills.hero.title') }}
            </h1>

            <p class="hero-subtitle mb-4">
              {{ $t('public.drills.hero.description') }}
            </p>

            <div class="hero-stats d-flex gap-4 bg-dark bg-opacity-25 rounded p-3">
              <div class="stat-item text-center flex-fill">
                <div class="stat-number d-flex justify-content-center mb-1">
                  <RollingNumber :target="drillStats.totalDrills > 0 ? drillStats.totalDrills : 0" />
                </div>
                <div class="stat-label text-uppercase">
                  {{ $t('public.drills.title') }}
                </div>
              </div>

              <div class="stat-item text-center flex-fill">
                <div class="stat-number d-flex justify-content-center mb-1">
                  <RollingNumber :target="levelOptions.length" />
                </div>
                <div class="stat-label text-uppercase">
                  {{ $t('public.drills.modal.form.level.label') }}
                </div>
              </div>

              <div class="stat-item text-center flex-fill">
                <div class="stat-number d-flex justify-content-center mb-1">
                  <RollingNumber :target="skillOptions.length" />
                </div>
                <div class="stat-label text-uppercase">
                  {{ $t('public.drills.modal.form.skill.label') }}
                </div>
              </div>
            </div>
          </div>
        </BCol>

        <BCol lg="6">
          <DrillListHeroFilterSection
            v-model:selected-levels="selectedLevels"
            v-model:selected-skills="selectedSkills"
            v-model:teacher-name="teacherName"
            :level-options="levelOptions"
            :skill-options="skillOptions"
            @clear-level-selection="clearLevelSelection"
            @clear-skill-selection="clearSkillSelection"
            @toggle-level="toggleLevel"
            @toggle-search-teacher="toggleSearchTeacher"
            @toggle-skill="toggleSkill"
          ></DrillListHeroFilterSection>
        </BCol>
      </div>
    </BContainer>
    <div class="floating-elements">
      <div class="floating-element ball-1"></div>
      <div class="floating-element ball-2"></div>
      <div class="floating-element ball-3"></div>
      <div class="floating-element cue-1"></div>
      <div class="floating-element rack-1"></div>
    </div>
  </section>
</template>

<script lang="ts" setup>
  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { OptionInterface } from '@/utils/interface/select-options';
  import { DrillStatsInterface } from '@/utils/interface/public/stats';

  import { fetchDrillStats } from '@/services/public/repositories/stats';

  import RollingNumber from '@/components/base/animations/RollingNumber.vue';
  import DrillListHeroFilterSection from './DrillListHeroFilterSection.vue';

  const teacherName = defineModel('teacherName', {
    type: String,
    required: true
  });

  const selectedLevels = defineModel('selectedLevels', {
    type: Array as () => number[],
    required: true
  });

  const selectedSkills = defineModel('selectedSkills', {
    type: Array as () => number[],
    required: true
  });

  const drillStats = ref<DrillStatsInterface>({} as DrillStatsInterface);

  const emits = defineEmits([
    'clear-level-selection',
    'clear-skill-selection',
    'toggle-level',
    'toggle-search-teacher',
    'toggle-skill'
  ]);

  defineProps({
    drills: {
      type: Array as PropType<DrillInterface[]>,
      required: true
    },
    levelOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    skillOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    }
  });

  const toggleSearchTeacher = () => {
    emits('toggle-search-teacher');
  };

  const clearLevelSelection = () => {
    emits('clear-level-selection');
  };

  const clearSkillSelection = () => {
    emits('clear-skill-selection');
  };

  const toggleLevel = (level: number) => {
    emits('toggle-level', level);
  };

  const toggleSkill = (skill: number) => {
    emits('toggle-skill', skill);
  };

  const fetchOverview = async () => {
    const { data } = await fetchDrillStats();
    drillStats.value = data.overviewStats;
  };

  onMounted(() => {
    fetchOverview();
  });
</script>

<style lang="scss" scoped>
  .hero-section {
    padding: 80px 0 100px;

    @media (max-width: 991px) {
      .hero-content {
        padding-right: 0 !important;
        text-align: center;
        margin-bottom: 40px;

        .hero-subtitle {
          max-width: 100%;
          margin-left: auto;
          margin-right: auto;
        }

        .hero-stats {
          justify-content: center;
        }
      }
    }

    @media (max-width: 768px) {
      .hero-content {
        .hero-title {
          font-size: 2.8rem !important;
        }
      }
    }

    @media (max-width: 576px) {
      .hero-content {
        .hero-title {
          font-size: 2.5rem !important;
        }

        .hero-subtitle {
          font-size: 1.2rem;
        }

        .hero-stats {
          gap: 20px !important;
          padding: 15px !important;

          .stat-number {
            font-size: 1.8rem !important;
          }
        }
      }
    }

    .hero-background {
      background: linear-gradient(135deg, #f39c12, #e67e22, #f1b44c);
      background-position: center;
      background-repeat: no-repeat;
    }

    .hero-content {
      .hero-badge {
        background: rgba(241, 180, 76, 0.9);
        font-weight: 600;
        box-shadow: 0 4px 10px rgba(241, 180, 76, 0.3);
        letter-spacing: 0.5px;
        text-transform: uppercase;
      }

      .hero-title {
        font-size: 3.8rem;
        font-weight: 800;
        line-height: 1.1;
        letter-spacing: -0.5px;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .hero-subtitle {
        font-size: 1.4rem;
        color: rgba(255, 255, 255, 0.9);
        max-width: 90%;
        line-height: 1.5;
        font-weight: 300;
      }

      .hero-stats {
        background: rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.1);

        .stat-item {
          .stat-number {
            font-size: 2.2rem;
            font-weight: 700;
            color: #f1b44c;
            text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
          }

          .stat-label {
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.9);
            text-transform: uppercase;
            letter-spacing: 1.5px;
            font-weight: 600;
          }
        }
      }
    }

    .floating-elements {
      position: absolute;
      z-index: 1;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      overflow: hidden;
      .floating-element {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 30px rgba(255, 255, 255, 0.2);
        animation: float 15s infinite ease-in-out;
        &.ball-1 {
          width: 80px;
          height: 80px;
          top: 15%;
          left: 10%;
          animation-delay: 0s;
          background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.05));
        }
        &.ball-2 {
          width: 60px;
          height: 60px;
          top: 60%;
          right: 12%;
          animation-delay: 2s;
          background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.05));
        }
        &.ball-3 {
          width: 40px;
          height: 40px;
          bottom: 20%;
          left: 20%;
          animation-delay: 4s;
          background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.05));
        }
        &.cue-1 {
          width: 200px;
          height: 8px;
          background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.2));
          border-radius: 4px;
          transform: rotate(45deg);
          top: 30%;
          right: 5%;
          animation: float-rotate 12s infinite ease-in-out;
        }
        &.rack-1 {
          width: 120px;
          height: 120px;
          bottom: 30%;
          right: 25%;
          background: none;
          box-shadow: none;
          animation-delay: 3s;
          background-image: url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M60 10L110 95H10L60 10Z' fill='rgba(255, 255, 255, 0.1)' stroke='rgba(255, 255, 255, 0.3)' stroke-width='2'/%3E%3C/svg%3E");
          background-size: contain;
          background-repeat: no-repeat;
          border-radius: 0;
        }
      }
    }
  }
</style>
