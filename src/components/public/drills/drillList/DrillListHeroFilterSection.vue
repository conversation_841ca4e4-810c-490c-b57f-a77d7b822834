<template>
  <div class="filter-container d-flex align-items-center h-100 position-relative z-10">
    <div class="filter-card w-100 shadow-lg rounded-4 border border-0 overflow-hidden">
      <div class="card-body p-4">
        <div class="filter-header mb-4 text-center">
          <h2 class="filter-header-title mb-2 fs-3">
            {{ $t('public.drills.form.title') }}
          </h2>
          <p class="filter-header-subtitle fs-5">
            {{ $t('public.drills.form.description') }}
          </p>
        </div>

        <div class="mb-4">
          <h3 class="filter-title mb-3 position-relative d-inline-block fs-5">
            {{ $t('public.drills.modal.form.teacher.label') }}
          </h3>

          <div class="search-input-wrapper position-relative d-flex align-items-center">
            <BFormInput
              v-model="teacherName"
              class="search-input w-100"
              :placeholder="$t('public.drills.modal.form.teacher.placeholder')"
              @keyup.enter="toggleSearchTeacher"
            />
            <i
              class="mdi mdi-magnify search-icon position-absolute fs-3 cursor-pointer end-0"
              @click="toggleSearchTeacher"
            ></i>
          </div>
        </div>

        <div class="mb-4">
          <h3 class="filter-title mb-3 position-relative d-inline-block fs-5">
            {{ $t('public.drills.modal.form.level.label') }}
          </h3>

          <div class="filter-chips d-flex flex-wrap gap-2 mt-2">
            <div
              class="filter-chip rounded-5 px-3 py-2 cursor-pointer bg-white"
              :class="{ active: selectedLevels.length === 0 }"
              @click="clearLevelSelection"
            >
              {{ $t('public.drills.modal.form.level.all') }}
            </div>

            <div
              v-for="(level, index) in levelOptions"
              class="filter-chip rounded-5 px-3 py-2 cursor-pointer bg-white"
              :class="{ active: selectedLevels.includes(Number(level.value)) }"
              :key="index"
              @click="toggleLevel(Number(level.value))"
            >
              {{ level.description }}
            </div>
          </div>
        </div>

        <div class="mb-4">
          <h3 class="filter-title mb-3 position-relative d-inline-block fs-5">
            {{ $t('public.drills.modal.form.skill.label') }}
          </h3>

          <div class="filter-chips d-flex flex-wrap gap-2 mt-2">
            <div
              class="filter-chip rounded-5 px-3 py-2 cursor-pointer bg-white"
              :class="{ active: selectedSkills.length === 0 }"
              @click="clearSkillSelection"
            >
              {{ $t('public.drills.modal.form.skill.all') }}
            </div>

            <div
              v-for="(skill, index) in skillOptions"
              :key="index"
              class="filter-chip rounded-5 px-3 py-2 cursor-pointer bg-white"
              :class="{ active: selectedSkills.includes(Number(skill.value)) }"
              @click="toggleSkill(Number(skill.value))"
            >
              {{ skill.label }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { OptionInterface } from '@/utils/interface/select-options';

  const emits = defineEmits([
    'clear-level-selection',
    'clear-skill-selection',
    'toggle-level',
    'toggle-search-teacher',
    'toggle-skill'
  ]);

  const teacherName = defineModel('teacherName', {
    type: String,
    required: true
  });

  const selectedLevels = defineModel('selectedLevels', {
    type: Array as () => number[],
    required: true
  });

  const selectedSkills = defineModel('selectedSkills', {
    type: Array as () => number[],
    required: true
  });

  defineProps({
    levelOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    },
    skillOptions: {
      type: Array as () => OptionInterface[],
      default: () => []
    }
  });

  const toggleSearchTeacher = () => {
    emits('toggle-search-teacher');
  };

  const clearLevelSelection = () => {
    emits('clear-level-selection');
  };

  const toggleLevel = (level: number) => {
    emits('toggle-level', level);
  };

  const clearSkillSelection = () => {
    emits('clear-skill-selection');
  };

  const toggleSkill = (skill: number) => {
    emits('toggle-skill', skill);
  };
</script>

<style lang="scss" scoped>
  .filter-container {
    z-index: 50;
    @media (max-width: 991px) {
      margin-top: 30px;

      .filter-card {
        max-width: 600px;
        margin: 0 auto;
      }
    }

    .filter-card {
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
      background-color: rgba(255, 255, 255, 0.85);
      backdrop-filter: blur(10px);

      .filter-header {
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        .filter-header-title {
          font-weight: 700;
          color: #2c3e50;
        }

        .filter-header-subtitle {
          color: #6c757d;
        }
      }

      .filter-title {
        font-weight: 600;
        color: #2c3e50;

        &::after {
          content: '';
          position: absolute;
          bottom: -5px;
          left: 0;
          width: 40px;
          height: 3px;
          background-color: #3498db;
          border-radius: 3px;
        }
      }

      .search-input-wrapper {
        .search-input {
          padding-right: 2.5rem;
        }

        .search-icon {
          color: #e99848;
          padding-inline: 10px;
          z-index: 30;
        }
      }

      .filter-chips {
        .filter-chip {
          border: 1px solid #e9ecef;
          background-color: #ffffff;
          color: #495057;
          font-weight: 500;
          transition: all 0.3s ease;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);

          &:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
          }

          &.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: #fff;
            border-color: transparent;
            box-shadow: 0 4px 10px rgba(52, 152, 219, 0.3);
          }
        }
      }
    }
  }
</style>
