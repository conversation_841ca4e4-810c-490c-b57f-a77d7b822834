<template>
  <BCard class="course-intro-sidebar">
    <template #img>
      <div class="ratio ratio-4x3">
        <img
          :src="course.banner ? course.banner : dummyBanner"
          alt="Course banner"
          class="course-image object-cover rounded-top-2"
        />
      </div>

      <span v-if="joined" class="status">
        <i class="mdi mdi-checkbox-marked-circle-outline text-white"></i>
        {{ $t('public.course.purchased') }}
      </span>
    </template>

    <div class="d-flex flex-column gap-3">
      <div class="d-block d-lg-none">
        <CourseDetailBanner :course="course" />
      </div>

      <PriceDisplay :price="course.price" :salePrice="course.salePrice" show-label></PriceDisplay>

      <div class="d-grid">
        <router-link
          v-if="!joined && !(course.salePrice == 0 || course.salePrice == null)"
          :to="`/preview-courses/${course.slug}`"
          class="mb-2 btn-primary preview-btn text-white py-2 rounded-3 text-center font-size-16"
        >
          {{ $t('public.course.preview') }}
        </router-link>
        <router-link
          v-if="joined"
          :to="`/my-courses/${course.slug}`"
          class="learn-now-btn text-white py-2 rounded-3 text-center font-size-16"
        >
          {{ $t('public.course.learn_now') }}
        </router-link>
        <Button
          v-else-if="isInvited && !joined"
          variant="warning"
          :disabled="isLoading"
          :loading="isLoading"
          size="lg"
          @click="showVerifyModal = true"
        >
          {{ $t('public.course.join_now') }}
        </Button>
        <Button
          v-else
          variant="warning"
          :disabled="isLoading"
          :loading="isLoading"
          size="lg"
          @click="handleJoinCourseAction"
        >
          {{
            course.salePrice == 0 || course.salePrice == null
              ? $t('public.course.join_now')
              : $t('public.course.buy_now')
          }}
        </Button>
      </div>
      
      <div>
        <h6>{{ $t('public.course.intro') }}:</h6>
        <div v-for="(item, index) in courseIncludes" :key="index" class="d-flex align-items-center gap-2 pb-1">
          <template v-if="!(index == 1 && !course.certificate?.isActive)">
            <i class="mdi font-size-20 text-warning" :class="item.icon"></i>
            <span>
              <span v-if="index == 0">{{ course.sectionItemCount }}</span>
              {{ index == 1 ? course.certificate?.title : $t(`public.course.${item.text}`) }}
            </span>
          </template>
        </div>
      </div>

      <BModal v-model="showVerifyModal" title="Xác nhận tham gia khoá học" no-footer>
        <div class="mb-3 text-center">{{ $t('user.user_course.verify_code_title') }}</div>
        <VerifyCodeValidator v-model="verifyCode" :label="$t('user.user_course.verify_code')" name="verifyCode" />

        <div class="d-flex justify-content-end mt-4">
          <Button variant="light" @click="showVerifyModal = false">{{ $t('common.cancel') }}</Button>

          <Button
            :disabled="isDisabled"
            variant="warning"
            class="ms-2 d-flex align-items-center gap-2"
            @click="verifyCodeCourse"
          >
            {{ $t('common.confirm_btn') }}
            <i class="mdi mdi-arrow-right font-size-18"></i>
          </Button>
        </div>
      </BModal>

      <BModal
        v-model="showBuyModal"
        size="lg"
        :title="$t('public.course.confirm_buy_course_modal.title')"
        title-class="font-18 fw-bold"
        lazy
        no-footer
        centered
        hide-header-close
      >
        <template #modal-header="{ close }">
          <div class="d-flex justify-content-between align-items-center w-100">
            <button type="button" class="btn-close" @click="close()"></button>
          </div>
        </template>

        <div class="confirmation-box mb-4">
          <p class="mb-0">
            {{ $t('public.course.confirm_buy_course_modal.package_content') }}
          </p>
        </div>

        <div class="mb-4">
          <div class="row g-3">
            <CoursePackageCard
              v-for="cpk in course.coursePackages"
              :key="cpk.packageDealId"
              :coursePackage="cpk"
              :packageName="getPackageName(cpk)"
              :packageDetail="getPackageDetail(cpk)"
              :selected="isSelected(cpk)"
              :column-size="getResponsiveColumnClass(course.coursePackages?.length || 0)"
              @select-package="selectPackage"
            />
          </div>
        </div>

        <div class="d-flex justify-content-end gap-2">
          <Button variant="outline-secondary" @click="showBuyModal = false">{{ $t('common.cancel') }}</Button>
          <Button variant="primary" @click="handleJoinCourse()">
            {{ $t('common.buy_now') }}
          </Button>
        </div>
      </BModal>
    </div>
  </BCard>
</template>

<script lang="ts" setup>
  import { UserCourseInterface, UserJoinCourseParams } from '@/utils/interface/user/userCourse';

  import Button from '@/components/base/Button.vue';
  import CourseDetailBanner from '@/components/public/course/CourseDetailBanner.vue';
  import PriceDisplay from '@/components/base/PriceDisplay.vue';
  import VerifyCodeValidator from '@/components/base/VerifyCodeValidator.vue';
  import CoursePackageCard from '@/components/teacher/courses/details/CoursePackageCard.vue';

  import dummyBanner from '@/assets/images/dummy_banner.png';
  import { packageDetails } from '@/utils/config-data-info';

  import { joinCourse } from '@/services/public/repositories/course';
  import { useAuthPublicStore } from '@/store/public/auth';
  import { useCoursePackage } from '@/composable/useCoursePackage';

  const props = defineProps({
    course: {
      type: Object as PropType<UserCourseInterface>,
      required: true
    },
    joined: {
      type: Boolean,
      required: false,
      default: false
    },
    isInvited: {
      type: Boolean,
      required: false,
      default: false
    }
  });
  const emits = defineEmits(['joinCourse', 'verifyCode']);
  const authStore = useAuthPublicStore();
  const router = useRouter();

  const accessToken = authStore.accessToken;

  const isLoading = ref<boolean>(false);
  const joined = ref(props.course.joined);
  const showVerifyModal = ref<boolean>(false);
  const verifyCode = ref<string>('');

  const courseIncludes = [
    { icon: 'mdi-play-circle-outline', text: 'section_item' },
    { icon: 'mdi-certificate-outline', text: 'certificate' },
    { icon: 'mdi-tablet-cellphone', text: 'access' },
    { icon: 'mdi-infinity', text: 'timeline' }
  ];

  const courseId = computed(() => {
    return props.course.id?.toString();
  });

  const showBuyModal = ref(false);
  const coursePackageID = ref('');

  const { getPackageName, getPackageDetail, isSelected, selectPackage, getResponsiveColumnClass } = useCoursePackage(
    coursePackageID,
    packageDetails
  );

  const isDisabled = computed(() => verifyCode.value.length < 6);

  const verifyCodeCourse = async () => {
    showVerifyModal.value = false;

    emits('verifyCode', verifyCode.value);
  };

  const handleJoinCourseAction = () => {
    if (accessToken) {
      showBuyModal.value = true;
    } else {
      router.push('/login');
    }
  };

  const handleJoinCourse = async () => {
    if (!courseId.value) return;
    isLoading.value = true;

    try {
      const joinParams: UserJoinCourseParams = {
        id: courseId.value
      };

      joinParams.coursePackageID = String(coursePackageID.value);

      await joinCourse(joinParams);

      showBuyModal.value = false;
      joined.value = true;
      emits('joinCourse');
    } finally {
      isLoading.value = false;
    }
  };

  watch(
    () => props.joined,
    () => {
      if (props.joined) {
        joined.value = props.joined;
      }
    }
  );

  onMounted(() => {
    if (props.course && props.course.coursePackages?.length > 0) {
      selectPackage(props.course.coursePackages[0].id);
    }
  });
</script>

<style scoped>
  .confirmation-box {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    color: #495057;
  }
</style>
