<template>
  <router-link :to="`/courses/${course.slug}`" class="course-card d-block h-100 bg-white rounded-4">
    <BCard body-class="d-flex flex-column justify-content-between gap-3" class="h-100 rounded-4 mb-0 shadow-none">
      <template #img>
        <div class="overflow-hidden rounded-top-4 card-image">
          <div class="ratio ratio-4x3">
            <img
              :src="course.banner ? course.banner : dummyBanner"
              alt="Course banner"
              class="course-banner object-cover rounded-top-4"
            />
          </div>

          <div class="teacher-section d-flex align-items-center">
            <div class="avatar-teacher-section">
              <BAvatar
                :src="course.teacher.imageUrl ? course.teacher.imageUrl : dummyAvatar"
                :alt="course.teacher.name"
                size="35"
                class="avatar-image"
              />
            </div>
            <h5 class="teacher-name mb-0 text-white">{{ course.teacher.name }}</h5>
          </div>
        </div>
      </template>

      <span class="course-status rounded-pill font-size-12 fw-medium" :class="course.instructionalLevel">
        <i :class="getLevelIcon(course.instructionalLevel)"></i>
        {{ course.instructionalLevelI18n }}
      </span>

      <div class="d-flex flex-column gap-2">
        <BCardTitle class="mb-0 line-clamp-2">
          {{ course.title }}
        </BCardTitle>

        <RatingForm v-if="course.averageRating" v-model="course.averageRating" readonly />

        <div class="text-muted d-flex flex-column gap-1">
          <p class="d-flex align-items-center gap-2 mb-0">
            <i class="bx bx-book-open text-warning"></i>
            <span>{{ course.sectionItemCount || 0 }} {{ $t('public.course.section_item') }}</span>
          </p>
          <p v-if="course.joinedUserCount" class="d-flex align-items-center gap-2 mb-0">
            <i class="mdi mdi-account-multiple-outline lh-1 font-size-14 text-warning"></i>
            <span>{{ course.joinedUserCount }} {{ $t('public.course.student') }}</span>
          </p>
        </div>
      </div>

      <PriceDisplay :price="course.price" :salePrice="course.salePrice" display-style="compact"></PriceDisplay>
    </BCard>
  </router-link>
</template>

<script lang="ts" setup>
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';
  import RatingForm from '@/components/base/RatingForm.vue';
  import PriceDisplay from '@/components/base/PriceDisplay.vue';
  import dummyBanner from '@/assets/images/dummy_banner.png';

  import { CourseInterface } from '@/utils/interface/public/course';

  defineProps({
    course: {
      type: Object as PropType<CourseInterface>,
      required: true
    }
  });

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'mdi mdi-bullseye';
      case 'intermediate':
        return 'mdi mdi-trending-up';
      case 'advanced':
        return 'bx bxs-zap';
      case 'allLevels':
        return 'mdi mdi-web';
      default:
        return '';
    }
  };
</script>
