<template>
  <div class="review-section text-black">
    <h4 class="mb-3 review-title">{{ $t('public.course.review.title') }}</h4>
    <div class="border rounded-3 px-lg-3 py-3">
      <div class="d-flex flex-column align-items-center justify-content-center mb-3">
        <h1 class="text-primary-warning fw-bold">{{ stats.averageRating }}</h1>
        <RatingForm v-model="stats.averageRating" readonly hiddenValue />
        <span class="mt-2">({{ stats.total }} {{ $t('public.course.review.label') }})</span>
      </div>

      <div class="rating-line">
        <div v-for="item in ratingStats" :key="item.star" class="d-flex align-items-center mb-2">
          <div class="d-flex align-items-center justify-content-center gap-1 star-number">
            <strong>{{ item.star }}</strong>
            <i class="mdi mdi-star text-warning font-size-18 lh-1"></i>
          </div>
          <BProgress
            :value="item.percent"
            height="10px"
            variant="warning"
            class="flex-grow-1 mx-3 w-50 progress-rating"
          />
          <div class="text-center rating-count">{{ item.count }}</div>
        </div>
      </div>
    </div>

    <div v-if="myReview">
      <ReviewForm
        v-if="showEditForm"
        :targetID="myReview.targetID.toString()"
        :targetType="myReview.targetType"
        :review="myReview"
        class="mt-4"
        @submitted="updateMyReview"
      >
        <Button variant="outline-secondary" size="md" class="mt-2 me-2" @click="showEditForm = false">
          {{ $t('common.cancel') }}
        </Button>
      </ReviewForm>

      <BCard v-else class="px-2 px-lg-3 py-3 mt-4 p-4 my-review-card" no-body>
        <BCardBody class="d-flex justify-content-between align-items-start p-0">
          <div class="d-flex gap-2">
            <img
              :src="myReview.authorUser.imageUrl ? myReview.authorUser.imageUrl : dummyAvatar"
              alt="Teacher avatar"
              class="rounded-circle object-cover author-avatar"
              width="40"
              height="40"
            />
            <div class="d-flex flex-column gap-2">
              <h5 class="author-name mb-0 d-flex align-items-center gap-1">
                <span class="badge badge-pill badge-soft-primary">
                  {{ $t('user.user_course.section_item.reviews.my_review.sub_title') }}
                </span>
              </h5>
              <RatingForm v-model="myReview.rating" readonly />

              <div class="comment-content mt-2">
                <span v-if="myReview.content" v-html="myReview.content" />
                <span v-else class="fst-italic">{{ $t('user.user_course.section_item.reviews.empty_content') }}</span>
              </div>
            </div>
          </div>

          <div class="d-flex flex-column align-items-end gap-2">
            <small class="text-muted my-review-date">{{ filters.formatRelativeTime(myReview.updatedAt) }}</small>
            <span class="d-flex align-items-center gap-1 text-primary cursor-pointer" @click="showEditForm = true">
              <i class="mdi mdi-pencil-outline lh-1 font-size-14"></i>
              <span>{{ $t('user.user_course.section_item.reviews.my_review.edit_btn') }}</span>
            </span>
          </div>
        </BCardBody>
      </BCard>
    </div>

    <div class="mt-4 rounded-3" :class="{ border: reviews.length }">
      <div
        v-for="(review, index) in reviews"
        :key="index"
        class="d-flex justify-content-between align-items-start gap-1 py-3 review-card px-2 px-lg-3"
        :class="{ 'border-bottom': index < reviews.length - 1 }"
      >
        <div class="d-flex gap-2">
          <img
            :src="review.authorUser.imageUrl ? review.authorUser.imageUrl : dummyAvatar"
            alt="Teacher avatar"
            class="rounded-circle object-cover author-avatar"
            width="40"
            height="40"
          />
          <div class="d-flex flex-column gap-2">
            <h5 class="author-name mb-0 d-flex align-items-center gap-2">{{ review.authorUser.name }}</h5>
            <RatingForm v-model="review.rating" readonly />

            <div class="comment-content mt-2">
              <span v-if="review.content" v-html="review.content" />
              <span v-else class="fst-italic">{{ $t('user.user_course.section_item.reviews.empty_content') }}</span>
            </div>
          </div>
        </div>
        <small class="text-muted">{{ filters.formatRelativeTime(review.updatedAt) }}</small>
      </div>
    </div>

    <Pagination v-if="metadata" :metadata="metadata" site="user" @change="emit('change-page', $event)" />
  </div>
</template>

<script lang="ts" setup>
  import RatingForm from '@/components/base/RatingForm.vue';
  import Button from '@/components/base/Button.vue';
  import Pagination from '@/components/base/Pagination.vue';
  import ReviewForm from '@/components/user/course/ReviewForm.vue';

  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';

  import filters from '@/utils/filters';

  import { ReviewInterface } from '@/utils/interface/user/comment';
  import { MetaDataInterface } from '@/utils/interface/common';

  const props = defineProps({
    reviews: {
      type: Array as PropType<ReviewInterface[]>,
      required: true
    },
    stats: {
      type: Object,
      required: true
    },
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    },
    myReview: {
      type: Object as PropType<ReviewInterface>,
      required: false
    }
  });

  const emit = defineEmits(['change-page']);

  const showEditForm = ref<boolean>(false);

  const ratingStats = computed(() => {
    const stars = [5, 4, 3, 2, 1];
    const total = props.stats.total || 1;

    return stars.map(star => {
      const count = props.stats[`star${star}`] || 0;
      const percent = Math.round((count / total) * 100);

      return {
        star,
        count,
        percent
      };
    });
  });

  const updateMyReview = (review: ReviewInterface) => {
    if (!props.myReview) return;

    props.myReview.rating = review.rating;
    props.myReview.updatedAt = review.updatedAt;
    props.myReview.content = review.content;

    showEditForm.value = false;
    emit('change-page', 1);
  };
</script>

<style lang="css" scoped>
  .comment-content {
    color: #495057;
    white-space: pre-wrap;
  }

  .review-card {
    transition: all 0.3s ease;
  }

  .review-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .my-review-card {
    position: relative;
    border-radius: 0.5rem;
    background: linear-gradient(90deg, #fff8e1, #fff3cd);
    border: 1px solid #f5d491;
    overflow: hidden;
    box-shadow: 0 0 0 1px rgba(245, 179, 1, 0.15);
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease;
  }

  .my-review-card:hover {
    transform: scale(1.015);
    box-shadow: 0 0 20px rgba(245, 179, 1, 0.35);
  }

  .my-review-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    width: calc(100% + 4px);
    height: calc(100% + 4px);
    background: linear-gradient(60deg, #fff8d2, #ffe6a1, #fff8e0, #ffe6a1, #fff8d2);
    background-size: 300% 300%;
    border-radius: 0.75rem;
    z-index: 0;
    filter: blur(2px);
    animation: electricFlow 3s linear infinite;
    transition: opacity 0.3s ease;
    opacity: 0.7;
  }

  .my-review-card:hover::before {
    opacity: 1;
  }

  .my-review-card > * {
    position: relative;
    z-index: 1;
  }

  .my-review-date {
    line-height: normal;
  }

  @keyframes electricFlow {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
</style>
