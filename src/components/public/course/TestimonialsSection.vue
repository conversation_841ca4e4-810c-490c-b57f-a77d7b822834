<template>
  <section class="testimonials-section py-5">
    <BContainer>
      <div class="section-header text-center mb-5">
        <h2 class="section-title">{{ $t('public.course.testimonials.heading') }}</h2>
        <p class="section-subtitle">{{ $t('public.course.testimonials.description') }}</p>
      </div>

      <div class="stats-counter mb-5">
        <div class="stat-item" v-for="stat in stats" :key="stat.label">
          <div class="stat-number">
            <span class="counter">
              <span class="number-wrapper">
                <RollingNumber :target="Number(stat.value)" :digitHeight="40" :digitWidth="28" v-if="stat.value" />
                <span v-else>{{ stat.value }}</span>
              </span>
            </span>
            <span class="suffix">{{ stat.suffix }}</span>
          </div>
          <div class="stat-label">{{ $t(stat.label) }}</div>
        </div>
      </div>

      <BRow>
        <BCol lg="4" md="6" class="mb-4" v-for="testimonial in testimonials" :key="testimonial.id">
          <div class="testimonial-card">
            <div class="testimonial-rating">
              <i class="mdi mdi-star" v-for="n in testimonial.rating" :key="n"></i>
            </div>
            <p class="testimonial-text">"{{ testimonial.quote }}"</p>
            <div class="testimonial-author">
              <img :src="testimonial.avatar" :alt="testimonial.name" class="author-avatar" />
              <div class="author-info">
                <h4 class="author-name">{{ testimonial.name }}</h4>
                <p class="author-title">{{ testimonial.role }}</p>
              </div>
            </div>
          </div>
        </BCol>
      </BRow>
    </BContainer>
  </section>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { useI18n } from 'vue-i18n';
  import avatarUser1 from '@/assets/images/homepage/user_1.png';
  import avatarUser2 from '@/assets/images/homepage/user_2.png';
  import avatarUser3 from '@/assets/images/homepage/user_3.png';
  import { fetchCourseOverviewStats } from '@/services/public/repositories/stats';
  import RollingNumber from '@/components/base/animations/RollingNumber.vue';

  const { t } = useI18n();

  const getStats = (overviewStats?: any) => [
    {
      value: overviewStats?.totalUsers ?? 15000,
      suffix: '+',
      label: 'public.course.testimonials.stats.students'
    },
    {
      value: overviewStats?.totalCourses ?? 200,
      suffix: '+',
      label: 'public.course.testimonials.stats.courses'
    },
    {
      value: overviewStats?.totalProfessionalTeachers ?? 50,
      suffix: '+',
      label: 'public.course.testimonials.stats.instructors'
    },
    {
      value: overviewStats?.satisfactionRate ?? 98,
      suffix: '%',
      label: 'public.course.testimonials.stats.satisfaction'
    }
  ];

  const stats = ref(getStats());

  const testimonials = [
    {
      id: 1,
      name: t('public.course.testimonials.user_1.name'),
      role: t('public.course.testimonials.user_1.role'),
      avatar: avatarUser1,
      rating: 5,
      quote: t('public.course.testimonials.user_1.quote')
    },
    {
      id: 2,
      name: t('public.course.testimonials.user_2.name'),
      role: t('public.course.testimonials.user_2.role'),
      avatar: avatarUser2,
      rating: 5,
      quote: t('public.course.testimonials.user_2.quote')
    },
    {
      id: 3,
      name: t('public.course.testimonials.user_3.name'),
      role: t('public.course.testimonials.user_3.role'),
      avatar: avatarUser3,
      rating: 5,
      quote: t('public.course.testimonials.user_3.quote')
    }
  ];

  onMounted(async () => {
    try {
      const statsData = await fetchCourseOverviewStats();
      if (statsData?.data?.overviewStats) {
        stats.value = getStats(statsData.data.overviewStats);
      }
    } catch (error) {
      console.error('Error fetching course overview stats:', error);
      // Keep default values on error
    }
  });
</script>

<style scoped>
  .testimonials-section {
    --primary-color: #0a3d62;
    --secondary-color: #3c6382;
    --accent-color: #f39c12;
    --accent-hover: #e67e22;
    --text-color: #2c3e50;
    --light-text: #7f8c8d;
    --light-bg: #f8f9fa;
    --dark-bg: #2c3e50;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 5rem 0;
    position: relative;
    overflow: hidden;

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
      opacity: 0.1;
    }
  }

  .section-header {
    position: relative;
    z-index: 2;
    margin-bottom: 3rem;
  }

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;

    &:after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, var(--accent-color), var(--accent-hover));
      border-radius: 2px;
    }
  }

  .section-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 700px;
    margin: 0 auto;
  }

  .stats-counter {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    margin-bottom: 3rem;
    position: relative;
    z-index: 2;
    gap: 1rem;
  }

  .stat-item {
    text-align: center;
    padding: 1rem;
    flex: 1;
    min-width: 200px;
  }

  .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60px;
  }

  .counter {
    display: inline-flex;
    align-items: center;
    gap: 4px;
  }

  .number-wrapper {
    display: inline-block;
  }

  .suffix {
    display: inline-block;
  }

  .stat-label {
    font-size: 1rem;
    opacity: 0.9;
    white-space: nowrap;
  }

  .testimonial-card {
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 2rem;
    height: 100%;
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease;
    position: relative;
    z-index: 2;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;

    &:hover {
      transform: translateY(-10px);
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    }
  }

  .testimonial-rating {
    color: var(--accent-color);
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }

  .testimonial-text {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    flex-grow: 1;
  }

  .testimonial-author {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-top: auto;
  }

  .author-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 50px;
  }

  .author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(255, 255, 255, 0.2);
    flex-shrink: 0;
  }

  .author-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.2rem;
  }

  .author-title {
    font-size: 0.9rem;
    opacity: 0.8;
  }

  /* Responsive Design */
  @media (max-width: 992px) {
    .stats-counter {
      justify-content: center;
    }

    .stat-item {
      min-width: 150px;
    }
  }

  @media (max-width: 768px) {
    .section-title {
      font-size: 2rem;
    }

    .section-subtitle {
      font-size: 1rem;
    }

    .stats-counter {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
      justify-items: center;
    }

    .stat-item {
      margin-bottom: 1rem;
      min-width: auto;
      width: 100%;
      max-width: none;
    }

    .stat-number {
      font-size: 2rem;
      min-height: 50px;
    }

    .testimonial-card {
      padding: 1.5rem;
    }
  }

  @media (max-width: 576px) {
    .section-title {
      font-size: 1.8rem;
    }

    .stats-counter {
      grid-template-columns: 1fr 1fr;
      gap: 0.8rem;
    }

    .stat-number {
      font-size: 1.8rem;
      min-height: 45px;
    }

    .testimonial-card {
      padding: 1rem;
    }

    .author-avatar {
      width: 40px;
      height: 40px;
    }

    .author-info {
      min-height: 40px;
    }
  }
</style>
