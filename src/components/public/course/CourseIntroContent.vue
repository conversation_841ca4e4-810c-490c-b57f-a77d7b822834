<template>
  <BCard class="course-intro-content text-black">
    <div class="d-flex flex-column gap-4">
      <div v-if="course.description" class="course-introduction border-bottom pb-4">
        <h4 class="fw-bold">{{ $t('public.course.introduction') }}</h4>
        <pre class="pt-2 pre-content">{{ course.description }}</pre>
      </div>

      <CourseLesson :course="course" />

      <div class="teacher-info border-bottom pb-4">
        <h4 class="fw-bold">{{ $t('public.course.teacher') }}</h4>
        <router-link :to="`/teachers/${course.teacher.slug}`" target="_blank" class="d-flex align-items-center pt-2">
          <img
            :src="course.teacher.imageUrl ? course.teacher.imageUrl : dummyAvatar"
            alt="Teacher avatar"
            class="rounded-circle object-cover"
            width="70"
            height="70"
          />
          <div class="ms-3 text-black">
            <h5 class="mb-0">{{ course.teacher.name }}</h5>
            <p class="text-muted mb-0">{{ course.teacher.contactEmail }}</p>
          </div>
        </router-link>
        <div v-if="course.teacher.description" class="pt-4">
          <h5>{{ $t('public.course.introduction') }}</h5>
          <pre class="pre-content mb-0">{{ course.teacher.description }}</pre>
        </div>
      </div>

      <CourseReview
        :reviews="reviews"
        :stats="stats"
        :metadata="metadata"
        @change-page="$emit('change-page', $event.page)"
      />
    </div>
  </BCard>
</template>

<script lang="ts" setup>
  import CourseReview from '@/components/public/course/CourseReview.vue';
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';
  import CourseLesson from './CourseLesson.vue';

  import { ReviewInterface } from '@/utils/interface/user/comment';
  import { UserCourseInterface } from '@/utils/interface/user/userCourse';
  import { MetaDataInterface } from '@/utils/interface/common';

  defineProps({
    course: {
      type: Object as PropType<UserCourseInterface>,
      required: true
    },
    reviews: {
      type: Array as PropType<ReviewInterface[]>,
      required: true
    },
    stats: {
      type: Object,
      required: true
    },
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    }
  });
</script>
