<template>
  <section class="hero-section">
    <div class="container">
      <div class="hero-content text-center">
        <h1 class="hero-title">{{ $t('user.user_course.search_form.title') }}</h1>
        <p class="hero-subtitle">{{ $t('user.user_course.search_form.description') }}</p>
      </div>
    </div>
    <div class="floating-elements">
      <div class="floating-element ball-1"></div>
      <div class="floating-element ball-2"></div>
      <div class="floating-element ball-3"></div>
      <div class="floating-element cue-1"></div>
      <div class="floating-element rack-1"></div>
    </div>
  </section>
</template>

<style lang="scss" scoped>
  // Variables
  $primary-color: #f1b44c;
  $secondary-color: #2a3042;
  $text-color: #495057;
  $text-light: #74788d;
  $border-radius: 12px;
  $box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  $transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  // Hero Section
  .hero-section {
    position: relative;
    padding: 80px 0 120px;
    overflow: hidden;
    background: linear-gradient(135deg, #f39c12, #e67e22, #f1b44c);
    margin-bottom: 40px;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
      opacity: 0.5;
    }
    .container {
      position: relative;
      z-index: 2;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }
  }
  .floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
    .floating-element {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.15);
      box-shadow: 0 0 30px rgba(255, 255, 255, 0.2);
      animation: float 15s infinite ease-in-out;
      &.ball-1 {
        width: 80px;
        height: 80px;
        top: 15%;
        left: 10%;
        animation-delay: 0s;
        background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.05));
      }
      &.ball-2 {
        width: 60px;
        height: 60px;
        top: 60%;
        right: 12%;
        animation-delay: 2s;
        background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.05));
      }
      &.ball-3 {
        width: 40px;
        height: 40px;
        bottom: 20%;
        left: 20%;
        animation-delay: 4s;
        background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.05));
      }
      &.cue-1 {
        width: 200px;
        height: 8px;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.2));
        border-radius: 4px;
        transform: rotate(45deg);
        top: 30%;
        right: 5%;
        animation: float-rotate 12s infinite ease-in-out;
      }
      &.rack-1 {
        width: 120px;
        height: 120px;
        bottom: 30%;
        right: 25%;
        background: none;
        box-shadow: none;
        animation-delay: 3s;
        background-image: url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M60 10L110 95H10L60 10Z' fill='rgba(255, 255, 255, 0.1)' stroke='rgba(255, 255, 255, 0.3)' stroke-width='2'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        border-radius: 0;
      }
    }
  }
  @keyframes float {
    0%,
    100% {
      transform: translateY(0) translateX(0);
    }
    50% {
      transform: translateY(-15px) translateX(10px);
    }
  }
  @keyframes float-rotate {
    0%,
    100% {
      transform: translateY(0) rotate(45deg);
    }
    50% {
      transform: translateY(-10px) rotate(40deg);
    }
  }
  .hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
  }
  .hero-title {
    color: white;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  }
  .hero-subtitle {
    color: rgba(255, 255, 255, 0.95);
    font-size: 1.2rem;
    margin-bottom: 30px;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
    line-height: 1.6;
  }
  @media (max-width: 768px) {
    .hero-title {
      font-size: 2rem;
    }
    .hero-subtitle {
      font-size: 1rem;
    }
  }
</style>
