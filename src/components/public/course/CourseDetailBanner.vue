<template>
  <div class="course-detail-banner d-flex flex-column gap-2 gap-lg-4 px-0 px-lg-4 py-2">
    <FadeInUp>
      <h2 class="text-break">{{ course.title }}</h2>

      <div class="d-flex flex-column flex-lg-row align-items-start align-items-lg-center gap-1 gap-lg-3">
        <div class="d-flex align-items-center gap-1">
          <i class="mdi mdi-account font-size-18"></i>
          <span>
            {{ $t('public.course.teacher') }}
            <b>{{ course.teacher?.name }}</b>
          </span>
        </div>
        <div class="d-none d-lg-flex align-items-center gap-1">
          <i class="mdi mdi-play-circle-outline font-size-18"></i>
          <span>{{ course.sectionItemCount }} {{ $t('public.course.section_item') }}</span>
        </div>
        <div class="d-flex align-items-center gap-1">
          <i class="mdi mdi-web font-size-18"></i>
          <span>{{ $t('public.course.vietnamese') }}</span>
        </div>
      </div>
    </FadeInUp>
  </div>
</template>

<script lang="ts" setup>
  import { UserCourseInterface } from '@/utils/interface/user/userCourse';

  defineProps({
    course: {
      type: Object as PropType<UserCourseInterface>,
      required: true
    }
  });
</script>
