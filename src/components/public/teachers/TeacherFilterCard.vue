<template>
  <div class="filters-card">
    <div class="filters-header">
      <div class="teachers-count">
        <span class="count-number">{{ metadata.total || 0 }}</span>
        <span class="count-label">{{ $t('public.teacher.title') }}</span>
      </div>
    </div>

    <div class="filters-tags">
      <div class="filter-tag" :class="{ active: activeAll }" @click="selectAll">
        <i class="bx bx-check"></i>
        <span>{{ $t('public.teacher.filters.all') }}</span>
      </div>

      <div class="filter-tag" :class="{ active: query.isProfessional === 'true' }" @click="selectFilter('experts')">
        <i class="bx bx-trophy"></i>
        <span>{{ $t('public.teacher.filters.experts') }}</span>
      </div>

      <div
        class="filter-tag"
        :class="{ active: orderBy === TEACHER_ORDER_BY.AVERAGE_RATING_DESC }"
        @click="selectFilter(TEACHER_ORDER_BY.AVERAGE_RATING_DESC)"
      >
        <i class="bx bx-star"></i>
        <span>{{ $t('public.teacher.filters.highly_rated') }}</span>
      </div>

      <div
        class="filter-tag"
        :class="{ active: orderBy === TEACHER_ORDER_BY.APPROVED_COURSE_COUNT_DESC }"
        @click="selectFilter(TEACHER_ORDER_BY.APPROVED_COURSE_COUNT_DESC)"
      >
        <i class="bx bx-book"></i>
        <span>{{ $t('public.teacher.filters.many_courses') }}</span>
      </div>

      <div
        class="filter-tag"
        :class="{ active: orderBy === TEACHER_ORDER_BY.STUDENTS_COUNT_DESC }"
        @click="selectFilter(TEACHER_ORDER_BY.STUDENTS_COUNT_DESC)"
      >
        <i class="bx bx-user"></i>
        <span>{{ $t('public.teacher.filters.many_students') }}</span>
      </div>

      <div
        class="filter-tag"
        :class="{ active: orderBy === TEACHER_ORDER_BY.CREATED_AT_DESC }"
        @click="selectFilter(TEACHER_ORDER_BY.CREATED_AT_DESC)"
      >
        <i class="bx bx-user-plus"></i>
        <span>{{ $t('public.teacher.filters.newly_joined') }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { TEACHER_ORDER_BY } from '@/utils/constant';
  import { MetaDataInterface } from '@/utils/interface/common';

  import { TeacherQueryFormInput } from '@/forms/public/teacher';

  const props = defineProps({
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    },
    orderBy: {
      type: String,
      required: true,
      default: TEACHER_ORDER_BY.STUDENTS_COUNT_DESC
    },
    query: {
      type: Object as PropType<TeacherQueryFormInput>,
      required: false,
      default: new TeacherQueryFormInput()
    }
  });

  const emit = defineEmits(['search', 'reset', 'update:orderBy']);

  const activeAll = computed(() => {
    return props.query.isProfessional === '' && props.query.nameCont === '';
  });

  function selectAll() {
    emit('reset');
  }

  function selectFilter(filter: string) {
    if (filter === 'experts') {
      props.query.isProfessional = props.query.isProfessional === 'true' ? '' : 'true';
    } else {
      emit('update:orderBy', filter);
    }

    emit('search');
  }
</script>

<style lang="scss" scoped>
  $primary-color: #f1b44c;
  $primary-dark: darken($primary-color, 10%);
  $text-color: #495057;
  $text-light: #74788d;
  $border-radius: 12px;
  $box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  $transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

  .filters-card {
    background-color: white;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    padding: 20px 25px;
    margin-bottom: 30px;
    transition: $transition;
    &:hover {
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }
  }

  .filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
  }

  .teachers-count {
    display: flex;
    align-items: baseline;
    gap: 8px;
    .count-number {
      font-size: 2.2rem;
      font-weight: 800;
      color: $primary-color;
      text-shadow: 0 2px 5px rgba($primary-color, 0.2);
    }
    .count-label {
      font-size: 1.1rem;
      color: $text-color;
      font-weight: 500;
    }
  }

  .sort-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    min-height: 40px;
  }

  .sort-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.95rem;
    color: $text-color;
    font-weight: 500;
    min-width: 90px;
    i {
      font-size: 1.2rem;
      color: $primary-color;
    }
  }

  .sort-select {
    height: 40px;
    min-width: 220px;
    font-size: 0.95rem;
    border-radius: 25px;
  }

  .reset-button {
    background-color: #f1b44c;
    color: #fff;
    border: none;
    border-radius: 25px;
    padding: 0 16px;
    height: 40px;
    font-size: 0.95rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 10px rgba(241, 180, 76, 0.15);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    cursor: pointer;
    min-width: 90px;
    i {
      font-size: 1.1rem;
    }
    &:hover,
    &:focus {
      background-color: darken(#f1b44c, 8%);
      color: #fff;
      box-shadow: 0 6px 15px rgba(241, 180, 76, 0.25);
      i {
        color: #fff;
      }
    }
  }

  .filters-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .filter-tag {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background-color: #f8f9fa;
    border-radius: 30px;
    font-size: 0.9rem;
    font-weight: 500;
    color: $text-color;
    cursor: pointer;
    transition: $transition;
    border: 1px solid transparent;
    i {
      font-size: 1.1rem;
      color: $text-light;
    }
    &:hover {
      background-color: #f1f5f9;
      transform: translateY(-2px);
    }
    &.active {
      background-color: rgba($primary-color, 0.1);
      color: $primary-color;
      border-color: rgba($primary-color, 0.2);
      i {
        color: $primary-color;
      }
    }
  }
</style>
