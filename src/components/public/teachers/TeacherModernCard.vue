<template>
  <div class="teacher-card">
    <div class="card-banner">
      <div class="banner-pattern"></div>
      <div class="teacher-stats">
        <BBadge class="stat-badge" v-if="teacher.approvedCourseCount && teacher.approvedCourseCount > 0">
          <i class="bx bx-book-open"></i>
          {{ teacher.approvedCourseCount }} {{ $t('public.teacher.stats.courses') }}
        </BBadge>

        <BBadge class="stat-badge" v-if="teacher.approvedDrillCount && teacher.approvedDrillCount > 0">
          <i class="bx bx-bullseye"></i>
          {{ teacher.approvedDrillCount }} {{ $t('public.teacher.stats.images') }}
        </BBadge>

        <BBadge class="stat-badge" v-if="teacher.studentCount && teacher.studentCount > 0">
          <i class="bx bx-user-check"></i>
          {{ teacher.studentCount }} {{ $t('public.teacher.stats.students') }}
        </BBadge>
      </div>
    </div>

    <div class="card-content">
      <div class="avatar-container">
        <img :src="teacher.imageUrl || dummyAvatar" :alt="teacher.name" class="teacher-avatar" />
        <div class="avatar-border"></div>

        <div class="stat-badge rating" v-if="teacher.averageRating && teacher.averageRating > 0">
          <i class="bx bxs-star"></i>
          <span>{{ Math.round(teacher.averageRating * 10) / 10 }}</span>
        </div>
      </div>

      <h3 class="teacher-name">{{ teacher.name }}</h3>

      <p class="teacher-description">
        {{ teacher.description || $t('public.teacher.default_description') }}
      </p>

      <div class="teacher-experience">
        <div class="experience-item" v-for="(experience, index) in experiences" :key="experience.id || index">
          <i :class="experience.icon?.class || 'mdi mdi-trophy'"></i>
          <span>{{ experience.title }}</span>
        </div>
      </div>

      <Button class="view-profile-btn" @click="viewTeacherProfile(teacher)">
        <span>{{ $t('public.teacher.view_profile') }}</span>
        <i class="bx bx-right-arrow-alt"></i>
      </Button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { TeacherInterface } from '@/utils/interface/public/teacher';
  import { useRouter } from 'vue-router';
  import Button from '@/components/base/Button.vue';
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';

  const router = useRouter();

  const props = defineProps({
    teacher: {
      type: Object as PropType<TeacherInterface>,
      required: true
    }
  });

  const experiences = computed(() => {
    return props.teacher.experiences?.slice(0, 2);
  });

  const viewTeacherProfile = (teacher: TeacherInterface) => {
    router.push(`/teachers/${teacher.slug || teacher.id}`);
  };
</script>

<style lang="scss" scoped>
  .teacher-card {
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    transform: translateZ(0);
    &:hover {
      transform: translateY(-10px) translateZ(0);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
      .card-banner {
        &::after {
          opacity: 0.7;
        }
      }
      .teacher-avatar {
        transform: scale(1.05);
      }
      .avatar-border {
        transform: scale(1.05);
      }
      .view-profile-btn {
        background-color: #f1b44c;
        color: white;
        box-shadow: 0 8px 15px rgba(241, 180, 76, 0.3);
      }
    }
  }
  .card-banner {
    height: 130px;
    background-color: #2a3042;
    position: relative;
    overflow: hidden;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to right, rgba(241, 180, 76, 0.3), rgba(42, 48, 66, 0.3));
      opacity: 0.5;
      transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    }
  }
  .banner-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    z-index: 1;
  }
  .teacher-stats {
    position: absolute;
    top: 15px;
    left: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    z-index: 2;
  }
  .stat-badge {
    background-color: rgba(255, 255, 255, 0.15) !important;
    color: white;
    font-size: 0.8rem;
    padding: 5px 10px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 5px;
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    i {
      font-size: 0.9rem;
    }
    &.rating {
      background-color: rgba(241, 180, 76, 0.9) !important;
      box-shadow: 0 3px 8px rgba(241, 180, 76, 0.3);
    }
  }
  .card-content {
    padding: 65px 25px 25px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
    position: relative;
  }
  .avatar-container {
    position: absolute;
    top: -50px;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: visible;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    z-index: 3;
  }
  .teacher-avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    transition: transform 0.5s ease;
  }
  .avatar-container .stat-badge.rating {
    position: absolute;
    bottom: -5px;
    right: -10px;
    background-color: rgba(241, 180, 76, 0.9);
    padding: 4px 8px;
    font-size: 0.75rem;
    z-index: 10;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  .avatar-border {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 4px solid #f1b44c;
    border-radius: 50%;
    box-sizing: border-box;
  }
  .teacher-name {
    margin-top: 10px;
    margin-bottom: 10px;
    font-size: 1.4rem;
    font-weight: 700;
    color: #343a40;
  }
  .teacher-description {
    color: #74788d;
    font-size: 0.95rem;
    margin-bottom: 15px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.5;
  }
  .teacher-experience {
    margin-bottom: 20px;
    width: 100%;
  }
  .experience-item {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background-color: #f8f9fa;
    padding: 8px 14px;
    border-radius: 8px;
    font-size: 0.85rem;
    color: #495057;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
    i {
      color: #f1b44c;
      font-size: 1rem;
    }
  }
  .view-profile-btn {
    margin-top: auto;
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    text-decoration: none;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03);
    i {
      font-size: 1.2rem;
      transition: transform 0.3s ease;
    }
    &:hover {
      background-color: #f1b44c;
      color: white;
      box-shadow: 0 8px 15px rgba(241, 180, 76, 0.3);
      i {
        transform: translateX(4px);
      }
    }
  }
</style>
