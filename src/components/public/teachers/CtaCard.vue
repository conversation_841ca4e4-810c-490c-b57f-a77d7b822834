<template>
  <section class="cta-section">
    <div class="cta-overlay"></div>
    <div class="container">
      <div class="cta-content">
        <div class="cta-text">
          <h2 class="cta-title">{{ $t('public.teacher.cta.title') }}</h2>
          <p class="cta-description">{{ $t('public.teacher.cta.description') }}</p>
          <div class="cta-buttons">
            <router-link :to="registerButtonLink" class="btn-primary-gradient">
              {{ $t('public.teacher.cta.register_button') }}
              <i class="mdi mdi-arrow-right"></i>
            </router-link>

            <router-link to="/courses" class="btn-outline">
              {{ $t('public.teacher.cta.learn_more_button') }}
            </router-link>
          </div>
        </div>
        <div class="cta-image">
          <img src="@/assets/images/pool-table.png" alt="Billiard Table" />
        </div>
      </div>
    </div>
    <div class="floating-elements">
      <div class="floating-element cue-1"></div>
      <div class="floating-element ball-4"></div>
      <div class="floating-element ball-5"></div>
    </div>
  </section>
</template>

<script setup lang="ts">
  import { useAuthPublicStore } from '@/store/public/auth';
  import { useTeacherAuthStore } from '@/store/teacher/auth';
  import { storeToRefs } from 'pinia';

  const authStore = useAuthPublicStore();
  const teacherAuthStore = useTeacherAuthStore();
  const { teacherProfile } = storeToRefs(teacherAuthStore);

  const registerButtonLink = computed(() => {
    if (!authStore.accessToken) {
      return '/register';
    }

    if (!teacherProfile.value) {
      return '/register';
    }

    if (!teacherProfile.value.basicEntered) {
      return '/teacher/setup';
    }

    return '/teacher/courses';
  });
</script>

<style lang="scss" scoped>
  $primary-color: #f1b44c;
  $primary-dark: darken($primary-color, 10%);
  $accent-color: #34c38f;
  $border-radius: 12px;
  $box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  $transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

  .cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, $primary-color, $primary-dark);
    position: relative;
    overflow: hidden;
    color: white;
  }

  .cta-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2V6h4V4H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.1;
  }

  .container {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .cta-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 2;
    @media (max-width: 992px) {
      flex-direction: column;
      text-align: center;
      gap: 2rem;
    }
  }

  .cta-text {
    flex: 0 0 55%;
    @media (max-width: 992px) {
      order: -1;
    }
  }

  .cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: white;
  }

  .cta-description {
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    max-width: 600px;
    line-height: 1.7;
    @media (max-width: 992px) {
      margin-left: auto;
      margin-right: auto;
    }
  }

  .cta-buttons {
    display: flex;
    gap: 1rem;
    @media (max-width: 992px) {
      justify-content: center;
    }
    @media (max-width: 576px) {
      flex-direction: column;
      align-items: center;
    }
  }

  .btn-primary-gradient {
    padding: 1rem 2rem;
    background: white;
    color: $accent-color;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: $transition;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    i {
      transition: transform 0.3s ease;
    }
    &:hover {
      background: #f8f9fa;
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
      i {
        transform: translateX(5px);
      }
    }
  }

  .btn-outline {
    padding: 1rem 2rem;
    background: transparent;
    color: white;
    border: 2px solid white;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: $transition;
    &:hover {
      background: rgba(255, 255, 255, 0.1);
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
  }

  .cta-image {
    flex: 0 0 40%;
    position: relative;
    transform-style: preserve-3d;
    perspective: 1000px;
    img {
      width: 100%;
      border-radius: $border-radius;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
      transform: rotateY(-15deg);
      transition: transform 0.5s ease;
    }
    &:hover img {
      transform: rotateY(0deg);
    }
  }

  .floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
    .floating-element {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.15);
      box-shadow: 0 0 30px rgba(255, 255, 255, 0.2);
      animation: float 15s infinite ease-in-out;
      &.ball-4 {
        width: 50px;
        height: 50px;
        top: 25%;
        right: 25%;
        animation-delay: 1s;
        background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.05));
      }
      &.ball-5 {
        width: 35px;
        height: 35px;
        bottom: 30%;
        right: 10%;
        animation-delay: 3s;
        background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.05));
      }
      &.cue-1 {
        width: 200px;
        height: 8px;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.2));
        border-radius: 4px;
        transform: rotate(45deg);
        top: 30%;
        right: 5%;
        animation: float-rotate 12s infinite ease-in-out;
      }
    }
  }
  @keyframes float {
    0%,
    100% {
      transform: translateY(0) translateX(0);
    }
    50% {
      transform: translateY(-15px) translateX(10px);
    }
  }
  @keyframes float-rotate {
    0%,
    100% {
      transform: translateY(0) rotate(45deg);
    }
    50% {
      transform: translateY(-10px) rotate(40deg);
    }
  }
</style>
