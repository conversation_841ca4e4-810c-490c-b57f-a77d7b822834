<template>
  <div class="review-section text-black">
    <div class="d-flex align-items-center justify-content-between mb-3">
      <h4 class="m-0 review-title">{{ $t('public.course.review.title') }}</h4>
      <Button
        v-if="isAvailableToRate"
        icon="bxs-star"
        variant="warning"
        class="d-flex align-items-center"
        @click="showModalRating = true"
      >
        {{ $t('user.user_course.rating') }}
      </Button>
    </div>
    <div class="border rounded-3 px-lg-3 py-3">
      <div class="d-flex flex-column align-items-center justify-content-center mb-3">
        <h1 class="text-primary-warning fw-bold">{{ stats.averageRating }}</h1>
        <RatingForm v-model="stats.averageRating" readonly hiddenValue />
        <span class="mt-2">({{ stats.total }} {{ $t('public.course.review.label') }})</span>
      </div>

      <div class="rating-line">
        <div v-for="item in ratingStats" :key="item.star" class="d-flex align-items-center mb-2">
          <div class="d-flex align-items-center justify-content-center gap-1 star-number">
            <strong>{{ item.star }}</strong>
            <i class="mdi mdi-star text-warning font-size-18 lh-1"></i>
          </div>
          <BProgress
            :value="item.percent"
            height="10px"
            variant="warning"
            class="flex-grow-1 mx-3 w-50 progress-rating"
          />
          <div class="text-center rating-count">{{ item.count }}</div>
        </div>
      </div>
    </div>

    <div class="mt-4 rounded-3 px-2 px-lg-3" :class="{ border: reviews.length }">
      <div
        v-for="(review, index) in reviews"
        :key="index"
        class="d-flex justify-content-between align-items-start gap-1 py-3"
        :class="{ 'border-bottom': index < reviews.length - 1 }"
      >
        <div class="d-flex gap-2">
          <img
            :src="review.authorUser.imageUrl ? review.authorUser.imageUrl : dummyAvatar"
            alt="Teacher avatar"
            class="rounded-circle object-cover author-avatar"
            width="40"
            height="40"
          />
          <div class="d-flex flex-column gap-2">
            <h5 class="author-name mb-0 d-flex align-items-center gap-2">{{ review.authorUser.name }}</h5>
            <RatingForm v-model="review.rating" readonly />
          </div>
        </div>
        <small class="text-muted">{{ filters.formatRelativeTime(review.createdAt) }}</small>
      </div>
    </div>

    <Pagination v-if="metadata" :metadata="metadata" site="user" @change="emit('change-page', $event)" />
  </div>
</template>

<script lang="ts" setup>
  import RatingForm from '@/components/base/RatingForm.vue';
  import Button from '@/components/base/Button.vue';
  import Pagination from '@/components/base/Pagination.vue';

  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';

  import filters from '@/utils/filters';

  import { ReviewInterface } from '@/utils/interface/user/comment';
  import { MetaDataInterface } from '@/utils/interface/common';

  const showModalRating = defineModel<boolean>('showModalRating', {
    default: false
  });

  const props = defineProps({
    reviews: {
      type: Array as PropType<ReviewInterface[]>,
      required: true
    },
    stats: {
      type: Object,
      required: true
    },
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    },
    isAvailableToRate: {
      type: Boolean,
      default: false
    }
  });

  const emit = defineEmits(['change-page']);

  const ratingStats = computed(() => {
    const stars = [5, 4, 3, 2, 1];
    const total = props.stats.total || 1;

    return stars.map(star => {
      const count = props.stats[`star${star}`] || 0;
      const percent = Math.round((count / total) * 100);

      return {
        star,
        count,
        percent
      };
    });
  });
</script>
