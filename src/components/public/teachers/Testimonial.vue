<template>
  <section class="testimonials-section">
    <div class="container">
      <h2 class="section-title">{{ $t('public.teacher.testimonials.title') }}</h2>
      <p class="section-subtitle">{{ $t('public.teacher.testimonials.subtitle') }}</p>

      <div class="testimonials-grid">
        <div class="testimonial-card" v-for="review in reviews" :key="review.id">
          <div class="testimonial-rating">
            <i class="bx bxs-star" v-for="i in review.rating" :key="i"></i>
          </div>

          <p class="testimonial-text">{{ review.content }}</p>

          <div class="testimonial-author">
            <img :src="review.authorUser.imageUrl || dummyAvatar" alt="Student" class="author-avatar" />
            <div class="author-info">
              <h4>{{ review.authorUser.name }}</h4>
              <p>{{ review.relatedCourse?.title }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts" setup>
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';

  defineProps({
    reviews: {
      type: Array as PropType<any[]>,
      required: true
    }
  });
</script>

<style lang="scss" scoped>
  // Variables
  $primary-color: #f1b44c;
  $dark-color: #343a40;
  $text-color: #495057;
  $text-light: #74788d;
  $border-radius: 12px;
  $transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

  // Testimonials Section
  .testimonials-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
      opacity: 0.5;
    }
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .section-title {
    text-align: center;
    font-size: 2.2rem;
    font-weight: 800;
    color: $dark-color;
    margin-bottom: 15px;
    position: relative;
    z-index: 1;
  }

  .section-subtitle {
    text-align: center;
    font-size: 1.1rem;
    color: $text-light;
    margin-bottom: 50px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    z-index: 1;
  }

  .testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
    position: relative;
    z-index: 1;
  }

  .testimonial-card {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: $border-radius;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: $transition;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &:hover {
      transform: translateY(-10px);
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    }
  }

  .testimonial-rating {
    margin-bottom: 20px;
    color: $primary-color;
    font-size: 1.2rem;

    i {
      margin-right: 3px;
    }
  }

  .testimonial-text {
    font-size: 1rem;
    line-height: 1.7;
    color: $text-color;
    margin-bottom: 25px;
    font-style: italic;
    display: -webkit-box;
    -webkit-line-clamp: 10;
    line-clamp: 10;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    hyphens: auto;
    align-self: flex-start;
    height: 100%;
  }

  .testimonial-author {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid white;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  }

  .author-info {
    h4 {
      font-size: 1rem;
      font-weight: 600;
      color: $dark-color;
      margin: 0 0 5px;
    }

    p {
      font-size: 0.85rem;
      color: $text-light;
      margin: 0;
    }
  }

  // Responsive
  @media (max-width: 768px) {
    .testimonials-grid {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
  }

  @media (max-width: 480px) {
    .testimonials-grid {
      grid-template-columns: 1fr;
    }

    .section-title {
      font-size: 1.8rem;
    }
  }
</style>
