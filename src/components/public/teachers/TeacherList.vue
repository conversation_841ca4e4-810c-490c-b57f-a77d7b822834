<template>
  <Loader :loading="loading">
    <BRow class="teacher-list g-4">
      <BCol lg="4" md="6" sm="12" v-for="teacher in teachers" :key="teacher.id">
        <TeacherCard :teacher="teacher" />
      </BCol>
    </BRow>

    <Pagination :metadata="metadata" @change="$emit('fetchList', $event)" site="user"></Pagination>
  </Loader>
</template>

<script lang="ts" setup>
  import Pagination from '@/components/base/Pagination.vue';
  import { MetaDataInterface } from '@/utils/interface/common';
  import { TeacherInterface } from '@/utils/interface/public/teacher';
  import TeacherCard from './TeacherCard.vue';

  defineEmits(['fetchList']);
  defineProps({
    metadata: {
      type: Object as PropType<MetaDataInterface>,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    teachers: {
      type: Array as PropType<TeacherInterface[]>,
      required: true
    }
  });
</script>

<style lang="scss" scoped>
  .action-btn-group {
    .btn {
      &:hover {
        color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity));
      }
    }
  }

  .teacher-list {
    margin-bottom: 2rem;
  }
</style>
