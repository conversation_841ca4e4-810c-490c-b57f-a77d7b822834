<template>
  <section class="content-section about-section">
    <div class="section-header">
      <h2 class="section-title">{{ $t('public.teacher.about_me') }}</h2>
      <div class="section-divider"></div>
    </div>
    <div class="section-content">
      <pre class="teacher-bio pre-content">
        {{ teacher.description }}
      </pre>

      <div class="achievements" v-if="teacher.experiences && teacher.experiences.length > 0">
        <div class="achievement-item" v-for="(experience, index) in teacher.experiences" :key="experience.id || index">
          <div class="achievement-icon"><i :class="experience.icon?.class || 'mdi mdi-trophy'"></i></div>
          <div class="achievement-content">
            <div class="achievement-title">{{ experience.title }}</div>
            <div class="achievement-year">{{ filters.formatYear(experience.experienceTime) }}</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
  import filters from '@/utils/filters';
  import { TeacherInterface } from '@/utils/interface/public/teacher';

  defineProps({
    teacher: {
      type: Object as PropType<TeacherInterface>,
      required: true
    }
  });
</script>

<style scoped lang="scss">
  $primary-color: #f1b44c;
  $text-color: #495057;
  $dark-color: #343a40;
  $border-radius: 12px;
  $box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  $transition: all 0.3s ease;

  @mixin flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .content-section {
    margin-bottom: 60px;
  }

  .section-header {
    margin-bottom: 30px;
    text-align: center;
  }

  .section-title {
    font-size: 28px;
    font-weight: 700;
    color: $dark-color;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
  }

  .section-divider {
    width: 100px;
    height: 2px;
    background-color: rgba($primary-color, 0.3);
    margin: 0 auto;
  }

  .about-section {
    .section-content {
      max-width: 800px;
      margin: 0 auto;
    }

    .teacher-bio {
      font-size: 16px;
      line-height: 1.8;
      margin-bottom: 30px;
      color: $text-color;
    }

    .achievements {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      justify-content: center;
    }

    .achievement-item {
      display: flex;
      align-items: center;
      gap: 15px;
      background-color: white;
      border-radius: $border-radius;
      padding: 15px 20px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
      flex: 1;
      min-width: 250px;
      max-width: 350px;
      transition: $transition;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
      }
    }

    .achievement-icon {
      width: 50px;
      height: 50px;
      background-color: rgba($primary-color, 0.1);
      border-radius: 12px;
      @include flex-center;
      font-size: 24px;
      color: $primary-color;
    }

    .achievement-content {
      flex: 1;
    }

    .achievement-title {
      font-weight: 600;
      color: $dark-color;
      margin-bottom: 5px;
    }

    .achievement-year {
      font-size: 14px;
      color: $text-color;
    }
  }

  .pre-content {
    white-space: pre-wrap;
    font-family: inherit;
    margin: 0;
    padding: 0;
  }
</style>
