<template>
  <section class="content-section reviews-section">
    <div class="section-header">
      <h2 class="section-title">{{ $t('public.teacher.review_section.title') }}</h2>
      <div class="section-divider"></div>
    </div>
    <div class="section-content">
      <div class="reviews-summary">
        <div class="rating-overview">
          <div class="average-rating">
            <div class="rating-value">{{ stats.averageRating }}</div>
            <div class="rating-stars">
              <i class="bx bxs-star" v-for="star in 5" :key="star"></i>
            </div>
            <div class="rating-count">
              {{ $t('public.teacher.review_section.review_count', { count: stats.total }) }}
            </div>
          </div>
          <div class="rating-bars">
            <div class="rating-bar-item" v-for="i in 5" :key="i">
              <div class="rating-label">
                <span>{{ 6 - i }}</span>
                <i class="bx bxs-star star-color"></i>
              </div>
              <div class="rating-bar">
                <div class="rating-fill" :style="{ width: getStarPercentage(6 - i) + '%' }"></div>
              </div>

              <div class="rating-bar-item-text">{{ stats[`star${6 - i}`] }}</div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="reviews.length" class="reviews-list">
        <div class="review-card" v-for="review in reviews" :key="review.id">
          <div class="review-header">
            <div class="reviewer-info">
              <BImg :src="review.authorUser.imageUrl || dummyAvatar" alt="Reviewer avatar" class="reviewer-avatar" />

              <div class="reviewer-details">
                <div class="reviewer-name">{{ review.authorUser.name }}</div>
                <div class="review-date">{{ filters.formatDate(review.createdAt) }}</div>
              </div>
            </div>
            <div class="review-rating">
              <i class="bx bxs-star" v-for="star in 5" :key="star" :class="{ inactive: star > review.rating }"></i>
            </div>
          </div>
          <div class="review-content">
            <p>
              {{ review.content }}
            </p>
          </div>
          <div class="review-course" v-if="review.relatedCourse">
            <i class="bx bx-book-open"></i>
            <span> {{ $t('public.teacher.review_section.review_course') }}: {{ review.relatedCourse?.title }} </span>
          </div>
        </div>

        <Pagination :metadata="metadata" @change="changePage" site="user" />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
  import { COMMENT_TARGET_TYPE } from '@/utils/constant';
  import { TeacherInterface } from '@/utils/interface/public/teacher';

  import { useGoList } from '@bachdx/b-vuse';

  import { reviewsList } from '@/services/public';

  import Pagination from '@/components/base/Pagination.vue';
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';
  import { filters } from '@/utils/filters';

  const props = defineProps({
    teacher: {
      type: Object as PropType<TeacherInterface>,
      required: true
    }
  });

  const route = useRoute();
  const router = useRouter();
  const stats = ref<any>({});
  const loading = ref(false);

  const {
    items: reviews,
    metadata,
    changePage,
    parseQueryAndFetch
  } = useGoList({
    fetchListFnc: async (params: any) => {
      const res = await reviewsList(params);
      stats.value = res.reviews.stats;

      return res;
    },
    fetchKey: 'reviews',
    route: route,
    router: router,
    perPage: 10,
    reflectUrl: false,
    extraParams: {
      targetID: props.teacher.id?.toString(),
      targetType: COMMENT_TARGET_TYPE.TEACHER
    }
  });

  function fetchList() {
    loading.value = true;

    parseQueryAndFetch({}).finally(() => {
      loading.value = false;
    });
  }

  onBeforeMount(() => {
    fetchList();
  });

  function getStarPercentage(star: number) {
    if (stats.value.total === 0) {
      return 0;
    }
    const starCount = stats.value[`star${star}`] || 0;
    return Number(((starCount / stats.value.total) * 100).toFixed(2));
  }

  defineExpose({ fetchList });
</script>

<style scoped lang="scss">
  $primary-color: #f1b44c;
  $text-color: #495057;
  $dark-color: #343a40;
  $border-radius: 12px;
  $box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  $transition: all 0.3s ease;

  @mixin flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .reviews-section {
    .reviews-summary {
      background-color: white;
      border-radius: $border-radius;
      padding: 30px;
      margin-bottom: 30px;
      box-shadow: $box-shadow;
    }

    .rating-overview {
      display: flex;
      gap: 40px;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 30px;
      }
    }

    .average-rating {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding-right: 40px;
      border-right: 1px solid #e9ecef;

      @media (max-width: 768px) {
        padding-right: 0;
        padding-bottom: 30px;
        border-right: none;
        border-bottom: 1px solid #e9ecef;
      }
    }

    .rating-value {
      font-size: 48px;
      font-weight: 700;
      color: $dark-color;
      line-height: 1;
      margin-bottom: 10px;
    }

    .rating-stars {
      display: flex;
      color: $primary-color;
      font-size: 20px;
      margin-bottom: 10px;
    }

    .rating-count {
      font-size: 14px;
      color: $text-color;
    }

    .star-color {
      color: $primary-color;
    }

    .rating-bars {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 10px;
      justify-content: center;
    }

    .rating-bar-item {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .rating-label {
      width: 50px;
      font-size: 14px;
      color: $text-color;
    }

    .rating-bar {
      flex: 1;
      height: 8px;
      background-color: #e9ecef;
      border-radius: 4px;
      overflow: hidden;
    }

    .rating-fill {
      height: 100%;
      background-color: $primary-color;
      border-radius: 4px;
    }

    .reviews-list {
      margin-bottom: 30px;
      padding: 10px 0 30px;
    }

    .review-card {
      background-color: white;
      border-radius: $border-radius;
      padding: 25px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
      transition: $transition;
      margin-bottom: 20px;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
      }
    }

    .review-header {
      @include flex-between;
      margin-bottom: 15px;

      @media (max-width: 576px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }
    }

    .reviewer-info {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .reviewer-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
    }

    .reviewer-details {
      display: flex;
      flex-direction: column;
    }

    .reviewer-name {
      font-weight: 600;
      color: $dark-color;
    }

    .review-date {
      font-size: 14px;
      color: $text-color;
    }

    .review-rating {
      display: flex;
      color: $primary-color;

      i {
        font-size: 18px;

        &.inactive {
          color: #d1d5db;
        }
      }
    }

    .review-content {
      margin-bottom: 15px;

      p {
        font-size: 15px;
        line-height: 1.6;
        color: $text-color;
        display: -webkit-box;
        -webkit-line-clamp: 10;
        line-clamp: 10;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
        hyphens: auto;
        height: 100%;
      }
    }

    .review-course {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      color: $text-color;

      i {
        color: $primary-color;
      }
    }
  }

  .section-header {
    margin-bottom: 30px;
    text-align: center;
  }

  .section-title {
    font-size: 28px;
    font-weight: 700;
    color: $dark-color;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
  }

  .section-divider {
    width: 100px;
    height: 2px;
    background-color: rgba($primary-color, 0.3);
    margin: 0 auto;
  }
</style>
