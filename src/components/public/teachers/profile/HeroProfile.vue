<template>
  <div class="hero-section">
    <div class="hero-content">
      <div class="profile-card">
        <div class="avatar-wrapper">
          <div class="avatar-container">
            <img :src="teacher.imageUrl ? teacher.imageUrl : dummyAvatar" alt="Billiard Coach" class="teacher-avatar" />
            <div class="avatar-border"></div>
            <div class="rating-badge" v-if="teacher.averageRating">
              <i class="bx bxs-star"></i>
              <span>{{ Number(teacher.averageRating).toFixed(1) }}</span>
            </div>
          </div>
        </div>
        <div class="profile-info">
          <h1 class="teacher-name">{{ teacher.name }}</h1>
          <div v-if="teacher.isProfessional" class="teacher-title">
            {{ $t('public.teacher.hero_profile.common_title') }}
          </div>

          <div class="stats-container">
            <div class="stat-item">
              <div class="stat-icon"><i class="bx bx-book-open"></i></div>
              <div class="stat-content">
                <div class="stat-value">{{ teacher.approvedCourseCount }}</div>
                <div class="stat-label">{{ $t('public.course.title') }}</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon"><i class="bx bx-target-lock"></i></div>
              <div class="stat-content">
                <div class="stat-value">{{ teacher.approvedDrillCount || 0 }}</div>
                <div class="stat-label">{{ $t('public.drills.title') }}</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon"><i class="bx bx-user-check"></i></div>
              <div class="stat-content">
                <div class="stat-value">{{ teacher.studentCount || 0 }}</div>
                <div class="stat-label">{{ $t('public.course.student') }}</div>
              </div>
            </div>
          </div>

          <!-- TODO: separate address - get only city -->
          <div class="teacher-location">
            <i class="bx bx-map-pin"></i>
            <span>{{ teacher.address }}</span>
          </div>

          <div class="teacher-social d-flex flex-wrap gap-2">
            <a
              v-if="teacher.socialLinks?.facebook"
              :href="teacher.socialLinks?.facebook"
              target="_blank"
              class="social-icon"
              ><i class="bx bxl-facebook"></i
            ></a>
            <a
              v-if="teacher.socialLinks?.youtube"
              :href="teacher.socialLinks?.youtube"
              target="_blank"
              class="social-icon"
              ><i class="bx bxl-youtube"></i
            ></a>
            <a
              v-if="teacher.socialLinks?.instagram"
              :href="teacher.socialLinks?.instagram"
              target="_blank"
              class="social-icon"
              ><i class="bx bxl-instagram"></i
            ></a>
            <a
              v-if="teacher.socialLinks?.twitter"
              :href="teacher.socialLinks?.twitter"
              target="_blank"
              class="social-icon"
              ><i class="bx bxl-twitter"></i
            ></a>
            <a
              v-if="teacher.socialLinks?.telegram"
              :href="teacher.socialLinks?.telegram"
              target="_blank"
              class="social-icon"
              ><i class="bx bxl-telegram"></i
            ></a>
            <a
              v-if="teacher.socialLinks?.discord"
              :href="teacher.socialLinks?.discord"
              target="_blank"
              class="social-icon"
              ><i class="bx bxl-discord"></i
            ></a>
            <a v-if="teacher.socialLinks?.zalo" :href="teacher.socialLinks?.zalo" target="_blank" class="social-icon">
              <img src="@/assets/images/zalo-icon.svg" alt="Zalo" width="20" height="20" />
            </a>

            <a
              v-if="teacher.socialLinks?.tiktok"
              :href="teacher.socialLinks?.tiktok"
              target="_blank"
              class="social-icon"
            >
              <img src="@/assets/images/tiktok-icon.svg" alt="TikTok" width="20" height="20" />
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { TeacherInterface } from '@/utils/interface/public/teacher';
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';

  defineProps({
    teacher: {
      type: Object as PropType<TeacherInterface>,
      required: true
    }
  });
</script>

<style scoped lang="scss">
  $primary-color: #f1b44c;
  $secondary-color: #2a3042;
  $text-color: #495057;
  $dark-color: #343a40;
  $border-radius: 12px;
  $box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  $transition: all 0.3s ease;

  @mixin flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .hero-section {
    position: relative;
    padding: 0;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);

    .hero-content {
      position: relative;
      z-index: 1;
    }

    .profile-card {
      display: flex;
      background-color: rgba(255, 255, 255, 0.95);
      border-radius: $border-radius;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
      overflow: hidden;

      @media (max-width: 768px) {
        flex-direction: column;
      }
    }

    .avatar-wrapper {
      padding: 40px;
      background: linear-gradient(135deg, rgba($primary-color, 0.1) 0%, rgba($primary-color, 0.2) 100%);
      @include flex-center;

      @media (max-width: 768px) {
        padding: 30px;
      }
    }

    .avatar-container {
      position: relative;
      width: 200px;
      height: 200px;
      border-radius: 50%;
      overflow: visible;

      @media (max-width: 768px) {
        width: 150px;
        height: 150px;
      }
    }

    .teacher-avatar {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 50%;
      border: 4px solid white;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .avatar-border {
      position: absolute;
      top: -10px;
      left: -10px;
      right: -10px;
      bottom: -10px;
      border: 2px dashed $primary-color;
      border-radius: 50%;
      animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }

    .rating-badge {
      position: absolute;
      bottom: -10px;
      right: -10px;
      background-color: $primary-color;
      color: white;
      border-radius: 20px;
      padding: 5px 10px;
      font-size: 14px;
      font-weight: bold;
      display: flex;
      align-items: center;
      gap: 5px;
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);

      i {
        font-size: 16px;
      }
    }

    .profile-info {
      flex: 1;
      padding: 40px;

      @media (max-width: 768px) {
        padding: 30px;
        text-align: center;
      }
    }

    .teacher-name {
      font-size: 32px;
      font-weight: 700;
      color: $dark-color;
      margin-bottom: 5px;
    }

    .teacher-title {
      font-size: 16px;
      color: $primary-color;
      margin-bottom: 25px;
      font-weight: 500;
    }

    .stats-container {
      display: flex;
      gap: 30px;
      margin-bottom: 25px;

      @media (max-width: 768px) {
        justify-content: center;
        flex-wrap: wrap;
        gap: 20px;
      }
    }

    .stat-item {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .stat-icon {
      width: 50px;
      height: 50px;
      background-color: rgba($primary-color, 0.1);
      border-radius: 12px;
      @include flex-center;
      font-size: 24px;
      color: $primary-color;
    }

    .stat-content {
      display: flex;
      flex-direction: column;
    }

    .stat-value {
      font-size: 20px;
      font-weight: 700;
      color: $dark-color;
    }

    .stat-label {
      font-size: 14px;
      color: $text-color;
    }

    .teacher-location {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 25px;
      color: $text-color;

      i {
        color: $primary-color;
        font-size: 18px;
      }

      @media (max-width: 768px) {
        justify-content: center;
      }
    }

    .teacher-social {
      display: flex;
      gap: 15px;

      @media (max-width: 768px) {
        justify-content: center;
      }
    }

    .social-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba($secondary-color, 0.1);
      color: $secondary-color;
      @include flex-center;
      font-size: 20px;
      transition: $transition;

      &:hover {
        background-color: $primary-color;
        color: white;
        transform: translateY(-3px);
      }
    }
  }
</style>
