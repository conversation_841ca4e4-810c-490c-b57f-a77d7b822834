<template>
  <BCard no-body class="h-100 teacher-card border-0 rounded-4 mb-0">
    <router-link :to="`/teachers/${teacher.slug}`" class="text-decoration-none text-black">
      <BCardHeader class="rounded-top-4"></BCardHeader>
      <BCardBody class="d-flex flex-column align-items-center fw-bold">
        <div class="avatar-section">
          <BImg
            :src="teacher.imageUrl ? teacher.imageUrl : dummyAvatar"
            :alt="teacher.name"
            rounded="circle"
            class="teacher-avatar"
          />
        </div>
        <div class="d-flex flex-column gap-2 w-100">
          <h3 class="h5 text-center mb-0 fw-bold">{{ teacher.name }}</h3>

          <div v-if="teacher.averageRating && teacher.averageRating > 0" class="rating-custom mx-auto">
            <RatingForm v-model="averageRating" readonly />
          </div>

          <div class="d-flex justify-content-center gap-3 mb-2">
            <div class="teacher-info-course w-50 d-flex flex-column align-items-center gap-1 rounded-3 py-2">
              <i class="bx bx-book-open lh-1 text-warning font-size-18"></i>
              <h4 class="fw-bold m-0">{{ teacher?.courses?.length || 0 }}</h4>
              <span class="fw-normal">{{ $t('public.course.title') }}</span>
            </div>
            <div class="teacher-info-user w-50 d-flex flex-column align-items-center rounded-3 py-2">
              <i class="mdi mdi-account-multiple-outline lh-1 text-primary font-size-20"></i>
              <h3 class="fw-bold m-0">{{ teacher.joinedStudentCount || 0 }}</h3>
              <span class="fw-normal">{{ $t('public.course.student') }}</span>
            </div>
          </div>

          <pre class="fw-normal line-clamp-5 text-start pre-content text-center">{{ teacher.description }}</pre>
        </div>
      </BCardBody>
    </router-link>
  </BCard>
</template>

<script lang="ts" setup>
  import { TeacherInterface } from '@/utils/interface/public/teacher';

  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';
  import RatingForm from '@/components/base/RatingForm.vue';
  import { computed } from 'vue';

  const props = defineProps({
    teacher: {
      type: Object as PropType<TeacherInterface>,
      required: true
    }
  });

  const averageRating = computed(() => Math.round((props.teacher.averageRating || 0) * 10) / 10);
</script>

<style scoped lang="scss">
  $primary: #f1b44c;

  .teacher-card {
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease;
    background-color: white;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 32px rgba($primary, 0.15);
    }

    .teacher-info-user {
      background-color: #f0f8ff;
    }

    .teacher-info-course {
      background-color: #fffbee;
    }
  }

  .primary-color {
    color: $primary;
  }

  .avatar-placeholder,
  .teacher-avatar {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    object-fit: cover;
  }

  .avatar-placeholder {
    background: linear-gradient(135deg, darken($primary, 10%), $primary);
  }

  .description {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  @media (max-width: 768px) {
    .avatar-placeholder,
    .teacher-avatar {
      width: 70px;
      height: 70px;
    }
  }

  .card-header {
    position: relative;
    height: 100px;
    background-image: url('@/assets/images/banner-course-detail.png');
    overflow: hidden;
    img {
      position: absolute;
      top: -15px;
      right: -70px;
      transform: rotate(-30deg);
    }
    .vector-billiard {
      width: 200px;
    }
    .bandge-course {
      background-color: #ffffff5c !important;
    }
  }

  .card-body {
    margin-top: 30px;
    .avatar-section {
      position: absolute;
      top: 50px;
      left: 50%;
      transform: translate(-50%, 0);
      border: 4px solid #eaaa17;
      border-radius: 50%;
    }
  }
</style>
