<template>
  <div class="public-footer bg-white">
    <BContainer class="py-4 py-lg-5 text-center text-lg-start">
      <BRow class="m-0">
        <BCol cols="12" lg="4" class="mb-5 mb-lg-0">
          <div class="company-info">
            <img src="@/assets/images/logo-dark.png" height="50" />
            <div class="pt-3">Vung cơ, chạm đỉnh – Bi-a Việt vươn xa!</div>
            <div class="d-flex align-items-center justify-content-center justify-content-lg-start pt-3 gap-2">
              <a
                v-for="(item, idx) in socialIcons"
                :key="idx"
                :href="item.url"
                class="social-item d-flex justify-content-center align-items-center"
                target="_blank"
                rel="noopener noreferrer"
              >
                <i :class="item.icon" class="font-size-15 text-white"></i>
              </a>
            </div>
          </div>
        </BCol>
        <BCol cols="12" lg="4" class="mb-4 mb-lg-0">
          <div class="quick-link">
            <h5 class="pb-1 mb-2 mb-lg-3">{{ $t('public.footer.quick_link') }}</h5>

            <ul class="list-unstyled quick-list">
              <li v-for="(link, idx) in quickLinks" :key="idx" class="pb-1">
                <router-link
                  :to="link.to"
                  class="d-inline-flex align-items-center justify-content-center justify-content-lg-start"
                >
                  <i class="mdi mdi-chevron-right font-size-18"></i>{{ $t(link.label) }}
                </router-link>
              </li>
            </ul>
          </div>
        </BCol>
        <BCol cols="12" lg="4">
          <div class="contact-us">
            <h5 class="pb-1 mb-2 mb-lg-3">{{ $t('public.footer.contact_us') }}</h5>
            <ul class="list-unstyled">
              <li class="justify-content-center justify-content-lg-start">
                <i class="mdi mdi-map-marker font-size-18"></i>{{ $t('public.footer.address_info') }}
              </li>
              <li class="justify-content-center justify-content-lg-start">
                <i class="mdi mdi-email font-size-18"></i><EMAIL>
              </li>
              <li class="justify-content-center justify-content-lg-start">
                <i class="mdi mdi-phone font-size-18"></i>0972672838 / 0987736891
              </li>
              <!----<li class="justify-content-center justify-content-lg-start">
                <i class="mdi mdi-phone-in-talk font-size-18"></i>+123-456-789
              </li>-->
            </ul>
          </div>
        </BCol>
      </BRow>
    </BContainer>
    <div class="text-center text-muted font-size-11 py-2">Copyright © 2025 Vibico || All Rights Reserved</div>
  </div>
</template>

<script lang="ts" setup>
  const socialIcons = [
    { icon: 'bx bxl-facebook', url: 'https://www.facebook.com/vietnambilliardscommunity' }
    // { icon: 'bx bxs-envelope', url: 'mailto:<EMAIL>' },
    // { icon: 'bx bxl-github', url: 'https://github.com/BehemothLtd' },
    // { icon: 'bx bxl-linkedin', url: 'https://www.linkedin.com/company/behemoth-vn' }
  ];

  const quickLinks = [
    { to: '/', label: 'public.top_menu.home' },
    { to: '/courses', label: 'public.top_menu.courses' },
    { to: '/drills', label: 'public.top_menu.drills' },
    { to: '/teachers', label: 'public.top_menu.teacher' },
    { to: '/my-courses', label: 'public.top_menu.my_courses' }
  ];
</script>

<style lang="scss" scoped></style>
