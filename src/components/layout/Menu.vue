<template>
  <div class="menu-list d-block d-lg-flex">
    <div v-for="(item, index) in menuItems" :key="index" class="p-2 p-lg-0">
      <div class="menu-link">
        <router-link
          v-if="(item.requiresAuth && isLoggedIn) || !item.requiresAuth"
          :class="{ active: isMenuItemActive(item) }"
          :to="item.link!"
          @click="handleClick"
        >
          {{ item.label }}
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useTopMenu } from '@/composable/useTopNav';
  import { useWindowSize } from '@vueuse/core';

  defineProps({
    isLoggedIn: {
      type: Boolean,
      default: false
    }
  });

  const { menuItems, isMenuItemActive } = useTopMenu(true);
  const { width } = useWindowSize();

  const emits = defineEmits(['mobileMenuClick']);

  const isMobile = computed(() => width.value < 992);

  const handleClick = () => {
    if (isMobile.value) {
      emits('mobileMenuClick');
    }
  };
</script>
