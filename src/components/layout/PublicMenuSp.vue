<template>
  <div class="topnav d-block d-lg-none">
    <BContainer fluid>
      <nav class="navbar navbar-light navbar-expand-lg topnav-menu active">
        <div class="collapse navbar-collapse active" id="topnav-menu-content">
          <Menu :is-logged-in="isLoggedIn" />
        </div>
      </nav>
    </BContainer>
  </div>
</template>

<script lang="ts" setup>
  import Menu from '@/components/layout/Menu.vue';

  import { useAuthPublicStore } from '@/store/public/auth';

  const authPublicStore = useAuthPublicStore();

  const isLoggedIn = computed(() => !!authPublicStore.accessToken);
</script>
