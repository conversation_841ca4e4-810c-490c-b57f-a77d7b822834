<template>
  <BRow>
    <BCol cols="12">
      <div class="page-title-box d-sm-flex align-items-center justify-content-between">
        <h4 class="mb-sm-0 font-size-18">{{ breadcrumb.title }}</h4>

        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li
              class="breadcrumb-item"
              v-for="(item, index) in breadcrumb.items"
              :key="index"
              :class="{ active: index === breadcrumb.items.length - 1 }"
            >
              <BLink v-if="item.href" :to="item.href">{{ item.text }}</BLink>
              <span v-else>{{ item.text }}</span>
            </li>
          </ol>
        </div>
      </div>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import { useBreadcrumb } from '@bachdx/b-vuse';

  const { breadcrumb, getBreadcrumb } = useBreadcrumb({});

  getBreadcrumb();

  defineProps({
    title: {
      type: String,
      default: ''
    },
    pageTitle: {
      type: String,
      default: ''
    }
  });
</script>
