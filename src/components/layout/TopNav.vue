<template>
  <div class="topnav">
    <BContainer fluid>
      <nav class="navbar navbar-light navbar-expand-lg topnav-menu active">
        <div class="collapse navbar-collapse active" id="topnav-menu-content">
          <ul class="navbar-nav">
            <template v-for="(item, index) in menuItems" :key="index">
              <li class="nav-item dropdown" :class="{ active: isMenuItemActive(item) }">
                <router-link
                  v-if="!hasItems(item)"
                  class="nav-link dropdown-toggle arrow-none"
                  :id="'topnav-components-' + index"
                  :to="item.link!"
                  role="button"
                >
                  <i :class="`bx ${item.icon} me-2`"></i>{{ item.label }}
                  <div class="arrow-down" v-if="hasItems(item)"></div>
                </router-link>

                <BLink
                  v-else
                  class="nav-link dropdown-toggle arrow-none"
                  :id="'topnav-components-' + index"
                  role="button"
                >
                  <i :class="`bx ${item.icon} me-1`"></i>
                  {{ item.label }}
                  <div class="arrow-down"></div>
                </BLink>

                <div
                  v-if="hasItems(item)"
                  class="dropdown-menu"
                  aria-labelledby="topnav-dashboard"
                  :class="{ 'dropdown-mega-menu-xl px-2': Array.isArray(item.subItems) && item.subItems.length > 11 }"
                >
                  <template v-for="(subitem, subIndex) in item.subItems" :key="subIndex">
                    <router-link
                      v-if="item.subItems!.length < 12 && !hasItems(subitem)"
                      class="col dropdown-item side-nav-link-ref"
                      :class="{ active: isMenuItemActive(subitem) }"
                      :to="subitem.link!"
                    >
                      {{ subitem.label }}
                    </router-link>

                    <div v-else-if="item.subItems!.length > 11">
                      <BRow v-if="subIndex % 3 === 0">
                        <BCol lg="4">
                          <router-link class="dropdown-item side-nav-link-ref" :to="item.subItems![subIndex].link!">
                            {{ item.subItems![subIndex].label }}
                          </router-link>
                        </BCol>
                        <BCol v-if="item.subItems![subIndex + 1]?.link" lg="4">
                          <router-link class="dropdown-item side-nav-link-ref" :to="item.subItems![subIndex + 1].link!">
                            {{ item.subItems![subIndex + 1].label }}
                          </router-link>
                        </BCol>
                        <BCol v-if="item.subItems![subIndex + 2]" lg="4">
                          <router-link class="dropdown-item side-nav-link-ref" :to="item.subItems![subIndex + 2].link!">
                            {{ item.subItems![subIndex + 2].label }}
                          </router-link>
                        </BCol>
                      </BRow>
                    </div>

                    <div v-if="hasItems(subitem)" class="dropdown" :key="subIndex">
                      <BLink class="dropdown-item dropdown-toggle" href="javascript:void(0);">
                        {{ subitem.label }}
                        <div class="arrow-down"></div>
                      </BLink>
                      <div class="dropdown-menu">
                        <template v-for="(subSubitem, subSubIndex) in subitem.subItems" :key="subSubIndex">
                          <router-link
                            v-if="!hasItems(subSubitem)"
                            class="dropdown-item side-nav-link-ref"
                            :class="{ active: isMenuItemActive(subSubitem) }"
                            :to="subSubitem.link!"
                          >
                            {{ subSubitem.label }}
                          </router-link>
                          <div v-else class="dropdown" :key="subSubIndex">
                            <BLink class="dropdown-item dropdown-toggle" href="javascript:void(0);">
                              {{ subSubitem.label }}
                              <div class="arrow-down"></div>
                            </BLink>
                            <div class="dropdown-menu">
                              <template
                                v-for="(subSubSubitem, subSubSubIndex) in subSubitem.subItems"
                                :key="subSubSubIndex"
                              >
                                <router-link
                                  class="dropdown-item side-nav-link-ref"
                                  :class="{ active: isMenuItemActive(subSubSubitem) }"
                                  :to="subSubSubitem.link!"
                                  routerLinkActive="active"
                                >
                                  {{ subSubSubitem.label }}
                                </router-link>
                              </template>
                            </div>
                          </div>
                        </template>
                      </div>
                    </div>
                  </template>
                </div>
              </li>
            </template>
          </ul>
        </div>
      </nav>
    </BContainer>
  </div>
</template>

<script lang="ts" setup>
  import { useTopMenu } from '@/composable/useTopNav';

  const { menuItems, hasItems, isMenuItemActive } = useTopMenu();
</script>
