<template>
  <section class="why-choose-us-section w-100">
    <BContainer>
      <div class="section-header text-center">
        <h2 class="section-title">{{ $t('public.homepage.about-us.heading') }}</h2>
        <p class="section-subtitle">{{ $t('public.homepage.about-us.description') }}</p>
      </div>

      <BRow class="mt-5 mx-0 justify-content-center justify-content-lg-between">
        <BCol lg="4" md="6" class="mb-4">
          <div class="feature-card">
            <div class="feature-icon">
              <img src="/src/assets/images/homepage/check-item.svg" alt="Best Tutors" />
            </div>
            <h3 class="feature-title">{{ $t('public.homepage.about-us.best-tutor.title') }}</h3>
            <p class="feature-description">
              {{ $t('public.homepage.about-us.best-tutor.description') }}
            </p>
          </div>
        </BCol>

        <BCol lg="4" md="6" class="mb-4">
          <div class="feature-card">
            <div class="feature-icon">
              <img src="/src/assets/images/homepage/infinity.svg" alt="Flexible Learning" />
            </div>
            <h3 class="feature-title">{{ $t('public.homepage.about-us.flexible.title') }}</h3>
            <p class="feature-description">
              {{ $t('public.homepage.about-us.flexible.description') }}
            </p>
          </div>
        </BCol>

        <BCol lg="4" md="6" class="mb-4">
          <div class="feature-card">
            <div class="feature-icon">
              <img src="/src/assets/images/homepage/connect.svg" alt="Easy Access" />
            </div>
            <h3 class="feature-title">{{ $t('public.homepage.about-us.easy-access.title') }}</h3>
            <p class="feature-description">
              {{ $t('public.homepage.about-us.easy-access.description') }}
            </p>
          </div>
        </BCol>
      </BRow>
    </BContainer>
  </section>
</template>

<style lang="scss" scoped>
  .why-choose-us-section {
    background: white;

    .feature-card {
      background: white;
      border-radius: 12px;
      padding: 2rem;
      height: 100%;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      border-bottom: 4px solid transparent;

      &:hover {
        transform: translateY(-10px);
        border-bottom: 4px solid #f39c12;
      }

      @media (max-width: 765px) {
        padding: 1rem;
      }
    }

    .feature-icon {
      width: 70px;
      height: 70px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8f9fa;
      border-radius: 50%;
      margin-bottom: 1.5rem;

      img {
        width: 40px;
        height: 40px;
        object-fit: contain;
      }
    }

    .feature-title {
      font-size: 1.3rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }

    .feature-description {
      color: #7f8c8d;
      line-height: 1.6;
    }
  }
</style>
