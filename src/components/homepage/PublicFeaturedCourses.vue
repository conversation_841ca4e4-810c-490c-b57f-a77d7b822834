<template>
  <section v-if="items.length" class="featured-courses-section w-100">
    <BContainer>
      <div class="section-header">
        <h2 class="section-title">{{ $t('public.homepage.features.heading') }}</h2>
        <p class="section-subtitle">
          {{ $t('public.homepage.features.description') }}
        </p>
      </div>

      <BRow class="mx-0 mb-3 mb-lg-0">
        <BCol lg="3" md="6" v-for="(course, index) in items.slice(0, 8)" :key="index" class="mb-4 d-none d-md-block">
          <CourseCard :course="course" />
        </BCol>
        <div class="d-block d-md-none">
          <BaseCarousel
            :items="items.slice(0, 8)"
            :breakpoints="{
              578: { slidesPerView: 1 },
              768: { slidesPerView: 2 },
              1400: { slidesPerView: 3 }
            }"
          >
            <template #default="{ item }">
              <CourseCard :course="item" />
            </template>
          </BaseCarousel>
        </div>
      </BRow>

      <div v-if="items.length > 6" class="text-center mt-lg-4">
        <router-link to="/courses?orderBy=bestSeller" class="btn-primary-gradient">
          {{ $t('public.homepage.features.more-btn') }}
          <i class="mdi mdi-arrow-right"></i>
        </router-link>
      </div>
    </BContainer>
  </section>
</template>

<script lang="ts" setup>
  import BaseCarousel from '@/components/base/BaseCarousel.vue';
  import CourseCard from '@/components/base/CourseCard.vue';

  import { useGoList } from '@bachdx/b-vuse';
  import { courseList } from '@/services/public/repositories/course';
  import { CourseListQueryFormModel } from '@/forms/public/course';

  const route = useRoute();
  const router = useRouter();

  const { items, parseQueryAndFetch, orderBy } = useGoList({
    fetchListFnc: courseList,
    fetchKey: 'courses',
    route: route,
    router: router,
    queryFormModels: CourseListQueryFormModel,
    perPage: 10
  });

  onMounted(async () => {
    orderBy.value = 'bestSeller';
    await parseQueryAndFetch();
  });
</script>

<style lang="scss" scoped>
  .featured-courses-section {
    background-color: #f8f9fa;
    position: relative;
    overflow: hidden;

    &:before {
      content: '';
      position: absolute;
      width: 300px;
      height: 300px;
      border-radius: 50%;
      background: rgba(10, 61, 98, 0.03);
      top: -150px;
      left: -150px;
    }

    &:after {
      content: '';
      position: absolute;
      width: 200px;
      height: 200px;
      border-radius: 50%;
      background: rgba(243, 156, 18, 0.03);
      bottom: -100px;
      right: -100px;
    }

    .btn-primary-gradient {
      padding: 0.5rem 1.5rem;
    }
  }
</style>
