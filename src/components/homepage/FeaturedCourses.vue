<template>
  <div class="bg-white w-100 rounded-3" v-if="hasCourse">
    <BContainer>
      <BRow v-for="(category, index) in userCourseCategories" :key="index" class="mt-4 mt-lg-0 mx-0">
        <div
          v-if="category.list.length"
          class="user-course-view mb-0 mb-lg-4 px-0"
          :class="category.title == 'recent_uploaded' ? 'mt-lg-4' : 'mt-lg-5'"
        >
          <FadeInUp>
            <div class="category-header d-flex align-items-center justify-content-between mb-2">
              <h3 class="d-inline-block mb-2 mb-lg-3 category-title text-dark">
                <!----<i class="mdi font-size-20 me-1 category-icon" :class="category.icon"></i>-->
                {{ $t(`public.course.category.${category.title}`) }}
              </h3>
              <router-link
                :to="category.title == 'in_progress' ? '/my-courses' : `/courses?orderBy=${category.link}`"
                class="d-none d-md-flex align-items-center btn btn-warning rounded-5 view-all-btn gap-1 border-0 fw-medium"
              >
                <span>{{ $t('public.homepage.my-courses.view-all') }}</span>
                <i class="bx bx-chevron-right font-size-20"></i>
              </router-link>
              <router-link
                :to="category.title == 'in_progress' ? '/my-courses' : `/courses?orderBy=${category.link}`"
                class="d-inline-flex d-md-none mb-2 cursor-pointer"
              >
                <i class="mdi mdi-chevron-right font-size-26 text-warning lh-1"></i>
              </router-link>
            </div>
          </FadeInUp>

          <FadeInLeft>
            <BaseCarousel :items="category.list">
              <template #default="{ item }">
                <CourseCard :course="item" />
              </template>
            </BaseCarousel>
            <div
              class="d-flex d-lg-none justify-content-end"
              :class="category.title == 'in_progress' ? 'mt-0' : 'mt-4'"
            ></div>
          </FadeInLeft>
        </div>
      </BRow>
    </BContainer>
  </div>
</template>

<script lang="ts" setup>
  import BaseCarousel from '@/components/base/BaseCarousel.vue';
  import CourseCard from '@/components/base/CourseCard.vue';

  import { courseList } from '@/services/public/repositories/course';
  import { myCourseList } from '@/services/user/repositories/userCourse';

  import { useAuthPublicStore } from '@/store/public/auth';

  import { UserCourseInterface } from '@/utils/interface/user/userCourse';

  type categoryConfig = {
    icon: string;
    title: string;
    queryKey: string;
  };

  const authStore = useAuthPublicStore();

  const loading = ref(true);
  const userCourseCategories = ref<{ icon: string; title: string; link: string; list: UserCourseInterface[] }[]>([]);

  const accessToken = authStore.accessToken;
  const categoryConfigs: categoryConfig[] = accessToken
    ? [
        { icon: 'mdi-book-check-outline text-primary', title: 'in_progress', queryKey: 'inProgress' },
        { icon: 'mdi-file-upload-outline text-primary', title: 'recent_uploaded', queryKey: 'recentUploaded' },
        { icon: 'mdi-trending-up text-danger', title: 'best_selling', queryKey: 'bestSeller' }
      ]
    : [
        { icon: 'mdi-file-upload-outline text-primary', title: 'recent_uploaded', queryKey: 'recentUploaded' },
        { icon: 'mdi-trending-up text-danger', title: 'best_selling', queryKey: 'bestSeller' }
      ];

  const hasCourse = computed(() => userCourseCategories.value.some(category => category.list.length > 0));

  const fetchCategories = async () => {
    loading.value = true;

    const requests = await Promise.all(
      categoryConfigs.map(config => {
        const params = {
          input: { page: 1, perPage: 20 }
        };

        if (config.queryKey == 'inProgress') {
          return myCourseList({
            ...params,
            query: { categoryCont: config.queryKey }
          });
        }

        return courseList({
          ...params,
          query: {},
          orderBy: config.queryKey
        });
      })
    );

    userCourseCategories.value = requests.map((res, index) => {
      const key = categoryConfigs[index].queryKey == 'inProgress' ? 'myCourses' : 'courses';

      return {
        icon: categoryConfigs[index].icon,
        title: categoryConfigs[index].title,
        link: categoryConfigs[index].queryKey,
        list: res[key]?.collection ?? []
      };
    });

    loading.value = false;
  };

  onMounted(() => {
    fetchCategories();
  });
</script>

<style lang="scss" scoped>
  .category-header {
    position: relative;
    margin-bottom: 30px;

    h3 {
      position: relative;
      padding-left: 15px;
      font-size: 1.5rem;
      font-weight: 800;
      color: #2a3042;
      display: inline-block;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 5px;
        background: #f1b44c;
        border-radius: 3px;
      }
    }

    .view-all-btn {
      padding: 0.5rem 1rem;
      transition: all 0.3s ease;
      background: linear-gradient(90deg, #f39c12, #e67e22);

      i {
        transition: transform 0.3s ease;
      }

      &:hover {
        background-color: #f1b44c;
        box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);

        i {
          transform: translateX(4px);
        }
      }
    }
  }
</style>
