<template>
  <section class="hero-section w-100">
    <div class="hero-overlay"></div>
    <BContainer>
      <BRow class="m-0 align-items-center">
        <BCol lg="6" class="hero-content">
          <div class="rating-badge">
            <i class="mdi mdi-star"></i>
            <i class="mdi mdi-star"></i>
            <i class="mdi mdi-star"></i>
            <i class="mdi mdi-star"></i>
            <i class="mdi mdi-star-half"></i>
            <span>4.9/5</span>
            <span class="d-none d-md-block">- {{ $t('public.homepage.hero-section.rating.label') }}</span>
          </div>
          <h1 class="hero-title">
            <span class="text-gradient">{{ $t('public.homepage.hero-section.heading.line-1') }}</span>
            <span class="d-block">{{ $t('public.homepage.hero-section.heading.line-2') }}</span>
            <span class="text-accent d-block">{{ $t('public.homepage.hero-section.heading.line-3') }}</span>
          </h1>
          <p class="hero-description">
            {{ $t('public.homepage.hero-section.description') }}
          </p>
          <div class="hero-cta">
            <router-link to="/register" class="btn-primary-gradient">
              {{ $t('public.homepage.hero-section.btn_start') }}
              <i class="mdi mdi-arrow-right"></i>
            </router-link>
            <router-link to="/courses" class="btn-outline">
              {{ $t('public.homepage.hero-section.btn_explore') }}
            </router-link>
          </div>
        </BCol>
        <BCol lg="6" class="hero-image-container">
          <img src="/src/assets/images/homepage/hero-image.webp" alt="Billiard Pro" class="hero-image" />
          <div v-if="stats?.totalUsers" class="floating-badge badge-students">
            <i class="mdi mdi-account-group"></i>
            <span>
              {{ filters.formatCount(stats.totalUsers) }} {{ $t('public.homepage.hero-section.label.student') }}
            </span>
          </div>
          <div v-if="stats?.totalCourses" class="floating-badge badge-courses">
            <i class="mdi mdi-book-open-variant"></i>
            <span>
              {{ filters.formatCount(stats.totalCourses) }} {{ $t('public.homepage.hero-section.label.course') }}
            </span>
          </div>
        </BCol>
      </BRow>
    </BContainer>
  </section>
</template>

<script lang="ts" setup>
  import filters from '@/utils/filters';

  import { fetchOverviewStats } from '@/services/public/repositories/stats';
  import { OverviewStatsInterface } from '@/utils/interface/public/stats';

  const stats = ref<OverviewStatsInterface>();

  onMounted(async () => {
    const { data } = await fetchOverviewStats();
    stats.value = data.overviewStats;
  });
</script>

<style lang="scss" scoped>
  .hero-section {
    position: relative;
    padding: 8rem 0;
    background: linear-gradient(135deg, #f6f9fc, #eef2f7);
    overflow: hidden;

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%230a3d62' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
      opacity: 0.5;
    }

    // Add animated gradient overlay
    &:after {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(243, 156, 18, 0.05) 0%, rgba(10, 61, 98, 0.05) 100%);
      transform: rotate(-15deg);
      z-index: 1;
      animation: gradientMove 15s ease-in-out infinite alternate;
    }

    @media (max-width: 992px) {
      padding: 4rem 0;
    }

    @media (max-width: 765px) {
      padding: 3rem 0;
    }
  }

  .hero-content {
    position: relative;
    z-index: 2;
  }

  .rating-badge {
    display: inline-flex;
    align-items: center;
    background: white;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

    i {
      color: #f39c12;
      margin-right: 2px;
    }

    span {
      font-weight: 500;
    }
  }

  .hero-title {
    font-size: 40px !important;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;

    .text-gradient {
      background: linear-gradient(90deg, #0a3d62, #f39c12);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      color: transparent;
    }

    .text-accent {
      color: #f39c12;
    }

    @media (max-width: 1200px) {
      font-size: 34px !important;
    }

    @media (max-width: 765px) {
      font-size: 25px !important;
    }
  }

  .hero-description {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin-bottom: 2.5rem;
    max-width: 95%;
    @media (max-width: 768px) {
      max-width: 100%;
    }
  }

  .hero-cta {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;

    .btn-primary-gradient {
      position: relative;
      overflow: hidden;

      &:after {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        bottom: -50%;
        left: -50%;
        background: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 0),
          rgba(255, 255, 255, 0.2) 50%,
          rgba(255, 255, 255, 0)
        );
        transform: rotateZ(60deg) translate(-5em, 7.5em);
        animation: shine 3s infinite;
        pointer-events: none;
      }
    }

    @media (max-width: 768px) {
      gap: 0.5rem;
      justify-content: center;
    }
  }

  .hero-image-container {
    position: relative;

    .hero-image {
      width: 100%;
      border-radius: 12px;
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
      transition: all 0.5s ease;
      transform: perspective(1000px) rotateY(-5deg);
      border: 5px solid white;

      &:hover {
        transform: perspective(1000px) rotateY(0);
      }
    }

    // Add decorative elements
    &:before {
      content: '';
      position: absolute;
      width: 150px;
      height: 150px;
      border-radius: 50%;
      background: rgba(243, 156, 18, 0.1);
      top: -30px;
      right: -30px;
      z-index: -1;
    }

    &:after {
      content: '';
      position: absolute;
      width: 100px;
      height: 100px;
      border-radius: 50%;
      background: rgba(10, 61, 98, 0.1);
      bottom: -20px;
      left: -20px;
      z-index: -1;
    }
  }

  .floating-badge {
    position: absolute;
    display: flex;
    align-items: center;
    background: white;
    padding: 0.5rem 1.2rem;
    border-radius: 50px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    animation: float 3s ease-in-out infinite;

    i {
      font-size: 1.5rem;
      margin-right: 0.5rem;
    }

    &.badge-students {
      top: -10%;
      right: -5%;
      animation-delay: 0.5s;

      i {
        color: #0a3d62;
      }
    }

    &.badge-courses {
      bottom: -10%;
      left: -5%;
      animation-delay: 1s;

      i {
        color: #f39c12;
      }
    }

    @media (max-width: 992px) {
      &.badge-students {
        top: -5%;
      }
      &.badge-courses {
        bottom: -5%;
      }
    }

    @media (max-width: 768px) {
      padding: 0 0.5rem;
      &.badge-students {
        top: auto;
        bottom: -5%;
        right: 5%;
      }
      &.badge-courses {
        bottom: -5%;
        left: 5%;
      }
    }
  }

  @keyframes gradientMove {
    0% {
      transform: rotate(-15deg) translateY(0);
    }

    100% {
      transform: rotate(-15deg) translateY(-30px);
    }
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0);
    }

    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes shine {
    0% {
      transform: rotateZ(60deg) translate(-5em, 7.5em);
    }

    100% {
      transform: rotateZ(60deg) translate(15em, -10em);
    }
  }
</style>
