<template>
  <section class="testimonials-section w-100">
    <BContainer>
      <div class="section-header text-center">
        <h2 class="section-title">{{ $t('public.homepage.testimonials.heading') }}</h2>
        <p class="section-subtitle text-white">{{ $t('public.homepage.testimonials.description') }}</p>
      </div>

      <BRow class="mt-5 mx-0 justify-content-center justify-content-lg-between">
        <BCol lg="4" md="6" class="mb-4" v-for="(testimonial, index) in testimonials" :key="index">
          <div class="testimonial-card">
            <div class="testimonial-rating">
              <i class="mdi mdi-star" v-for="n in 5" :key="n"></i>
            </div>
            <p class="testimonial-text">{{ testimonial.text }}</p>
            <div class="testimonial-author">
              <img :src="testimonial.avatar" :alt="testimonial.name" class="author-avatar" />
              <div>
                <h4 class="author-name">{{ testimonial.name }}</h4>
                <p class="author-title">{{ testimonial.role }}</p>
              </div>
            </div>
          </div>
        </BCol>
      </BRow>
    </BContainer>
  </section>
</template>
<script lang="ts" setup>
  import i18n from '@/plugin/i18n';
  import avatarUser1 from '@/assets/images/homepage/user_1.png';
  import avatarUser2 from '@/assets/images/homepage/user_2.png';
  import avatarUser3 from '@/assets/images/homepage/user_3.png';

  const testimonials = computed(() => [
    {
      name: i18n.global.t('public.homepage.testimonials.user_1.name'),
      role: i18n.global.t('public.homepage.testimonials.user_1.role'),
      avatar: avatarUser1,
      text: i18n.global.t('public.homepage.testimonials.user_1.feedback')
    },
    {
      name: i18n.global.t('public.homepage.testimonials.user_2.name'),
      role: i18n.global.t('public.homepage.testimonials.user_2.role'),
      avatar: avatarUser2,
      text: i18n.global.t('public.homepage.testimonials.user_2.feedback')
    },
    {
      name: i18n.global.t('public.homepage.testimonials.user_3.name'),
      role: i18n.global.t('public.homepage.testimonials.user_3.role'),
      avatar: avatarUser3,
      text: i18n.global.t('public.homepage.testimonials.user_3.feedback')
    }
  ]);
</script>

<style lang="scss" scoped>
  .testimonials-section {
    background: linear-gradient(135deg, #0a3d62, #3c6382);
    color: white;

    .section-title,
    .section-subtitle {
      color: white;
    }

    .section-title:after {
      background: #f39c12;
    }
  }

  .testimonial-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.5rem;
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;

    &:hover {
      transform: translateY(-10px);
      background: rgba(255, 255, 255, 0.15);
    }
  }

  .testimonial-rating {
    margin-bottom: 1rem;

    i {
      color: #f39c12;
      margin-right: 2px;
    }
  }

  .testimonial-text {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-style: italic;
    flex-grow: 1;
  }

  .testimonial-author {
    display: flex;
    align-items: center;

    .author-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
      margin-right: 1rem;
      border: 2px solid #f39c12;
    }

    .author-name {
      font-weight: 700;
      margin-bottom: 0.2rem;
    }

    .author-title {
      opacity: 0.8;
      margin: 0;
    }
  }
</style>
