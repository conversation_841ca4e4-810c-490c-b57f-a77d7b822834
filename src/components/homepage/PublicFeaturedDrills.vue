<template>
  <section class="featured-drill-section w-100">
    <BContainer>
      <div class="section-header">
        <h2 class="section-title">{{ $t('public.homepage.featured-drills.title') }}</h2>
        <p class="section-subtitle text-white">
          {{ $t('public.homepage.featured-drills.subtitle') }}
        </p>
      </div>

      <BRow class="mx-0">
        <BCol
          cols="12"
          lg="8"
          class="drill-swiper-container mb-4 mb-lg-0 d-flex align-items-center justify-content-center"
        >
          <swiper
            ref="swiperRef"
            :effect="'cards'"
            :grabCursor="true"
            :modules="[EffectCards]"
            @slideChange="onSlideChange"
            class="featured-drill-carousel"
          >
            <SwiperSlide v-for="(item, index) in items" :key="index" class="w-100 rounded-4">
              <img
                class="btn p-0 w-100 drill-image img-fluid"
                :src="
                  item.diagrams && item.diagrams.length && item.diagrams[0].imageUrl
                    ? item.diagrams[0].imageUrl
                    : '/pool-table.png'
                "
              />
              <div v-if="index == activeIndex" class="drill-title text-center mt-3">{{ item.title }}</div>
            </SwiperSlide>
          </swiper>
        </BCol>
        <BCol cols="12" lg="4">
          <div class="mb-3 d-flex align-items-center gap-2">
            <i class="bx bxs-zap font-size-18"></i>
            <div class="list-step-title mb-0">{{ $t('public.homepage.featured-drills.steps.title') }}</div>
          </div>
          <div v-if="currentItemSteps.length">
            <div class="d-flex flex-wrap gap-2 mb-3">
              <BButton
                v-for="(step, idx) in currentItemSteps"
                :key="idx"
                variant="light"
                class="btn-step border-0"
                :active="selectedStep === idx"
                @click="selectStep(idx)"
              >
                {{ $t(`public.homepage.featured-drills.steps.step.title`, { stepNumber: idx + 1 }) }}
              </BButton>
            </div>

            <div class="step-card-wrapper position-relative mb-0">
              <BCard no-body text="dark" class="step-card mb-0 rounded-3 border text-white">
                <BCardBody class="custom-height">
                  <img
                    :src="currentItemSteps[selectedStep]?.diagramImage || '/pool-table.png'"
                    alt="Step Image"
                    width="100%"
                    class="step-img"
                  />
                  <pre class="step-description pre-content lh-sm mt-3 mb-0">{{
                    currentItemSteps[selectedStep]?.description
                  }}</pre>
                </BCardBody>
              </BCard>
              <router-link
                :to="`/drills/${items[activeIndex].slug}`"
                class="overlay-link position-absolute inset-0 d-flex justify-content-center align-items-center gap-2 px-3 py-1 rounded-5 text-white"
              >
                <i class="mdi mdi-eye font-size-20"></i> {{ $t('public.homepage.featured-drills.detail-btn') }}
              </router-link>
            </div>
          </div>

          <div v-else class="step-card-wrapper position-relative mb-0">
            <BCard no-body text="dark" class="step-card card-empty rounded-3 border text-white">
              <BCardBody class="d-flex align-items-center justify-content-center text-center custom-height">
                {{ $t('public.homepage.featured-drills.steps.data-empty') }}
              </BCardBody>
            </BCard>
            <router-link
              :to="`/drills/${items[activeIndex]?.slug}`"
              class="overlay-link position-absolute inset-0 d-flex justify-content-center align-items-center gap-2 px-3 py-1 rounded-5 text-white"
            >
              <i class="mdi mdi-eye font-size-20"></i> {{ $t('public.homepage.featured-drills.detail-btn') }}
            </router-link>
          </div>
        </BCol>
      </BRow>

      <div class="view-more-btn text-center mt-lg-4">
        <router-link to="/drills" class="btn-primary-gradient mt-4">
          {{ $t('public.homepage.featured-drills.more-btn') }}
          <i class="mdi mdi-arrow-right"></i>
        </router-link>
      </div>
    </BContainer>
  </section>
</template>

<script setup lang="ts">
  import { Swiper, SwiperSlide } from 'swiper/vue';
  import type { Swiper as SwiperType } from 'swiper';
  import { EffectCards } from 'swiper/modules';

  import { useGoList } from '@bachdx/b-vuse';
  import { drillList } from '@/services/public/repositories/drill';
  import { DrillListQueryFormModel } from '@/forms/public/drill';

  const route = useRoute();
  const router = useRouter();

  const swiperRef = ref<SwiperType | null>(null);
  const activeIndex = ref(0);
  const selectedStep = ref(0);

  const { items, parseQueryAndFetch } = useGoList({
    fetchListFnc: drillList,
    fetchKey: 'drills',
    route: route,
    queryFormModels: DrillListQueryFormModel,
    router: router,
    perPage: 3
  });

  const currentItemSteps = computed(() => items.value[activeIndex.value]?.step || []);

  const selectStep = (idx: number) => {
    selectedStep.value = idx;
  };

  const onSlideChange = (swiper: SwiperType) => {
    activeIndex.value = swiper.activeIndex;
    selectedStep.value = 0;
  };

  onMounted(async () => {
    await parseQueryAndFetch();
  });
</script>

<style lang="scss" scoped>
  .featured-drill-section {
    background: linear-gradient(135deg, #0a3d62, #3c6382);
    color: white;
    .drill-swiper-container {
      @media (max-width: 992px) {
        overflow: hidden;
      }
    }
    .featured-drill-carousel {
      width: 90%;
      @media (max-width: 992px) {
        width: 80%;
      }
    }
    .drill-title {
      font-size: 1.1rem;
      font-weight: 600;
      @media (max-width: 765px) {
        font-size: 1rem;
      }
    }
    .list-step-title {
      font-size: 1.2rem;
      font-weight: 600;
      @media (max-width: 765px) {
        font-size: 1.1rem;
      }
    }
    .btn-step {
      font-size: 13px;
      font-weight: 500;
      &.active {
        background-color: #f39c12 !important;
        color: white;
      }
    }

    .step-card {
      background-color: #ffffff21;
      .custom-height {
        height: 358px;
        @media (max-width: 1200px) {
          height: 325px;
        }
        @media (max-width: 992px) {
          height: 100%;
        }
      }
    }

    .step-card-wrapper {
      .step-card {
        transition: filter 0.3s ease;

        .step-description {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 8;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 1.4;
          max-height: calc(1.4em * 8);
        }
      }

      .overlay-link {
        opacity: 0;
        transition: opacity 0.3s ease;
        text-decoration: none;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.6);
      }

      &:hover {
        .step-card {
          filter: brightness(70%);
        }
        .overlay-link {
          opacity: 1;
        }
      }
    }

    .bxs-zap {
      color: #f39c11;
    }

    .view-more-btn {
      a {
        padding: 0.5rem 1.5rem;
      }
    }
  }
</style>
