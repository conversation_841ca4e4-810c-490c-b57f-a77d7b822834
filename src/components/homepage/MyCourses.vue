<template>
  <BContainer v-if="!!myCourses.length" fluid>
    <FadeInUp>
      <div name="My Courses Section" class="my-courses-section">
        <BRow class="pt-5">
          <div class="d-flex flex-column flex-md-row align-items-center justify-content-md-between pb-4 pb-lg-5 gap-2">
            <h2 class="framer-text framer-styles-preset-shv5t8">
              {{ $t('public.homepage.my-courses.title') }}
            </h2>
            <router-link to="/my-courses">
              <div class="framer-ktwg9w bg-gradient" name="Button">
                <div
                  class="framer-17fg6tt d-flex flex-column justify-content-start flex-shrink-0"
                  style="outline: none; transform: none"
                >
                  <p
                    style="
                      --framer-font-family: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
                      --framer-font-size: 18px;
                      --framer-font-weight: 500;
                      --framer-text-color: rgb(255, 255, 255);
                    "
                    class="framer-text"
                  >
                    <span class="framer-text framer-styles-preset-wo1gvy">
                      {{ $t('public.homepage.my-courses.view-all') }}
                    </span>
                  </p>
                </div>
              </div>
            </router-link>
          </div>

          <BCol lg="6" v-for="course in myCourses" :key="course.id" class="mb-4">
            <router-link :to="`/my-courses/${course.slug}`" class="d-flex flex-column flex-xxl-row gap-3">
              <img class="d-block object-fit-cover rounded-4" :src="course.banner ? course.banner : dummyBanner" />

              <div class="course-info min-h-135 d-flex flex-column justify-content-between">
                <div class="pb-4 pb-md-0">
                  <h3 class="framer-text framer-styles-preset-x7mwn6 line-clamp-2 mb-2">
                    {{ course.title }}
                  </h3>
                  <p class="framer-text">
                    <span class="line-clamp-5">
                      {{ course.description }}
                    </span>
                  </p>
                </div>
                <div>
                  <div class="text-black d-flex justify-content-between mb-2 w-100">
                    <span>
                      {{ $t('user.user_course.progress') }}
                    </span>
                    <span>{{ course.processPercent }}%</span>
                  </div>
                </div>
              </div>
            </router-link>
          </BCol>
        </BRow>
      </div>
    </FadeInUp>
  </BContainer>
</template>

<script lang="ts" setup>
  import { myCourseList } from '@/services/user/repositories/userCourse';
  import { UserCourseInterface } from '@/utils/interface/user/userCourse';

  import dummyBanner from '@/assets/images/dummy_banner.png';

  const myCourses = ref<UserCourseInterface[]>([]);

  const fetchCourses = async () => {
    const data = await myCourseList({
      input: { page: 1, perPage: 4 },
      query: { categoryCont: 'inProgress' }
    });
    myCourses.value = data.myCourses.collection;
  };

  onMounted(async () => {
    await fetchCourses();
  });
</script>
