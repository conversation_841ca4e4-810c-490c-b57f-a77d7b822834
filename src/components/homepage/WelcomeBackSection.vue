<template>
  <section class="welcome-section w-100">
    <BContainer>
      <BCard
        no-body
        class="welcome-card mb-0 w-100 d-flex flex-column flex-lg-row justify-content-between gap-3 gap-lg-0 align-items-center bg-white shadow rounded-4 p-4"
      >
        <div class="d-flex flex-column flex-lg-row align-items-center gap-3">
          <div class="avatar">
            <BAvatar
              variant="warning"
              :src="userProfile?.imageUrl ? userProfile.imageUrl : dummyAvatar"
              size="80"
              class="mt-0"
            ></BAvatar>
          </div>
          <div class="greeting">
            <h1>
              {{ $t('teacher.welcome-back-section.greeting') }},
              {{ userProfile?.name || $t('teacher.welcome-back-section.user') }}!
            </h1>
            <p>{{ $t('teacher.welcome-back-section.wish') }}</p>
          </div>
        </div>
        <BButton class="btn-primary-gradient border-0 font-size-13" @click="router.push('/profile')">
          {{ $t('teacher.welcome-back-section.edit-profile') }}
        </BButton>
      </BCard>
    </BContainer>
  </section>
</template>

<script lang="ts" setup>
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';

  import { storeToRefs } from 'pinia';
  import { useUserAuthStore } from '@/store/user/auth';

  const userAuthStore = useUserAuthStore();
  const { userProfile } = storeToRefs(userAuthStore);

  const router = useRouter();
</script>

<style scoped lang="scss">
  .welcome-section {
    padding: 0 0 2.5rem 0;
  }

  .avatar {
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    border: 3px solid lighten(#f1b44c, 25%);
  }

  .greeting {
    h1 {
      font-size: 1.5rem;
      font-weight: 700;
      margin: 0 0 5px 0;
      color: #2a3042;
      @media (max-width: 768px) {
        font-size: 1.25rem;
      }
    }

    p {
      font-size: 1rem;
      color: #74788d;
      margin: 0;
      @media (max-width: 768px) {
        font-size: 0.75rem;
      }
    }
  }
</style>
