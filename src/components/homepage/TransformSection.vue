<template>
  <section class="cta-section w-100">
    <BContainer>
      <div class="cta-content text-center">
        <h2 class="cta-title">{{ $t('public.homepage.transform.title') }}</h2>
        <p class="cta-description">
          {{ $t('public.homepage.transform.description') }}
        </p>
        <div class="cta-buttons">
          <router-link to="/register" class="btn-primary-gradient">
            {{ $t('public.homepage.transform.button_register') }}
            <i class="mdi mdi-arrow-right"></i>
          </router-link>
          <router-link to="/courses" class="btn-outline">
            {{ $t('public.homepage.transform.button_view') }}
          </router-link>
        </div>
      </div>
    </BContainer>
  </section>
</template>
<style lang="scss" scoped>
  .cta-section {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    text-align: center;
  }

  .cta-content {
    max-width: 800px;
    margin: 0 auto;
  }

  .cta-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    @media (max-width: 768px) {
      font-size: 1.75rem;
    }
  }

  .cta-description {
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    @media (max-width: 768px) {
      font-size: 1rem;
    }
  }

  .cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;

    .btn-primary-gradient {
      background: white;
      color: #f39c12;
      box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);

      &:hover {
        box-shadow: 0 8px 25px rgba(255, 255, 255, 0.4);
      }
    }

    .btn-outline {
      border-color: white;
      color: white;

      &:hover {
        background: white;
        color: #f39c12;
      }
    }

    @media (max-width: 768px) {
      gap: 0.75rem;
    }
  }
</style>
