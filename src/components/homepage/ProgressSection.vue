<template>
  <section class="progress-section w-100 rounded-3 bg-white mb-4 py-4">
    <BContainer>
      <div class="section-header d-flex align-items-center justify-content-between mb-4 px-2">
        <h2>{{ $t('public.homepage.progress.title') }}</h2>
        <div class="d-flex align-items-center">
          <div class="progress-stat">
            <div class="stat-value">{{ averageCourseProcessPercent }}%</div>
            <div class="stat-label">{{ $t('public.homepage.progress.complete') }}</div>
          </div>
        </div>
      </div>

      <BRow class="mx-0 d-flex justify-content-between align-items-center">
        <BCol lg="3" md="6">
          <BCard no-body class="stat-card h-100">
            <i class="bx bx-book-open stat-icon"></i>
            <div class="stat-value">{{ stats?.totalCoursesInProcess }}</div>
            <div class="stat-label">{{ $t('public.homepage.progress.stats.in-progress-courses') }}</div>
          </BCard>
        </BCol>
        <BCol lg="3" md="6">
          <BCard no-body class="stat-card">
            <i class="bx bx-trophy stat-icon"></i>
            <div class="stat-value">{{ stats?.totalCoursesCompleted }}</div>
            <div class="stat-label">{{ $t('public.homepage.progress.stats.completed-courses') }}</div>
          </BCard>
        </BCol>
        <BCol lg="3" md="6">
          <BCard no-body class="stat-card">
            <i class="bx bx-time stat-icon"></i>
            <div class="stat-value">{{ stats?.totalHoursStudied }}</div>
            <div class="stat-label">{{ $t('public.homepage.progress.stats.total-hours') }}</div>
          </BCard>
        </BCol>
        <BCol lg="3" md="6">
          <BCard no-body class="stat-card">
            <i class="bx bx-check-shield stat-icon"></i>
            <div class="stat-value">{{ stats?.totalPracticeSubmissionsApproved }}</div>
            <div class="stat-label">{{ $t('public.homepage.progress.stats.practice-approved') }}</div>
          </BCard>
        </BCol>
      </BRow>
    </BContainer>
  </section>
</template>
<script lang="ts" setup>
  import { fetchProfileStats } from '@/services/user/repositories/stats';
  import { ProfileStatsInterface } from '@/utils/interface/user/stats';

  const stats = ref<ProfileStatsInterface>();

  const averageCourseProcessPercent = computed(() => Math.round(stats.value?.averageCourseProcess ?? 0));

  onMounted(async () => {
    const { data } = await fetchProfileStats();
    stats.value = data.profileStats;
  });
</script>

<style lang="scss" scoped>
  .section-header {
    h2 {
      font-size: 1.5rem;
      font-weight: 700;
      color: #2a3042;
      margin: 0;
      @media (max-width: 768px) {
        font-size: 1.25rem;
      }
    }

    .view-all {
      display: flex;
      align-items: center;
      color: #f1b44c;
      font-weight: 600;
      text-decoration: none;
      transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

      i {
        margin-left: 4px;
        font-size: 1.2rem;
      }

      &:hover {
        color: darken(#f1b44c, 10%);
      }
    }
  }

  .progress-stat {
    min-width: 100px;

    .stat-value {
      font-size: 1.8rem;
      font-weight: 800;
      color: #f1b44c;
      @media (max-width: 768px) {
        font-size: 1.5rem;
      }
    }

    .stat-label {
      font-size: 0.9rem;
      color: #74788d;
    }
  }

  .stat-card {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    padding: 24px;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .stat-icon {
      font-size: 2.5rem;
      color: #f1b44c;
      margin-bottom: 15px;
    }

    .stat-value {
      font-size: 2rem;
      font-weight: 700;
      color: #2a3042;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 0.9rem;
      color: #74788d;
    }
  }
</style>
