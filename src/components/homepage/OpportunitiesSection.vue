<template>
  <section class="opportunities-section">
    <BContainer>
      <BRow class="align-items-center mx-0">
        <BCol lg="6" class="mb-5 mb-lg-0">
          <div class="opportunities-image">
            <img src="/src/assets/images/homepage/opportunities-image.webp" alt="Opportunities" />
            <div class="image-overlay"></div>
          </div>
        </BCol>
        <BCol lg="6">
          <div class="opportunities-content">
            <h2 class="section-title">{{ $t('public.homepage.opportunities.title') }}</h2>
            <p class="section-description">
              {{ $t('public.homepage.opportunities.description') }}
            </p>

            <div class="opportunity-item">
              <div class="opportunity-icon">
                <img src="/src/assets/images/homepage/skill.svg" alt="Skill Development" />
              </div>
              <div>
                <h3 class="opportunity-title">{{ $t('public.homepage.opportunities.sections.section-1.title') }}</h3>
                <p class="opportunity-description">
                  {{ $t('public.homepage.opportunities.sections.section-1.description') }}
                </p>
              </div>
            </div>

            <div class="opportunity-item">
              <div class="opportunity-icon">
                <img src="/src/assets/images/homepage/progress.svg" alt="Career Progress" />
              </div>
              <div>
                <h3 class="opportunity-title">{{ $t('public.homepage.opportunities.sections.section-2.title') }}</h3>
                <p class="opportunity-description">
                  {{ $t('public.homepage.opportunities.sections.section-2.description') }}
                </p>
              </div>
            </div>

            <div class="start-btn">
              <router-link to="/register" class="btn-primary-gradient mt-4">
                {{ $t('public.homepage.opportunities.button') }}
                <i class="mdi mdi-arrow-right"></i>
              </router-link>
            </div>
          </div>
        </BCol>
      </BRow>
    </BContainer>
  </section>
</template>

<style lang="scss" scoped>
  .opportunities-section {
    background: #f8f9fa;
    .section-title {
      text-align: left;
      &::after {
        left: 0;
        transform: none;
      }
      @media (max-width: 992px) {
        display: block;
        text-align: center;
        &::after {
          left: 50%;
          transform: translate(-50%, 0);
        }
      }
    }
    .section-description {
      @media (max-width: 992px) {
        text-align: center;
      }
    }
    .btn-primary-gradient {
      padding: 0.5rem 1.5rem;
    }
  }

  .opportunities-image {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

    img {
      width: 100%;
      display: block;
    }

    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to right, rgba(10, 61, 98, 0.3), rgba(10, 61, 98, 0));
    }
  }

  .opportunities-content {
    padding: 0 0 0 2rem;

    @media (max-width: 768px) {
      padding: 0;
    }
  }

  .section-description {
    color: #7f8c8d;
    margin-bottom: 2rem;
    line-height: 1.6;
  }

  .opportunity-item {
    display: flex;
    margin-bottom: 2rem;

    .opportunity-icon {
      flex-shrink: 0;
      width: 60px;
      height: 60px;
      background: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 1.5rem;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

      img {
        width: 30px;
        height: 30px;
        object-fit: contain;
      }
    }

    .opportunity-title {
      font-size: 1.2rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }

    .opportunity-description {
      color: #7f8c8d;
      line-height: 1.6;
    }
  }

  .start-btn {
    @media (max-width: 992px) {
      text-align: center;
    }
  }
</style>
