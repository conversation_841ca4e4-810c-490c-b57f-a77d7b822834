<template>
  <FadeInDown>
    <div v-if="validVideos.length" class="video-content-player embed-responsive embed-responsive-16by9">
      <CarouselVideo :videos="validVideos" @SlideChange="onSlideChange" :disableChanged="blockAccess">
        <template #slide="{ video, index }">
          <VideoPlayer
            :key="video.id"
            :ref="el => setVideoPlayerRef(el, index)"
            :video-id="String(video.id)"
            class="mw-100"
            :title="video.title"
            :thumbnail="video.thumbnailURL"
            :duration="video.duration"
            :current-position="video.currentPosition"
            @purchaseCourse="$emit('purchaseCourse')"
            @update-watch-progress="onUpdateWatchProgress"
            @video-ended="onVideoEnded"
            :blockAccess="blockAccess"
          />
        </template>

        <template #thumb="{ video }">
          <div :key="video.id" class="video-thumb-container">
            <img class="btn p-0 w-200 video-thumbnail-img" :src="video.thumbnailURL" :alt="video.title" />
            <i class="mdi mdi-play-circle"></i>
            <BBadge variant="danger" class="font-size-12 now-playing-badge">
              {{ $t('user.user_course.section_item.drill.now_watching') }}
            </BBadge>
          </div>
        </template>
      </CarouselVideo>
    </div>

    <QuillEditor v-if="lesson.content" v-model="lesson.content" read-only :site="ROUTE_PREFIX_ENUMS.USER" class="p-3" />
  </FadeInDown>
</template>

<script lang="ts" setup>
  import CarouselVideo from '@/components/base/CarouselVideo.vue';
  import QuillEditor from '@/components/base/quill/QuillEditor.vue';
  import VideoPlayer from '@/components/base/VideoPlayer.vue';

  import { CourseSectionItemInterface } from '@/utils/interface/user/courseSectionItem';
  import { ROUTE_PREFIX_ENUMS } from '@/utils/constant';

  import { markVideoProgress } from '@/services/user/repositories/userCourseSectionItem';

  import { useVideoProgress } from '@/composable/useVideoProgress';

  const props = defineProps({
    lesson: {
      type: Object as PropType<CourseSectionItemInterface>,
      required: true
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    blockAccess: {
      type: Boolean,
      default: false
    }
  });

  defineEmits(['purchaseCourse']);

  const videoPlayers = ref<InstanceType<typeof VideoPlayer>[]>([]);
  const currentSlideIndex = ref(0);
  const { updateVideoProgress } = useVideoProgress();

  const validVideos = computed(() => props.lesson.videos?.filter(video => video.isPlayable) || []);

  function setVideoPlayerRef(el: Element | ComponentPublicInstance | null, index: number) {
    if (el && '$el' in el) {
      videoPlayers.value[index] = el as InstanceType<typeof VideoPlayer>;
    }
  }

  const onVideoEnded = async () => {
    const progressData = getCurrentVideoProgress();

    if (progressData && progressData.progress && progressData.videoId) {
      try {
        await markVideoProgress(progressData.videoId, Math.round(progressData.progress));
      } catch (error) {
        console.error(error);
      }
      const currentVideo = validVideos.value.find(v => v.id === Number(progressData.videoId));

      if (currentVideo) {
        updateVideoProgress(
          String(props.lesson.id),
          String(currentVideo.id),
          Math.round(progressData.progress),
          currentVideo.duration
        );
      }
    }
  };

  function getCurrentVideoProgress() {
    const player = videoPlayers.value[currentSlideIndex.value];
    if (!player) return null;

    const progress = player.getCurrentTime?.() ?? 0;
    player.pause?.();

    const videoId = player.videoId;

    return {
      videoId: videoId,
      progress
    };
  }

  async function onUpdateWatchProgress(videoId: string, progress: number) {
    try {
      await markVideoProgress(videoId, progress);
    } catch (error) {
      console.error(error);
    } finally {
      const video = validVideos.value.find(v => v.id === Number(videoId));
      if (video) {
        updateVideoProgress(String(props.lesson.id), String(video.id), progress, video.duration);
      }
    }
  }

  async function onSlideChange(index: number) {
    const progressData = getCurrentVideoProgress();

    if (progressData && progressData.progress && progressData.videoId) {
      try {
        await markVideoProgress(progressData.videoId, Math.round(progressData.progress));
      } catch (error) {
        console.error(error);
      } finally {
        const prevVideo = validVideos.value.find(v => v.id === Number(progressData.videoId));
        if (prevVideo) {
          updateVideoProgress(String(props.lesson.id), String(prevVideo.id), progressData.progress, prevVideo.duration);
        }
      }
    }

    currentSlideIndex.value = index;
  }

  defineExpose({ getCurrentVideoProgress });
</script>

<style lang="scss" scoped>
  .video-thumb-container {
    position: relative;
    width: 100%;
    height: 120px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;

    @media (max-width: 765px) {
      height: 70px;
    }
  }

  .video-thumbnail-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
</style>
