<template>
  <div ref="imageContainer" class="content-course w-100 bg-white">
    <BTabs v-if="hasContent && lesson.drills?.length" class="default-tabs" lazy>
      <BTab active>
        <template v-slot:title>
          <span>{{ $t('user.user_course.section_item.tabs.content') }}</span>
        </template>
        <FadeInUp>
          <CoursePlayerContent
            ref="currentProgressRef"
            :lesson="lesson"
            :isPreview="isPreview"
            :blockAccess="blockAccess"
            @purchase-course="handlePurchaseCourse"
          />
        </FadeInUp>
      </BTab>

      <BTab>
        <template v-slot:title>
          <span>{{ $t('user.user_course.section_item.type.drill') }}</span>
        </template>
        <DrillSection
          v-if="lesson.drills.length"
          :lesson="lesson"
          :drills="lesson.drills"
          :loading="loading"
          :courseId="String(course.id)"
          :isPreview="isPreview"
          :blockAccess="blockAccess"
        />
      </BTab>
    </BTabs>

    <div v-else-if="hasContent && !lesson.drills?.length">
      <FadeInUp>
        <CoursePlayerContent :lesson="lesson" ref="currentProgressRef" :blockAccess="blockAccess" />
      </FadeInUp>
    </div>

    <div v-else-if="!hasContent && lesson.drills?.length">
      <DrillSection
        v-if="lesson.drills.length"
        :lesson="lesson"
        :drills="lesson.drills"
        :loading="loading"
        :courseId="String(course.id)"
        :isPreview="isPreview"
      />
    </div>

    <div v-else class="pb-5">
      <DataEmpty :message="$t('user.user_course.section_item.data_empty')" height="150" />
    </div>

    <div class="d-flex justify-content-between bg-body py-3 px-2 px-lg-0">
      <div>
        <Button
          v-if="prevLesson"
          class="step-action back-step"
          :disabled="completeSectionItemLoading || hasPrevLockedLesson()"
          @click="navigateTo(String(prevLesson.slug))"
        >
          <i class="mdi mdi-chevron-left"></i>
          {{ $t('user.user_course.section_item.prev_lesson') }}
        </Button>
      </div>
      <div>
        <Button
          v-if="nextLesson"
          class="step-action next-step"
          :disabled="completeSectionItemLoading || hasNextLockedLesson()"
          @click="navigateTo(String(nextLesson.slug))"
        >
          <i class="mdi mdi-chevron-right"></i>
          {{ $t('user.user_course.section_item.next_lesson') }}
        </Button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { CourseSectionItemInterface } from '@/utils/interface/user/courseSectionItem';
  import { UserCourseInterface } from '@/utils/interface/user/userCourse';
  import DataEmpty from '@/components/utility/DataEmpty.vue';
  import CoursePlayerContent from './CoursePlayerContent.vue';
  import DrillSection from '@/components/user/course/DrillSection.vue';
  import Button from '@/components/base/Button.vue';
  import { joinCourse } from '@/services/public/repositories/course';

  import { markVideoProgress } from '@/services/user/repositories/userCourseSectionItem';
  import { setSectionItemStatus } from '@/services/user/repositories/userCourseSectionItem';
  import { useVideoProgress } from '@/composable/useVideoProgress';

  import useMyCourse from '@/composable/useMyCourse';

  const { getProgress, setProgress } = useVideoProgress();

  const props = defineProps({
    course: {
      type: Object as PropType<UserCourseInterface>,
      required: true
    },
    lesson: {
      type: Object as PropType<CourseSectionItemInterface>,
      required: true
    },
    loading: {
      type: Boolean,
      required: false
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    blockAccess: {
      type: Boolean,
      default: false
    }
  });

  const emit = defineEmits(['fetchLesson']);

  const route = useRoute();
  const router = useRouter();

  const { completeSectionItemLoading } = useMyCourse();

  const currentProgressRef = ref();

  const contentCourseSlug = computed(() => route.query.courseContentSlug?.toString());

  const allLessons = computed(() => {
    return (
      props.course.courseSections?.reduce((acc, section) => {
        return acc.concat(section.courseSectionItems ?? []);
      }, [] as CourseSectionItemInterface[]) ?? []
    );
  });

  const currentIndex = computed(() => allLessons.value.findIndex(item => item.slug == contentCourseSlug.value));

  const prevLesson = computed(() => findAvailableLesson(currentIndex.value, 'prev'));

  const nextLesson = computed(() => findAvailableLesson(currentIndex.value, 'next'));

  const validVideo = computed(() => {
    return props.lesson.videos?.find(
      video => video.status === 'approved' && video.videoPlatforms?.some(p => p.status === 'available')
    );
  });

  const hasContent = computed(() => {
    return !!props.lesson.content || (!!props.lesson.videos?.length && validVideo.value);
  });

  const findAvailableLesson = (startIndex: number, direction: 'prev' | 'next') => {
    if (!props.blockAccess) {
      return direction === 'prev'
        ? currentIndex.value > 0
          ? allLessons.value[currentIndex.value - 1]
          : null
        : currentIndex.value < allLessons.value.length - 1
          ? allLessons.value[currentIndex.value + 1]
          : null;
    }
    let index = startIndex;
    while (index >= 0 && index < allLessons.value.length) {
      index = direction === 'prev' ? index - 1 : index + 1;
      const lesson = allLessons.value[index];
      if (lesson?.isFree) {
        return lesson;
      }
    }
    return null;
  };

  function hasNextLockedLesson(): boolean {
    if (!allLessons.value || allLessons.value.length <= 1) return false;

    const currentIdx = allLessons.value.findIndex(l => l.id === props.lesson.id);
    if (currentIdx === -1 || currentIdx === allLessons.value.length - 1) return false;

    const nextLesson = allLessons.value[currentIdx + 1];
    return !!nextLesson?.isLocked;
  }

  function hasPrevLockedLesson(): boolean {
    if (!allLessons.value || allLessons.value.length <= 1) return false;

    const currentIdx = allLessons.value.findIndex(l => l.id === props.lesson.id);
    if (currentIdx === -1 || currentIdx === 0) return false;

    const prevLesson = allLessons.value[currentIdx - 1];
    return !!prevLesson?.isLocked;
  }

  const navigateTo = async (lessonSlug: string) => {
    if (completeSectionItemLoading.value) return;

    const lesson = allLessons.value.find(item => item.slug === contentCourseSlug.value);
    if (!lesson) return;

    completeSectionItemLoading.value = true;

    const lessonId = lesson.id?.toString();
    const courseId = props.course.id?.toString();

    if (lessonId && courseId && !lesson.isCompleted && !props.isPreview) {
      try {
        await setSectionItemStatus(lessonId, courseId, 'completed');
        lesson.isCompleted = true;
      } catch (err) {
        console.error('Failed to mark lesson as complete:', err);
      }
    }

    if (props.isPreview && props.blockAccess) {
      emit('fetchLesson', lessonSlug);
    }
    window.scrollTo({ top: 0, behavior: 'smooth' });

    setTimeout(() => {
      router.push({
        name: route.name,
        params: route.params,
        query: {
          ...route.query,
          courseContentSlug: lessonSlug
        }
      });
    }, 300);

    completeSectionItemLoading.value = false;
  };

  const handlePurchaseCourse = () => {
    joinCourse(String(props.course?.id)).then(() => {
      router.push(`/my-courses/${props.course.slug}`);
    });
  };

  const saveCurrentVideoProgress = async () => {
    // Add null check and ensure we have a valid ref
    if (!currentProgressRef.value) {
      console.warn('currentProgressRef is null, cannot save video progress');
      return;
    }

    // Ensure the component has the method before calling it
    if (typeof currentProgressRef.value.getCurrentVideoProgress !== 'function') {
      console.warn('getCurrentVideoProgress method not available');
      return;
    }

    const currentVideoProgress = currentProgressRef.value.getCurrentVideoProgress();
    if (!currentVideoProgress) {
      console.warn('No current video progress available');
      return;
    }

    const { videoId, progress } = currentVideoProgress;
    const progressMap = getProgress(String(props.lesson.id));

    if (!progressMap) {
      console.warn('No progress map available for lesson:', props.lesson.id);
      return;
    }

    const video = props.lesson.videos?.find(item => item.id === Number(videoId));
    if (!video) {
      console.warn('Video not found for videoId:', videoId);
      return;
    }

    const pct = (progress / video.duration) * 100;
    if (pct > 0 && pct < 100) {
      progressMap.set(String(videoId), Math.round(pct));
      try {
        await markVideoProgress(videoId, Math.round(progress));
      } catch (error) {
        console.error('Failed to mark video progress:', error);
      } finally {
        setProgress(String(props.lesson.id), new Map(progressMap));
      }
    }
  };

  onBeforeRouteUpdate(async () => {
    await saveCurrentVideoProgress();
  });

  const handleVideoEvent = async () => {
    await saveCurrentVideoProgress();
  };

  onMounted(() => {
    document.addEventListener('visibilitychange', handleVideoEvent);
    window.addEventListener('beforeunload', handleVideoEvent);
  });

  onUnmounted(() => {
    document.removeEventListener('visibilitychange', handleVideoEvent);
    window.removeEventListener('beforeunload', handleVideoEvent);
  });

  onBeforeRouteLeave(async (to, from, next) => {
    await saveCurrentVideoProgress();
    next();
  });
</script>
