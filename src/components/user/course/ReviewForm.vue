<template>
  <BForm>
    <BCard class="border-warning border-2 mb-5 review-card">
      <div class="text-center mb-4">
        <h5 class="fw-semibold mb-4">{{ prompt || $t('user.user_course.section_item.reviews.prompt') }}</h5>

        <RatingForm v-model="form.rating" class="justify-content-center" />
      </div>

      <div class="mb-4">
        <label for="comment" class="form-label fw-medium">
          {{ $t('user.user_course.section_item.reviews.content_label') }}
        </label>
        <BFormTextarea
          id="comment"
          v-model="form.content"
          :placeholder="$t('user.user_course.section_item.reviews.content_placeholder')"
          rows="5"
          max-rows="8"
          :maxlength="contentMaxLength"
          class="comment-textarea"
        />
        <div class="d-flex justify-content-between align-items-center mt-2">
          <small class="text-muted">
            {{ $t('user.user_course.section_item.reviews.max_length_hint', { max: contentMaxLength }) }}
          </small>
          <small class="text-muted">{{ form.content?.length || 0 }}/{{ contentMaxLength }}</small>
        </div>
      </div>

      <div class="d-flex justify-content-end justify-content-center-mobile mt-2">
        <slot />
        <Button
          size="md"
          class="mt-2 submit-button"
          :disabled="isSubmitting"
          :loading="isSubmitting"
          @click="handleSubmit"
        >
          <i class="mdi mdi-send-outline"></i>
          {{ $t('user.user_course.section_item.reviews.create_btn') }}
        </Button>
      </div>
    </BCard>
  </BForm>
</template>

<script setup lang="ts">
  import Button from '@/components/base/Button.vue';
  import RatingForm from '@/components/base/RatingForm.vue';

  import { ReviewMutateForm } from '@/forms/user/comment';
  import { reviewCreate, reviewUpdate } from '@/services/user';

  import { ReviewInterface } from '@/utils/interface/user/comment';
  import { ReviewCreateInterface } from '@/utils/interface/user/comment';

  const props = defineProps({
    targetID: {
      type: String,
      required: true
    },
    targetType: {
      type: String,
      required: true
    },
    referenceID: {
      type: String,
      default: ''
    },
    review: {
      type: Object as PropType<ReviewInterface>,
      required: false
    },
    prompt: {
      type: String,
      default: ''
    }
  });

  const emits = defineEmits(['submitted']);

  const contentMaxLength = 1000;

  const form = ref<ReviewCreateInterface>(new ReviewMutateForm(props.review || {}));
  const isSubmitting = ref(false);

  const handleSubmit = async () => {
    isSubmitting.value = true;

    try {
      if (props.review) {
        const input = {
          rating: form.value.rating,
          content: form.value.content
        };
        const res = await reviewUpdate(props.review.id.toString(), input);
        emits('submitted', res.reviewUpdate.comment);
        return;
      }

      form.value.targetID = props.targetID;
      form.value.targetType = props.targetType;

      const input = {
        ...form.value
      };

      const res = await reviewCreate(input);
      emits('submitted', res.reviewCreate.comment);
    } catch (error) {
      console.error(error);
    } finally {
      isSubmitting.value = false;
    }
  };
</script>

<style scoped>
  @media (max-width: 576px) {
    .justify-content-center-mobile {
      justify-content: center !important;
    }
  }

  .review-card {
    background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
    transition: all 0.3s ease;
  }

  .review-card:hover {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .comment-textarea {
    transition: all 0.3s ease;
    resize: none;
  }

  .comment-textarea:focus {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
  }

  .submit-button {
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%) !important;
    border: none !important;
    border-radius: 6px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    white-space: nowrap;
    font-size: 14px;
    height: 40px;
    padding: 8px 16px;
    position: relative;
    overflow: hidden;
  }

  .submit-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #ffb300 0%, #ff9500 100%) !important;
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(255, 179, 0, 0.4);
  }

  .submit-button:active:not(:disabled) {
    transform: scale(0.98);
  }

  .submit-button:focus:not(:disabled) {
    outline: none;
    box-shadow:
      0 0 0 2px rgba(253, 126, 20, 0.2),
      0 0 0 4px rgba(253, 126, 20, 0.1);
  }

  .submit-button:disabled {
    opacity: 0.5;
    pointer-events: none;
  }
</style>
