<template>
  <div v-if="!!currentDrill.step?.length" class="px-4">
    <h5 class="fw-bold mb-4">{{ $t('user.user_course.section_item.step.description') }}</h5>
    <div v-for="(step, index) in currentDrill.step" :key="index" class="drill-step position-relative mb-3 pb-4">
      <div
        class="step-ball position-absolute translate-middle bg-warning text-white rounded-circle d-flex align-items-center justify-content-center"
      >
        {{ index + 1 }}
      </div>
      <div>
        <b>{{ $t('user.user_course.section_item.step.title') }} {{ index + 1 }}:</b>
        <pre class="pre-content">{{ step.description }}</pre>
      </div>
      <div class="step-item">
        <FullscreenWrapper :src="step.diagramImage" />
      </div>
      <div v-if="index < currentDrill.step.length - 1" class="line-ball position-absolute"></div>
    </div>
  </div>
  <div v-else>
    <DataEmpty :message="$t('user.user_course.section_item.drill.data_step_empty')" height="150" />
  </div>
</template>

<script lang="ts" setup>
  import DataEmpty from '@/components/utility/DataEmpty.vue';
  import FullscreenWrapper from '@/components/base/FullscreenWrapper.vue';

  import { DrillInterface } from '@/utils/interface/drill/drill';

  defineProps({
    currentDrill: {
      type: Object as PropType<DrillInterface>,
      required: true
    }
  });
</script>
