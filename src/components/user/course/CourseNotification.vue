<template>
  <router-link :to="`/courses/${invitedCourse.course?.slug}`" class="text-decoration-none">
    <FadeInUp>
      <div class="d-flex align-items-start gap-3 course-notification py-2">
        <img
          :src="invitedCourse.course?.banner ? invitedCourse.course?.banner : dummyBanner"
          alt="Course banner"
          class="course-banner"
        />
        <div class="course-info d-flex flex-column gap-1">
          <h6 class="mb-0 text-primary-warning">{{ invitedCourse.course?.title }}</h6>
          <small class="text-muted">
            {{ $t('public.course.notification.invitation.by') }}:
            <span class="fw-bold">{{ invitedCourse.course?.teacher.name }}</span>
          </small>
          <small>{{ filters.formatRelativeTime(invitedCourse.createdAt) }}</small>
        </div>
      </div>
    </FadeInUp>
  </router-link>

  <hr class="my-2" />
</template>

<script lang="ts" setup>
  import filters from '@/utils/filters';
  import dummyBanner from '@/assets/images/dummy_banner.png';

  import { InvitedCourseUserInterface } from '@/utils/interface/user/userCourse';

  defineProps({
    invitedCourse: {
      type: Object as PropType<InvitedCourseUserInterface>,
      required: true
    }
  });
</script>

<style lang="scss" scoped>
  .course-notification {
    img {
      width: 100px;
      object-fit: cover;
      border-radius: 10px;
    }
  }
</style>
