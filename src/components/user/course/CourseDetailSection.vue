<template>
  <div class="user-course-detail">
    <div class="course-title d-flex justify-content-between align-items-center">
      <h3 class="pb-lg-3 pe-2 pe-lg-0">{{ course.title }}</h3>
      <i
        class="d-block d-lg-none mdi mdi-format-list-bulleted font-size-24 lh-1 text-white"
        @click="toggleRightSidebar"
      />
    </div>

    <!-- Mobile Sidebar -->
    <div v-if="isMobile && isSidebarVisible" v-click-outside="hideRightSidebar" class="right-bar">
      <simplebar class="h-100 p-3">
        <FadeInUp>
          <CourseSidebar
            :course="course"
            :isPreview="isPreview"
            :teacher="teacher"
            @completed-percent="toggleCompletedPercent"
            :block-access="blockAccess"
          />
          <i class="mdi mdi-close" @click="hideRightSidebar" />
        </FadeInUp>
      </simplebar>
    </div>
    <div v-if="isMobile && isSidebarVisible" @click="hideRightSidebar" class="rightbar-overlay" />

    <!-- Main Content -->
    <div class="lesson-container">
      <BRow class="m-0">
        <BCol cols="12" lg="8" class="p-0">
          <CourseContentPlayer
            :course="course"
            :lesson="currentLesson"
            :loading="loading"
            :isPreview="isPreview"
            :blockAccess="blockAccess"
            @fetchLesson="fetchLesson"
          />

          <FadeInUp>
            <CourseContentBottom
              :course="course"
              :lesson="currentLesson"
              :isPreview="isPreview"
              :blockAccess="blockAccess"
              :completed-percent="completedPercent"
              @refresh-user-review="refreshMyCourse"
            />
          </FadeInUp>
        </BCol>

        <!-- Desktop Sidebar -->
        <BCol v-if="!isMobile" cols="12" lg="4" class="d-none d-lg-block">
          <FadeInLeft>
            <CourseSidebar
              :course="course"
              :isPreview="isPreview"
              :blockAccess="blockAccess"
              @completed-percent="toggleCompletedPercent"
              @fetchLesson="fetchLesson"
            />
          </FadeInLeft>
        </BCol>
      </BRow>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { useWindowSize } from '@vueuse/core';
  import CourseContentPlayer from '@/components/user/course/CourseContentPlayer.vue';
  import CourseSidebar from '@/components/user/course/CourseSidebar.vue';
  import CourseContentBottom from '@/components/user/course/CourseContentBottom.vue';

  import simplebar from 'simplebar-vue';
  import type { UserCourseInterface } from '@/utils/interface/user/userCourse';
  import type { CourseSectionItemInterface } from '@/utils/interface/user/courseSectionItem';

  import { useVideoProgress } from '@/composable/useVideoProgress';
  import { useCourseProgressSave } from '@/composable/useCourseProgressSave';

  const { initCourse } = useCourseProgressSave();

  const { initializeCourseProgress } = useVideoProgress();
  const props = defineProps({
    course: {
      type: Object as PropType<UserCourseInterface>,
      required: true
    },
    currentLesson: {
      type: Object as PropType<CourseSectionItemInterface>,
      required: true
    },
    loading: {
      type: Boolean,
      required: false
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    blockAccess: {
      type: Boolean,
      default: false
    }
  });

  const emit = defineEmits(['refreshMyCourse', 'fetchLesson']);

  const refreshMyCourse = () => {
    emit('refreshMyCourse');
  };

  const fetchLesson = (lessonSlug: string) => {
    emit('fetchLesson', lessonSlug);
  };

  const completedPercent = ref<number>();

  const toggleCompletedPercent = (value: number) => {
    completedPercent.value = value;
  };

  const isSidebarVisible = ref(false);
  const { width } = useWindowSize();

  const isMobile = computed(() => width.value < 992);

  const toggleRightSidebar = () => {
    isSidebarVisible.value = !isSidebarVisible.value;
    document.body.classList.toggle('right-bar-enabled');
  };

  const hideRightSidebar = () => {
    isSidebarVisible.value = false;
    document.body.classList.remove('right-bar-enabled');
  };

  const teacher = computed(() => props.course.teacher!);

  onMounted(() => {
    initCourse(props.course);
    initializeCourseProgress(props.course);
  });
</script>
