<template>
  <BCard class="mb-0 border" body-class="p-0">
    <div class="px-2 px-lg-3 py-2 py-lg-3 cursor-pointer" @click="toggleCollapse">
      <div class="d-flex justify-content-between align-items-center">
        <h5 class="mb-0 pe-2">{{ section.title }}</h5>
        <i
          v-if="section.courseSectionItems?.length"
          :class="['mdi', isCollapsed ? 'mdi-chevron-down' : 'mdi-chevron-right', 'font-size-24', 'text-muted', 'lh-1']"
        ></i>
      </div>
      <span class="text-muted">({{ completedCount }}/{{ section.courseSectionItems?.length || 0 }})</span>
    </div>

    <BCollapse v-model="isCollapsed">
      <BCardBody class="border-top p-0 text-black">
        <CourseSectionItemCard
          v-for="(lesson, index) in section.courseSectionItems"
          :key="index"
          :lesson="lesson"
          :course="course"
          :isPreview="isPreview"
          :blockAccess="blockAccess"
          @update:complete="updateComplete"
          @fetchLesson="fetchLesson"
        />
      </BCardBody>
    </BCollapse>
  </BCard>
</template>

<script lang="ts" setup>
  import { CourseSectionInterface } from '@/utils/interface/user/courseSection';
  import { UserCourseInterface } from '@/utils/interface/user/userCourse';
  import CourseSectionItemCard from '@/components/user/course/CourseSectionItemCard.vue';

  const props = defineProps({
    course: {
      type: Object as PropType<UserCourseInterface>,
      required: true
    },
    section: {
      type: Object as PropType<CourseSectionInterface>,
      required: true
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    blockAccess: {
      type: Boolean,
      default: false
    }
  });

  const emit = defineEmits(['update:complete', 'fetchLesson']);

  const route = useRoute();

  const isCollapsed = ref(false);
  const completedMap = ref<Record<number, boolean>>({});

  const completedCount = computed(() => {
    return Object.values(completedMap.value).filter(Boolean).length;
  });

  const toggleCollapse = () => {
    if (props.section.courseSectionItems?.length) {
      isCollapsed.value = !isCollapsed.value;
    }
  };

  const updateComplete = ({ id, isCompleted }: { id: number; isCompleted: boolean }) => {
    completedMap.value[id] = isCompleted;

    const item = props.section.courseSectionItems?.find(i => i.id === id);
    if (item) item.isCompleted = isCompleted;

    emit('update:complete', { id, isCompleted });
  };

  const fetchLesson = (lessonSlug: string) => {
    emit('fetchLesson', lessonSlug);
  };

  watchEffect(() => {
    const lessonSlug = route.query.courseContentSlug?.toString();
    const isMatch = props.section.courseSectionItems?.some(item => item.slug?.toString() === lessonSlug);
    if (isMatch) {
      isCollapsed.value = true;
    }
  });

  onMounted(() => {
    props.section.courseSectionItems?.forEach(item => {
      completedMap.value[Number(item.id)] = item.isCompleted;
    });
  });
</script>
