<template>
  <Loader :loading="loading">
    <div v-if="submissions.length" class="mt-5 px-0">
      <h5 class="mx-3 mb-3 fw-bold text-black">{{ $t('user.practice_submission.list.title') }}</h5>
      <BCard
        v-for="item in submissions"
        :key="item.id"
        header-class="practice-card-header py-3"
        class="mb-4 shadow-sm border border-light"
      >
        <template #header>
          <h5 class="d-flex flex-column gap-1 mb-0">
            <div class="d-flex align-items-start gap-2">
              <i class="bx bx-message text-muted"></i>
              <pre class="submission-content pre-content">{{ item.content }}</pre>
              <BBadge pill :variant="getStatusClass(item.status)">{{ item.statusI18n }}</BBadge>
            </div>

            <small class="ms-4 text-muted">{{ filters.formatDatetime(item.createdAt) }}</small>
          </h5>
        </template>

        <div class="px-5 py-2">
          <VideoPlayer
            v-if="item.videos?.[0] && item.videos?.[0].isPlayable"
            :video-id="String(item.videos[0]?.id)"
            :thumbnail="String(item.videos[0]?.thumbnailURL)"
            :video-title="item.videos[0]?.title"
          />

          <div v-else>
            <img class="btn w-100 video-thumbnail-img" src="/processing-video.png" alt="video processing" />
          </div>
        </div>

        <hr />
        <Comment v-if="item.comments" :comments="item.comments" />

        <BForm>
          <div class="d-flex">
            <div class="me-3">
              <BAvatar variant="warning" :src="userProfile?.imageUrl ? userProfile.imageUrl : dummyAvatar" size="30" />
            </div>
            <BFormTextarea
              v-model="reply[item.id]"
              :placeholder="$t(`user.practice_submission.comment.form.content.placeholder`)"
              rows="5"
            />
          </div>

          <div class="d-flex justify-content-end">
            <BButton
              size="md"
              variant="success"
              class="mt-2"
              :disabled="!reply[item.id] || isLoading"
              :loading="isLoading"
              @click="submitReply(item.id)"
            >
              <i class="mdi mdi-send-outline"></i>
              {{ $t(`user.practice_submission.comment.create_btn`) }}
            </BButton>
          </div>
        </BForm>
      </BCard>

      <Pagination :metadata="metadata" @change="changePage($event)" />
    </div>

    <div v-else class="submissions-data-empty d-flex flex-column align-items-center gap-2 mt-5">
      <i class="bx bx-message"></i>
      <span>{{ $t('user.practice_submission.form.content.data_empty') }}</span>
    </div>
  </Loader>
</template>

<script lang="ts" setup>
  import filters from '@/utils/filters';

  import Pagination from '@/components/base/Pagination.vue';
  import VideoPlayer from '@/components/base/VideoPlayer.vue';
  import Comment from '@/components/user/course/Comment.vue';
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';

  import { useGoList } from '@bachdx/b-vuse';
  import { CourseSectionItemInterface } from '@/utils/interface/user/courseSectionItem';
  import { PracticeSubmissionInterface } from '@/utils/interface/user/practiceSubmission';

  import { practiceSubmissionList, commentCreate } from '@/services/user';
  import { PracticeSubmissionQueryFormModel } from '@/forms/user/practiceSubmission';

  import { useUserAuthStore } from '@/store/user/auth';

  const props = defineProps({
    currentLesson: {
      type: Object as PropType<CourseSectionItemInterface>,
      default: () => ({})
    }
  });

  const route = useRoute();
  const router = useRouter();

  const isLoading = ref<boolean>(false);

  const {
    items: submissions,
    metadata,
    changePage,
    query,
    parseQueryAndFetch
  } = useGoList({
    fetchListFnc: practiceSubmissionList,
    fetchKey: 'practiceSubmissions',
    route: route,
    queryFormModels: PracticeSubmissionQueryFormModel,
    router: router,
    perPage: 10,
    reflectUrl: false
  });

  const authUserStore = useUserAuthStore();
  const { userProfile } = storeToRefs(authUserStore);

  const loading = ref(false);
  const reply = ref<Record<number, string>>({});

  async function submitReply(submissionID: number) {
    if (!reply.value[submissionID]) return;

    isLoading.value = true;
    try {
      const input = {
        targetID: submissionID.toString(),
        targetType: 'PracticeSubmission',
        content: reply.value[submissionID]
      };

      const res = await commentCreate(input);

      const submission: PracticeSubmissionInterface = submissions.value.find(
        (item: PracticeSubmissionInterface) => item.id == submissionID
      );

      if (!submission.comments) submission.comments = [];

      submission.comments.push(res.commentCreate.comment);

      reply.value[submissionID] = '';
    } catch (error) {
      console.error(error);
    } finally {
      isLoading.value = false;
    }
  }

  function fetchSubmissions() {
    loading.value = true;
    query.value.practiceIDEq = props.currentLesson.id;
    query.value.practiceTypeEq = 'CourseSectionItem';

    parseQueryAndFetch(query.value).finally(() => {
      loading.value = false;
    });
  }
  enum SubmissionStatus {
    Submitted = 'submitted',
    Approved = 'approved',
    Rejected = 'rejected'
  }

  const StatusBadgeVariantMap: Record<SubmissionStatus, string> = {
    [SubmissionStatus.Submitted]: 'warning',
    [SubmissionStatus.Approved]: 'success',
    [SubmissionStatus.Rejected]: 'danger'
  };

  const getStatusClass = (status: string) => StatusBadgeVariantMap[status as SubmissionStatus] || '';

  defineExpose({
    fetchSubmissions
  });

  watch(
    () => props.currentLesson,
    () => {
      fetchSubmissions();
    },
    { immediate: true }
  );
</script>

<style lang="scss" scoped>
  .comment-content {
    white-space: pre-wrap;
  }

  .px-5.py-2 {
    @media (max-width: 576px) {
      padding-left: 0 !important;
      padding-right: 0 !important;
      padding-top: 0.5rem !important;
      padding-bottom: 0.5rem !important;
    }
  }

  .submission-content {
    flex: 1 1 0;
  }
</style>
