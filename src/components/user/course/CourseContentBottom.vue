<template>
  <BCard no-body>
    <BCardBody class="pt-3">
      <BTabs nav-class="nav-tabs-custom" content-class="pt-4 p-lg-3 text-muted" class="course-content-tabs" lazy>
        <BTab class="text-black mt-2">
          <template v-slot:title>
            <span>{{ $t('user.user_course.overview') }}</span>
          </template>
          <FadeInUp>
            <div v-if="course.description" class="description pb-4" :class="{ 'border-bottom': !isPreview }">
              <h5>{{ $t('user.user_course.introduction') }}</h5>
              <pre class="pre-content mb-0">{{ course.description }}</pre>
            </div>
            <div v-if="!isPreview || blockAccess" class="teacher pt-lg-3">
              <h5>{{ $t('user.user_course.teacher') }}</h5>
              <div class="d-flex w-100 justify-content-between align-items-center">
                <div class="d-flex align-items-center gap-2">
                  <BAvatar
                    variant="warning"
                    size="md"
                    :src="course.teacher.imageUrl ? course.teacher.imageUrl : dummyAvatar"
                  />
                  {{ course.teacher.name }}
                </div>
                <router-link
                  :to="{
                    path: `/teachers/${course.teacher.slug}`,
                    hash: '#rating'
                  }"
                  target="_blank"
                >
                  <Button icon="bxs-star" variant="warning">
                    {{ $t('user.user_course.rating') }}
                  </Button>
                </router-link>
              </div>
            </div>
          </FadeInUp>
        </BTab>

        <BTab v-if="!isPreview || blockAccess" :disabled="blockAccess || isBasicPackage">
          <template v-slot:title>
            <i v-if="blockAccess || isBasicPackage" class="mdi mdi-lock m-1"></i>
            <span>{{ $t('user.user_course.section_item.type.submit_practice') }}</span>
          </template>

          <FadeInUp>
            <SubmitAssignmentForm
              :practiceID="lesson.id?.toString() || ''"
              practiceType="CourseSectionItem"
              @submitted="refreshSubmissions"
            />
            <PracticeSubmissionList ref="submissionListRef" :currentLesson="lesson" />
          </FadeInUp>
        </BTab>

        <BTab v-if="!isPreview || blockAccess" :disabled="blockAccess">
          <template v-slot:title>
            <i v-if="blockAccess" class="mdi mdi-lock m-1"></i>
            <span>{{ $t('user.user_course.section_item.type.reviews') }}</span>
          </template>

          <FadeInUp>
            <ReviewForm
              v-if="displayRatingForm"
              :targetID="course.id?.toString() || ''"
              targetType="Course"
              @submitted="refreshReviews"
              class="mb-4 mt-2"
            />
            <ReviewList
              ref="reviewList"
              :targetID="course.id?.toString() || ''"
              targetType="Course"
              :my-review="course.myReview"
            />
          </FadeInUp>
        </BTab>
      </BTabs>
    </BCardBody>
  </BCard>
</template>

<script lang="ts" setup>
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';

  import Button from '@/components/base/Button.vue';
  import PracticeSubmissionList from '@/components/user/course/PracticeSubmissionList.vue';
  import ReviewForm from '@/components/user/course/ReviewForm.vue';
  import ReviewList from '@/components/user/course/ReviewList.vue';
  import SubmitAssignmentForm from '@/components/user/course/SubmitAssignmentForm.vue';

  import { CourseSectionItemInterface } from '@/utils/interface/user/courseSectionItem';
  import { ReviewInterface } from '@/utils/interface/user/comment';

  import { UserCourseInterface } from '@/utils/interface/user/userCourse';
  import { PACKAGES_NAMES } from '@/utils/constant';

  const props = defineProps({
    course: {
      type: Object as PropType<UserCourseInterface>,
      required: true
    },
    lesson: {
      type: Object as PropType<CourseSectionItemInterface>,
      required: true
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    blockAccess: {
      type: Boolean,
      default: false
    },
    completedPercent: {
      type: Number,
      default: 0
    }
  });

  const isBasicPackage = computed(() => {
    return props.course.myPackage?.packageDeal?.name === PACKAGES_NAMES.BASIC;
  });

  const submissionListRef = useTemplateRef<InstanceType<typeof PracticeSubmissionList>>('submissionListRef');
  const reviewList = useTemplateRef<InstanceType<typeof ReviewList>>('reviewList');

  const displayRatingForm = computed(() => !props.isPreview && !props.course.myReview);
  // const blockExperimentFeatures = import.meta.env.VITE_APP_BLOCK_EXPERIMENTAL_FEATURES.toLowerCase() === 'true';

  const refreshSubmissions = () => {
    submissionListRef.value?.fetchSubmissions?.();
  };

  const refreshReviews = (review: ReviewInterface) => {
    props.course.myReview = review;
    reviewList.value?.fetchList();
  };
</script>
