<template>
  <BCard class="course-side-bar mb-0" body-class="p-0">
    <div class="border-bottom pb-4 p-lg-4">
      <BCardTitle class="pb-2">
        <h5>
          <b class="text-warning">{{ course.sectionItemCount }}</b> {{ $t('user.user_course.section_item.title') }}
        </h5>
      </BCardTitle>
      <div class="d-flex justify-content-between pb-1">
        <span>{{ $t('user.user_course.progress') }}</span>
        <span class="text-warning fw-bold">{{ completedProgress }}%</span>
      </div>
      <BProgress height="5px" :value="completedProgress" :max="100" variant="primary"></BProgress>
    </div>

    <div class="list-lesson px-lg-3 mt-3">
      <CourseSectionCard
        v-for="(section, index) in course.courseSections"
        :key="index"
        :section="section"
        :course="course"
        :isPreview="isPreview"
        :blockAccess="blockAccess"
        @update:complete="updateCompletedProgress"
        @fetchLesson="fetchLesson"
      />
    </div>

    <div class="text-center text-muted mt-4 py-3 border-top">
      {{ $t('user.user_course.section_item.status.completed') }}
      {{ completedCount }}/{{ course.sectionItemCount }}
      <span class="text-lowercase">{{ $t('user.user_course.section_item.title') }}</span>
    </div>
  </BCard>
</template>
<script lang="ts" setup>
  import CourseSectionCard from '@/components/user/course/CourseSectionCard.vue';
  import { UserCourseInterface } from '@/utils/interface/user/userCourse';

  import { useCourseProgressSave } from '@/composable/useCourseProgressSave';

  const { completedProgress, completedCount, updateCompletedProgress } = useCourseProgressSave();
  defineProps({
    course: {
      type: Object as PropType<UserCourseInterface>,
      required: true
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    blockAccess: {
      type: Boolean,
      default: false
    }
  });

  const emit = defineEmits(['fetchLesson']);

  const fetchLesson = (lessonSlug: string) => {
    emit('fetchLesson', lessonSlug);
  };
</script>
