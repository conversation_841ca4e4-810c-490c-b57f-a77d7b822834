<template>
  <Loader :loading="loading">
    <div v-if="goListInstance.items.length">
      <h3 class="mb-4">{{ $t('user.user_course.related_courses') }}</h3>

      <BaseCarousel :items="goListInstance.items">
        <template #default="{ item }">
          <CourseCard :course="item" />
        </template>
      </BaseCarousel>
    </div>
  </Loader>
</template>

<script lang="ts" setup>
  import { useGoList } from '@bachdx/b-vuse';

  import { relatedCourses } from '@/services/public/repositories/course';

  import CourseCard from '@/components/public/course/CourseCard.vue';
  import BaseCarousel from '@/components/base/BaseCarousel.vue';

  const route = useRoute();
  const router = useRouter();

  const goListInstance = ref<ReturnType<typeof useGoList>>({});
  const loading = ref(false);

  const courseSlug = computed(() => route.params.slug.toString());

  const createGoList = () => {
    goListInstance.value = useGoList({
      fetchListFnc: relatedCourses,
      fetchKey: 'relatedCourses',
      route: route,
      router: router,
      perPage: 10,
      reflectUrl: false,
      extraParams: {
        courseSlug: courseSlug.value
      }
    });
  };

  watch(
    courseSlug,
    async newSlug => {
      if (newSlug) {
        createGoList();
        loading.value = true;

        try {
          await goListInstance.value.parseQueryAndFetch();
        } catch (error) {
          console.error('Error fetching related courses:', error);
        } finally {
          loading.value = false;
        }
      }
    },
    { immediate: true }
  );
</script>
