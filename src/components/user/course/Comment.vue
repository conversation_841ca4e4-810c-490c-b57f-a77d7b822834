<template>
  <simplebar data-simplebar style="max-height: 310px" class="pb-3">
    <BListGroup>
      <BListGroupItem v-for="comment in comments" :key="comment" class="py-2 px-0 border-0">
        <div class="d-flex">
          <div class="avatar-xs me-3">
            <BAvatar variant="warning" :src="getAvatar(comment)" size="25" />
          </div>
          <div class="flex-grow-1">
            <h5 class="d-flex align-items-center gap-2 font-size-14 mb-1">
              <span
                class="badge rounded-pill font-size-12"
                :class="comment.authorType == 'Teacher' ? 'badge-soft-danger' : 'badge-soft-primary'"
              >
                {{ $t(`user.practice_submission.comment.type.${comment.authorType}`) }}
              </span>

              {{ getAuthorName(comment) }}

              <small class="text-muted float-end">{{ filters.formatDatetime(comment.createdAt) }}</small>
            </h5>

            <div v-html="comment.content" class="comment-content mt-2 p-2"></div>
          </div>
        </div>
      </BListGroupItem>
    </BListGroup>
  </simplebar>
</template>

<script setup lang="ts">
  import simplebar from 'simplebar-vue';
  import filters from '@/utils/filters';

  import { CommentInterface } from '@/utils/interface/user/comment';

  defineProps({
    comments: {
      type: Array as PropType<CommentInterface[]>,
      default: () => []
    }
  });

  function getAvatar(comment: CommentInterface) {
    const authorType = comment.authorType;
    const avatar = authorType === 'Teacher' ? comment.authorTeacher.imageUrl : comment.authorUser.imageUrl;

    return avatar;
  }

  function getAuthorName(comment: CommentInterface) {
    return comment.authorType === 'Teacher' ? comment.authorTeacher.name : comment.authorUser.name;
  }
</script>

<style lang="scss" scoped>
  .comment-content {
    white-space: pre-wrap;
    background-color: #f9f9fa;
  }
</style>
