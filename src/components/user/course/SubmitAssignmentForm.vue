<template>
  <BForm>
    <h5 class="mx-3 mb-2 fw-bold text-black mt-2">{{ $t('user.practice_submission.form.title') }}</h5>

    <BAlert :model-value="true" variant="warning" class="d-flex mb-4 mx-0 mx-lg-3">
      <i class="mdi mdi-alert-circle-outline lh-1 me-2 font-size-16"></i>
      <div class="d-flex flex-column">
        <span>{{ $t('user.practice_submission.form.alert.content') }}</span>
      </div>
    </BAlert>

    <BaseFormValidator
      :label="$t(`user.practice_submission.form.content.label`)"
      name="content"
      class="mb-3 px-0 px-lg-3 font-size-15 text-black fw-bold"
    >
      <textarea
        v-model="form.content"
        class="form-control"
        type="text"
        :placeholder="$t(`user.practice_submission.form.content.placeholder`)"
        rows="5"
      />
    </BaseFormValidator>

    <UploadVideo
      v-if="!fileName"
      site="user"
      parentType="PracticeSubmission"
      @upload-success="handleUploadSuccess"
      enableDefaultTitle
    />
    <div v-else class="uploaded-file-name d-flex flex-column py-2 mb-2 rounded text-dark">
      <div class="d-flex align-items-center pb-1">
        <i class="mdi mdi-check-circle text-success me-2 lh-1 font-size-16"></i>
        <h6 class="mb-0">{{ $t('video.upload_progress.success') }}</h6>
      </div>
      <div class="ps-4 text-primary fw-bold d-flex align-items-center">
        <div><i class="mdi mdi-file-video font-size-18"></i> {{ fileName }}</div>
        <button
          class="btn btn-link p-0 text-danger ms-3 font-size-18"
          type="button"
          @click="handleDeleteVideo"
          :disabled="isDeletingVideo"
        >
          <i class="mdi" :class="isDeletingVideo ? 'mdi-loading mdi-spin' : 'mdi-delete'"></i>
        </button>
      </div>
    </div>
  </BForm>

  <BRow>
    <BCol class="d-flex justify-content-end">
      <Button
        variant="success"
        :disabled="!fileName || isLoading"
        :loading="isLoading"
        @click="submit"
        class="d-flex align-items-center gap-1 px-4"
      >
        <i class="bx bx-upload font-size-16"></i> {{ $t(`user.practice_submission.form.create_btn`) }}
      </Button>
    </BCol>
  </BRow>
</template>

<script setup lang="ts">
  import Button from '@/components/base/Button.vue';
  import UploadVideo from '@/components/base/video/UploadVideo.vue';

  import { PracticeSubmissionForm } from '@/forms/user/practiceSubmission';
  import { practiceSubmissionCreate, selfVideoDelete } from '@/services/user';

  const props = defineProps({
    practiceID: {
      type: String,
      required: true
    },
    practiceType: {
      type: String,
      required: true
    }
  });

  const emit = defineEmits(['submitted']);

  const form = ref(new PracticeSubmissionForm());
  const isSubmitting = ref(false);
  const fileName = ref('');
  const videoID = ref(0);
  const isDeletingVideo = ref(false);
  const isLoading = ref<boolean>(false);

  const submit = async () => {
    if (videoID.value) {
      isLoading.value = true;
      isSubmitting.value = true;

      form.value.practiceId = props.practiceID;
      form.value.practiceType = props.practiceType;
      form.value.videoIDs = videoID.value.toString();

      try {
        const res = await practiceSubmissionCreate(form.value);
        form.value = res.practiceSubmissionCreate.practiceSubmission;

        emit('submitted');

        form.value = new PracticeSubmissionForm();
        fileName.value = '';
        videoID.value = 0;
      } catch (error) {
        console.log(error);
      } finally {
        isSubmitting.value = false;
      }
      isLoading.value = false;
    }
  };

  const handleUploadSuccess = (uploadedFileName: string, uploadVideoID: number) => {
    fileName.value = uploadedFileName;
    videoID.value = uploadVideoID;
  };

  const handleDeleteVideo = async () => {
    try {
      isDeletingVideo.value = true;
      await selfVideoDelete(videoID.value.toString());
      fileName.value = '';
      videoID.value = 0;
    } catch (error) {
      console.error('Error deleting video:', error);
    } finally {
      isDeletingVideo.value = false;
    }
  };
</script>

<style scoped>
  :deep(.dropzone) {
    max-height: 150px !important;
  }
</style>
