<template>
  <router-link :to="`/my-courses/${course.slug}`" class="course-card">
    <BCard body-class="d-flex flex-column justify-content-between min-h-120" class="rounded-4">
      <template #img>
        <div class="overflow-hidden rounded-top-4 card-image">
          <div class="ratio ratio-4x3">
            <img
              :src="course.banner ? course.banner : dummyBanner"
              alt="Course banner"
              class="course-banner object-cover rounded-top-4"
            />
          </div>

          <div class="teacher-section d-flex align-items-center">
            <div class="avatar-teacher-section">
              <BAvatar
                :src="course.teacher.imageUrl ? course.teacher.imageUrl : dummyAvatar"
                :alt="course.teacher.name"
                size="35"
                class="avatar-image"
              />
            </div>
            <h5 class="teacher-name mb-0 text-white">{{ course.teacher.name }}</h5>
          </div>
        </div>
      </template>
      <div>
        <BCardTitle class="mb-2 line-clamp-2">
          {{ course.title }}
        </BCardTitle>
      </div>
      <div>
        <div class="d-flex justify-content-between pb-1">
          <span>{{ $t('user.user_course.progress') }}</span>
          <span>{{ filters.roundMathPercent(course.processPercent) }}%</span>
        </div>
        <BProgress
          height="5px"
          :value="filters.roundMathPercent(course.processPercent)"
          :max="100"
          variant="info"
        ></BProgress>
      </div>
    </BCard>
  </router-link>
</template>

<script lang="ts" setup>
  import filters from '@/utils/filters';
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';
  import dummyBanner from '@/assets/images/dummy_banner.png';

  import { UserCourseInterface } from '@/utils/interface/user/userCourse';

  defineProps({
    course: {
      type: Object as PropType<UserCourseInterface>,
      required: true
    }
  });
</script>
