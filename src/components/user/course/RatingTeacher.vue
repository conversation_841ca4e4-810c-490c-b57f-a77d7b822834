<template>
  <div class="d-flex flex-column align-items-center">
    <template v-if="isLoading">
      <div class="skeleton-avatar rounded-circle"></div>
      <div class="skeleton-text w-50 mt-3"></div>
      <div class="skeleton-line w-75 mt-4"></div>
      <div class="skeleton-line w-75 mt-2"></div>
    </template>

    <template v-else>
      <img
        :src="teacher?.imageUrl || dummyAvatar"
        class="teacher-avatar rounded-circle"
        style="width: 100px !important; height: 100px !important"
      />
      <p class="teacher_name">{{ teacher?.name }}</p>

      <ReviewForm
        v-if="showCreateForm"
        :targetID="String(teacher?.id)"
        :targetType="COMMENT_TARGET_TYPE.TEACHER"
        :commentID="String(commentId)"
        :review="userReview"
        :prompt="$t('user.user_course.section_item.reviews.teacher_prompt')"
        @submitted="refresh"
      />

      <div v-else class="rating-block">
        <span class="my_rating">{{ $t('user.comment.form.my_rating') }}</span>
        <RatingForm v-model="stats" readonly class="mt-2" />
        <div style="width: 100%; text-align: end" class="mb-2">
          <span class="rerate" @click="showCreateForm = true">
            {{ $t('user.comment.form.rerate') }}
          </span>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';

  import RatingForm from '@/components/base/RatingForm.vue';
  import ReviewForm from '@/components/user/course/ReviewForm.vue';

  import { myReview } from '@/services/user';

  import { COMMENT_TARGET_TYPE } from '@/utils/constant';
  import { ReviewInterface } from '@/utils/interface/user/comment';
  import { TeacherInterface } from '@/utils/interface/user/teacher';

  const props = defineProps({
    teacher: {
      type: Object as PropType<TeacherInterface>,
      required: true
    }
  });
  const emits = defineEmits(['refreshCourse']);

  const commentId = computed(() => userReview.value?.id || '');
  const stats = computed(() => userReview.value?.rating || 0);

  const showCreateForm = ref(true);
  const userReview = ref<ReviewInterface>();
  const isLoading = ref(true);

  const handleGetMyReview = async () => {
    try {
      const res = await myReview(String(props.teacher.id), COMMENT_TARGET_TYPE.TEACHER);
      if (res?.review) {
        userReview.value = res.review;
        showCreateForm.value = false;
      }
    } catch (error) {
      console.error(error);
    } finally {
      isLoading.value = false;
    }
  };

  const refresh = async () => {
    showCreateForm.value = false;
    await handleGetMyReview();
    emits('refreshCourse');
  };

  onMounted(() => {
    handleGetMyReview();
  });
</script>

<style lang="scss" scoped>
  .review_teacher_alert {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .alert-txt {
      font-size: 14px;
      font-weight: 500;
      font-style: italic;
    }
  }

  .teacher_name {
    font-size: 18px;
    font-weight: 500;
    margin-top: 16px;
  }

  .rating-block {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding-inline: 12px;

    .my_rating {
      font-size: 18px;
      font-weight: 500;
      text-align: center;
    }

    .rerate {
      width: fit-content;
      font-style: italic;
      text-decoration: underline;
      cursor: pointer;
      transition:
        color 0.3s ease,
        transform 0.3s ease;

      &:hover {
        color: #ffc107;
        transform: scale(1.05);
      }
    }
  }

  // Skeleton style
  .skeleton-avatar,
  .skeleton-text,
  .skeleton-line {
    background-color: #dee2e6;
    border-radius: 0.25rem;
    animation: pulse 1.2s infinite ease-in-out;
  }

  .skeleton-avatar {
    width: 100px;
    height: 100px;
  }

  .skeleton-text {
    height: 18px;
  }

  .skeleton-line {
    height: 16px;
  }

  @keyframes pulse {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      opacity: 1;
    }
  }
</style>
