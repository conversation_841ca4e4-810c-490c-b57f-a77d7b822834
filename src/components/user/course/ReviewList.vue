<template>
  <Loader :loading="loading">
    <BContainer v-if="targetType === COMMENT_TARGET_TYPE.TEACHER" class="px-0 pt-lg-3">
      <TeacherReview
        v-model:show-modal-rating="showModalRating"
        :reviews="reviews"
        :stats="stats"
        :metadata="metadata"
        @change-page="changePage"
        :is-available-to-rate="isAvailableToRate"
      />
    </BContainer>
    <div v-else>
      <BContainer v-if="reviews.length" class="px-0 pt-lg-3">
        <CourseReview
          :reviews="reviews"
          :stats="stats"
          :metadata="metadata"
          :my-review="myReview"
          @change-page="changePage"
        />
      </BContainer>

      <div v-else class="submissions-data-empty d-flex flex-column align-items-center gap-2 data-empty-css">
        <DataEmpty :message="$t('user.user_course.section_item.reviews.empty')" height="130" />
      </div>
    </div>
  </Loader>
</template>

<script lang="ts" setup>
  import { useGoList } from '@bachdx/b-vuse';

  import { reviewsList } from '@/services/public';

  import CourseReview from '@/components/public/course/CourseReview.vue';
  import TeacherReview from '@/components/public/teachers/TeacherReview.vue';
  import DataEmpty from '@/components/utility/DataEmpty.vue';

  import { ReviewInterface } from '@/utils/interface/user/comment';
  import { COMMENT_TARGET_TYPE } from '@/utils/constant';

  const showModalRating = defineModel<boolean>('showModalRating', {
    default: false
  });

  const props = defineProps({
    targetID: {
      type: String,
      required: true
    },
    targetType: {
      type: String,
      required: true
    },
    myReview: {
      type: Object as PropType<ReviewInterface>,
      required: false
    },
    isAvailableToRate: {
      type: Boolean,
      default: false
    }
  });

  const route = useRoute();
  const router = useRouter();

  const {
    items: reviews,
    metadata,
    changePage,
    parseQueryAndFetch
  } = useGoList({
    fetchListFnc: async (params: any) => {
      const res = await reviewsList(params);
      stats.value = res.reviews.stats;
      return res;
    },
    fetchKey: 'reviews',
    route: route,
    router: router,
    perPage: 10,
    reflectUrl: false,
    extraParams: {
      targetID: props.targetID,
      targetType: props.targetType
    }
  });

  const loading = ref(false);
  const stats = ref({});

  function fetchList() {
    loading.value = true;

    parseQueryAndFetch({}).finally(() => {
      loading.value = false;
    });
  }

  onBeforeMount(() => {
    fetchList();
  });

  defineExpose({
    fetchList
  });
</script>

<style lang="scss" scoped>
  .data-empty-css {
    padding-bottom: 10px;
    div {
      padding: 0 !important;
      font-size: 16px;
    }
  }
</style>
