<template>
  <div v-if="currentDrill" class="drill-section position-relative w-100 py-3 d-flex flex-column gap-2 gap-lg-3">
    <div class="w-100 d-flex justify-content-end align-items-center flex-wrap gap-2 p-2">
      <div class="custom-select-option">
        <FadeInUp>
          <Multiselect
            v-model="currentDrillIndex"
            :options="drillOptions"
            :canDeselect="false"
            label="text"
            :appendToBody="true"
          ></Multiselect>
        </FadeInUp>
      </div>
    </div>

    <Loader :loading="loading">
      <div class="d-flex flex-column justify-content-center align-items-center w-100">
        <h5 class="p-2 p-lg-3 text-center mb-0">{{ currentDrill.title }}</h5>
        <FullscreenWrapper
          :src="
            currentDrill.diagrams[currentDiagramIndex]?.imageUrl
              ? currentDrill.diagrams[currentDiagramIndex]?.imageUrl
              : '/pool-table.png'
          "
        />

        <div class="pt-3">
          <span
            v-for="item in currentDrill.skills"
            class="badge-soft-primary badge rounded-pill mx-1 font-size-12"
            :key="item.id"
          >
            {{ item.nameI18n }}
          </span>
        </div>

        <div v-if="currentDrill.diagrams.length > 1" class="tab-list-diagram d-flex gap-2 mt-3">
          <Button
            v-for="(diagram, diagramIndex) in currentDrill.diagrams"
            size="sm"
            :key="diagramIndex"
            :variant="currentDiagramIndex == diagramIndex ? 'dark' : 'light'"
            @click="selectDiagram(diagramIndex)"
          >
            {{ diagramIndex + 1 }}
          </Button>
        </div>

        <div class="button-toggle-group my-5">
          <button
            class="toggle-btn d-flex align-items-center justify-content-center border-0 px-lg-3 py-2"
            :class="{ active: activeTab === 'step' }"
            @click="activeTab = 'step'"
          >
            <span class="action-ic">
              <i class="mdi mdi-layers-triple-outline font-size-18 lh-1"></i>
            </span>
            {{ $t('user.user_course.section_item.drill.step_btn') }}
            <span v-if="activeTab === 'step'" class="count-badge ms-2">
              {{ currentDrill.step?.length }}
              {{ $t('user.user_course.section_item.drill.step') }}
            </span>
          </button>

          <button
            class="toggle-btn d-flex align-items-center justify-content-center border-0 px-lg-3 py-2"
            :class="{ active: activeTab === 'video' }"
            :disabled="blockAccess"
            @click="handleShowVideos"
          >
            <span class="action-ic">
              <i v-if="blockAccess" class="mdi mdi-lock-outline font-size-18 lh-1"></i>
              <i v-else class="mdi mdi-play-circle-outline font-size-20 lh-1"></i>
            </span>
            {{ $t('user.user_course.section_item.drill.video_btn') }}
            <span v-if="activeTab === 'video'" class="count-badge ms-2">
              {{ validVideosDrill.length }}
              {{ $t('user.user_course.section_item.drill.video') }}
            </span>
          </button>
        </div>
      </div>

      <DrillStep v-if="activeTab === 'step'" :currentDrill="currentDrill" />

      <Loader :loading="loadingTabVideo">
        <BRow v-if="activeTab === 'video'" class="mx-2">
          <template v-if="validVideosDrill.length">
            <BCol v-for="(video, index) in validVideosDrill" :key="index" lg="6" class="pb-4">
              <FadeInUp>
                <VideoPlayer
                  :key="index"
                  :video-id="String(video.id)"
                  :title="video.title"
                  :thumbnail="video.thumbnailURL"
                  class="w-100"
                />
              </FadeInUp>
            </BCol>
          </template>
          <template v-else>
            <DataEmpty :message="$t('user.user_course.section_item.drill.data_video_empty')" height="150" />
          </template>
        </BRow>
      </Loader>
    </Loader>
  </div>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';

  import { CourseSectionItemInterface } from '@/utils/interface/user/courseSectionItem';
  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { VideoInterface } from '@/utils/interface/video';

  import { drillShow } from '@/services/teacher';
  import { showMyDrill } from '@/services/user/repositories/drill';

  import Button from '@/components/base/Button.vue';
  import DataEmpty from '@/components/utility/DataEmpty.vue';
  import DrillStep from './DrillStep.vue';
  import FullscreenWrapper from '@/components/base/FullscreenWrapper.vue';
  import Multiselect from '@vueform/multiselect';
  import VideoPlayer from '@/components/base/VideoPlayer.vue';

  const props = defineProps({
    lesson: {
      type: Object as PropType<CourseSectionItemInterface>,
      required: true
    },
    drills: {
      type: Array as PropType<DrillInterface[]>,
      required: true
    },
    loading: {
      type: Boolean,
      required: false
    },
    courseId: {
      type: String,
      required: true
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    blockAccess: {
      type: Boolean,
      default: false
    }
  });

  const currentDrillIndex = ref(0);
  const currentDiagramIndex = ref(0);
  const activeTab = ref<'video' | 'step'>('step');
  const validVideosDrill = ref<VideoInterface[]>([]);
  const loadingTabVideo = ref<boolean>(false);

  const drillOptions = computed(() =>
    props.drills.map((drill, index) => ({
      value: index,
      text: `${i18n.global.t('user.user_course.section_item.type.drill')} ${index + 1}`
    }))
  );

  const currentDrill = computed(() => {
    return props.drills?.[currentDrillIndex.value];
  });

  const selectDiagram = (index: number) => {
    currentDiagramIndex.value = index;
  };

  const handleShowVideos = async () => {
    loadingTabVideo.value = true;

    let res = null;
    if (props.isPreview) {
      res = await drillShow(String(currentDrill.value.slug));

      validVideosDrill.value = res.data.drill.videos?.filter((video: VideoInterface) => video.isPlayable == true) || [];
    } else {
      res = await showMyDrill(String(props.courseId), String(props.lesson.id), String(currentDrill.value.id));

      validVideosDrill.value = res.videos?.filter((video: VideoInterface) => video.isPlayable == true) || [];
    }

    activeTab.value = 'video';
    loadingTabVideo.value = false;
  };

  watch(
    () => props.lesson,
    () => {
      activeTab.value = 'step';
      currentDrillIndex.value = 0;
      currentDiagramIndex.value = 0;
    },
    { immediate: true }
  );

  watch(currentDrillIndex, () => {
    activeTab.value = 'step';
    currentDiagramIndex.value = 0;
  });
</script>
