<template>
  <div class="lesson-card border">
    <div
      class="px-4 py-3 cursor-pointer"
      :class="{ active: lesson.slug == lessonSlug, 'bg-active': completedValue }"
      @click="handleLesson"
    >
      <div class="d-flex gap-2">
        <BFormGroup class="form-check-warning" v-if="!blockAccess && !lesson.isLocked">
          <BFormCheckbox
            :model-value="isPreview ? false : completedValue"
            class="complete-icon"
            :disabled="isPreview || completeSectionItemLoading"
            @click.prevent="toggleComplete"
          ></BFormCheckbox>
        </BFormGroup>
        <div v-if="isLockIcon" class="text-muted">
          <i class="mdi mdi-lock"></i>
        </div>

        <div class="d-flex flex-column">
          <h6 class="mb-0">{{ lesson.title }}</h6>
          <div class="lesson-type mt-2">
            <div v-if="lesson.type == 'text'" class="text-muted">
              <i class="mdi mdi-file-document-outline"></i>
              {{ $t('user.user_course.section_item.type.text') }}
            </div>
            <div v-if="lesson.type == 'drill'" class="text-muted">
              <i class="mdi mdi-vector-square"></i>
              {{ $t('user.user_course.section_item.type.drill') }}
            </div>
            <div v-if="lesson.type == 'video'" class="text-muted">
              <i class="mdi mdi-play-circle-outline"></i>
              {{ $t('user.user_course.section_item.type.video') }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="videoProgressPCT" class="progress">
      <div
        class="progress-bar"
        role="progressbar"
        :aria-valuenow="videoProgressPCT"
        aria-valuemin="0"
        aria-valuemax="100"
        :style="{ width: videoProgressPCT + '%' }"
      >
        {{ videoProgressPCT }}%
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { setSectionItemStatus } from '@/services/user/repositories/userCourseSectionItem';
  import { CourseSectionItemInterface } from '@/utils/interface/user/courseSectionItem';
  import { UserCourseInterface } from '@/utils/interface/user/userCourse';

  import { useVideoProgress } from '@/composable/useVideoProgress';
  import useMyCourse from '@/composable/useMyCourse';

  const props = defineProps({
    course: {
      type: Object as PropType<UserCourseInterface>,
      required: true
    },
    lesson: {
      type: Object as PropType<CourseSectionItemInterface>,
      required: true
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    blockAccess: {
      type: Boolean,
      default: false
    }
  });

  const emit = defineEmits(['update:complete', 'fetchLesson']);

  const route = useRoute();
  const router = useRouter();

  const { completeSectionItemLoading } = useMyCourse();
  const { getProgress } = useVideoProgress();

  const completedValue = ref(props.lesson.isCompleted);

  const lessonSlug = computed(() => route.query.courseContentSlug?.toString());
  const lessonId = computed(() => props.lesson.id?.toString());
  const courseId = computed(() => props.course.id?.toString());
  const isLockIcon = computed(() => {
    return (
      (!props.lesson.isFree && props.lesson.slug !== lessonSlug.value && props.blockAccess) || props.lesson.isLocked
    );
  });

  const handleLesson = async () => {
    if ((props.blockAccess && !props.lesson.isFree) || props.lesson.isLocked) {
      return;
    }

    document.body.classList.remove('right-bar-enabled');
    if (!lessonId.value || !courseId.value) return;
    window.scrollTo({ top: 0, behavior: 'smooth' });

    setTimeout(() => {
      router.push({
        name: route.name,
        params: route.params,
        query: {
          ...route.query,
          courseContentSlug: props.lesson.slug
        }
      });
    }, 300);

    emit('fetchLesson', props.lesson.slug);
  };

  const toggleComplete = async () => {
    if (props.isPreview) return;
    if (!lessonId.value || !courseId.value) return;

    completeSectionItemLoading.value = true;

    try {
      const status = completedValue.value ? 'studying' : 'completed';
      await setSectionItemStatus(lessonId.value, courseId.value, status);

      completedValue.value = !completedValue.value;

      emit('update:complete', { id: props.lesson.id, isCompleted: completedValue.value });
    } catch (error) {
      console.error('Error updating lesson completion:', error);
    }

    completeSectionItemLoading.value = false;
  };

  watch(
    () => props.lesson.isCompleted,
    newVal => {
      completedValue.value = newVal;
      emit('update:complete', { id: props.lesson.id, isCompleted: completedValue.value });
    }
  );

  const videoProgressPCT = computed(() => {
    if (Array.isArray(props.lesson.videos) && props.lesson.videos.length > 0) {
      const progressMap = getProgress(String(props.lesson.id));
      if (!progressMap || progressMap.size === 0) return 0;

      let total = 0;
      progressMap.forEach(value => {
        total += value;
      });
      return Math.round(total / props.lesson.videos.length);
    }
    return 0;
  });
</script>
