<template>
  <router-link :to="`/drills/${drill.slug}`">
    <BCard no-body class="drill-card shadow-none rounded-4 mb-0 h-100">
      <BCardHeader class="bg-white rounded-top-4 py-3">
        <img
          class="btn p-0 w-100 drill-image"
          :src="
            drill.diagrams && drill.diagrams.length && drill.diagrams[0].imageUrl
              ? drill.diagrams[0].imageUrl
              : '/pool-table.png'
          "
        />
      </BCardHeader>
      <BCardBody class="py-0 pb-4">
        <div class="d-flex flex-column gap-2">
          <BadgeLevel :value="drill.levelI18n" :level="drill.level" class="font-size-12 drill-badge"></BadgeLevel>

          <div class="d-flex align-items-center justify-content-between mb-2">
            <div class="d-flex align-items-center">
              <BAvatar
                :src="drill.owner.imageUrl ? drill.owner.imageUrl : dummyAvatar"
                :alt="drill.owner.name"
                size="35"
                class="avatar-image me-2"
              />
              <h5 class="owner-name m-0">{{ drill.owner.name }}</h5>
            </div>

            <PriceDisplay
              :price="drill.price"
              :salePrice="drill.salePrice"
              displayStyle="compact"
              position="end"
            ></PriceDisplay>
          </div>

          <h4 class="text-black text-truncate d-block mb-0">
            {{ drill.title }}
          </h4>
          <div>
            <span v-for="item in drill.skills" class="badge-soft-primary badge rounded-pill mx-2" :key="item.id">
              {{ item.nameI18n }}
            </span>
          </div>
        </div>
      </BCardBody>
    </BCard>
  </router-link>
</template>

<script lang="ts" setup>
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';

  import { DrillInterface } from '@/utils/interface/drill/drill';

  import BadgeLevel from '@/components/base/DrillBadgeLevel.vue';
  import PriceDisplay from '@/components/base/PriceDisplay.vue';

  defineProps({
    drill: {
      type: Object as PropType<DrillInterface>,
      required: true
    }
  });
</script>
<style lang="scss" scoped>
  .owner-name {
    font-size: 16px;
    font-weight: 500;
  }
</style>
