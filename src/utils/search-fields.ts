import { Component } from 'vue';

import { OptionInterface, MultipleSelectOptionInterface } from './interface/select-options';

export default class SearchField {
  title: string;
  ransacker: string;
  icon: string;
  component: Component;
  colSizes?: {
    xxl?: number;
    lg?: number;
    md?: number;
  };
  options?: SearchFieldOptions;
  classes?: string;
  label?: string;

  constructor(
    title: string,
    ransacker: string,
    icon: string,
    component: Component,
    colSizes?: { xxl?: number; lg?: number; md?: number },
    options: SearchFieldOptions = new SearchFieldOptions(),
    classes?: string
  ) {
    this.title = title;
    this.icon = icon;
    this.ransacker = ransacker;
    this.component = component;
    this.colSizes = colSizes;
    this.options = options;
    this.classes = classes;
  }
}

export class SearchFieldOptions {
  selectOptions: OptionInterface[];
  dateRangeDefault: OptionInterface[] | null;
  defaultOptions: OptionInterface[];
  classes: string | null;
  multipleSelectOptions?: MultipleSelectOptionInterface;

  constructor({
    selectOptions = [],
    dateRangeDefault = [],
    defaultOptions = [],
    classes = null,
    multipleSelectOptions = {}
  }: {
    selectOptions?: OptionInterface[];
    dateRangeDefault?: OptionInterface[];
    defaultOptions?: OptionInterface[];
    classes?: string | null;
    multipleSelectOptions?: MultipleSelectOptionInterface;
  } = {}) {
    this.selectOptions = selectOptions;
    this.dateRangeDefault = dateRangeDefault;
    this.classes = classes;
    this.defaultOptions = defaultOptions;
    this.multipleSelectOptions = multipleSelectOptions;
  }
}
