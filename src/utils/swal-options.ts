import i18n from '@/plugin/i18n';

export const SwalIconOptions = {
  Warning: 'warning',
  Question: 'question',
  Success: 'success',
  Error: 'error',
  Info: 'info'
} as const;

type SwalIconType = (typeof SwalIconOptions)[keyof typeof SwalIconOptions];

interface SwalOptionsParams {
  title?: string;
  icon?: SwalIconType;
  html?: string;
  text?: string;
  showCancelButton?: boolean;
  confirmButtonText?: string;
  cancelButtonText?: string;
  confirmButtonColor?: string;
  scrollbarPadding?: boolean;
}

export class SwalOptions {
  title: string;
  icon: SwalIconType;
  html: string;
  text: string;
  showCancelButton: boolean;
  confirmButtonText: string;
  cancelButtonText: string;
  confirmButtonColor: string;
  scrollbarPadding: boolean;

  constructor({
    title = i18n.global.t('common.warning_title'),
    icon = SwalIconOptions.Warning,
    html = '',
    text = "You won't be able to revert this!",
    showCancelButton = true,
    confirmButtonText = i18n.global.t('common.ok_btn'),
    cancelButtonText = i18n.global.t('common.cancel'),
    confirmButtonColor = '#556ee6',
    scrollbarPadding = true
  }: SwalOptionsParams = {}) {
    this.title = title;
    this.icon = icon;
    this.html = html;
    this.text = text;
    this.showCancelButton = showCancelButton;
    this.confirmButtonText = confirmButtonText;
    this.cancelButtonText = cancelButtonText;
    this.confirmButtonColor = confirmButtonColor;
    this.scrollbarPadding = scrollbarPadding;
  }
}
