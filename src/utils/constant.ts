import { SiteType } from '@/utils/interface/common';

const AXIOS_METHOD_ENUMS = {
  GET: 'get',
  POST: 'post'
};
const DEFAULT_AXIOS_HEADER_ENUMS = {
  JSON: { 'Content-Type': 'application/json' },
  FORM_DATA: { 'Content-Type': 'multipart/form-data' }
};
const GQL_API_ENDPOINT_ENUMS = {
  TEACHER: 'teacher',
  PUBLIC: 'public',
  ADMIN: 'admin',
  USER: 'user'
};

const ROUTE_PREFIX_ENUMS = {
  TEACHER: '/teacher',
  ADMIN: '/admin',
  USER: '/user'
};

const GUARD = {
  TEACHER: 'teacher',
  ADMIN: 'admin',
  USER: 'user'
};

const OWNER_TYPE_ENUMS = {
  ADMIN: 'Admin',
  TEACHER: 'Teacher'
};

const ROUTE_PATH = {
  LOGIN: '/login',
  TEACHER_SETUP: '/teacher/setup',
  TEACHER_DASHBOARD: '/teacher/dashboard',
  TEACHER_COURSES: '/teacher/courses',
  ADMIN_SIGNIN: '/admin/login',
  ADMIN_DASHBOARD: '/admin/dashboard',
  ADMIN_TEACHERS: '/admin/teachers',
  USER_SIGNIN: '/user/login',
  NOT_FOUND: '/404',
  HOMEPAGE: '/',
  USER_DASHBOARD: '/home'
};

const DEFAULT_AXIOS_HEADER = DEFAULT_AXIOS_HEADER_ENUMS.JSON;
const DEFAULT_AXIOS_BASE_URL = import.meta.env.VITE_APP_BASE_API;

const DEFAULT_GQL_BASE_URL = `${DEFAULT_AXIOS_BASE_URL}/graphql`;
const DEFAULT_REST_BASE_URL = `${DEFAULT_AXIOS_BASE_URL}/rest`;
const DEFAULT_AXIOS_TIMEOUT = 60000;
const I18N_LOCALE = import.meta.env.VITE_APP_I18N_LOCALE || 'en';

const DEFAULT_AXIOS_CONFIG = {
  headers: DEFAULT_AXIOS_HEADER,
  baseURL: DEFAULT_REST_BASE_URL,
  timeout: DEFAULT_AXIOS_TIMEOUT
};

const DEFAULT_API_OPTIONS = {
  loading: true,
  toast: true
};

const CREATE_AXIOS_DEFAULT_CONFIG = {
  ...DEFAULT_AXIOS_CONFIG,
  ...DEFAULT_API_OPTIONS
};

const TEACHER_GQL_API_BASE_URL = `${DEFAULT_GQL_BASE_URL}/${GQL_API_ENDPOINT_ENUMS.TEACHER}`;
const ADMIN_GQL_API_BASE_URL = `${DEFAULT_GQL_BASE_URL}/${GQL_API_ENDPOINT_ENUMS.ADMIN}`;
const USER_GQL_API_BASE_URL = `${DEFAULT_GQL_BASE_URL}/${GQL_API_ENDPOINT_ENUMS.USER}`;
const PUBLIC_GQL_API_BASE_URL = `${DEFAULT_GQL_BASE_URL}/${GQL_API_ENDPOINT_ENUMS.PUBLIC}`;

const GQL_API_BASE_URL_ENUMS = {
  TEACHER: TEACHER_GQL_API_BASE_URL,
  ADMIN: ADMIN_GQL_API_BASE_URL,
  USER: USER_GQL_API_BASE_URL,
  PUBLIC: PUBLIC_GQL_API_BASE_URL
};
const LANGUAGES = {
  VI: 'vi',
  EN: 'en'
};

const SITES: Record<string, SiteType> = {
  TEACHER: 'teacher',
  ADMIN: 'admin',
  USER: 'user',
  PUBLIC: 'public'
};

const CENSOR_STATUS_ENUMS = {
  SUBMITTED: 'submitted',
  REJECTED: 'rejected',
  APPROVED: 'approved',
  DRAFT: 'draft'
} as const;

const COURSE_PUBLIC_STATUS_ENUMS = {
  PRIVATE: 'private',
  PUBLIC: 'public'
};

const NAVIGATION_TYPE = {
  NEXT: 'next',
  PREV: 'prev'
};

const COURSE_SECTION_ITEM_TYPE = {
  TEXT: 'text',
  VIDEO: 'video',
  DRILL: 'drill'
};

const COURSE_ORDER_BY = {
  CREATED_AT_DESC: 'recentUploaded',
  STUDENTS_COUNT_DESC: 'bestSeller',
  RATING_COUNT_DESC: 'bestRated',
  PRICE_DESC: 'price desc',
  PRICE_ASC: 'price asc'
};

const DRILL_ORDER_BY = {
  CREATED_AT_DESC: 'drills.created_at desc',
  CREATED_AT_ASC: 'drills.created_at asc'
};

const TEACHER_ORDER_BY = {
  CREATED_AT_DESC: 'id DESC',
  CREATED_AT_ASC: 'id ASC',
  APPROVED_COURSE_COUNT_DESC: 'approved_course_count DESC',
  APPROVED_COURSE_COUNT_ASC: 'approved_course_count ASC',
  STUDENTS_COUNT_DESC: 'student_count DESC',
  STUDENTS_COUNT_ASC: 'student_count ASC',
  AVERAGE_RATING_DESC: 'average_rating DESC'
};

const NUMBER_MAX_LENGTH_9 = '#########';
const MAX_LENGTH_500 = 500;
const MAX_LENGTH_255 = 255;

const COURSE_ELEMENT_TYPE = {
  COURSE: 'course',
  COURSE_SECTION: 'courseSection',
  COURSE_SECTION_ITEM: 'courseSectionItem'
};

const EDITOR_MASTER_MEDIA_TYPE = {
  COURSE: 'course',
  DRILL: 'drills'
};

const COUNTDOWN_SECONDS = 60;

const ASPECT_RATIO_ENUMS = {
  '21:9': 21 / 9,
  '16:9': 16 / 9,
  '4:3': 4 / 3,
  '3:2': 3 / 2,
  '1:1': 1,
  FREE: 0
};

const THEME_COLOR_ENUMS = {
  PRIMARY: 'primary',
  SECONDARY: 'secondary',
  SUCCESS: 'success',
  INFO: 'info',
  WARNING: 'warning',
  DANGER: 'danger',
  PINK: 'pink',
  LIGHT: 'light',
  DARK: 'dark'
};

const FILE_TYPE_ENUMS = {
  AVIF: 'image/avif',
  GIF: 'image/gif',
  JPEG: 'image/jpeg',
  JPG: 'image/jpg',
  M4V: 'video/x-m4v',
  MATROSKA: 'video/x-matroska',
  MP4: 'video/mp4',
  MPEG: 'video/mpeg',
  OGG: 'video/ogg',
  PNG: 'image/png',
  QUICKTIME: 'video/quicktime',
  SVG: 'image/svg+xml',
  WEBM: 'video/webm',
  WEBP: 'image/webp'
} as const;

const IMAGE_SMOOTHING_QUALITY_ENUMS = {
  HIGH: 'high',
  LOW: 'low',
  MEDIUM: 'medium'
};

const COMMENT_TARGET_TYPE = {
  COURSE: 'Course',
  TEACHER: 'Teacher',
  PRACTICE_SUBMISSION: 'PracticeSubmission'
};

const QUILL_THEME_ENUMS = {
  SNOW: 'snow',
  BUBBLE: 'bubble'
};

const QUILL_EMITTER_SOURCE_ENUMS = {
  API: 'api',
  SILENT: 'silent',
  USER: 'user'
} as const;

const QUILL_EMBED_TYPE_ENUMS = {
  DIVIDER: 'divider',
  IMAGE: 'image',
  VIDEO: 'video',
  TWEET: 'tweet'
};

const SWIPER_DIRECTION_ENUMS = {
  HORIZONTAL: 'horizontal',
  VERTICAL: 'vertical'
} as const;

const SWIPER_MODULE_ENUMS = {
  A11Y: 'A11y',
  AUTOPLAY: 'Autoplay',
  CONTROLLER: 'Controller',
  EFFECT_COVERFLOW: 'EffectCoverflow',
  EFFECT_CUBE: 'EffectCube',
  EFFECT_FADE: 'EffectFade',
  EFFECT_FLIP: 'EffectFlip',
  EFFECT_CREATIVE: 'EffectCreative',
  EFFECT_CARDS: 'EffectCards',
  HASH_NAVIGATION: 'HashNavigation',
  HISTORY: 'History',
  KEYBOARD: 'Keyboard',
  MOUSEWHEEL: 'Mousewheel',
  NAVIGATION: 'Navigation',
  PAGINATION: 'Pagination',
  PARALLAX: 'Parallax',
  SCROLLBAR: 'Scrollbar',
  THUMBS: 'Thumbs',
  VIRTUAL: 'Virtual',
  ZOOM: 'Zoom',
  FREE_MODE: 'FreeMode',
  GRID: 'Grid',
  MANIPULATION: 'Manipulation'
} as const;

const POSITION_ENUMS = {
  START: 'start',
  END: 'end',
  CENTER: 'center'
} as const;

const PACKAGES_NAMES = {
  BASIC: 'Basic',
  ADVANCE: 'Advance',
  OFFLINE: 'Offline'
};

const POOL_TABLE_PLACEHOLDER_URL = '/pool-table.png';

const SCREEN_SIZE_BREAKPOINT_ENUMS = {
  XXL: {
    key: 'xxl',
    minWidth: 1400,
    maxWidth: Infinity
  },
  XL: {
    key: 'xl',
    maxWidth: 1399,
    minWidth: 1200
  },
  LG: {
    key: 'lg',
    maxWidth: 1199,
    minWidth: 992
  },
  MD: {
    key: 'md',
    maxWidth: 991,
    minWidth: 768
  },
  SM: {
    key: 'sm',
    maxWidth: 767,
    minWidth: 576
  },
  XS: {
    key: 'xs',
    maxWidth: 575,
    minWidth: 0
  }
};

export {
  ASPECT_RATIO_ENUMS,
  AXIOS_METHOD_ENUMS,
  CENSOR_STATUS_ENUMS,
  COMMENT_TARGET_TYPE,
  COUNTDOWN_SECONDS,
  COURSE_ELEMENT_TYPE,
  COURSE_ORDER_BY,
  COURSE_PUBLIC_STATUS_ENUMS,
  COURSE_SECTION_ITEM_TYPE,
  CREATE_AXIOS_DEFAULT_CONFIG,
  DEFAULT_AXIOS_HEADER_ENUMS,
  DEFAULT_GQL_BASE_URL,
  DRILL_ORDER_BY,
  EDITOR_MASTER_MEDIA_TYPE,
  FILE_TYPE_ENUMS,
  GQL_API_BASE_URL_ENUMS,
  GQL_API_ENDPOINT_ENUMS,
  GUARD,
  I18N_LOCALE,
  IMAGE_SMOOTHING_QUALITY_ENUMS,
  LANGUAGES,
  MAX_LENGTH_255,
  MAX_LENGTH_500,
  NAVIGATION_TYPE,
  NUMBER_MAX_LENGTH_9,
  OWNER_TYPE_ENUMS,
  PACKAGES_NAMES,
  POOL_TABLE_PLACEHOLDER_URL,
  POSITION_ENUMS,
  QUILL_EMBED_TYPE_ENUMS,
  QUILL_EMITTER_SOURCE_ENUMS,
  QUILL_THEME_ENUMS,
  ROUTE_PATH,
  ROUTE_PREFIX_ENUMS,
  SCREEN_SIZE_BREAKPOINT_ENUMS,
  SITES,
  SWIPER_DIRECTION_ENUMS,
  SWIPER_MODULE_ENUMS,
  TEACHER_ORDER_BY,
  THEME_COLOR_ENUMS
};
