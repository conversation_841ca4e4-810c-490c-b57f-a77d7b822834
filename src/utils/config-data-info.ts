import i18n from '@/plugin/i18n';

export const packageDetails = {
  basic: {
    title: i18n.global.t('teacher.course_package.basic.title'),
    description: i18n.global.t('teacher.course_package.basic.description'),
    headerIcon: {
      icon: 'mdi mdi-account-circle',
      class: 'text-success'
    },
    headerBadge: '',
    features: [
      {
        icon: 'mdi mdi-clock-outline',
        content: i18n.global.t('teacher.course_package.basic.features.1.content'),
        class: 'text-success'
      },
      {
        icon: 'mdi mdi-check-circle-outline',
        content: i18n.global.t('teacher.course_package.basic.features.2.content'),
        class: 'text-success'
      }
    ]
  },
  advance: {
    title: i18n.global.t('teacher.course_package.advance.title'),
    description: i18n.global.t('teacher.course_package.advance.description'),
    headerIcon: {
      icon: 'mdi mdi-star-circle',
      class: 'text-warning'
    },
    extraIcon: {
      icon: 'mdi mdi-check-circle-outline',
      class: 'selected-icon'
    },
    headerBadge: {
      title: i18n.global.t('teacher.course_package.advance.badge_title'),
      class: 'popular-badge'
    },
    features: [
      {
        icon: 'mdi mdi-clock-outline',
        content: i18n.global.t('teacher.course_package.advance.features.1.content'),
        class: 'text-success'
      },
      {
        icon: 'mdi mdi-check-circle-outline',
        content: i18n.global.t('teacher.course_package.advance.features.2.content'),
        class: 'text-success'
      },
      {
        icon: 'mdi mdi-check-circle-outline',
        content: i18n.global.t('teacher.course_package.advance.features.3.content'),
        class: 'text-success'
      },
      {
        icon: 'mdi mdi-check-circle-outline',
        content: i18n.global.t('teacher.course_package.advance.features.4.content'),
        class: 'text-success'
      }
    ]
  },
  offline: {
    title: i18n.global.t('teacher.course_package.offline.title'),
    description: i18n.global.t('teacher.course_package.offline.description'),
    headerIcon: {
      icon: 'mdi mdi-diamond-stone',
      class: 'text-primary'
    },
    extraIcon: {
      icon: 'mdi mdi-check-circle-outline',
      class: 'selected-icon'
    },
    headerBadge: {
      title: i18n.global.t('teacher.course_package.offline.badge_title'),
      class: 'popular-badge'
    },
    features: [
      {
        icon: 'mdi mdi-clock-outline',
        content: i18n.global.t('teacher.course_package.offline.features.1.content'),
        class: 'text-success'
      },
      {
        icon: 'mdi mdi-check-circle-outline',
        content: i18n.global.t('teacher.course_package.offline.features.2.content'),
        class: 'text-success'
      },
      {
        icon: 'mdi mdi-check-circle-outline',
        content: i18n.global.t('teacher.course_package.offline.features.3.content'),
        class: 'text-success'
      },
      {
        icon: 'mdi mdi-check-circle-outline',
        content: i18n.global.t('teacher.course_package.offline.features.4.content'),
        class: 'text-success'
      }
    ]
  }
};
