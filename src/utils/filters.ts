import i18n from '@/plugin/i18n';
import moment from 'moment';

export const filters: Record<string, (...args: any[]) => string | number | null> = {
  formatDate(time: string): string {
    if (!time || time == '0001-01-01T00:00:00Z') return '';
    return moment(time).format('YYYY/MM/DD');
  },

  formatDatetime(time: string): string {
    if (!time || time == '0001-01-01T00:00:00Z') return '';
    return moment(time).format('YYYY/MM/DD HH:mm');
  },

  formatDatetimeUTC(time: string): string {
    if (!time || time == '0001-01-01T00:00:00Z') return '';
    return moment(time).utc().format('YYYY/MM/DD HH:mm');
  },

  formatDatetimeUTC_DDMMYYYY(time: string): string {
    if (!time || time == '0001-01-01T00:00:00Z') return '';
    return moment(time).utc().format('DD/MM/YYYY HH:mm');
  },

  formatDateUTC_DDMMYYYY(time: string): string {
    if (!time || time == '0001-01-01T00:00:00Z') return '';
    return moment(time).utc().format('DD/MM/YYYY');
  },

  formatDatetimeSecond(time: string): string {
    if (!time || time == '0001-01-01T00:00:00Z') return '';
    return moment(time).format('YYYY/MM/DD HH:mm:ss');
  },

  formatYear(time: string): string {
    if (!time || time == '0001-01-01T00:00:00Z') return '';
    return moment(time).format('YYYY');
  },

  toNumberOrNull(value: any): number | null {
    return value === '' || value === null || value === undefined ? null : Number(value);
  },

  toCamelCase(str: string): string {
    return str
      .replace(/([-_][a-z])/g, group => group.toUpperCase().replace(/[-_]/g, ''))
      .replace(/^\w/, firstChar => firstChar.toLowerCase());
  },

  formattedPrice(price: number): string {
    return Number(price).toLocaleString('vi-VN', { style: 'currency', currency: 'VND' });
  },

  formatRelativeTime(time: string): string {
    if (!time || time === '0001-01-01T00:00:00Z') return '';

    const now = moment();
    const target = moment(time);
    const diffInMinutes = now.diff(target, 'minutes');
    const diffInHours = now.diff(target, 'hours');

    if (diffInMinutes == 0) {
      return i18n.global.t('time.now');
    }

    if (diffInMinutes < 60) {
      return `${diffInMinutes} ${i18n.global.t('time.minutes_ago')}`;
    }

    if (diffInHours < 24) {
      return `${diffInHours} ${i18n.global.t('time.hours_ago')}`;
    }

    return target.format('YYYY/MM/DD HH:mm');
  },

  convertKeysToCamelCase(obj: any): any {
    if (Array.isArray(obj)) {
      return obj.map(v => this.convertKeysToCamelCase(v));
    } else if (obj !== null && obj.constructor === Object) {
      return Object.keys(obj).reduce(
        (result: Record<string, any>, key: string) => ({
          ...result,
          [this.toCamelCase(key) as any]: this.convertKeysToCamelCase(obj[key])
        }),
        {}
      );
    }
    return obj;
  },

  roundMathPercent(percent: number): number {
    return Math.round(percent);
  },

  formatCount(count: number): string {
    if (count <= 10) return count.toString();
    const rounded = count > 1000 ? Math.floor(count / 100) * 100 : Math.floor(count / 10) * 10;
    return `${rounded}+`;
  }
};
export default filters;
