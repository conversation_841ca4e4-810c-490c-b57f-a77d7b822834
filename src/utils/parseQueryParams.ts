export default {
  parseIdsArray: (value: string | string[] | number[] | undefined) => {
    if (value) {
      return value instanceof Array ? value.map(e => Number(e)) : [Number(value)];
    } else return [];
  },
  parseBoolean: (value: string | number | boolean | undefined) => {
    if (value === undefined) return;

    if (typeof value === 'string') {
      return /^(true|1)$/i.test(value);
    }
    return Boolean(value);
  }
};
