import i18n from '@/plugin/i18n';
import { ROUTE_PREFIX_ENUMS } from '@/utils/constant';

export const teacherMenuItems = [
  {
    id: 1,
    label: i18n.global.t('teacher.top_menu.users.label'),
    icon: 'bx-user',
    link: `${ROUTE_PREFIX_ENUMS.TEACHER}/users`
  },
  {
    id: 2,
    label: i18n.global.t('teacher.top_menu.course.label'),
    icon: 'bx-food-menu',
    link: `${ROUTE_PREFIX_ENUMS.TEACHER}/courses`
  },
  {
    id: 3,
    label: i18n.global.t('teacher.top_menu.drills.label'),
    icon: 'bx-border-all',
    link: `${ROUTE_PREFIX_ENUMS.TEACHER}/drills`
  },
  {
    id: 4,
    label: i18n.global.t('teacher.top_menu.editor.label'),
    icon: 'bx-edit-alt',
    link: `${ROUTE_PREFIX_ENUMS.TEACHER}/editor`
  }
];

export const adminMenuItems = [
  {
    id: 1,
    label: i18n.global.t('admin.top_menu.teachers.label'),
    icon: 'bx-user-check',
    link: `${ROUTE_PREFIX_ENUMS.ADMIN}/teachers`
  },
  {
    id: 2,
    label: i18n.global.t('admin.top_menu.users.label'),
    icon: 'bx-user-check',
    link: `${ROUTE_PREFIX_ENUMS.ADMIN}/users`
  },
  {
    id: 3,
    label: i18n.global.t('admin.top_menu.courses.label'),
    icon: 'bx-food-menu',
    link: `${ROUTE_PREFIX_ENUMS.ADMIN}/courses`
  },
  {
    id: 4,
    label: i18n.global.t('admin.top_menu.drills.label'),
    icon: 'bx-border-all',
    link: `${ROUTE_PREFIX_ENUMS.ADMIN}/drills`
  },
  {
    id: 5,
    label: i18n.global.t('admin.top_menu.editor.label'),
    icon: 'bx-edit-alt',
    link: `${ROUTE_PREFIX_ENUMS.ADMIN}/editor/drill/new?tabIndex=0`,
    activeOverridePath: `${ROUTE_PREFIX_ENUMS.ADMIN}/editor`
  }
];

export const publicMenuItems = [
  {
    id: 1,
    label: i18n.global.t('public.top_menu.home'),
    link: '/home',
    activeOverridePath: '/'
  },
  {
    id: 2,
    label: i18n.global.t('public.top_menu.courses'),
    link: '/courses'
  },
  {
    id: 3,
    label: i18n.global.t('admin.top_menu.drills.label'),
    link: '/drills'
  },
  {
    id: 5,
    label: i18n.global.t('public.top_menu.teachers'),
    link: '/teachers',
    requiresAuth: false
  },
  {
    id: 4,
    label: i18n.global.t('public.top_menu.my_courses'),
    link: '/my-courses',
    requiresAuth: true
  }
];
