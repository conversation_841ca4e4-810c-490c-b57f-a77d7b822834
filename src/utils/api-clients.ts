import { RestApiClient } from '@/services/base/restful';
import { GraphQLApiClient } from '@/services/base/graphql';
import { GQL_API_ENDPOINT_ENUMS, GQL_API_BASE_URL_ENUMS } from '@/utils/constant';

const restApiClient = new RestApiClient();

const graphqlApiClients = Object.keys(GQL_API_ENDPOINT_ENUMS).reduce(
  (clients: Record<string, GraphQLApiClient>, endpoint) => {
    clients[endpoint as keyof typeof GQL_API_BASE_URL_ENUMS] = new GraphQLApiClient(endpoint);
    return clients;
  },
  {}
);

export { restApiClient, graphqlApiClients };
