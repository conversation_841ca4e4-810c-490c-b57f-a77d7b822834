import Notify from 'simple-notify';
import { IArgs } from 'simple-notify';

import 'simple-notify/dist/simple-notify.css';

interface ToastProps {
  title?: string;
  text?: string;
}

class Toast {
  private static readonly DefaultSetting: IArgs = {
    type: 'outline',
    effect: 'fade',
    position: 'right top',
    showIcon: true,
    customIcon: '',
    showCloseButton: true,
    customClass: '',
    speed: 300,
    autoclose: true,
    autotimeout: 2000,
    notificationsGap: 20,
    notificationsPadding: 20,
    customWrapper: ''
  };

  private static activeNotifications: Set<string> = new Set();

  private static createKey(status: string, title: string, text?: string): string {
    return `${status}:${title}:${text || ''}`;
  }

  private static showNotification(status: string, title: string, text?: string): boolean {
    const key = this.createKey(status, title, text);
    if (this.activeNotifications.has(key)) {
      return false;
    }

    this.activeNotifications.add(key);
    setTimeout(
      () => {
        this.activeNotifications.delete(key);
      },
      status === 'error' ? 3000 : 2300
    );

    return true;
  }

  public static error({ title = 'Error Happened!', text }: ToastProps) {
    if (!this.showNotification('error', title, text)) return;
    new Notify({
      ...this.DefaultSetting,
      status: 'error',
      title: title,
      text: text,
      speed: 1000
    });
  }

  public static success({ title = 'Success!!!', text }: ToastProps) {
    if (!this.showNotification('success', title, text)) return;
    new Notify({
      ...this.DefaultSetting,
      status: 'success',
      title: title,
      text: text
    });
  }

  public static warning({ title = 'Warning!!!', text }: ToastProps) {
    if (!this.showNotification('warning', title, text)) return;
    new Notify({
      ...this.DefaultSetting,
      status: 'warning',
      title: title,
      text: text
    });
  }
}

export default Toast;
