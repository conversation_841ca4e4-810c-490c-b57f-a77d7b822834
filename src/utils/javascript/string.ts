String.prototype.toNumberOrNull = function () {
  if (this == '') return null;
  return Number(this);
};

String.prototype.removeSnakeCase = function () {
  return this.replace(/_/g, ' ');
};

String.prototype.convertPascalCaseWithSpaces = function () {
  // input: "matchStatus"
  // output: "MATCH STATUS"

  return this.replace(/([A-Z])/g, ' $1')
    .trim()
    .toUpperCase();
};

String.prototype.convertPascalCaseWithSpaces = function () {
  // input: "matchStatus"
  // output: "MATCH STATUS"

  return this.replace(/([A-Z])/g, ' $1')
    .trim()
    .toUpperCase();
};

String.prototype.toCamelCase = function () {
  return this.toString()
    .replace(/[_\s]+(.)/g, (_, char) => char.toUpperCase())
    .replace(/^(.)/, char => char.toLowerCase());
};
