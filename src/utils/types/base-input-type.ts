export class BaseInputType {
  [key: string]: any;

  assignAttributes(input: any) {
    if (input) {
      Object.keys(this).forEach(key => {
        if (Object.prototype.hasOwnProperty.call(input, key) && !(this[key] instanceof BaseInputType)) {
          this[key] = cloneDeep(input[key]);
        }
      });
    }
  }

  toNumberOrNull(value: unknown): number | null {
    if (value === null || value === undefined || value === '') {
      return null;
    }

    const num = Number(value);
    return isNaN(num) ? null : num;
  }
}
