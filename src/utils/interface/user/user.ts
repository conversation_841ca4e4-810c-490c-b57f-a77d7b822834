import { InvitedCourseUserInterface } from './userCourse';

export interface UserInterface {
  id?: string | number | null;
  name?: string;
  gender?: string;
  imageUrl?: string;
  birthDate?: string;
  createdAt?: string;
  updatedAt?: string;
  invitedCourseUsers?: InvitedCourseUserInterface[];
  unreadNotificationCount?: number;
}

export interface UserDrillInterface {
  id: number;
  userId: number;
  drillId: number;
}
