import { PagyInput } from '../common';
import { CourseSectionInterface } from './courseSection';
import { TeacherInterface } from './teacher';
import { ReviewInterface } from '@/utils/interface/user/comment';

export interface UserCourseInterface {
  id?: number;
  teacherId?: number;
  title?: string;
  description?: string;
  salePrice?: number;
  price?: number;
  bonusPoint?: number;
  bonusPointPercent?: number;
  joined?: boolean;
  processPercent?: number;
  sectionCount?: number;
  sectionItemCount?: number;
  banner?: string;
  slug?: string;
  joinedUserCount?: number;
  instructionalLevel?: string;
  instructionalLevelI18n?: string;
  averageRating?: number;
  isPublic?: boolean;
  status?: string;
  courseUserMetadata?: CourseUserMetadata;
  currentUserJoining?: CurrentUserJoiningInterface;
  createdAt?: string;
  updatedAt?: string;
  teacher: TeacherInterface;
  courseSections: CourseSectionInterface[];
  myReview?: ReviewInterface;
  coursePackages: CoursePackageInterface[];
  myPackage?: CoursePackageInterface;
  certificate?: CertificateInterface;
}

export interface UserCoursesQueryInput {
  titleCont?: string;
  descriptionCont?: string;
  statusEq?: string;
  categoryCont?: string;
}

export interface UserCourseListInput {
  input: PagyInput;
  query: UserCoursesQueryInput;
  orderBy?: string;
}

export interface CurrentUserJoiningInterface {
  id?: number;
  courseId?: number;
  userId?: number;
  status?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface InvitedCourseUserInterface {
  id?: number;
  courseId?: number;
  userId?: number;
  status?: string;
  createdAt?: string;
  updatedAt?: string;
  course?: UserCourseInterface;
}

export interface CourseUserMetadata {
  isReviewTeacherInCourse?: boolean;
}

export interface CoursePackageInterface {
  id?: string | number | null;
  salePrice?: number | null;
  packageDealId: string;
  price?: number | null;
  packageDeal?: PackageDeal | null;
}

export interface PackageDeal {
  id: string | number | null;
  name: string | null;
  description?: string | null;
}

export interface UserJoinCourseParams {
  id: string;
  verifyCode?: string | null;
  coursePackageID?: string | null;
}

export interface CertificateInterface {
  title?: string;
  description?: string;
  isActive?: string;
}
