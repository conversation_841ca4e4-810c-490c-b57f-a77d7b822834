import { TeacherInterface } from '@/utils/interface/user/teacher';
import { UserInterface } from '@/utils/interface/user/user';

export interface CommentInterface {
  id: number;
  content: string;
  authorID: number;
  authorType: string;
  targetID: string;
  targetType: string;
  parentID?: string;
  createdAt: string;
  updatedAt: string;

  authorUser: UserInterface;
  authorTeacher: TeacherInterface;
}

export interface CommentCreateInterface {
  targetID: string;
  targetType: string;
  content: string;
  referenceID?: string;
}

export interface ReviewInterface extends CommentInterface {
  rating: number;
}

export interface ReviewCreateInterface extends CommentCreateInterface {
  rating: number;
}

export interface ReviewUpdateInterface {
  rating: number;
  content: string;
}
