import { PagyInput } from '../common';
import { CommentInterface } from './comment';

export interface PracticeSubmissionInterface {
  id: number;
  practiceId: number;
  practiceType: string;
  userId: string;
  content?: string;
  status: string;
  createdAt: string;
  updatedAt: string;

  comments: CommentInterface[];
}

export interface PracticeSubmissionCreateInterface {
  practiceId: string;
  practiceType: string;
  content?: string;
}

export interface PracticeSubmissionListInput {
  input: PagyInput;
  query: PracticeSubmissionsQueryInput;
}

export interface PracticeSubmissionsQueryInput {
  statusEq?: string | null;
  practiceIDEq?: number | null;
  practiceTypeEq?: string | null;
}
