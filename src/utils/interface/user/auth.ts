export interface SignInInputInterface {
  identifier: string;
  password: string;
}

export interface SignUpInputInterface {
  phoneNumber: string;
  password: string;
  passwordConfirmation: string;
}

export interface ChangePasswordInterface {
  password?: string | null;
  newPassword?: string | null;
  confirmPassword?: string | null;
}

export interface ResetPasswordVerifyInterface {
  phoneNumber?: string | null;
  code?: string | null;
}

export interface ResetPasswordInterface {
  phoneNumber?: string | null;
  code?: string | null;
  password?: string | null;
  passwordConfirmation?: string | null;
}
