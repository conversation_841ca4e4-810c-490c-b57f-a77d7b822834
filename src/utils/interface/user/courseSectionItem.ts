import { DrillInterface } from '../drill/drill';
import { UserCourseSectionItemInterface } from './userCourseSectionItem';
import { VideoInterface } from '@/utils/interface/video';

export interface CourseSectionItemInterface {
  id?: string | number | null;
  courseSectionId: string | number;
  title?: string;
  slug?: string;
  type: string;
  isFree: boolean;
  isCompleted: boolean;
  currentUserSectionItem?: UserCourseSectionItemInterface;
  content?: string;
  isLocked: boolean;
  drills?: DrillInterface[];
  videos: VideoInterface[];
}
