import { PagyInput } from '../common';

export interface TeacherInterface {
  id?: string | number | null;
  name?: string;
  slug?: string;
  description?: string;
  phoneNumber?: string;
  contactEmail?: string;
  basicEntered?: boolean;
  award?: string;
  address?: string;
  imageUrl?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface TeachersQueryInput {
  nameCont?: string;
}

export interface TeacherListInput {
  input: PagyInput;
  query: TeachersQueryInput;
  orderBy?: string;
}
