import { DrillInterface } from '@/utils/interface/drill/drill';
import { PagyInput } from '@/utils/interface/common';

export interface DrillListQueryInput {
  titleCont?: string;
}

export interface DrillListParamsInterface {
  input: PagyInput;
  query: DrillListQueryInput;
  orderBy?: string;
}

export interface DrillResponseInterface {
  drill: DrillInterface;
}

export interface UserDrillListInterface {
  input: PagyInput;
}
