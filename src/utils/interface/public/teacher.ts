import { CourseInterface } from './course';
import { DrillInterface } from '@/utils/interface/drill/drill';
import { PagyInput } from '../common';
import { RatingInterface } from './comment';

export interface TeacherInterface {
  id?: string | number | null;
  name?: string;
  description?: string;
  phoneNumber?: string;
  slug?: string;
  contactEmail?: string;
  basicEntered?: boolean;
  award?: string;
  address?: string;
  imageUrl?: string;
  courses: CourseInterface[];
  drills: DrillInterface[];
  ratings: RatingInterface[];
  approvedCourseCount?: number;
  approvedDrillCount?: number;
  averageRating: number;
  studentCount?: number;
  createdAt?: string;
  updatedAt?: string;
  joinedStudentCount?: number;
  availableRating?: boolean;
  isProfessional?: boolean;
  socialLinks?: SocialLinksInterface;
  experiences?: ExperienceInterface[];
}

export interface SocialLinksInterface {
  facebook?: string;
  twitter?: string;
  instagram?: string;
  youtube?: string;
  telegram?: string;
  discord?: string;
  zalo?: string;
  tiktok?: string;
}

export interface TeachersQueryInput {
  nameCont?: string;
  isProfessional?: string;
}

export interface TeacherListInput {
  input: PagyInput;
  query: TeachersQueryInput;
  orderBy: string;
}

export interface ExperienceInterface {
  id?: string | number | null;
  ownerID?: string | number | null;
  ownerType?: string;
  iconID?: string | number | null;
  experienceType?: string;
  experienceTime?: string;
  isOutstanding?: boolean;
  title?: string;
  description?: string;
  createdAt?: string;
  updatedAt?: string;
  icon?: Icon;
}

export interface Icon {
  id?: string | number | null;
  key?: string;
  class?: string;
}
