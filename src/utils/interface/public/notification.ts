export interface NotificationQueryInput {
  isReadEq?: boolean;
}

export interface NotificationInterface {
  id: string;
  isRead: boolean;
  title: string;
  body: string;
  noticeKind: string;
  createdAt: string;
  readAt: string;
  sender: {
    id: string;
    name: string;
    imageUrl: string;
    type: string;
  };
  entity: {
    id: string;
    title: string;
    slug: string;
    type: string;
  };
}
