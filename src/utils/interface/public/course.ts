import { PagyInput } from '@/utils/interface/common';
import { TeacherInterface } from './teacher';

export interface CourseListQueryInput {
  titleCont?: string;
  categoryCont?: string;
  teacherNameCont?: string;
  salePriceRange?: [number, number];
}

export interface CourseInterface {
  id: number;
  teacherId: number;
  title: string;
  slug: string;
  description: string;
  salePrice: number;
  price: number;
  bonusPoint: number;
  bonusPointPercent: number;
  status: string;
  banner: string;
  processPercent?: number;
  studyDuration?: number;
  sectionCount: number;
  sectionItemCount: number;
  joinedUserCount: number;
  instructionalLevel: string;
  instructionalLevelI18n: string;
  totalRateCount?: number;
  averageRating: number;
  teacher: TeacherInterface;
}

export interface RelatedCoursesParamsInterface {
  input: PagyInput;
  courseSlug: string;
}
