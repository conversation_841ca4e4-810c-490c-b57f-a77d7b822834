import { InternalAxiosRequestConfig, AxiosRequestConfig } from 'axios';

interface CustomInternalAxiosRequestConfig extends InternalAxiosRequestConfig {
  toast?: boolean;
  site?: string;
  message?: string;
  tokenSource?: string;
}

interface CustomAxiosRequestConfig {
  toast?: boolean;
  site?: string;
  headers?: AxiosRequestConfig['headers'];
  tokenSource?: string;
}

export { CustomInternalAxiosRequestConfig, CustomAxiosRequestConfig };
