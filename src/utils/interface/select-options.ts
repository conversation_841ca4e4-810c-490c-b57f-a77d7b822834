export interface SelectOptionsInterface {
  skillOptions?: OptionInterface[];
  levelOptions?: OptionInterface[];
  courseStatusOptions?: OptionInterface[];
  teacherOptions?: OptionInterface[];
  courseInstructionalLevelOptions?: OptionInterface[];
  packageDealOptions?: OptionInterface[];
  iconOptions?: IconInterface[];
}

export interface SelectOptionsResponseInterface {
  selectOptions: SelectOptionsInterface;
}

export interface OptionInterface {
  label: string;
  value: string | number | number[] | boolean;
  description?: string;
}

export interface MultipleSelectOptionInterface {
  label?: string;
  valueProp?: string;
}

export interface IconInterface {
  label: string;
  value: number;
  class: string;
}
