import { PagyInput } from '../common';

export interface TeacherInterface {
  id?: string | number | null;
  name?: string;
  username?: string;
  award?: string;
  address?: string;
  phoneNumber?: string;
  contactEmail?: string;
  basicEntered?: boolean;
  canInviteStudents: boolean;
  description?: string;
  imageUrl?: string;
  active?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface TeacherListQueryInput {
  nameCont?: string;
  phoneNumberCont?: string;
}

export interface TeacherListParams {
  input: PagyInput;
  query?: TeacherListQueryInput;
}
