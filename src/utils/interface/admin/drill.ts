import { DrillInterface } from '@/utils/interface/drill/drill';
import { PagyInput } from '@/utils/interface/common';

export interface DrillListQueryInput {
  titleCont?: string;
  levelIn?: number[];
  skillIDIn?: number[];
}

export interface DrillListParamsInterface {
  input: PagyInput;
  query: DrillListQueryInput;
}

export interface DrillCreateResponseInterface {
  drillCreate: {
    message: string;
    drill: DrillInterface;
  };
}

export interface DrillUpdateResponseInterface {
  drillUpdate: {
    message: string;
    drill: DrillInterface;
  };
}

export interface DrillResponseInterface {
  drill: DrillInterface;
}
