import { PagyInput } from '@/utils/interface/common';

export interface UserInterface {
  id?: string | number | null;
  name?: string;
  phoneNumber?: string;
  gender?: string;
  imageUrl?: string;
  birthDate?: string;
  active?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface UserListInput {
  input: PagyInput;
  query?: UserListQueryInput;
}

export interface UserListQueryInput {
  nameCont?: string;
  phoneNumberCont?: string;
}
