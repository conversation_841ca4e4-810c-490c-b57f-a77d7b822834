import { PagyInput } from '@/utils/interface/common';
import { TeacherInterface } from './teacher';

import { CensorHistory } from '../censorHistory';

export interface CourseListQueryInput {
  titleCont?: string;
  descriptionCont?: string;
  statusIn?: string[];
}

export interface CourseListParamsInterface {
  input: PagyInput;
  query: CourseListQueryInput;
}

export interface CourseFormInterface {
  id?: number;
  teacherId?: number;
  teacher?: TeacherInterface;
  courseCensorHistories?: CensorHistory[];
  title?: string;
  description?: string;
  salePrice?: number;
  price?: number;
  bonusPoint?: number;
  bonusPointPercent?: number;
  joinedUserCount?: number;
  status?: string;
  statusI18n?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface CourseInterface {
  id: number;
  teacherId: number;
  title: string;
  description: string;
  salePrice: number;
  price: number;
  bonusPoint: number;
  bonusPointPercent: number;
  status: string;
  statusI18n: string;
  banner: string;
  joinedUserCount: number;
  instructionalLevel: string;
  instructionalLevelI18n: string;
  averageRating: number;
  sectionCount: number;
  teacher: TeacherInterface;
}
