import { DrillInterface } from '@/utils/interface/drill/drill';
import { PagyInput } from '@/utils/interface/common';

export interface DrillListQueryInput {
  titleCont?: string;
  levelIn?: number[];
  skillIDIn?: number[];
}

export interface DrillListParamsInterface {
  input: PagyInput;
  query: DrillListQueryInput;
}

export interface DrillResponseInterface {
  message: string;
  drill: DrillInterface;
}

export interface DrillCreateResponseInterface {
  drillCreate: DrillResponseInterface;
}

export interface DrillUpdateResponseInterface {
  drillUpdate: DrillResponseInterface;
}

export interface DrillResponseInterface {
  drill: DrillInterface;
}

export interface DrillDiagramsInterface {
  title: string;
  description: string;
  diagramURL: string;
}
