import { PagyInput } from '../common';
import { PracticeSubmissionInterface } from './practiceSubmission';

export interface CourseSectionItemUsersWithPracticeInterface {
  id?: string | number | null;
  name?: string;
  imageUrl?: string;
  gender?: string;
  birthDate?: string;
  practiceSubmissions?: PracticeSubmissionInterface[];
}

export interface statsInterface {
  TotalEnrolledUsers: number;
  TotalSubmittedUsers: number;
  TotalViewedSectionUsers: number;
}

export interface courseSectionItemUsersParamsInterface {
  courseSlug: string;
  sectionItemSLug: string;
  input: PagyInput;
  query: courseSectionItemUsersQueryInput;
}

export interface courseSectionItemUsersQueryInput {
  nameCont?: string;
  statusEq?: string;
}
