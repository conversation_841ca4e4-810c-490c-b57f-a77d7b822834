import { PagyInput } from '@/utils/interface/common';
import { CommentInterface } from '@/utils/interface/teacher/comment';
import { VideoInterface } from '@/utils/interface/video';

export interface PracticeSubmissionInterface {
  id: number;
  practiceId: number;
  practiceType: string;
  userId: string;
  content: string;
  status: string;
  createdAt: string;
  updatedAt: string;

  comments: CommentInterface[];
  videos: VideoInterface[];
}

export interface PracticeSubmissionListInput {
  input: PagyInput;
  query: PracticeSubmissionsQueryInput;
}

export interface PracticeSubmissionsQueryInput {
  statusEq?: string | null;
}
