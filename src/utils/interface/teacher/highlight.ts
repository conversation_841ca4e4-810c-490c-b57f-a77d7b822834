export interface HighlightInterface {
  id: number;
  title: string;
  description: string;
  iconId: number;
  icon: {
    id: number;
    key: string;
    class: string;
  };
}

export interface HighlightInputInterface {
  title: string;
  description: string;
  iconId: number;
}

export interface HighlightModifyInterface {
  title: string;
  description: string;
  iconId: string | number;
}

export interface HighlightQueryInput {
  titleCont?: string;
}
