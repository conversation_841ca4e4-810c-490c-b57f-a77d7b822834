import { CENSOR_STATUS_ENUMS } from '@/utils/constant';

import { CensorHistory } from '../censorHistory';
import { CourseSectionInterface } from './courseSection';
import { CourseUserInterface, UserInterface } from './user';
import { PagyInput } from '../common';
import { TeacherInterface } from './teacher';
import { CertificateInterface } from './certificate';

type CensorStatusType = (typeof CENSOR_STATUS_ENUMS)[keyof typeof CENSOR_STATUS_ENUMS];

export interface CourseFormInterface {
  id?: number | string | null;
  title?: string;
  description?: string;
  salePrice?: number | null;
  price?: number | null;
  status?: string;
  instructionalLevel: string;
  bonusPoint?: number | null;
  slug?: string;
  banner?: string;
  isPublic?: boolean;
  bonusPointPercent?: number | null;
  createdAt?: string;
  updatedAt?: string;
}

export interface CourseInputInterface {
  title: string;
  description?: string;
  salePrice?: number;
  price?: number;
  status?: string;
  instructionalLevel?: string;
  bonusPoint?: number;
  slug?: string;
  banner?: string;
  coursePackageAttributes?: CoursePackageInterface[];
  courseSetting?: CourseSettingInterface;
  highlightIds?: string[];
}

export interface CourseUpdateResponseInterface {
  courseUpdate: {
    message: string;
    course: CourseInterface;
  };
}

export interface CourseListQueryInput {
  titleCont?: string;
  descriptionCont?: string;
  statusEq?: string;
}

export interface CourseListParamsInterface {
  input: PagyInput;
  query: CourseListQueryInput;
}

export interface CourseInterface {
  id: number;
  teacherId: number;
  title: string;
  slug: string;
  description: string;
  salePrice: number;
  price: number;
  bonusPoint: number;
  bonusPointPercent: number;
  instructionalLevel: string;
  status: CensorStatusType;
  statusI18n: string;
  isPublic: boolean;
  banner: string;
  sectionCount: number;
  sectionItemCount: number;
  joinedUserCount: number;
  courseSections: CourseSectionInterface[];
  courseCensorHistories: CensorHistory[];
  courseUsers: CourseUserInterface[];
  teacher: TeacherInterface;
  students: UserInterface[];
  coursePackages: CoursePackageInterface[];
  courseSetting: CourseSettingInterface;
  courseHighlights: CourseHighlightInterface[];
  certificate: CertificateInterface;
}

export interface CourseResponseInterface {
  course: CourseInterface;
}

export interface CourseUserListParams {
  courseId: string;
  input: PagyInput;
  query: CourseUserListQueryInput;
}

export interface CourseUserListQueryInput {
  createdAtGteq?: string;
}

export interface InviteUserParams {
  courseId: string;
  phoneNumber?: string;
  coursePackageID?: string;
}

export interface CoursePackageInterface {
  id?: string | number | null;
  salePrice?: number | null;
  packageDealId: string;
  price?: number | null;
  packageDeal?: PackageDeal | null;
  isDeleted?: boolean;
}

export interface PackageDeal {
  id: string | number | null;
  name: string | null;
  description?: string | null;
}

export interface CourseSettingInterface {
  lockingItem: boolean;
}

export interface CourseHighlightInterface {
  id: string;
  highlight: HighlightInterface;
}

export interface HighlightInterface {
  id: string;
  icon: IconInterface;
  title: string;
  description: string;
}

export interface IconInterface {
  class: string;
}
