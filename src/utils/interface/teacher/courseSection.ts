import { CourseSectionItemInterface, CourseSectionItemWithPracticeInterface } from './courseSectionItem';

export interface CourseSectionInputInterface {
  title: string;
}

export interface CourseSectionInterface {
  id: number;
  title: string;
  slug: string;
  courseId: string;
  position: number;
  courseSectionItems: CourseSectionItemInterface[];
}

export interface CourseSectionWithPracticeInterface {
  id?: string | number | null;
  title?: string;
  courseId?: string | number;
  position?: number;
  courseSectionItems?: CourseSectionItemWithPracticeInterface[];
}

export interface CourseSectionDraggablePayload {
  moved: {
    element: CourseSectionInterface;
    oldIndex: number;
    newIndex: number;
  };
}

export interface CourseSectionItemDraggablePayload {
  moved: {
    element: CourseSectionItemInterface;
    oldIndex: number;
    newIndex: number;
  };
}
