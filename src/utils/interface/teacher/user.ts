import { PagyInput } from '@/utils/interface/common';

export interface UserInterface {
  id?: string | number | null;
  name?: string;
  gender?: string;
  imageUrl?: string;
  birthDate?: string;
  phoneNumber?: string;
  active?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface CourseUserInterface {
  id?: string | number | null;
  courseId?: string | number;
  userId?: string | number;
  user?: UserInterface;
  createdAt?: string;
  updatedAt?: string;
}

export interface UserListInput {
  input: PagyInput;
  query?: UserListQueryInput;
}

export interface UserListQueryInput {
  nameCont?: string;
  phoneNumberCont?: string;
}
