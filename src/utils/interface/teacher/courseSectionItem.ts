import { DrillInterface } from '@/utils/interface/drill/drill';
import { VideoInterface } from '../video';

export interface CourseSectionItemInputInterface {
  title: string;
  type: string;
  content: string;
  isFree: boolean;
}

export interface CourseSectionItemInterface {
  id: string;
  courseSectionId: string;
  title: string;
  slug: string;
  type: string;
  content: string;
  drills: DrillInterface[];
  videos: VideoInterface[];
  isFree: boolean;
}

export interface CourseSectionItemWithPracticeInterface {
  id?: string | number | null;
  courseSectionId: string | number;
  title?: string;
  slug?: string;
  type: string;
  isFree: boolean;
  content?: string;
  submittedPracticeCount?: number;
}

export interface CourseSectionItemResponseInterface {
  courseSectionItem: CourseSectionItemInterface;
}
