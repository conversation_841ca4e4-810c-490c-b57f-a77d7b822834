import { CENSOR_STATUS_ENUMS } from '@/utils/constant';
import { CensorHistory } from '@/utils/interface/censorHistory';
import { DiagramInterface } from '@/utils/interface/diagram';
import { DrillDiagramInputInterface } from '@/utils/interface/drill/diagram';
import { DrillStepInterface, DrillStepInputInterface } from '@/utils/interface/drill/step';
import { SkillInterface } from '@/utils/interface/skill';
import { UserDrillInterface } from '@/utils/interface/user/user';
import { VideoInterface } from '@/utils/interface/video';

import { EDrillLevel } from '@/enums/drill';

type CensorStatusType = (typeof CENSOR_STATUS_ENUMS)[keyof typeof CENSOR_STATUS_ENUMS];

export interface DrillInterface {
  id: number;
  title: string;
  description: string;
  level: EDrillLevel;
  levelI18n: string;
  salePrice: number;
  price: number;
  slug: string;
  status: string;
  statusI18n: string;
  censor: CensorStatusType;
  censorI18n: string;
  step: DrillStepInterface[];
  isMaster: boolean;
  ownerID: number;
  ownerType: string;
  stepCount: number;
  isVisible: boolean;
  videoCount: number;
  userDrill: UserDrillInterface;
  videos: VideoInterface[];
  diagrams: DiagramInterface[];
  skills: SkillInterface[];
  censorHistories: CensorHistory[];
  userDrillCount: number;
  owner: OwnerInterface;
  createdAt: string;
}

export interface DrillInputInterface {
  title: string;
  description?: string;
  level?: string;
  price?: number;
  salePrice?: number;
  skillIds?: number[];
  step: DrillStepInputInterface[];
  diagramAttributes: DrillDiagramInputInterface[];
  isMaster?: boolean;
}

export interface OwnerInterface {
  id: number;
  slug?: string;
  name: string;
  imageUrl?: string;
  contactEmail?: string;
}
