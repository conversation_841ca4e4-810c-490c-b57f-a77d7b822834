import { DiagramItem } from '@/composable/poolTableLayout/config/types';

export interface DrillStepInterface {
  description: string;
  diagramImage: string;
  setting: DiagramItem;
}

export interface DrillStepInputInterface extends DrillStepInterface {}

export const createBlankDiagramItem = (): DiagramItem => {
  return {
    balls: [],
    targetingBalls: [],
    targetingZones: [],
    textBoxes: [],
    speedBars: []
  };
};
