export interface PagyInput {
  perPage: number;
  page: number;
}

export interface MessageInfoInterface {
  message: string;
}

export interface MetaDataInterface {
  total: number;
  perPage: number;
  page: number;
  pages: number;
  count: number;
  next: number;
  prev: number;
  from: number;
  to: number;
}

export interface Step {
  id: number;
  name: string;
  tip: string;
  route: {
    name: string;
  };
}

export interface ErrorObjectInterface {
  errors: {
    [key: string]: string[];
  };
  message: string;
}

export type SiteType = 'admin' | 'teacher' | 'user' | 'public';
