<template>
  <component :is="layout">
    <router-view />
  </component>
</template>

<script lang="ts" setup>
  import { useLayoutStore } from '@/store/layout';

  const layoutStore = useLayoutStore();
  const route = useRoute();

  const layout = computed(() => route.meta.layout);
  const mode = computed(() => layoutStore.mode);

  watch(
    mode,
    newVal => {
      switch (newVal) {
        case 'light':
          document.body.setAttribute('data-bs-theme', 'light');
          break;
        case 'dark':
          document.body.setAttribute('data-bs-theme', 'dark');
          break;
        default:
          document.body.setAttribute('data-bs-theme', 'dark');
          break;
      }
    },
    { immediate: true }
  );
</script>
