@use 'sass:math';

@import 'custom/components/animation';

// ======================Width and height============================
$max-number: 2000;
@for $i from 0 through $max-number {
  .width-#{$i} {
    width: #{$i}px !important;
  }
  .height-#{$i} {
    height: #{$i}px !important;
  }
  .min-w-#{$i} {
    min-width: #{$i}px !important;
  }
  .max-w-#{$i} {
    max-width: #{$i}px !important;
  }
  .min-h-#{$i} {
    min-height: #{$i}px !important;
  }
  .max-h-#{$i} {
    max-height: #{$i}px !important;
  }
}
.width-fit-content {
  width: fit-content !important;
}

// ======================Position============================
$max-number: 2000;
@for $i from 0 through $max-number {
  .top-#{$i}-px {
    top: #{$i}px !important;
  }
  .bottom-#{$i}-px {
    bottom: #{$i}px !important;
  }
  .left-#{$i}-px {
    left: #{$i}px !important;
  }
  .right-#{$i}-px {
    right: #{$i}px !important;
  }
}

// ======================Line clamp============================
$max-line: 100;
@for $i from 1 through $max-line {
  .line-clamp-#{$i} {
    display: -webkit-box;
    -webkit-line-clamp: #{$i};
    line-clamp: #{$i};
    -webkit-box-orient: vertical;
    overflow: hidden;
    max-height: calc(1.5em * #{$i});
  }
}

// ======================Line clamp with custom line height============================
$max-line: 100;
$line-height: 50;

@for $lh from 1 through $line-height {
  @for $lines from 1 through $max-line {
    .line-clamp-#{$lines}-#{$lh} {
      display: -webkit-box;
      -webkit-line-clamp: #{$lines};
      line-clamp: #{$lines};
      -webkit-box-orient: vertical;
      overflow: hidden;
      max-height: calc(#{$lh}px * #{$lines});
    }
  }
}

// ======================DO NOT MODIFY THE ABOVE CODE======================

.modal-backdrop {
  opacity: 0.5;
}

.modal-header,
.alert {
  .close {
    padding: 0.3rem 1.75rem;
    background: transparent;
    border: 0;
    font-size: 25px;
    position: absolute;
    top: 0;
    right: 0;
    opacity: 0.4;
    width: 1em;
    height: 1em;
    z-index: 2;

    button {
      display: none;
    }
  }
}

.form-row {
  display: flex;
  flex-wrap: wrap;
}

.vhd__datepicker__input {
  height: $input-height !important;
  line-height: $input-height !important;
}

.form-check {
  .custom-control-input {
    border-radius: 0.25em;
    float: left;
    margin: 3px 3px 3px -19px !important;
  }
}

.check-group {
  input {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none;
  }
}

.vue-simple-drawer {
  background: $gray-100 !important;
  color: $gray-800 !important;

  .close-btn {
    .leftright,
    .rightleft {
      width: 20px !important;
      background-color: $gray-600 !important;
    }
  }
}

body {
  * {
    outline: none;
  }
}

.b-form-btn-label-control {
  padding: 0;
  border: none;

  > .btn {
    position: absolute;
    right: 0;
    top: 0;

    &:focus {
      box-shadow: none;
    }
  }
}

.vue-simple-drawer {
  // default style
  padding: 20px;
  color: $--simple-drawer-fg-color;
  background: $--simple-drawer-bg-color;
  // common
  position: fixed;
  overflow: auto;

  &.closeable {
    padding-top: 40px;
  }

  &.left {
    left: 0;
    top: 0;
    bottom: 0;
  }

  &.right {
    right: 0;
    top: 0;
    bottom: 0;
  }

  &.up {
    top: 0;
    left: 0;
    right: 0;
  }

  &.down {
    bottom: 0;
    left: 0;
    right: 0;
  }

  .close-btn {
    width: $--simple-drawer-close-width;
    height: $--simple-drawer-close-width;
    position: absolute;
    right: 0;
    top: 20px;
    transform: translate(-50%, -50%);
    color: currentColor;
    font-size: 20px;
    cursor: pointer;
    user-select: none;

    .leftright {
      height: 4px;
      width: $--simple-drawer-close-width;
      position: absolute;
      margin-top: math.div($--simple-drawer-close-width, 2);
      background-color: $--simple-drawer-softorange;
      border-radius: 2px;
      transform: rotate(45deg);
      transition: all 0.3s ease-in;
    }

    .rightleft {
      height: 4px;
      width: $--simple-drawer-close-width;
      position: absolute;
      margin-top: math.div($--simple-drawer-close-width, 2);
      background-color: $--simple-drawer-softorange;
      border-radius: 2px;
      transform: rotate(-45deg);
      transition: all 0.3s ease-in;
    }

    .close {
      margin: 60px 0 0 5px;
      position: absolute;
    }

    &:hover .leftright {
      transform: rotate(-45deg);
      background-color: $--simple-drawer-tomatored;
    }

    &:hover .rightleft {
      transform: rotate(45deg);
      background-color: $--simple-drawer-tomatored;
    }
  }
}

.mask {
  position: fixed;
  background: grey;
  opacity: 0.5;
  width: 100%;
  left: 0;
  top: 0;
  height: 100%;
}

.slider-tooltip,
.slider-connect {
  background: $primary !important;
}

.slider-horizontal .slider-tooltip-top:before {
  border: $primary !important;
}

.slider-touch-area {
  box-shadow: none !important;
}

.slider-tooltip {
  border-color: $primary !important;
}

// modal
.v-modal-custom {
  .modal-dialog {
    .modal-content {
      z-index: 1056;
    }
  }

  .v-modal-footer {
    padding: var(--vz-modal-padding) 0 0;
  }
}

.dropdown-toggle::after {
  display: none;
}

// ===========================
.advance-form-switches {
  .vue-switcher-theme--custom {
    &.vue-switcher-color--secondary {
      &.vue-switcher--unchecked {
        div {
          background-color: lighten($secondary, 40%);

          &:after {
            background-color: lighten($secondary, 2%);
          }
        }
      }
    }

    &.vue-switcher-color--warning {
      &.vue-switcher--unchecked {
        div {
          background-color: lighten($warning, 30%);

          &:after {
            background-color: lighten($warning, 2%);
          }
        }
      }
    }

    &.vue-switcher-color--danger {
      &.vue-switcher--unchecked {
        div {
          background-color: lighten($danger, 20%);

          &:after {
            background-color: lighten($danger, 2%);
          }
        }
      }
    }

    &.vue-switcher-color--primary {
      &.vue-switcher--unchecked {
        div {
          background-color: lighten($primary, 30%);

          &:after {
            background-color: lighten($primary, 2%);
          }
        }
      }
    }

    &.vue-switcher-color--success {
      &.vue-switcher--unchecked {
        div {
          background-color: lighten($success, 30%);

          &:after {
            background-color: lighten($success, 2%);
          }
        }
      }
    }

    &.vue-switcher-color--info {
      &.vue-switcher--unchecked {
        div {
          background-color: lighten($info, 30%);

          &:after {
            background-color: lighten($info, 2%);
          }
        }
      }
    }
  }
}

.crypto-buy-tabs {
  .nav-item {
    .nav-link {
      color: var(--#{$prefix}body-color);

      &.active {
        color: white;
      }
    }
  }
}

//
.right {
  .conversation-list {
    .chat-dropdown {
      float: left;
    }
  }
}

.conversation-list {
  .chat-dropdown {
    float: right;
  }
}

// EDITOR

.ck .ck-editor__main {
  background-color: var(--#{$prefix}secondary-bg);
}

.ck.ck-editor__main > .ck-editor__editable {
  background-color: var(--#{$prefix}secondary-bg) !important;
  border-color: var(--#{$prefix}border-color) !important;
}

.ck.ck-toolbar {
  background-color: var(--#{$prefix}secondary-bg) !important;
  border-color: var(--#{$prefix}border-color) !important;
  color: var(--#{$prefix}body-color) !important;
}

.ck .ck-toolbar__items {
  .ck-button {
    color: var(--#{$prefix}body-color) !important;

    &:hover,
    &.active {
      background-color: var(--#{$prefix}border-color) !important;
    }
  }
}

.ck-toolbar__separator {
  background-color: var(--#{$prefix}border-color) !important;
}

.ck-button__label {
  &:hover,
  &.active {
    background-color: var(--#{$prefix}secondary-bg) !important;
  }
}

.ck .ck-list__item {
  background-color: var(--#{$prefix}secondary-bg) !important;
  border-color: var(--#{$prefix}border-color) !important;
}

.ck .ck-icon {
  background-color: var(--#{$prefix}tertiary-bg) !important;
}

.ck.ck-button.ck-on,
a.ck.ck-button.ck-on {
  background-color: var(--#{$prefix}tertiary-bg) !important;
}

.ck-dropdown__panel,
.ck-labeled-field-view {
  border-color: var(--#{$prefix}border-color) !important;
  background-color: var(--#{$prefix}secondary-bg) !important;
}

.ck .ck-input,
.ck-labeled-field-view__status,
.ck-media-form,
.ck-link-form {
  background-color: var(--#{$prefix}secondary-bg) !important;
  color: var(--#{$prefix}body-color) !important;
  border-color: var(--#{$prefix}border-color) !important;
  box-shadow: none !important;
}

.ck-link-form {
  border-color: var(--#{$prefix}border-color) !important;
}

.cursor-pointer {
  cursor: pointer !important;
}

.cursor-move {
  cursor: move !important;
}

input.form-control {
  height: 40px;
}

.form-control:disabled {
  opacity: 0.5;
}
.form-control:disabled:hover {
  cursor: not-allowed;
}

button.btn-link {
  &.dropdown-toggle {
    width: 100%;
    padding: 0;
  }
}

.cursor-not-allowed {
  cursor: not-allowed !important;
}

.border-dashed {
  border-style: dashed !important;
}

// multiselect
.multiselect-tag {
  background-color: $primary !important;
}
.multiselect-option.is-selected {
  span {
    color: white !important;
  }
}

// Carousel
.base-carousel-wrapper {
  .carousel-nav {
    width: 100%;
    button {
      position: absolute;
      top: 50%;
      z-index: 1;

      width: 50px;
      height: 50px;
      background-color: #fff;
      border: 1px solid #ccc;
      border-radius: 50%;
      transition: all 0.3s ease;
      &.prev-btn {
        left: -10px;
        transform: translate(0, -50%);
      }
      &.next-btn {
        right: -10px;
        transform: translate(0, -50%);
      }
      i {
        font-size: 24px;

        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      @media (max-width: 992px) {
        width: 40px;
        height: 40px;
        &.prev-btn {
          left: 0;
        }
        &.next-btn {
          right: 0;
        }
        i {
          font-size: 20px;
        }
      }
    }

    button[disabled] {
      opacity: 0;
    }
  }

  .horizontal {
    .swiper-slide {
      height: unset !important;
    }
  }

  .swiper-slide {
    align-self: stretch;
  }
}

pre.pre-content {
  font-family: 'Be Vietnam Pro', sans-serif;
  font-size: 100%;
  white-space: pre-wrap; /* Since CSS 2.1 */
  white-space: -moz-pre-wrap; /* Mozilla, since 1999 */
  white-space: -pre-wrap; /* Opera 4-6 */
  white-space: -o-pre-wrap; /* Opera 7 */
  word-wrap: break-word; /* Internet Explorer 5.5+ */
}

.right-bar {
  width: 330px;
  right: -340px;
  .mdi-close {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 20px;
    color: #000000b3;
    cursor: pointer;
  }
}

.register-verify {
  .shield-ic {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 60px;
    height: 60px;
    font-size: 30px;
    color: white;
    background: linear-gradient(45deg, #f2bb2d 0%, #eaaa17 50%, #d99000 100%);
    border-radius: 50%;
  }

  .cellphone-ic {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    font-size: 20px;
    color: #e09b0a;
    background: #fff8f3;
    border-radius: 50%;
  }

  .auth-submit-btn {
    background: linear-gradient(45deg, #f2bb2d 0%, #eaaa17 50%, #d99000 100%);
  }

  .countdown-bar {
    background: #e5e7eb;
    .b-progress-bar {
      transform-origin: right center;
    }
    .progress-bar {
      background: linear-gradient(45deg, #f2bb2d 0%, #eaaa17 50%, #d99000 100%);
    }
  }
}

.multiselect-clear-icon {
  display: none !important;
}

.swiper-thumb-wrapper {
  padding: 10px 0 !important;
  .swiper-slide {
    opacity: 0.4;
    position: relative;
    img {
      width: 100%;
      object-fit: cover;
    }
    .mdi-play-circle {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

      font-size: 40px;
      color: #838487;
      background: white;
      border-radius: 999px;
      line-height: 1;
      @media (max-width: 675px) {
        font-size: 30px;
      }
    }
    .now-playing-badge {
      display: none;
    }

    &.swiper-slide-thumb-active {
      opacity: 1;
    }

    &.swiper-slide-thumb-active {
      .mdi-play-circle {
        color: #000000b3;
      }
      .now-playing-badge {
        display: inline-block;
        position: absolute;
        top: 5px;
        left: 5px;
        border-radius: 0;
      }
    }
  }
}

.plyr--video {
  height: 100%;
}

.swiper-video-wrapper .swiper-slide,
.swiper-video-wrapper .video-slide {
  position: relative;
  width: 100%;
  aspect-ratio: 16 / 9;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.swiper-video-wrapper iframe,
.swiper-video-wrapper video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.drill-modal-dialog-wrapper {
  --bs-modal-width: 1300px !important;
}

.drill-step-modal-dialog-wrapper {
  --bs-modal-width: 1200px !important;
}

.pagination {
  &.bg-orange {
    .active > .page-link {
      background-color: #eaaa17;
      border-color: #eaaa17;
    }
    .page-item.disabled .page-link {
      opacity: 0.4;
      pointer-events: none;
    }
  }
}

.md-editor {
  img {
    object-fit: contain;
  }
}

.related-course-card {
  .base-carousel {
    .swiper-slide {
      display: flex;
      height: auto;
    }
  }
}

.step-drill-section {
  .swiper {
    padding: 10px 0;
    .product-img {
      padding: 3px;
      @media (max-width: 992px) {
        padding: 5px;
      }
    }
  }

  @media (max-width: 765px) {
    .swiper {
      padding: 0 !important;
    }
  }
}

@media (max-width: 600px) {
  .custom-dropdown-menu {
    transform: none !important;
    top: 70px !important;
  }
}

// Fullscreen
.fullscreen-wrapper {
  .image-section {
    width: 80%;

    @media (max-width: 992px) {
      width: 100%;
    }

    &:hover,
    &:active {
      img {
        filter: brightness(0.8);
      }
      .fullscreen-action {
        display: block;
        i {
          color: white;
        }
      }
    }

    .rotated-img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(90deg);
      width: 80vh !important;
      height: auto;
      object-fit: contain;
    }

    .fullscreen-action {
      display: none;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 1;
      i {
        font-size: 36px;
        cursor: pointer;
      }
      @media (max-width: 992px) {
        i {
          font-size: 30px;
        }
      }
    }

    &.image-fullscreen {
      &.image-landscape {
        .rotated-img {
          transform: translate(-50%, -50%) rotate(360deg);
          width: 90vw !important;
          height: 90vh;
        }

        .fullscreen-action {
          right: 10px;
          left: initial;
        }
      }
      .fullscreen-action {
        display: block;
        top: initial;
        left: 10px;
        bottom: 10px;
        transform: none;
      }

      &:hover,
      &:active {
        img {
          filter: none;
        }
      }
    }
  }

  .fullscreen-overlay {
    position: fixed;
    inset: 0;
    background: black;
    z-index: 9999;
  }
}

.drill-detail-container {
  .image-section {
    width: 100%;
  }
}

.eye-icon-action {
  color: #6b7280;
  transition: color 0.2s;

  &:hover {
    color: #495057;
  }
}

input.custom-input {
  &.input-password {
    padding-right: 40px !important;
  }
}

body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) {
  overflow: auto !important;
}

.rating-custom {
  .rating {
    .star-container {
      width: 15px;
      height: 15px;
      font-size: 12px;
    }
    .value {
      width: auto;
      font-size: 12px;
      margin-left: 5px;
    }
  }
}
