.course-status {
  position: absolute;
  top: 10px;
  right: 10px;

  &.draft {
    color: #1f2937;
    background-color: #f3f4f6;
  }

  &.approved {
    color: #166534;
    background-color: #dcfce7;
  }

  &.submitted {
    color: #1e40af;
    background-color: #dbeafe;
  }

  &.rejected {
    color: #991b1b;
    background-color: #fee2e2;
  }
}

.section-list {
  .lesson-card {
    &:hover,
    &:active,
    &.active {
      background-color: #edf6fe;
    }
  }
}

.course-info-container {
  min-height: calc(100vh - 300px);
}

.table-submission-card {
  background-color: #fbfdff;
  th,
  td {
    background-color: #fbfdff;
  }
}

.course-preview {
  .user-course-detail {
    margin-top: 0;
    .lesson-container {
      margin-top: 10px;
    }
  }
}
