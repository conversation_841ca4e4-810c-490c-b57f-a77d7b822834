.custom-layout {
  display: flex;
  flex-flow: column;
  min-height: 100vh;
}

.custom-flex-shrink {
  flex-grow: 1;
  flex-shrink: 1;
}

.auth-btn {
  a {
    font-size: 14px;
    border-radius: 5px;
    padding: 8px 20px;
    &.login-btn {
      color: #ecac24;
      border: 1px solid #ecac24;
      &:hover {
        color: white;
        background-color: #ecac24;
      }
    }

    &.register-btn {
      color: white;
      background-color: #ecac24;
      &:hover {
        background-color: #e1a522;
      }
    }

    @media (max-width: 765px) {
      padding: 8px 15px;
      line-height: 1;
    }
  }
}

.user-site-badge {
  color: #1d4ed8;
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
}

.teacher-site-btn {
  color: #15803d;
  background-color: transparent;
  border: 1px solid #bbf7d0;
  border-radius: 5px;
  &:hover {
    background-color: #f0fdf4;
  }
}

// Menu
.menu-list {
  gap: 60px;
  .menu-link {
    a {
      color: black;
      font-size: 14px;
      &.active {
        color: #ecac24;
      }
      &:hover {
        color: #ecac24;
      }
    }
  }
}

// Footer
.public-footer {
  h5 {
    position: relative;
    display: inline-block;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 50%;
      height: 3px;
      background-color: #ecac24;
      margin-top: 3px;
    }
  }
  .company-info {
    .social-item {
      width: 25px;
      height: 25px;
      background-color: #ecac24;
      border-radius: 50%;
    }
  }
  .quick-link {
    a {
      color: #495057;
      i {
        color: #ecac24;
      }
      &:hover {
        color: #ecac24;
      }
    }
  }
  .contact-us {
    li {
      display: flex;
      align-items: center;
      gap: 7px;
      padding-bottom: 5px;
      i {
        color: #ecac24;
      }
    }
  }
}

.custom-dropdown:focus-visible,
.custom-dropdown:focus {
  box-shadow: none !important;
  outline: none !important;
  border: none !important;
}
