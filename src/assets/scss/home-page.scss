.home-page-custom {
  margin-top: 50px;
  min-height: 100vh !important;
  width: auto !important;

  @media (max-width: 992px) {
    margin-top: 70px;
  }

  * {
    box-sizing: border-box;
    -webkit-font-smoothing: inherit;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  figure {
    margin: 0;
  }

  input,
  textarea,
  select,
  button {
    font-size: 12px;
    font-family: 'Be Vietnam Pro', sans-serif !important;
  }

  section {
    padding: 5rem 0;
    @media (max-width: 768px) {
      padding: 3rem 0;
    }
  }

  .section-header {
    position: relative;
    z-index: 2;
    margin-bottom: 3rem;
    text-align: center;
    @media (max-width: 768px) {
      margin-bottom: 2rem;
    }
  }

  .section-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;

    &:after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translate(-50%, 0);
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, #f39c12, #f39c12);
      border-radius: 2px;
    }
    @media (max-width: 768px) {
      font-size: 1.75rem;
    }
  }

  .section-subtitle {
    font-size: 1.1rem;
    color: #7f8c8d;
    margin-bottom: 2rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    padding-top: 0.5rem;
    @media (max-width: 768px) {
      font-size: 1rem;
    }
  }

  .btn-primary-gradient {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(90deg, #f39c12, #e67e22);
    color: white;
    padding: 0.8rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
    }

    i {
      font-size: 1.2rem;
    }

    @media (max-width: 768px) {
      padding: 0.5rem 0.75rem;
    }
  }

  .btn-outline {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: transparent;
    color: #0a3d62;
    padding: 0.8rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid #0a3d62;

    &:hover {
      background: #0a3d62;
      color: white;
      transform: translateY(-3px);
    }

    @media (max-width: 768px) {
      padding: 0.5rem 0.75rem;
    }
  }
}

[data-framer-component-type] {
  position: absolute;
}

[data-framer-component-type='Text'] {
  cursor: inherit;
}

[data-framer-component-text-autosized] * {
  white-space: pre;
}

[data-framer-component-type='Text'] > * {
  text-align: var(--framer-text-alignment, start);
}

[data-framer-component-type='Text'] span span,
[data-framer-component-type='Text'] p span,
[data-framer-component-type='Text'] h1 span,
[data-framer-component-type='Text'] h2 span,
[data-framer-component-type='Text'] h3 span,
[data-framer-component-type='Text'] h4 span,
[data-framer-component-type='Text'] h5 span,
[data-framer-component-type='Text'] h6 span {
  display: block;
}

[data-framer-component-type='Text'] span span span,
[data-framer-component-type='Text'] p span span,
[data-framer-component-type='Text'] h1 span span,
[data-framer-component-type='Text'] h2 span span,
[data-framer-component-type='Text'] h3 span span,
[data-framer-component-type='Text'] h4 span span,
[data-framer-component-type='Text'] h5 span span,
[data-framer-component-type='Text'] h6 span span {
  display: unset;
}

[data-framer-component-type='Text'] div div span,
[data-framer-component-type='Text'] a div span,
[data-framer-component-type='Text'] span span span,
[data-framer-component-type='Text'] p span span,
[data-framer-component-type='Text'] h1 span span,
[data-framer-component-type='Text'] h2 span span,
[data-framer-component-type='Text'] h3 span span,
[data-framer-component-type='Text'] h4 span span,
[data-framer-component-type='Text'] h5 span span,
[data-framer-component-type='Text'] h6 span span,
[data-framer-component-type='Text'] a {
  font-family: var(--font-family);
  font-style: var(--font-style);
  font-weight: min(calc(var(--framer-font-weight-increase, 0) + var(--font-weight, 400)), 900);
  color: var(--text-color);
  letter-spacing: var(--letter-spacing);
  font-size: var(--font-size);
  text-transform: var(--text-transform);
  text-decoration: var(--text-decoration);
  line-height: var(--line-height);
}

[data-framer-component-type='Text'] div div span,
[data-framer-component-type='Text'] a div span,
[data-framer-component-type='Text'] span span span,
[data-framer-component-type='Text'] p span span,
[data-framer-component-type='Text'] h1 span span,
[data-framer-component-type='Text'] h2 span span,
[data-framer-component-type='Text'] h3 span span,
[data-framer-component-type='Text'] h4 span span,
[data-framer-component-type='Text'] h5 span span,
[data-framer-component-type='Text'] h6 span span,
[data-framer-component-type='Text'] a {
  --font-family: var(--framer-font-family);
  --font-style: var(--framer-font-style);
  --font-weight: var(--framer-font-weight);
  --text-color: var(--framer-text-color);
  --letter-spacing: var(--framer-letter-spacing);
  --font-size: var(--framer-font-size);
  --text-transform: var(--framer-text-transform);
  --text-decoration: var(--framer-text-decoration);
  --line-height: var(--framer-line-height);
}

[data-framer-component-type='Text'] a,
[data-framer-component-type='Text'] a div span,
[data-framer-component-type='Text'] a span span span,
[data-framer-component-type='Text'] a p span span,
[data-framer-component-type='Text'] a h1 span span,
[data-framer-component-type='Text'] a h2 span span,
[data-framer-component-type='Text'] a h3 span span,
[data-framer-component-type='Text'] a h4 span span,
[data-framer-component-type='Text'] a h5 span span,
[data-framer-component-type='Text'] a h6 span span {
  --font-family: var(--framer-link-font-family, var(--framer-font-family));
  --font-style: var(--framer-link-font-style, var(--framer-font-style));
  --font-weight: var(--framer-link-font-weight, var(--framer-font-weight));
  --text-color: var(--framer-link-text-color, var(--framer-text-color));
  --font-size: var(--framer-link-font-size, var(--framer-font-size));
  --text-transform: var(--framer-link-text-transform, var(--framer-text-transform));
  --text-decoration: var(--framer-link-text-decoration, var(--framer-text-decoration));
}

[data-framer-component-type='Text'] a:hover,
[data-framer-component-type='Text'] a div span:hover,
[data-framer-component-type='Text'] a span span span:hover,
[data-framer-component-type='Text'] a p span span:hover,
[data-framer-component-type='Text'] a h1 span span:hover,
[data-framer-component-type='Text'] a h2 span span:hover,
[data-framer-component-type='Text'] a h3 span span:hover,
[data-framer-component-type='Text'] a h4 span span:hover,
[data-framer-component-type='Text'] a h5 span span:hover,
[data-framer-component-type='Text'] a h6 span span:hover {
  --font-family: var(--framer-link-hover-font-family, var(--framer-link-font-family, var(--framer-font-family)));
  --font-style: var(--framer-link-hover-font-style, var(--framer-link-font-style, var(--framer-font-style)));
  --font-weight: var(--framer-link-hover-font-weight, var(--framer-link-font-weight, var(--framer-font-weight)));
  --text-color: var(--framer-link-hover-text-color, var(--framer-link-text-color, var(--framer-text-color)));
  --font-size: var(--framer-link-hover-font-size, var(--framer-link-font-size, var(--framer-font-size)));
  --text-transform: var(
    --framer-link-hover-text-transform,
    var(--framer-link-text-transform, var(--framer-text-transform))
  );
  --text-decoration: var(
    --framer-link-hover-text-decoration,
    var(--framer-link-text-decoration, var(--framer-text-decoration))
  );
}

[data-framer-component-type='Text'].isCurrent a,
[data-framer-component-type='Text'].isCurrent a div span,
[data-framer-component-type='Text'].isCurrent a span span span,
[data-framer-component-type='Text'].isCurrent a p span span,
[data-framer-component-type='Text'].isCurrent a h1 span span,
[data-framer-component-type='Text'].isCurrent a h2 span span,
[data-framer-component-type='Text'].isCurrent a h3 span span,
[data-framer-component-type='Text'].isCurrent a h4 span span,
[data-framer-component-type='Text'].isCurrent a h5 span span,
[data-framer-component-type='Text'].isCurrent a h6 span span {
  --font-family: var(--framer-link-current-font-family, var(--framer-link-font-family, var(--framer-font-family)));
  --font-style: var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style)));
  --font-weight: var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight)));
  --text-color: var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color)));
  --font-size: var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size)));
  --text-transform: var(
    --framer-link-current-text-transform,
    var(--framer-link-text-transform, var(--framer-text-transform))
  );
  --text-decoration: var(
    --framer-link-current-text-decoration,
    var(--framer-link-text-decoration, var(--framer-text-decoration))
  );
}

p.framer-text,
div.framer-text,
h1.framer-text,
h2.framer-text,
h3.framer-text,
h4.framer-text,
h5.framer-text,
h6.framer-text,
ol.framer-text,
ul.framer-text {
  margin: 0;
  padding: 0;
}

p.framer-text,
div.framer-text,
h1.framer-text,
h2.framer-text,
h3.framer-text,
h4.framer-text,
h5.framer-text,
h6.framer-text,
li.framer-text,
ol.framer-text,
ul.framer-text,
span.framer-text:not([data-text-fill]) {
  font-family: var(--framer-font-family, Be Vietnam Pro, Inter Placeholder, sans-serif);
  font-style: var(--framer-font-style, normal);
  font-weight: var(--framer-font-weight, 400);
  color: var(--framer-text-color, #000);
  font-size: calc(var(--framer-font-size, 16px) * var(--framer-font-size-scale, 1));
  letter-spacing: var(--framer-letter-spacing, 0);
  text-transform: var(--framer-text-transform, none);
  text-decoration: var(--framer-text-decoration, none);
  line-height: var(--framer-line-height, 1.2em);
  text-align: var(--framer-text-alignment, start);
}

.framer-fit-text .framer-text {
  white-space: nowrap;
  white-space-collapse: preserve;
}

strong.framer-text {
  font-family: var(--framer-font-family-bold);
  font-style: var(--framer-font-style-bold);
  font-weight: var(--framer-font-weight-bold, bolder);
}

em.framer-text {
  font-family: var(--framer-font-family-italic);
  font-style: var(--framer-font-style-italic, italic);
  font-weight: var(--framer-font-weight-italic);
}

em.framer-text > strong.framer-text {
  font-family: var(--framer-font-family-bold-italic);
  font-style: var(--framer-font-style-bold-italic, italic);
  font-weight: var(--framer-font-weight-bold-italic, bolder);
}

p.framer-text:not(:first-child),
div.framer-text:not(:first-child),
h1.framer-text:not(:first-child),
h2.framer-text:not(:first-child),
h3.framer-text:not(:first-child),
h4.framer-text:not(:first-child),
h5.framer-text:not(:first-child),
h6.framer-text:not(:first-child),
ol.framer-text:not(:first-child),
ul.framer-text:not(:first-child),
.framer-image.framer-text:not(:first-child) {
  margin-top: var(--framer-paragraph-spacing, 0);
}

li.framer-text > ul.framer-text:nth-child(2),
li.framer-text > ol.framer-text:nth-child(2) {
  margin-top: 0;
}

.framer-text[data-text-fill] {
  display: inline-block;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  padding: max(0em, calc(calc(1.3em - var(--framer-line-height, 1.3em)) / 2));
  margin: min(0em, calc(calc(1.3em - var(--framer-line-height, 1.3em)) / -2));
}

code.framer-text,
code.framer-text span.framer-text:not([data-text-fill]) {
  font-family: var(--framer-code-font-family, var(--framer-font-family, Be Vietnam Pro, Inter Placeholder, sans-serif));
  font-style: var(--framer-code-font-style, var(--framer-font-style, normal));
  font-weight: var(--framer-code-font-weight, var(--framer-font-weight, 400));
  color: var(--framer-code-text-color, var(--framer-text-color, #000));
  font-size: calc(var(--framer-font-size, 16px) * var(--framer-font-size-scale, 1));
  letter-spacing: var(--framer-letter-spacing, 0);
  line-height: var(--framer-line-height, 1.2em);
}

a.framer-text,
a.framer-text span.framer-text:not([data-text-fill]) {
  font-family: var(--framer-link-font-family, var(--framer-font-family, Be Vietnam Pro, Inter Placeholder, sans-serif));
  font-style: var(--framer-link-font-style, var(--framer-font-style, normal));
  font-weight: var(--framer-link-font-weight, var(--framer-font-weight, 400));
  color: var(--framer-link-text-color, var(--framer-text-color, #000));
  font-size: calc(var(--framer-link-font-size, var(--framer-font-size, 16px)) * var(--framer-font-size-scale, 1));
  text-transform: var(--framer-link-text-transform, var(--framer-text-transform, none));
  text-decoration: var(--framer-link-text-decoration, var(--framer-text-decoration, none));
  cursor: var(--framer-custom-cursors, pointer);
}

code.framer-text a.framer-text,
code.framer-text a.framer-text span.framer-text:not([data-text-fill]) {
  font-family: var(--framer-code-font-family, var(--framer-font-family, Be Vietnam Pro, Inter Placeholder, sans-serif));
  font-style: var(--framer-code-font-style, var(--framer-font-style, normal));
  font-weight: var(--framer-code-font-weight, var(--framer-font-weight, 400));
  color: var(--framer-link-text-color, var(--framer-code-text-color, var(--framer-text-color, #000)));
  font-size: calc(var(--framer-link-font-size, var(--framer-font-size, 16px)) * var(--framer-font-size-scale, 1));
}

a.framer-text:hover,
a.framer-text:hover span.framer-text:not([data-text-fill]) {
  font-family: var(
    --framer-link-hover-font-family,
    var(--framer-link-font-family, var(--framer-font-family, Be Vietnam Pro, Inter Placeholder, sans-serif))
  );
  font-style: var(--framer-link-hover-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)));
  font-weight: var(--framer-link-hover-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)));
  color: var(--framer-link-hover-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)));
  font-size: calc(
    var(--framer-link-hover-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))) *
      var(--framer-font-size-scale, 1)
  );
  text-transform: var(
    --framer-link-hover-text-transform,
    var(--framer-link-text-transform, var(--framer-text-transform, none))
  );
  text-decoration: var(
    --framer-link-hover-text-decoration,
    var(--framer-link-text-decoration, var(--framer-text-decoration, none))
  );
}

code.framer-text a.framer-text:hover,
code.framer-text a.framer-text:hover span.framer-text:not([data-text-fill]) {
  font-family: var(--framer-code-font-family, var(--framer-font-family, Be Vietnam Pro, Inter Placeholder, sans-serif));
  font-style: var(--framer-code-font-style, var(--framer-font-style, normal));
  font-weight: var(--framer-code-font-weight, var(--framer-font-weight, 400));
  color: var(
    --framer-link-hover-text-color,
    var(--framer-link-text-color, var(--framer-code-text-color, var(--framer-text-color, #000)))
  );
  font-size: calc(
    var(--framer-link-hover-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))) *
      var(--framer-font-size-scale, 1)
  );
}

a.framer-text[data-framer-page-link-current],
a.framer-text[data-framer-page-link-current] span.framer-text:not([data-text-fill]) {
  font-family: var(
    --framer-link-current-font-family,
    var(--framer-link-font-family, var(--framer-font-family, Be Vietnam Pro, Inter Placeholder, sans-serif))
  );
  font-style: var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)));
  font-weight: var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)));
  color: var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)));
  font-size: calc(
    var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))) *
      var(--framer-font-size-scale, 1)
  );
  text-transform: var(
    --framer-link-current-text-transform,
    var(--framer-link-text-transform, var(--framer-text-transform, none))
  );
  text-decoration: var(
    --framer-link-current-text-decoration,
    var(--framer-link-text-decoration, var(--framer-text-decoration, none))
  );
}

code.framer-text a.framer-text[data-framer-page-link-current],
code.framer-text a.framer-text[data-framer-page-link-current] span.framer-text:not([data-text-fill]) {
  font-family: var(--framer-code-font-family, var(--framer-font-family, Be Vietnam Pro, Inter Placeholder, sans-serif));
  font-style: var(--framer-code-font-style, var(--framer-font-style, normal));
  font-weight: var(--framer-code-font-weight, var(--framer-font-weight, 400));
  color: var(
    --framer-link-current-text-color,
    var(--framer-link-text-color, var(--framer-code-text-color, var(--framer-text-color, #000)))
  );
  font-size: calc(
    var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))) *
      var(--framer-font-size-scale, 1)
  );
}

a.framer-text[data-framer-page-link-current]:hover,
a.framer-text[data-framer-page-link-current]:hover span.framer-text:not([data-text-fill]) {
  font-family: var(
    --framer-link-hover-font-family,
    var(
      --framer-link-current-font-family,
      var(--framer-link-font-family, var(--framer-font-family, Be Vietnam Pro, Inter Placeholder, sans-serif))
    )
  );
  font-style: var(
    --framer-link-hover-font-style,
    var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)))
  );
  font-weight: var(
    --framer-link-hover-font-weight,
    var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)))
  );
  color: var(
    --framer-link-hover-text-color,
    var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)))
  );
  font-size: calc(
    var(
        --framer-link-hover-font-size,
        var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px)))
      ) *
      var(--framer-font-size-scale, 1)
  );
  text-transform: var(
    --framer-link-hover-text-transform,
    var(--framer-link-current-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none)))
  );
  text-decoration: var(
    --framer-link-hover-text-decoration,
    var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none)))
  );
}

code.framer-text a.framer-text[data-framer-page-link-current]:hover,
code.framer-text a.framer-text[data-framer-page-link-current]:hover span.framer-text:not([data-text-fill]) {
  font-family: var(--framer-code-font-family, var(--framer-font-family, Be Vietnam Pro, Inter Placeholder, sans-serif));
  font-style: var(--framer-code-font-style, var(--framer-font-style, normal));
  font-weight: var(--framer-code-font-weight, var(--framer-font-weight, 400));
  color: var(
    --framer-link-hover-text-color,
    var(
      --framer-link-current-text-color,
      var(--framer-link-text-color, var(--framer-code-text-color, var(--framer-text-color, #000)))
    )
  );
  font-size: calc(
    var(
        --framer-link-hover-font-size,
        var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px)))
      ) *
      var(--framer-font-size-scale, 1)
  );
}

.framer-image.framer-text {
  display: block;
  max-width: 100%;
  height: auto;
}

.text-styles-preset-reset.framer-text {
  --framer-font-family: Be Vietnam Pro, Inter Placeholder, sans-serif;
  --framer-font-style: normal;
  --framer-font-weight: 500;
  --framer-text-color: #000;
  --framer-font-size: 16px;
  --framer-letter-spacing: 0;
  --framer-text-transform: none;
  --framer-text-decoration: none;
  --framer-line-height: 1.2em;
  --framer-text-alignment: start;
}

ol.framer-text {
  --list-style-type: decimal;
}

ul.framer-text,
ol.framer-text {
  display: table;
  width: 100%;
}

li.framer-text {
  display: table-row;
  counter-increment: list-item;
  list-style: none;
}

ol.framer-text > li.framer-text:before {
  display: table-cell;
  width: 2.25ch;
  box-sizing: border-box;
  padding-inline-end: 0.75ch;
  content: counter(list-item, var(--list-style-type)) '.';
  white-space: nowrap;
}

ul.framer-text > li.framer-text:before {
  display: table-cell;
  width: 2.25ch;
  box-sizing: border-box;
  padding-inline-end: 0.75ch;
  content: '\2022';
}

.framer-text-module[style*='aspect-ratio'] > :first-child {
  width: 100%;
}

@supports not (aspect-ratio: 1) {
  .framer-text-module[style*='aspect-ratio'] {
    position: relative;
  }
}

@supports not (aspect-ratio: 1) {
  .framer-text-module[style*='aspect-ratio']:before {
    content: '';
    display: block;
    padding-bottom: calc(100% / calc(var(--aspect-ratio)));
  }
}

@supports not (aspect-ratio: 1) {
  .framer-text-module[style*='aspect-ratio'] > :first-child {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
  }
}

[data-framer-component-type='DeprecatedRichText'] {
  cursor: inherit;
}

[data-framer-component-type='DeprecatedRichText'] .text-styles-preset-reset {
  --framer-font-family: Be Vietnam Pro, Inter Placeholder, sans-serif;
  --framer-font-style: normal;
  --framer-font-weight: 500;
  --framer-text-color: #000;
  --framer-font-size: 16px;
  --framer-letter-spacing: 0;
  --framer-text-transform: none;
  --framer-text-decoration: none;
  --framer-line-height: 1.2em;
  --framer-text-alignment: start;
}

[data-framer-component-type='DeprecatedRichText'] p,
[data-framer-component-type='DeprecatedRichText'] div,
[data-framer-component-type='DeprecatedRichText'] h1,
[data-framer-component-type='DeprecatedRichText'] h2,
[data-framer-component-type='DeprecatedRichText'] h3,
[data-framer-component-type='DeprecatedRichText'] h4,
[data-framer-component-type='DeprecatedRichText'] h5,
[data-framer-component-type='DeprecatedRichText'] h6 {
  margin: 0;
  padding: 0;
}

[data-framer-component-type='DeprecatedRichText'] p,
[data-framer-component-type='DeprecatedRichText'] div,
[data-framer-component-type='DeprecatedRichText'] h1,
[data-framer-component-type='DeprecatedRichText'] h2,
[data-framer-component-type='DeprecatedRichText'] h3,
[data-framer-component-type='DeprecatedRichText'] h4,
[data-framer-component-type='DeprecatedRichText'] h5,
[data-framer-component-type='DeprecatedRichText'] h6,
[data-framer-component-type='DeprecatedRichText'] li,
[data-framer-component-type='DeprecatedRichText'] ol,
[data-framer-component-type='DeprecatedRichText'] ul,
[data-framer-component-type='DeprecatedRichText'] span:not([data-text-fill]) {
  font-family: var(--framer-font-family, Be Vietnam Pro, Inter Placeholder, sans-serif);
  font-style: var(--framer-font-style, normal);
  font-weight: var(--framer-font-weight, 400);
  color: var(--framer-text-color, #000);
  font-size: var(--framer-font-size, 16px);
  letter-spacing: var(--framer-letter-spacing, 0);
  text-transform: var(--framer-text-transform, none);
  text-decoration: var(--framer-text-decoration, none);
  line-height: var(--framer-line-height, 1.2em);
  text-align: var(--framer-text-alignment, start);
}

[data-framer-component-type='DeprecatedRichText'] p:not(:first-child),
[data-framer-component-type='DeprecatedRichText'] div:not(:first-child),
[data-framer-component-type='DeprecatedRichText'] h1:not(:first-child),
[data-framer-component-type='DeprecatedRichText'] h2:not(:first-child),
[data-framer-component-type='DeprecatedRichText'] h3:not(:first-child),
[data-framer-component-type='DeprecatedRichText'] h4:not(:first-child),
[data-framer-component-type='DeprecatedRichText'] h5:not(:first-child),
[data-framer-component-type='DeprecatedRichText'] h6:not(:first-child),
[data-framer-component-type='DeprecatedRichText'] ol:not(:first-child),
[data-framer-component-type='DeprecatedRichText'] ul:not(:first-child),
[data-framer-component-type='DeprecatedRichText'] .framer-image:not(:first-child) {
  margin-top: var(--framer-paragraph-spacing, 0);
}

[data-framer-component-type='DeprecatedRichText'] span[data-text-fill] {
  display: inline-block;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

[data-framer-component-type='DeprecatedRichText'] a,
[data-framer-component-type='DeprecatedRichText'] a span:not([data-text-fill]) {
  font-family: var(--framer-link-font-family, var(--framer-font-family, Be Vietnam Pro, Inter Placeholder, sans-serif));
  font-style: var(--framer-link-font-style, var(--framer-font-style, normal));
  font-weight: var(--framer-link-font-weight, var(--framer-font-weight, 400));
  color: var(--framer-link-text-color, var(--framer-text-color, #000));
  font-size: var(--framer-link-font-size, var(--framer-font-size, 16px));
  text-transform: var(--framer-link-text-transform, var(--framer-text-transform, none));
  text-decoration: var(--framer-link-text-decoration, var(--framer-text-decoration, none));
}

[data-framer-component-type='DeprecatedRichText'] a:hover,
[data-framer-component-type='DeprecatedRichText'] a:hover span:not([data-text-fill]) {
  font-family: var(
    --framer-link-hover-font-family,
    var(--framer-link-font-family, var(--framer-font-family, Be Vietnam Pro, Inter Placeholder, sans-serif))
  );
  font-style: var(--framer-link-hover-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)));
  font-weight: var(--framer-link-hover-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)));
  color: var(--framer-link-hover-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)));
  font-size: var(--framer-link-hover-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px)));
  text-transform: var(
    --framer-link-hover-text-transform,
    var(--framer-link-text-transform, var(--framer-text-transform, none))
  );
  text-decoration: var(
    --framer-link-hover-text-decoration,
    var(--framer-link-text-decoration, var(--framer-text-decoration, none))
  );
}

a[data-framer-page-link-current],
a[data-framer-page-link-current] span:not([data-text-fill]) {
  font-family: var(
    --framer-link-current-font-family,
    var(--framer-link-font-family, var(--framer-font-family, Be Vietnam Pro, Inter Placeholder, sans-serif))
  );
  font-style: var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)));
  font-weight: var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)));
  color: var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)));
  font-size: var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px)));
  text-transform: var(
    --framer-link-current-text-transform,
    var(--framer-link-text-transform, var(--framer-text-transform, none))
  );
  text-decoration: var(
    --framer-link-current-text-decoration,
    var(--framer-link-text-decoration, var(--framer-text-decoration, none))
  );
}

a[data-framer-page-link-current]:hover,
a[data-framer-page-link-current]:hover span:not([data-text-fill]) {
  font-family: var(
    --framer-link-hover-font-family,
    var(
      --framer-link-current-font-family,
      var(--framer-link-font-family, var(--framer-font-family, Be Vietnam Pro, Inter Placeholder, sans-serif))
    )
  );
  font-style: var(
    --framer-link-hover-font-style,
    var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)))
  );
  font-weight: var(
    --framer-link-hover-font-weight,
    var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)))
  );
  color: var(
    --framer-link-hover-text-color,
    var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)))
  );
  font-size: var(
    --framer-link-hover-font-size,
    var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px)))
  );
  text-transform: var(
    --framer-link-hover-text-transform,
    var(--framer-link-current-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none)))
  );
  text-decoration: var(
    --framer-link-hover-text-decoration,
    var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none)))
  );
}

[data-framer-component-type='DeprecatedRichText'] strong {
  font-weight: bolder;
}

[data-framer-component-type='DeprecatedRichText'] em {
  font-style: italic;
}

[data-framer-component-type='DeprecatedRichText'] .framer-image {
  display: block;
  max-width: 100%;
  height: auto;
}

[data-framer-component-type='DeprecatedRichText'] ul,
[data-framer-component-type='DeprecatedRichText'] ol {
  display: table;
  width: 100%;
  padding-left: 0;
  margin: 0;
}

[data-framer-component-type='DeprecatedRichText'] li {
  display: table-row;
  counter-increment: list-item;
  list-style: none;
}

[data-framer-component-type='DeprecatedRichText'] ol > li:before {
  display: table-cell;
  width: 2.25ch;
  box-sizing: border-box;
  padding-right: 0.75ch;
  content: counter(list-item) '.';
  white-space: nowrap;
}

[data-framer-component-type='DeprecatedRichText'] ul > li:before {
  display: table-cell;
  width: 2.25ch;
  box-sizing: border-box;
  padding-right: 0.75ch;
  content: '\2022';
}

:not([data-framer-generated]) > [data-framer-stack-content-wrapper] > *,
:not([data-framer-generated]) > [data-framer-stack-content-wrapper] > [data-framer-component-type],
:not([data-framer-generated]) > [data-framer-stack-content-wrapper] > [data-framer-legacy-stack-gap-enabled] > *,
:not([data-framer-generated])
  > [data-framer-stack-content-wrapper]
  > [data-framer-legacy-stack-gap-enabled]
  > [data-framer-component-type] {
  position: relative;
}

.flexbox-gap-not-supported [data-framer-legacy-stack-gap-enabled='true'] > *,
[data-framer-legacy-stack-gap-enabled='true'][data-framer-stack-flexbox-gap='false'] {
  margin-top: calc(var(--stack-gap-y) / 2);
  margin-bottom: calc(var(--stack-gap-y) / 2);
  margin-right: calc(var(--stack-gap-x) / 2);
  margin-left: calc(var(--stack-gap-x) / 2);
}

[data-framer-stack-content-wrapper][data-framer-stack-gap-enabled='true'] {
  row-gap: var(--stack-native-row-gap);
  column-gap: var(--stack-native-column-gap);
}

.flexbox-gap-not-supported [data-framer-stack-content-wrapper][data-framer-stack-gap-enabled='true'] {
  row-gap: unset;
  column-gap: unset;
}

.flexbox-gap-not-supported
  [data-framer-stack-direction-reverse='false']
  [data-framer-legacy-stack-gap-enabled='true']
  > *:first-child,
[data-framer-stack-direction-reverse='false']
  [data-framer-legacy-stack-gap-enabled='true'][data-framer-stack-flexbox-gap='false']
  > *:first-child,
.flexbox-gap-not-supported
  [data-framer-stack-direction-reverse='true']
  [data-framer-legacy-stack-gap-enabled='true']
  > *:last-child,
[data-framer-stack-direction-reverse='true']
  [data-framer-legacy-stack-gap-enabled='true'][data-framer-stack-flexbox-gap='false']
  > *:last-child {
  margin-top: 0;
  margin-left: 0;
}

.flexbox-gap-not-supported
  [data-framer-stack-direction-reverse='false']
  [data-framer-legacy-stack-gap-enabled='true']
  > *:last-child,
[data-framer-stack-direction-reverse='false']
  [data-framer-legacy-stack-gap-enabled='true'][data-framer-stack-flexbox-gap='false']
  > *:last-child,
.flexbox-gap-not-supported
  [data-framer-stack-direction-reverse='true']
  [data-framer-legacy-stack-gap-enabled='true']
  > *:first-child,
[data-framer-stack-direction-reverse='true']
  [data-framer-legacy-stack-gap-enabled='true'][data-framer-stack-flexbox-gap='false']
  > *:first-child {
  margin-right: 0;
  margin-bottom: 0;
}

NavigationContainer [data-framer-component-type='NavigationContainer'] > *,
[data-framer-component-type='NavigationContainer'] > [data-framer-component-type] {
  position: relative;
}

[data-framer-component-type='Scroll']::-webkit-scrollbar {
  display: none;
}

[data-framer-component-type='ScrollContentWrapper'] > * {
  position: relative;
}

[data-framer-component-type='NativeScroll'] {
  -webkit-overflow-scrolling: touch;
}

[data-framer-component-type='NativeScroll'] > * {
  position: relative;
}

[data-framer-component-type='NativeScroll'].direction-both {
  overflow-x: scroll;
  overflow-y: scroll;
}

[data-framer-component-type='NativeScroll'].direction-vertical {
  overflow-x: hidden;
  overflow-y: scroll;
}

[data-framer-component-type='NativeScroll'].direction-horizontal {
  overflow-x: scroll;
  overflow-y: hidden;
}

[data-framer-component-type='NativeScroll'].direction-vertical > * {
  width: 100% !important;
}

[data-framer-component-type='NativeScroll'].direction-horizontal > * {
  height: 100% !important;
}

[data-framer-component-type='NativeScroll'].scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

[data-framer-component-type='PageContentWrapper'] > *,
[data-framer-component-type='PageContentWrapper'] > [data-framer-component-type] {
  position: relative;
}

[data-framer-component-type='DeviceComponent'].no-device > * {
  width: 100% !important;
  height: 100% !important;
}

[data-is-present='false'],
[data-is-present='false'] * {
  pointer-events: none !important;
}

[data-framer-cursor='pointer'] {
  cursor: pointer;
}

[data-framer-cursor='grab'] {
  cursor: grab;
}

[data-framer-cursor='grab']:active {
  cursor: grabbing;
}

[data-framer-component-type='Frame'] *,
[data-framer-component-type='Stack'] * {
  pointer-events: auto;
}

[data-framer-generated] * {
  pointer-events: unset;
}

.svgContainer svg {
  display: block;
}

[data-reset='button'] {
  border-width: 0;
  padding: 0;
  background: none;
}

[data-hide-scrollbars='true']::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}

[data-hide-scrollbars='true']::-webkit-scrollbar-thumb {
  background: transparent;
}

.framer-cursor-none,
.framer-cursor-none * {
  cursor: none !important;
}

.framer-pointer-events-none,
.framer-pointer-events-none * {
  pointer-events: none !important;
}

.framer-body-augiA20Il-framer-9NO9m {
  background: white;
}

.home-page-custom.framer-lux5qc,
.home-page-custom .framer-lux5qc {
  display: block;
}

.home-page-custom.home-page-custom-7 {
  align-content: center;
  align-items: center;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 0px;
  height: min-content;
  justify-content: flex-start;
  padding: 0;
  position: relative;
  width: 1300px;
}

.home-page-custom .framer-1k3vmhf-container {
  flex: none;
  height: auto;
  left: 50%;
  max-width: 100%;
  position: absolute;
  top: 0;
  transform: translate(-50%);
  width: 1300px;
  z-index: 1;
}

.home-page-custom .framer-1f5r9mg {
  align-content: center;
  align-items: center;
  background-color: #f3f2f8;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 0px;
  height: auto;
  justify-content: center;
  overflow: hidden;
  padding: 140px 10px 50px;
  position: relative;
  width: 100%;
}

.home-page-custom .framer-1basxbz {
  align-content: center;
  align-items: center;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  height: min-content;
  justify-content: space-between;
  overflow: hidden;
  padding: 0;
  position: relative;
  width: 100%;
}

.home-page-custom .framer-1cf4im2 {
  align-content: flex-start;
  align-items: flex-start;
  display: flex;
  flex: none;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 30px;
  height: min-content;
  justify-content: center;
  overflow: hidden;
  padding: 30px 0;
  position: relative;
}

.home-page-custom .framer-bd5hir {
  align-content: flex-start;
  align-items: flex-start;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10px;
  height: auto;
  justify-content: center;
  overflow: hidden;
  padding: 0;
  position: relative;
  width: auto;
}

.home-page-custom .framer-7nl5u {
  flex: none;
  height: 25px;
  overflow: hidden;
  position: relative;
  width: 25px;
}

.home-page-custom .framer-1qoyyp3,
.home-page-custom .framer-kfr0i8,
.home-page-custom .framer-1m7o1jj,
.home-page-custom .framer-eatcjt,
.home-page-custom .framer-1hlc95u,
.home-page-custom .framer-ld1k1l,
.home-page-custom .framer-13km1df,
.home-page-custom .framer-73t4un,
.home-page-custom .framer-1u5zot8,
.home-page-custom .framer-1yig0c6,
.home-page-custom .framer-1jcv21a,
.home-page-custom .framer-w8u46y,
.home-page-custom .framer-1joklh3,
.home-page-custom .framer-b550br,
.home-page-custom .framer-1isqchj,
.home-page-custom .framer-icsjx6,
.home-page-custom .framer-175w30r,
.home-page-custom .framer-r81t4f,
.home-page-custom .framer-16vom65,
.home-page-custom .framer-sv63i3 {
  --framer-link-text-color: #0099ff;
  --framer-link-text-decoration: underline;
  flex: none;
  height: auto;
  position: relative;
  white-space: pre;
  width: auto;
}

.home-page-custom .framer-ias8c9,
.home-page-custom .framer-1oh3mcl,
.home-page-custom .framer-25d0l5,
.home-page-custom .framer-q44gzi,
.home-page-custom .framer-1ba90y0,
.home-page-custom .framer-15nn0f7,
.home-page-custom .framer-nsy5mj,
.home-page-custom .framer-18424fl,
.home-page-custom .framer-1u9u5yx,
.home-page-custom .framer-127g18z,
.home-page-custom .framer-1ui6t2h,
.home-page-custom .framer-z3sapc,
.home-page-custom .framer-ladfks,
.home-page-custom .framer-1vu33tw,
.home-page-custom .framer-37fyg1,
.home-page-custom .framer-194jsat,
.home-page-custom .framer-1ahppkw,
.home-page-custom .framer-e0904z,
.home-page-custom .framer-bivqd1,
.home-page-custom .framer-1i2wnyt,
.home-page-custom .framer-1hx2vwg,
.home-page-custom .framer-1061xis,
.home-page-custom .framer-147r0v3 {
  --framer-link-text-color: #0099ff;
  --framer-link-text-decoration: underline;
  flex: none;
  height: auto;
  position: relative;
  white-space: pre-wrap;
  width: 100%;
  word-break: break-word;
  word-wrap: break-word;
}

.home-page-custom .framer-1xr2u5k {
  align-content: center;
  align-items: center;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 30px;
  height: min-content;
  justify-content: center;
  overflow: hidden;
  padding: 0;
  position: relative;
  width: min-content;
}

.home-page-custom .framer-1ihpbf6 {
  align-content: center;
  align-items: center;
  background-color: #ecac24;
  border-radius: 5px;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10px;
  height: auto;
  justify-content: center;
  overflow: hidden;
  padding: 15px;
  position: relative;
  width: auto;
  will-change: var(--framer-will-change-override, transform);
}

.home-page-custom .framer-dnpnyl,
.home-page-custom .framer-1c7vau5,
.home-page-custom .framer-17fg6tt,
.home-page-custom .framer-13g00b6,
.home-page-custom .framer-56c3rp,
.home-page-custom .framer-16g7uxc,
.home-page-custom .framer-1jl9jch,
.home-page-custom .framer-luqceq {
  flex: none;
  height: auto;
  position: relative;
  white-space: pre;
  width: auto;
}

.home-page-custom .framer-1s1d003 {
  flex: none;
  height: 18px;
  overflow: hidden;
  position: relative;
  width: 24px;
}

.home-page-custom .framer-i5ywx5 {
  flex: none;
  height: 500px;
  overflow: hidden;
  position: relative;
  width: 47%;
}

.home-page-custom .framer-1bffjxr {
  align-content: flex-start;
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: none;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 15px;
  height: auto;
  justify-content: center;
  overflow: hidden;
  padding: 0;
  position: relative;
  width: 22%;
  z-index: 1;
}

.home-page-custom .framer-6i9pkl {
  align-content: center;
  align-items: center;
  display: flex;
  flex: 1 0 0px;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 30px;
  height: 250px;
  justify-content: flex-start;
  overflow: visible;
  padding: 0;
  position: relative;
  width: 1px;
}

.home-page-custom .framer-1ld3y1i,
.home-page-custom .framer-5iual9 {
  --border-bottom-width: 1px;
  --border-color: #7b7b7b;
  --border-left-width: 1px;
  --border-right-width: 1px;
  --border-style: solid;
  --border-top-width: 1px;
  align-content: flex-start;
  align-items: flex-start;
  border-radius: 10px;
  display: flex;
  flex: 1 0 0px;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 15px;
  height: 100%;
  justify-content: center;
  overflow: hidden;
  padding: 20px;
  position: relative;
  width: 1px;
  will-change: var(--framer-will-change-override, transform);
  z-index: 1;
}

.home-page-custom .framer-t3m7xu,
.home-page-custom .framer-s8of2i,
.home-page-custom .framer-1xtfyzj {
  flex: none;
  height: 40px;
  overflow: hidden;
  position: relative;
  width: 40px;
}

.home-page-custom .framer-a3oink {
  align-content: flex-start;
  align-items: flex-start;
  display: flex;
  flex: none;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 30px;
  height: min-content;
  justify-content: center;
  max-width: 1300px;
  overflow: hidden;
  padding: 40px 10px;
  position: relative;
  width: 100%;
}

.home-page-custom .framer-guky4z {
  align-content: center;
  align-items: center;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 0px;
  height: min-content;
  justify-content: flex-start;
  padding: 0;
  position: relative;
  width: 100%;
}

.home-page-custom .framer-1mnppt8,
.home-page-custom .framer-9rkqq4,
.home-page-custom .framer-1fkjzky {
  --framer-link-text-color: #0099ff;
  --framer-link-text-decoration: underline;
  flex: 1 0 0px;
  height: auto;
  position: relative;
  white-space: pre-wrap;
  width: 1px;
  word-break: break-word;
  word-wrap: break-word;
}

.home-page-custom .framer-16hx91z-container {
  flex: none;
  height: 358px;
  position: relative;
  width: 100%;
}

.home-page-custom .framer-1084rkw,
.home-page-custom .framer-prks2j,
.home-page-custom .framer-12eq2fb,
.home-page-custom .framer-q65zs5,
.home-page-custom .framer-12gz4g1,
.home-page-custom .framer-1xbn8az {
  --border-bottom-width: 1px;
  --border-color: #7b7b7b;
  --border-left-width: 1px;
  --border-right-width: 1px;
  --border-style: solid;
  --border-top-width: 1px;
  align-content: flex-start;
  align-items: flex-start;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 10px;
  height: 365px;
  justify-content: center;
  padding: 20px;
  position: relative;
  text-decoration: none;
  width: 395px;
}

.home-page-custom .framer-1foeow1,
.home-page-custom .framer-1vy34m1,
.home-page-custom .framer-141iih5 {
  align-content: flex-end;
  align-items: flex-end;
  border-radius: 10px;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10px;
  height: 205px;
  justify-content: center;
  overflow: hidden;
  padding: 0 0 20px;
  position: relative;
  width: 100%;
  will-change: var(--framer-will-change-override, transform);
}

.home-page-custom .framer-m841la,
.home-page-custom .framer-1wtt9yy,
.home-page-custom .framer-loeln5,
.home-page-custom .framer-r1619f,
.home-page-custom .framer-vb9ps5,
.home-page-custom .framer-1qbycr3 {
  align-content: flex-start;
  align-items: flex-start;
  display: flex;
  flex: none;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 10px;
  height: min-content;
  justify-content: center;
  overflow: hidden;
  padding: 0;
  position: relative;
  width: 100%;
}

.home-page-custom .framer-cqwgdx,
.home-page-custom .framer-18oas4d,
.home-page-custom .framer-tawppn,
.home-page-custom .framer-1m6rjb6,
.home-page-custom .framer-5kgvqx {
  align-content: center;
  align-items: center;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 4px;
  height: min-content;
  justify-content: center;
  overflow: hidden;
  padding: 0;
  position: relative;
  width: min-content;
  z-index: 1;
}

.home-page-custom .framer-1qxx6ig,
.home-page-custom .framer-r5ik0h,
.home-page-custom .framer-g8k5om,
.home-page-custom .framer-rze4ds,
.home-page-custom .framer-1mtrwt3,
.home-page-custom .framer-vz4a09 {
  align-content: center;
  align-items: center;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 15px;
  height: min-content;
  justify-content: center;
  overflow: hidden;
  padding: 5px 2px 10px;
  position: relative;
  width: min-content;
  z-index: 1;
}

.home-page-custom .framer-w3ueca,
.home-page-custom .framer-17t7q4z,
.home-page-custom .framer-c9rzho,
.home-page-custom .framer-1gnkgon,
.home-page-custom .framer-1kpjnpz,
.home-page-custom .framer-uoadof,
.home-page-custom .framer-15eaoaj,
.home-page-custom .framer-1w4ic65,
.home-page-custom .framer-1i49r7n,
.home-page-custom .framer-l9o47p,
.home-page-custom .framer-prue0r,
.home-page-custom .framer-1mmkshw {
  align-content: center;
  align-items: center;
  background-color: #fff;
  border-radius: 50%;
  box-shadow: 1px 3px 10px #00000026;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10px;
  height: min-content;
  justify-content: center;
  overflow: hidden;
  padding: 5px;
  position: relative;
  width: min-content;
  will-change: var(--framer-will-change-override, transform);
}

.home-page-custom .framer-rqioxc,
.home-page-custom .framer-1tgfsiy,
.home-page-custom .framer-q3l069,
.home-page-custom .framer-lq3c2j,
.home-page-custom .framer-1st7og5,
.home-page-custom .framer-1ltaqr,
.home-page-custom .framer-1wc3q7a,
.home-page-custom .framer-1nt7gq7,
.home-page-custom .framer-hv3xx0,
.home-page-custom .framer-w5xjgn,
.home-page-custom .framer-7aa0x7,
.home-page-custom .framer-noocrq {
  align-content: center;
  align-items: center;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10px;
  height: 14px;
  justify-content: center;
  overflow: hidden;
  padding: 0;
  position: relative;
  width: 14px;
}

.home-page-custom .framer-150p2xi,
.home-page-custom .framer-11bnel3,
.home-page-custom .framer-1csw3f9 {
  align-content: flex-end;
  align-items: flex-end;
  border-radius: 10px;
  display: flex;
  flex: 1 0 0px;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10px;
  height: 1px;
  justify-content: center;
  overflow: hidden;
  padding: 0 0 20px;
  position: relative;
  width: 100%;
  will-change: var(--framer-will-change-override, transform);
}

.home-page-custom .framer-1kjlstk {
  align-content: center;
  align-items: center;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 70px;
  height: auto;
  justify-content: center;
  overflow: hidden;
  padding: 100px 10px 50px;
  position: relative;
}

.home-page-custom .framer-1mtnlym {
  align-content: center;
  align-items: center;
  border-radius: 30px;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10px;
  height: 750px;
  justify-content: center;
  overflow: visible;
  padding: 0 50px 0 0;
  position: relative;
  width: 45%;
  z-index: 2;
}

.home-page-custom .framer-52ykyo {
  --border-bottom-width: 2px;
  --border-color: #ecac24;
  --border-left-width: 2px;
  --border-right-width: 2px;
  --border-style: solid;
  --border-top-width: 2px;
  border-radius: 20px;
  flex: none;
  height: 100%;
  left: 40px;
  overflow: hidden;
  position: absolute;
  right: -40px;
  top: -40px;
  will-change: var(--framer-will-change-override, transform);
  z-index: -1;
}

.home-page-custom .framer-xplmi1 {
  align-content: flex-start;
  align-items: flex-start;
  display: flex;
  flex: 1 0 0px;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 35px;
  height: min-content;
  justify-content: center;
  padding: 0 0 0 50px;
  position: relative;
  width: 1px;
}

.home-page-custom .framer-10secyz,
.home-page-custom .framer-r6aav1 {
  align-content: flex-start;
  align-items: flex-start;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 20px;
  height: min-content;
  justify-content: center;
  overflow: hidden;
  padding: 10px;
  position: relative;
  width: 450px;
}

.home-page-custom .framer-rttqa,
.home-page-custom .framer-11j1vop {
  align-content: center;
  align-items: center;
  border-radius: 50%;
  box-shadow: 8px 11px 19px #00000040;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10px;
  height: 50px;
  justify-content: center;
  overflow: hidden;
  padding: 0;
  position: relative;
  width: 50px;
  will-change: var(--framer-will-change-override, transform);
}

.home-page-custom .framer-iy1wke,
.home-page-custom .framer-1dzbgmo {
  align-content: center;
  align-items: center;
  border-radius: 19px;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10px;
  height: 24px;
  justify-content: flex-start;
  overflow: hidden;
  padding: 0;
  position: relative;
  width: 24px;
  will-change: var(--framer-will-change-override, transform);
}

.home-page-custom .framer-7areok,
.home-page-custom .framer-vut9vp {
  align-content: center;
  align-items: center;
  display: flex;
  flex: 1 0 0px;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 10px;
  height: 120px;
  justify-content: flex-start;
  overflow: hidden;
  padding: 0;
  position: relative;
  width: 1px;
}

.home-page-custom .framer-54i8jr,
.home-page-custom .framer-ktwg9w,
.home-page-custom .framer-uljs1t {
  align-content: center;
  align-items: center;
  background-color: #ecac24;
  border-radius: 7px;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10px;
  height: auto;
  justify-content: flex-end;
  overflow: hidden;
  padding: 15px;
  position: relative;
  width: auto;
  will-change: var(--framer-will-change-override, transform);
}

.home-page-custom .framer-11box35 {
  align-content: flex-start;
  align-items: flex-start;
  display: flex;
  flex: none;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 10px;
  height: min-content;
  justify-content: center;
  overflow: visible;
  padding: 50px 10px 10px;
  position: relative;
  width: 100%;
}

.home-page-custom .framer-gnci1m {
  align-content: center;
  align-items: center;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  height: auto;
  justify-content: space-between;
  overflow: hidden;
  padding: 0;
  position: relative;
  width: 100%;
  z-index: 1;
}

.home-page-custom .framer-j6xwqh {
  align-content: center;
  align-items: center;
  display: flex;
  flex: 1 0 0px;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 0px;
  height: min-content;
  justify-content: flex-start;
  padding: 0;
  position: relative;
  width: 1px;
}

.home-page-custom .framer-1i9t6vo {
  align-content: center;
  align-items: center;
  display: flex;
  flex: 1 0 0px;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10px;
  justify-content: flex-end;
  padding: 0;
  position: relative;
  width: 1px;
}

.home-page-custom .framer-1lswng1 {
  align-content: flex-start;
  align-items: flex-start;
  display: flex;
  flex: none;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 0px;
  height: min-content;
  justify-content: center;
  overflow: visible;
  padding: 30px 0 50px;
  position: relative;
  width: 100%;
}

.home-page-custom .framer-127xgtk {
  align-content: center;
  align-items: center;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10px;
  height: 24px;
  justify-content: center;
  overflow: hidden;
  padding: 0;
  position: relative;
  width: min-content;
}

.home-page-custom .framer-vswkk1 {
  flex: none;
  height: 18px;
  overflow: hidden;
  position: relative;
  width: 18px;
}

.home-page-custom .framer-3ldttv {
  align-content: center;
  align-items: center;
  display: flex;
  flex: none;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 0px;
  height: min-content;
  justify-content: center;
  overflow: visible;
  padding: 50px 10px 10px;
  position: relative;
  width: 100%;
}

.home-page-custom .framer-1irlews {
  flex: none;
  height: 176px;
  left: -25px;
  overflow: hidden;
  position: absolute;
  top: 132px;
  width: 176px;
  z-index: 0;
}

.home-page-custom .framer-1kp00hc {
  display: grid;
  flex: none;
  gap: 30px;
  grid-auto-rows: minmax(0, 1fr);
  grid-template-columns: repeat(2, minmax(200px, 1fr));
  height: min-content;
  justify-content: center;
  max-width: 100%;
  padding: 50px 0 60px;
  position: relative;
  width: 100%;
  z-index: 1;
}

.home-page-custom .framer-1oo7ocx {
  border-radius: 10px;
  flex: none;
  height: 170px;
  overflow: visible;
  position: relative;
  width: 284px;
  z-index: 1;
}

.home-page-custom .framer-11pnqj3 {
  align-content: flex-start;
  align-items: flex-start;
  display: flex;
  flex: 1 0 0px;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 20px;
  height: auto;
  justify-content: flex-start;
  min-width: 221px;
  padding: 0;
  position: relative;
  width: 1px;
}

.home-page-custom .framer-1wyc3qt {
  align-content: center;
  align-items: center;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 20px;
  height: min-content;
  justify-content: center;
  overflow: hidden;
  padding: 0;
  position: relative;
  width: min-content;
}

.home-page-custom .framer-1krtfx {
  align-content: center;
  align-items: center;
  background-color: #2caac1;
  border-radius: 25px;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10px;
  height: min-content;
  justify-content: center;
  overflow: hidden;
  padding: 8px 12px;
  position: relative;
  width: min-content;
  will-change: var(--framer-will-change-override, transform);
}

.home-page-custom .framer-1kya2au {
  align-content: center;
  align-items: center;
  background-color: #ff7350;
  display: flex;
  flex: none;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 10px;
  height: min-content;
  justify-content: center;
  overflow: hidden;
  padding: 0;
  position: relative;
  width: 100%;
  z-index: 1;
}

.home-page-custom .framer-nth0bs {
  align-content: center;
  align-items: center;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 0px;
  height: auto;
  justify-content: center;
  max-width: 1300px;
  overflow: hidden;
  padding: 100px 10px;
  position: relative;
  width: 100%;
  z-index: 1;
}

.home-page-custom .framer-i045tb {
  align-content: flex-start;
  align-items: flex-start;
  display: flex;
  flex: none;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 50px;
  height: min-content;
  justify-content: center;
  max-width: 80%;
  padding: 0;
  position: relative;
  width: 100%;
  z-index: 1;
}

.home-page-custom .framer-5oimzk {
  align-content: center;
  align-items: center;
  display: flex;
  flex: none;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 30px;
  height: min-content;
  justify-content: center;
  overflow: hidden;
  padding: 0;
  position: relative;
  width: 100%;
}

.home-page-custom .framer-gj4dpl-container,
.home-page-custom .framer-xv7oib-container,
.home-page-custom .framer-1id6wc2-container,
.home-page-custom .framer-p575pe-container {
  flex: none;
  height: auto;
  position: relative;
  width: 100%;
}

@supports (background: -webkit-named-image(i)) and (not (scale: 1)) {
  .home-page-custom.home-page-custom-7,
  .home-page-custom .framer-1f5r9mg,
  .home-page-custom .framer-1cf4im2,
  .home-page-custom .framer-bd5hir,
  .home-page-custom .framer-1xr2u5k,
  .home-page-custom .framer-1ihpbf6,
  .home-page-custom .framer-1bffjxr,
  .home-page-custom .framer-6i9pkl,
  .home-page-custom .framer-1ld3y1i,
  .home-page-custom .framer-5iual9,
  .home-page-custom .framer-a3oink,
  .home-page-custom .framer-guky4z,
  .home-page-custom .framer-1084rkw,
  .home-page-custom .framer-1foeow1,
  .home-page-custom .framer-m841la,
  .home-page-custom .framer-cqwgdx,
  .home-page-custom .framer-1qxx6ig,
  .home-page-custom .framer-w3ueca,
  .home-page-custom .framer-rqioxc,
  .home-page-custom .framer-17t7q4z,
  .home-page-custom .framer-1tgfsiy,
  .home-page-custom .framer-prks2j,
  .home-page-custom .framer-1vy34m1,
  .home-page-custom .framer-1wtt9yy,
  .home-page-custom .framer-18oas4d,
  .home-page-custom .framer-r5ik0h,
  .home-page-custom .framer-c9rzho,
  .home-page-custom .framer-q3l069,
  .home-page-custom .framer-1gnkgon,
  .home-page-custom .framer-lq3c2j,
  .home-page-custom .framer-12eq2fb,
  .home-page-custom .framer-150p2xi,
  .home-page-custom .framer-loeln5,
  .home-page-custom .framer-tawppn,
  .home-page-custom .framer-g8k5om,
  .home-page-custom .framer-1kpjnpz,
  .home-page-custom .framer-1st7og5,
  .home-page-custom .framer-uoadof,
  .home-page-custom .framer-1ltaqr,
  .home-page-custom .framer-q65zs5,
  .home-page-custom .framer-141iih5,
  .home-page-custom .framer-r1619f,
  .home-page-custom .framer-1m6rjb6,
  .home-page-custom .framer-rze4ds,
  .home-page-custom .framer-15eaoaj,
  .home-page-custom .framer-1wc3q7a,
  .home-page-custom .framer-1w4ic65,
  .home-page-custom .framer-1nt7gq7,
  .home-page-custom .framer-12gz4g1,
  .home-page-custom .framer-11bnel3,
  .home-page-custom .framer-vb9ps5,
  .home-page-custom .framer-5kgvqx,
  .home-page-custom .framer-1mtrwt3,
  .home-page-custom .framer-1i49r7n,
  .home-page-custom .framer-hv3xx0,
  .home-page-custom .framer-l9o47p,
  .home-page-custom .framer-w5xjgn,
  .home-page-custom .framer-1xbn8az,
  .home-page-custom .framer-1csw3f9,
  .home-page-custom .framer-1qbycr3,
  .home-page-custom .framer-vz4a09,
  .home-page-custom .framer-prue0r,
  .home-page-custom .framer-7aa0x7,
  .home-page-custom .framer-1mmkshw,
  .home-page-custom .framer-noocrq,
  .home-page-custom .framer-1kjlstk,
  .home-page-custom .framer-1mtnlym,
  .home-page-custom .framer-xplmi1,
  .home-page-custom .framer-10secyz,
  .home-page-custom .framer-rttqa,
  .home-page-custom .framer-iy1wke,
  .home-page-custom .framer-7areok,
  .home-page-custom .framer-r6aav1,
  .home-page-custom .framer-11j1vop,
  .home-page-custom .framer-1dzbgmo,
  .home-page-custom .framer-vut9vp,
  .home-page-custom .framer-54i8jr,
  .home-page-custom .framer-11box35,
  .home-page-custom .framer-j6xwqh,
  .home-page-custom .framer-1i9t6vo,
  .home-page-custom .framer-ktwg9w,
  .home-page-custom .framer-1lswng1,
  .home-page-custom .framer-9busuh,
  .home-page-custom .framer-anapib,
  .home-page-custom .framer-8eju73,
  .home-page-custom .framer-it00y6,
  .home-page-custom .framer-127xgtk,
  .home-page-custom .framer-3ldttv,
  .home-page-custom .framer-11pnqj3,
  .home-page-custom .framer-1wyc3qt,
  .home-page-custom .framer-1krtfx,
  .home-page-custom .framer-uljs1t,
  .home-page-custom .framer-1kya2au,
  .home-page-custom .framer-nth0bs,
  .home-page-custom .framer-i045tb,
  .home-page-custom .framer-5oimzk {
    gap: 0px;
  }

  .home-page-custom.home-page-custom-7 > *,
  .home-page-custom .framer-1lswng1 > *,
  .home-page-custom .framer-3ldttv > * {
    margin: 0;
  }

  .home-page-custom.home-page-custom-7 > :first-child,
  .home-page-custom .framer-1cf4im2 > :first-child,
  .home-page-custom .framer-1bffjxr > :first-child,
  .home-page-custom .framer-1ld3y1i > :first-child,
  .home-page-custom .framer-5iual9 > :first-child,
  .home-page-custom .framer-a3oink > :first-child,
  .home-page-custom .framer-1084rkw > :first-child,
  .home-page-custom .framer-m841la > :first-child,
  .home-page-custom .framer-prks2j > :first-child,
  .home-page-custom .framer-1wtt9yy > :first-child,
  .home-page-custom .framer-12eq2fb > :first-child,
  .home-page-custom .framer-loeln5 > :first-child,
  .home-page-custom .framer-q65zs5 > :first-child,
  .home-page-custom .framer-r1619f > :first-child,
  .home-page-custom .framer-12gz4g1 > :first-child,
  .home-page-custom .framer-vb9ps5 > :first-child,
  .home-page-custom .framer-1xbn8az > :first-child,
  .home-page-custom .framer-1qbycr3 > :first-child,
  .home-page-custom .framer-xplmi1 > :first-child,
  .home-page-custom .framer-7areok > :first-child,
  .home-page-custom .framer-vut9vp > :first-child,
  .home-page-custom .framer-11box35 > :first-child,
  .home-page-custom .framer-1lswng1 > :first-child,
  .home-page-custom .framer-9busuh > :first-child,
  .home-page-custom .framer-anapib > :first-child,
  .home-page-custom .framer-8eju73 > :first-child,
  .home-page-custom .framer-3ldttv > :first-child,
  .home-page-custom .framer-11pnqj3 > :first-child,
  .home-page-custom .framer-1kya2au > :first-child,
  .home-page-custom .framer-i045tb > :first-child,
  .home-page-custom .framer-5oimzk > :first-child {
    margin-top: 0;
  }

  .home-page-custom.home-page-custom-7 > :last-child,
  .home-page-custom .framer-1cf4im2 > :last-child,
  .home-page-custom .framer-1bffjxr > :last-child,
  .home-page-custom .framer-1ld3y1i > :last-child,
  .home-page-custom .framer-5iual9 > :last-child,
  .home-page-custom .framer-a3oink > :last-child,
  .home-page-custom .framer-1084rkw > :last-child,
  .home-page-custom .framer-m841la > :last-child,
  .home-page-custom .framer-prks2j > :last-child,
  .home-page-custom .framer-1wtt9yy > :last-child,
  .home-page-custom .framer-12eq2fb > :last-child,
  .home-page-custom .framer-loeln5 > :last-child,
  .home-page-custom .framer-q65zs5 > :last-child,
  .home-page-custom .framer-r1619f > :last-child,
  .home-page-custom .framer-12gz4g1 > :last-child,
  .home-page-custom .framer-vb9ps5 > :last-child,
  .home-page-custom .framer-1xbn8az > :last-child,
  .home-page-custom .framer-1qbycr3 > :last-child,
  .home-page-custom .framer-xplmi1 > :last-child,
  .home-page-custom .framer-7areok > :last-child,
  .home-page-custom .framer-vut9vp > :last-child,
  .home-page-custom .framer-11box35 > :last-child,
  .home-page-custom .framer-1lswng1 > :last-child,
  .home-page-custom .framer-9busuh > :last-child,
  .home-page-custom .framer-anapib > :last-child,
  .home-page-custom .framer-8eju73 > :last-child,
  .home-page-custom .framer-3ldttv > :last-child,
  .home-page-custom .framer-11pnqj3 > :last-child,
  .home-page-custom .framer-1kya2au > :last-child,
  .home-page-custom .framer-i045tb > :last-child,
  .home-page-custom .framer-5oimzk > :last-child {
    margin-bottom: 0;
  }

  .home-page-custom .framer-1f5r9mg > *,
  .home-page-custom .framer-guky4z > *,
  .home-page-custom .framer-j6xwqh > *,
  .home-page-custom .framer-nth0bs > * {
    margin: 0;
  }

  .home-page-custom .framer-1f5r9mg > :first-child,
  .home-page-custom .framer-bd5hir > :first-child,
  .home-page-custom .framer-1xr2u5k > :first-child,
  .home-page-custom .framer-1ihpbf6 > :first-child,
  .home-page-custom .framer-6i9pkl > :first-child,
  .home-page-custom .framer-guky4z > :first-child,
  .home-page-custom .framer-1foeow1 > :first-child,
  .home-page-custom .framer-cqwgdx > :first-child,
  .home-page-custom .framer-1qxx6ig > :first-child,
  .home-page-custom .framer-w3ueca > :first-child,
  .home-page-custom .framer-rqioxc > :first-child,
  .home-page-custom .framer-17t7q4z > :first-child,
  .home-page-custom .framer-1tgfsiy > :first-child,
  .home-page-custom .framer-1vy34m1 > :first-child,
  .home-page-custom .framer-18oas4d > :first-child,
  .home-page-custom .framer-r5ik0h > :first-child,
  .home-page-custom .framer-c9rzho > :first-child,
  .home-page-custom .framer-q3l069 > :first-child,
  .home-page-custom .framer-1gnkgon > :first-child,
  .home-page-custom .framer-lq3c2j > :first-child,
  .home-page-custom .framer-150p2xi > :first-child,
  .home-page-custom .framer-tawppn > :first-child,
  .home-page-custom .framer-g8k5om > :first-child,
  .home-page-custom .framer-1kpjnpz > :first-child,
  .home-page-custom .framer-1st7og5 > :first-child,
  .home-page-custom .framer-uoadof > :first-child,
  .home-page-custom .framer-1ltaqr > :first-child,
  .home-page-custom .framer-141iih5 > :first-child,
  .home-page-custom .framer-1m6rjb6 > :first-child,
  .home-page-custom .framer-rze4ds > :first-child,
  .home-page-custom .framer-15eaoaj > :first-child,
  .home-page-custom .framer-1wc3q7a > :first-child,
  .home-page-custom .framer-1w4ic65 > :first-child,
  .home-page-custom .framer-1nt7gq7 > :first-child,
  .home-page-custom .framer-11bnel3 > :first-child,
  .home-page-custom .framer-5kgvqx > :first-child,
  .home-page-custom .framer-1mtrwt3 > :first-child,
  .home-page-custom .framer-1i49r7n > :first-child,
  .home-page-custom .framer-hv3xx0 > :first-child,
  .home-page-custom .framer-l9o47p > :first-child,
  .home-page-custom .framer-w5xjgn > :first-child,
  .home-page-custom .framer-1csw3f9 > :first-child,
  .home-page-custom .framer-vz4a09 > :first-child,
  .home-page-custom .framer-prue0r > :first-child,
  .home-page-custom .framer-7aa0x7 > :first-child,
  .home-page-custom .framer-1mmkshw > :first-child,
  .home-page-custom .framer-noocrq > :first-child,
  .home-page-custom .framer-1kjlstk > :first-child,
  .home-page-custom .framer-1mtnlym > :first-child,
  .home-page-custom .framer-10secyz > :first-child,
  .home-page-custom .framer-rttqa > :first-child,
  .home-page-custom .framer-iy1wke > :first-child,
  .home-page-custom .framer-r6aav1 > :first-child,
  .home-page-custom .framer-11j1vop > :first-child,
  .home-page-custom .framer-1dzbgmo > :first-child,
  .home-page-custom .framer-54i8jr > :first-child,
  .home-page-custom .framer-j6xwqh > :first-child,
  .home-page-custom .framer-1i9t6vo > :first-child,
  .home-page-custom .framer-ktwg9w > :first-child,
  .home-page-custom .framer-it00y6 > :first-child,
  .home-page-custom .framer-127xgtk > :first-child,
  .home-page-custom .framer-1wyc3qt > :first-child,
  .home-page-custom .framer-1krtfx > :first-child,
  .home-page-custom .framer-uljs1t > :first-child,
  .home-page-custom .framer-nth0bs > :first-child {
    margin-left: 0;
  }

  .home-page-custom .framer-1f5r9mg > :last-child,
  .home-page-custom .framer-bd5hir > :last-child,
  .home-page-custom .framer-1xr2u5k > :last-child,
  .home-page-custom .framer-1ihpbf6 > :last-child,
  .home-page-custom .framer-6i9pkl > :last-child,
  .home-page-custom .framer-guky4z > :last-child,
  .home-page-custom .framer-1foeow1 > :last-child,
  .home-page-custom .framer-cqwgdx > :last-child,
  .home-page-custom .framer-1qxx6ig > :last-child,
  .home-page-custom .framer-w3ueca > :last-child,
  .home-page-custom .framer-rqioxc > :last-child,
  .home-page-custom .framer-17t7q4z > :last-child,
  .home-page-custom .framer-1tgfsiy > :last-child,
  .home-page-custom .framer-1vy34m1 > :last-child,
  .home-page-custom .framer-18oas4d > :last-child,
  .home-page-custom .framer-r5ik0h > :last-child,
  .home-page-custom .framer-c9rzho > :last-child,
  .home-page-custom .framer-q3l069 > :last-child,
  .home-page-custom .framer-1gnkgon > :last-child,
  .home-page-custom .framer-lq3c2j > :last-child,
  .home-page-custom .framer-150p2xi > :last-child,
  .home-page-custom .framer-tawppn > :last-child,
  .home-page-custom .framer-g8k5om > :last-child,
  .home-page-custom .framer-1kpjnpz > :last-child,
  .home-page-custom .framer-1st7og5 > :last-child,
  .home-page-custom .framer-uoadof > :last-child,
  .home-page-custom .framer-1ltaqr > :last-child,
  .home-page-custom .framer-141iih5 > :last-child,
  .home-page-custom .framer-1m6rjb6 > :last-child,
  .home-page-custom .framer-rze4ds > :last-child,
  .home-page-custom .framer-15eaoaj > :last-child,
  .home-page-custom .framer-1wc3q7a > :last-child,
  .home-page-custom .framer-1w4ic65 > :last-child,
  .home-page-custom .framer-1nt7gq7 > :last-child,
  .home-page-custom .framer-11bnel3 > :last-child,
  .home-page-custom .framer-5kgvqx > :last-child,
  .home-page-custom .framer-1mtrwt3 > :last-child,
  .home-page-custom .framer-1i49r7n > :last-child,
  .home-page-custom .framer-hv3xx0 > :last-child,
  .home-page-custom .framer-l9o47p > :last-child,
  .home-page-custom .framer-w5xjgn > :last-child,
  .home-page-custom .framer-1csw3f9 > :last-child,
  .home-page-custom .framer-vz4a09 > :last-child,
  .home-page-custom .framer-prue0r > :last-child,
  .home-page-custom .framer-7aa0x7 > :last-child,
  .home-page-custom .framer-1mmkshw > :last-child,
  .home-page-custom .framer-noocrq > :last-child,
  .home-page-custom .framer-1kjlstk > :last-child,
  .home-page-custom .framer-1mtnlym > :last-child,
  .home-page-custom .framer-10secyz > :last-child,
  .home-page-custom .framer-rttqa > :last-child,
  .home-page-custom .framer-iy1wke > :last-child,
  .home-page-custom .framer-r6aav1 > :last-child,
  .home-page-custom .framer-11j1vop > :last-child,
  .home-page-custom .framer-1dzbgmo > :last-child,
  .home-page-custom .framer-54i8jr > :last-child,
  .home-page-custom .framer-j6xwqh > :last-child,
  .home-page-custom .framer-1i9t6vo > :last-child,
  .home-page-custom .framer-ktwg9w > :last-child,
  .home-page-custom .framer-it00y6 > :last-child,
  .home-page-custom .framer-127xgtk > :last-child,
  .home-page-custom .framer-1wyc3qt > :last-child,
  .home-page-custom .framer-1krtfx > :last-child,
  .home-page-custom .framer-uljs1t > :last-child,
  .home-page-custom .framer-nth0bs > :last-child {
    margin-right: 0;
  }

  .home-page-custom .framer-1cf4im2 > *,
  .home-page-custom .framer-a3oink > *,
  .home-page-custom .framer-5oimzk > * {
    margin: 15px 0;
  }

  .home-page-custom .framer-bd5hir > *,
  .home-page-custom .framer-1ihpbf6 > *,
  .home-page-custom .framer-1foeow1 > *,
  .home-page-custom .framer-w3ueca > *,
  .home-page-custom .framer-rqioxc > *,
  .home-page-custom .framer-17t7q4z > *,
  .home-page-custom .framer-1tgfsiy > *,
  .home-page-custom .framer-1vy34m1 > *,
  .home-page-custom .framer-c9rzho > *,
  .home-page-custom .framer-q3l069 > *,
  .home-page-custom .framer-1gnkgon > *,
  .home-page-custom .framer-lq3c2j > *,
  .home-page-custom .framer-150p2xi > *,
  .home-page-custom .framer-1kpjnpz > *,
  .home-page-custom .framer-1st7og5 > *,
  .home-page-custom .framer-uoadof > *,
  .home-page-custom .framer-1ltaqr > *,
  .home-page-custom .framer-141iih5 > *,
  .home-page-custom .framer-15eaoaj > *,
  .home-page-custom .framer-1wc3q7a > *,
  .home-page-custom .framer-1w4ic65 > *,
  .home-page-custom .framer-1nt7gq7 > *,
  .home-page-custom .framer-11bnel3 > *,
  .home-page-custom .framer-1i49r7n > *,
  .home-page-custom .framer-hv3xx0 > *,
  .home-page-custom .framer-l9o47p > *,
  .home-page-custom .framer-w5xjgn > *,
  .home-page-custom .framer-1csw3f9 > *,
  .home-page-custom .framer-prue0r > *,
  .home-page-custom .framer-7aa0x7 > *,
  .home-page-custom .framer-1mmkshw > *,
  .home-page-custom .framer-noocrq > *,
  .home-page-custom .framer-1mtnlym > *,
  .home-page-custom .framer-rttqa > *,
  .home-page-custom .framer-iy1wke > *,
  .home-page-custom .framer-11j1vop > *,
  .home-page-custom .framer-1dzbgmo > *,
  .home-page-custom .framer-54i8jr > *,
  .home-page-custom .framer-1i9t6vo > *,
  .home-page-custom .framer-ktwg9w > *,
  .home-page-custom .framer-it00y6 > *,
  .home-page-custom .framer-127xgtk > *,
  .home-page-custom .framer-1krtfx > *,
  .home-page-custom .framer-uljs1t > * {
    margin: 0 5px;
  }

  .home-page-custom .framer-1xr2u5k > *,
  .home-page-custom .framer-6i9pkl > * {
    margin: 0 15px;
  }

  .home-page-custom .framer-1bffjxr > *,
  .home-page-custom .framer-1ld3y1i > *,
  .home-page-custom .framer-5iual9 > * {
    margin: 7.5px 0;
  }

  .home-page-custom .framer-1084rkw > *,
  .home-page-custom .framer-m841la > *,
  .home-page-custom .framer-prks2j > *,
  .home-page-custom .framer-1wtt9yy > *,
  .home-page-custom .framer-12eq2fb > *,
  .home-page-custom .framer-loeln5 > *,
  .home-page-custom .framer-q65zs5 > *,
  .home-page-custom .framer-r1619f > *,
  .home-page-custom .framer-12gz4g1 > *,
  .home-page-custom .framer-vb9ps5 > *,
  .home-page-custom .framer-1xbn8az > *,
  .home-page-custom .framer-1qbycr3 > *,
  .home-page-custom .framer-7areok > *,
  .home-page-custom .framer-vut9vp > *,
  .home-page-custom .framer-11box35 > *,
  .home-page-custom .framer-anapib > *,
  .home-page-custom .framer-1kya2au > * {
    margin: 5px 0;
  }

  .home-page-custom .framer-cqwgdx > *,
  .home-page-custom .framer-18oas4d > *,
  .home-page-custom .framer-tawppn > *,
  .home-page-custom .framer-1m6rjb6 > *,
  .home-page-custom .framer-5kgvqx > * {
    margin: 0 2px;
  }

  .home-page-custom .framer-1qxx6ig > *,
  .home-page-custom .framer-r5ik0h > *,
  .home-page-custom .framer-g8k5om > *,
  .home-page-custom .framer-rze4ds > *,
  .home-page-custom .framer-1mtrwt3 > *,
  .home-page-custom .framer-vz4a09 > * {
    margin: 0 7.5px;
  }

  .home-page-custom .framer-1kjlstk > * {
    margin: 0 35px;
  }

  .home-page-custom .framer-xplmi1 > * {
    margin: 17.5px 0;
  }

  .home-page-custom .framer-10secyz > *,
  .home-page-custom .framer-r6aav1 > *,
  .home-page-custom .framer-1wyc3qt > * {
    margin: 0 10px;
  }

  .home-page-custom .framer-9busuh > *,
  .home-page-custom .framer-8eju73 > *,
  .home-page-custom .framer-11pnqj3 > * {
    margin: 10px 0;
  }

  .home-page-custom .framer-i045tb > * {
    margin: 25px 0;
  }
}

@media (min-width: 1300px) {
  .home-page-custom .hidden-72rtr7 {
    display: none !important;
  }
}

@media (min-width: 896px) and (max-width: 1299px) {
  .home-page-custom .hidden-yurcy1 {
    display: none !important;
  }

  .framer-body-augiA20Il-framer-9NO9m {
    background: white;
  }

  .home-page-custom.home-page-custom-7 {
    width: 896px;
  }

  .home-page-custom .framer-1k3vmhf-container {
    order: 0;
  }

  .home-page-custom .framer-1f5r9mg {
    gap: 30px;
    padding: 100px 10px 50px;
  }

  .home-page-custom .framer-1basxbz {
    order: 0;
    width: 100%;
  }

  .home-page-custom .framer-1cf4im2 {
    order: 0;
    // width: 50%;
  }

  .home-page-custom .framer-1xr2u5k,
  .home-page-custom .framer-11pnqj3 {
    gap: 10px;
  }

  .home-page-custom .framer-i5ywx5 {
    aspect-ratio: 1.1278409090909092 / 1;
    height: var(--framer-aspect-ratio-supported, 346px);
    order: 1;
    width: 49%;
  }

  .home-page-custom .framer-1bffjxr {
    align-self: unset;
    height: min-content;
    width: 75%;
  }

  .home-page-custom .framer-6i9pkl {
    flex: none;
    gap: 20px;
    justify-content: center;
    width: 100%;
  }

  // .home-page-custom .framer-1ld3y1i,
  // .home-page-custom .framer-5iual9,
  .home-page-custom .framer-7areok,
  .home-page-custom .framer-vut9vp {
    height: min-content;
  }

  .home-page-custom .framer-a3oink {
    align-content: center;
    align-items: center;
    justify-content: flex-start;
    order: 3;
  }

  .home-page-custom .framer-guky4z {
    flex-direction: column;
    justify-content: flex-end;
  }

  .home-page-custom .framer-1mnppt8 {
    flex: none;
    width: 100%;
  }

  .home-page-custom .framer-16hx91z-container {
    height: 375px;
  }

  .home-page-custom .framer-1kjlstk {
    gap: 40px;
    order: 4;
    padding: 70px 10px 40px;
    width: 100%;
  }

  .home-page-custom .framer-1mtnlym {
    height: 700px;
    width: 50%;
  }

  .home-page-custom .framer-52ykyo {
    right: -32px;
    top: -34px;
  }

  .home-page-custom .framer-xplmi1 {
    gap: 20px;
    padding: 0 10px 0 20px;
  }

  .home-page-custom .framer-10secyz,
  .home-page-custom .framer-r6aav1 {
    width: 100%;
  }

  .home-page-custom .framer-11box35 {
    align-content: center;
    align-items: center;
    gap: 0px;
    justify-content: flex-start;
    padding: 40px 10px 10px;
  }

  .home-page-custom .framer-j6xwqh {
    flex: none;
    flex-direction: column;
    justify-content: flex-end;
    width: min-content;
  }

  .home-page-custom .framer-9rkqq4 {
    flex: none;
    white-space: pre;
    width: auto;
  }

  .home-page-custom .framer-1i9t6vo {
    flex: none;
    flex-direction: column;
    justify-content: center;
    width: min-content;
  }

  .home-page-custom .framer-1lswng1,
  .home-page-custom .framer-1kp00hc {
    padding: 30px 0;
  }

  .home-page-custom .framer-1754pfs {
    grid-template-columns: repeat(2, minmax(200px, 1fr));
  }

  .home-page-custom .framer-3ldttv {
    gap: 10px;
    max-width: 100%;
    order: 1;
    overflow: hidden;
    padding: 40px 10px 60px;
  }

  .home-page-custom .framer-1irlews {
    bottom: 818px;
    height: unset;
    left: 5px;
    top: 80px;
    width: 160px;
  }

  .home-page-custom .framer-1oo7ocx {
    height: 250px;
    width: 100%;
  }

  .home-page-custom .framer-1kya2au {
    order: 7;
  }

  .home-page-custom .framer-nth0bs {
    padding: 60px 10px;
  }

  .home-page-custom .framer-i045tb {
    flex: 1 0 0px;
    max-width: 90%;
    width: 1px;
  }

  .home-page-custom .framer-5oimzk {
    align-content: flex-start;
    align-items: flex-start;
  }

  .home-page-custom .framer-p575pe-container {
    order: 8;
  }

  @supports (background: -webkit-named-image(i)) and (not (scale: 1)) {
    .home-page-custom .framer-1f5r9mg,
    .home-page-custom .framer-1xr2u5k,
    .home-page-custom .framer-6i9pkl,
    .home-page-custom .framer-guky4z,
    .home-page-custom .framer-1kjlstk,
    .home-page-custom .framer-xplmi1,
    .home-page-custom .framer-11box35,
    .home-page-custom .framer-j6xwqh,
    .home-page-custom .framer-1i9t6vo,
    .home-page-custom .framer-3ldttv,
    .home-page-custom .framer-11pnqj3 {
      gap: 0px;
    }

    .home-page-custom .framer-1f5r9mg > * {
      margin: 0 15px;
    }

    .home-page-custom .framer-1f5r9mg > :first-child,
    .home-page-custom .framer-1xr2u5k > :first-child,
    .home-page-custom .framer-6i9pkl > :first-child,
    .home-page-custom .framer-1kjlstk > :first-child {
      margin-left: 0;
    }

    .home-page-custom .framer-1f5r9mg > :last-child,
    .home-page-custom .framer-1xr2u5k > :last-child,
    .home-page-custom .framer-6i9pkl > :last-child,
    .home-page-custom .framer-1kjlstk > :last-child {
      margin-right: 0;
    }

    .home-page-custom .framer-1xr2u5k > * {
      margin: 0 5px;
    }

    .home-page-custom .framer-guky4z > :first-child,
    .home-page-custom .framer-xplmi1 > :first-child,
    .home-page-custom .framer-11box35 > :first-child,
    .home-page-custom .framer-j6xwqh > :first-child,
    .home-page-custom .framer-1i9t6vo > :first-child,
    .home-page-custom .framer-3ldttv > :first-child,
    .home-page-custom .framer-11pnqj3 > :first-child {
      margin-top: 0;
    }

    .home-page-custom .framer-guky4z > :last-child,
    .home-page-custom .framer-xplmi1 > :last-child,
    .home-page-custom .framer-11box35 > :last-child,
    .home-page-custom .framer-j6xwqh > :last-child,
    .home-page-custom .framer-1i9t6vo > :last-child,
    .home-page-custom .framer-3ldttv > :last-child,
    .home-page-custom .framer-11pnqj3 > :last-child {
      margin-bottom: 0;
    }

    .home-page-custom .framer-6i9pkl > * {
      margin: 0 10px;
    }

    .home-page-custom .framer-guky4z > *,
    .home-page-custom .framer-11box35 > *,
    .home-page-custom .framer-j6xwqh > * {
      margin: 0;
    }

    .home-page-custom .framer-1kjlstk > * {
      margin: 0 20px;
    }

    .home-page-custom .framer-xplmi1 > * {
      margin: 10px 0;
    }

    .home-page-custom .framer-1i9t6vo > *,
    .home-page-custom .framer-3ldttv > *,
    .home-page-custom .framer-11pnqj3 > * {
      margin: 5px 0;
    }
  }
}

@media (max-width: 895px) {
  .home-page-custom .hidden-1wswnjk {
    display: none !important;
  }

  .framer-body-augiA20Il-framer-9NO9m {
    background: white;
  }

  .home-page-custom.home-page-custom-7 {
    width: 390px;
  }

  .home-page-custom .framer-1k3vmhf-container {
    order: 0;
    width: 100%;
  }

  .home-page-custom .framer-1f5r9mg {
    flex-direction: column;
    gap: 10px;
    padding: 100px 0 30px;
  }

  .home-page-custom .framer-1basxbz {
    flex-direction: column;
    gap: 20px;
    height: 668px;
    justify-content: center;
    order: 0;
  }

  .home-page-custom .framer-1cf4im2 {
    gap: 20px;
    justify-content: flex-start;
    padding: 0 10px;
    width: 100%;
  }

  .home-page-custom .framer-kfr0i8 {
    white-space: pre-wrap;
    width: 100%;
    word-break: break-word;
    word-wrap: break-word;
  }

  .home-page-custom .framer-ias8c9 {
    width: 100%;
  }

  .home-page-custom .framer-1xr2u5k {
    gap: 10px;
    padding: 10px 0;
  }

  .home-page-custom .framer-1ihpbf6 {
    gap: 8px;
    padding: 12px 10px;
  }

  .home-page-custom .framer-1s1d003 {
    height: 16px;
  }

  .home-page-custom .framer-i5ywx5 {
    flex: 1 0 0px;
    height: 1px;
    width: 100%;
  }

  .home-page-custom .framer-1bffjxr {
    align-self: unset;
    gap: 20px;
    height: min-content;
    order: 0;
    width: 100%;
  }

  .home-page-custom .framer-1oh3mcl,
  .home-page-custom .framer-t3m7xu,
  .home-page-custom .framer-s8of2i,
  .home-page-custom .framer-1xtfyzj {
    order: 0;
  }

  .home-page-custom .framer-25d0l5,
  .home-page-custom .framer-q44gzi,
  .home-page-custom .framer-15nn0f7,
  .home-page-custom .framer-18424fl {
    order: 1;
  }

  .home-page-custom .framer-6i9pkl {
    flex: none;
    flex-direction: column;
    gap: 20px;
    height: auto;
    order: 1;
    width: 100%;
  }

  .home-page-custom .framer-1ld3y1i,
  .home-page-custom .framer-5iual9 {
    flex: none;
    gap: 20px;
    height: min-content;
    width: 100%;
  }

  .home-page-custom .framer-1ba90y0,
  .home-page-custom .framer-nsy5mj,
  .home-page-custom .framer-1u9u5yx {
    order: 2;
  }

  .home-page-custom .framer-a3oink {
    align-content: center;
    align-items: center;
    gap: 20px;
    order: 3;
    padding: 20px 10px 30px;
  }

  .home-page-custom .framer-guky4z {
    align-content: flex-start;
    align-items: flex-start;
    flex-direction: column;
  }

  .home-page-custom .framer-1mnppt8,
  .home-page-custom .framer-9rkqq4 {
    flex: none;
    white-space: pre;
    width: auto;
  }

  .home-page-custom .framer-1kjlstk {
    flex-direction: column;
    gap: 30px;
    order: 4;
    padding: 50px 10px 30px;
    width: 100%;
  }

  .home-page-custom .framer-1mtnlym {
    height: 550px;
    justify-content: flex-start;
    order: 0;
    width: 80%;
  }

  .home-page-custom .framer-52ykyo {
    right: unset;
    width: 98%;
  }

  .home-page-custom .framer-xplmi1 {
    flex: none;
    gap: 20px;
    order: 1;
    padding: 0;
    width: 100%;
  }

  .home-page-custom .framer-10secyz,
  .home-page-custom .framer-r6aav1 {
    gap: 15px;
    justify-content: flex-start;
    width: 100%;
  }

  .home-page-custom .framer-iy1wke,
  .home-page-custom .framer-1dzbgmo {
    width: 50%;
  }

  .home-page-custom .framer-7areok {
    align-content: flex-start;
    align-items: flex-start;
    height: 121px;
    justify-content: flex-end;
  }

  .home-page-custom .framer-bivqd1 {
    flex: 1 0 0px;
    height: 1px;
  }

  .home-page-custom .framer-vut9vp {
    align-content: flex-start;
    align-items: flex-start;
    height: min-content;
    justify-content: flex-end;
  }

  .home-page-custom .framer-54i8jr {
    justify-content: center;
    padding: 12px;
  }

  .home-page-custom .framer-11box35 {
    align-content: center;
    align-items: center;
    padding: 20px 10px 10px;
  }

  .home-page-custom .framer-gnci1m {
    flex-direction: column;
    gap: 10px;
    justify-content: center;
    width: auto;
  }

  .home-page-custom .framer-j6xwqh {
    flex: none;
    flex-direction: column;
    justify-content: flex-end;
    width: min-content;
  }

  .home-page-custom .framer-1i9t6vo {
    flex: none;
    flex-direction: column;
    height: min-content;
    justify-content: center;
    width: min-content;
  }

  .home-page-custom .framer-ktwg9w,
  .home-page-custom .framer-uljs1t {
    padding: 12px;
  }

  .home-page-custom .framer-1lswng1 {
    padding: 10px 0 20px;
  }

  .home-page-custom .framer-1754pfs {
    grid-template-columns: repeat(1, minmax(200px, 1fr));
  }

  .home-page-custom .framer-8eju73,
  .home-page-custom .framer-11pnqj3 {
    gap: 10px;
  }

  .home-page-custom .framer-3ldttv {
    max-width: 100%;
    order: 1;
    overflow: hidden;
    padding: 10px 10px 40px;
  }

  .home-page-custom .framer-1kp00hc {
    gap: 20px;
    grid-template-columns: repeat(1, minmax(200px, 1fr));
    padding: 20px 0;
  }

  .home-page-custom .framer-1oo7ocx {
    height: 200px;
    width: 100%;
  }

  .home-page-custom .framer-1kya2au {
    flex-wrap: wrap;
    order: 7;
  }

  .home-page-custom .framer-nth0bs {
    order: 0;
    padding: 50px 10px;
  }

  .home-page-custom .framer-i045tb {
    flex: 1 0 0px;
    gap: 30px;
    max-width: 100%;
    width: 1px;
  }

  .home-page-custom .framer-5oimzk {
    align-content: flex-start;
    align-items: flex-start;
    gap: 25px;
  }

  .home-page-custom .framer-p575pe-container {
    order: 8;
  }

  @supports (background: -webkit-named-image(i)) and (not (scale: 1)) {
    .home-page-custom .framer-1f5r9mg,
    .home-page-custom .framer-1basxbz,
    .home-page-custom .framer-1cf4im2,
    .home-page-custom .framer-1xr2u5k,
    .home-page-custom .framer-1ihpbf6,
    .home-page-custom .framer-1bffjxr,
    .home-page-custom .framer-6i9pkl,
    .home-page-custom .framer-1ld3y1i,
    .home-page-custom .framer-5iual9,
    .home-page-custom .framer-a3oink,
    .home-page-custom .framer-guky4z,
    .home-page-custom .framer-1kjlstk,
    .home-page-custom .framer-xplmi1,
    .home-page-custom .framer-10secyz,
    .home-page-custom .framer-r6aav1,
    .home-page-custom .framer-gnci1m,
    .home-page-custom .framer-j6xwqh,
    .home-page-custom .framer-1i9t6vo,
    .home-page-custom .framer-8eju73,
    .home-page-custom .framer-1kp00hc,
    .home-page-custom .framer-11pnqj3,
    .home-page-custom .framer-i045tb,
    .home-page-custom .framer-5oimzk {
      gap: 0px;
    }

    .home-page-custom .framer-1f5r9mg > *,
    .home-page-custom .framer-gnci1m > *,
    .home-page-custom .framer-1i9t6vo > *,
    .home-page-custom .framer-8eju73 > *,
    .home-page-custom .framer-11pnqj3 > * {
      margin: 5px 0;
    }

    .home-page-custom .framer-1f5r9mg > :first-child,
    .home-page-custom .framer-1basxbz > :first-child,
    .home-page-custom .framer-1cf4im2 > :first-child,
    .home-page-custom .framer-1bffjxr > :first-child,
    .home-page-custom .framer-6i9pkl > :first-child,
    .home-page-custom .framer-1ld3y1i > :first-child,
    .home-page-custom .framer-5iual9 > :first-child,
    .home-page-custom .framer-a3oink > :first-child,
    .home-page-custom .framer-guky4z > :first-child,
    .home-page-custom .framer-1kjlstk > :first-child,
    .home-page-custom .framer-xplmi1 > :first-child,
    .home-page-custom .framer-gnci1m > :first-child,
    .home-page-custom .framer-j6xwqh > :first-child,
    .home-page-custom .framer-1i9t6vo > :first-child,
    .home-page-custom .framer-8eju73 > :first-child,
    .home-page-custom .framer-11pnqj3 > :first-child,
    .home-page-custom .framer-i045tb > :first-child,
    .home-page-custom .framer-5oimzk > :first-child {
      margin-top: 0;
    }

    .home-page-custom .framer-1f5r9mg > :last-child,
    .home-page-custom .framer-1basxbz > :last-child,
    .home-page-custom .framer-1cf4im2 > :last-child,
    .home-page-custom .framer-1bffjxr > :last-child,
    .home-page-custom .framer-6i9pkl > :last-child,
    .home-page-custom .framer-1ld3y1i > :last-child,
    .home-page-custom .framer-5iual9 > :last-child,
    .home-page-custom .framer-a3oink > :last-child,
    .home-page-custom .framer-guky4z > :last-child,
    .home-page-custom .framer-1kjlstk > :last-child,
    .home-page-custom .framer-xplmi1 > :last-child,
    .home-page-custom .framer-gnci1m > :last-child,
    .home-page-custom .framer-j6xwqh > :last-child,
    .home-page-custom .framer-1i9t6vo > :last-child,
    .home-page-custom .framer-8eju73 > :last-child,
    .home-page-custom .framer-11pnqj3 > :last-child,
    .home-page-custom .framer-i045tb > :last-child,
    .home-page-custom .framer-5oimzk > :last-child {
      margin-bottom: 0;
    }

    .home-page-custom .framer-1basxbz > *,
    .home-page-custom .framer-1cf4im2 > *,
    .home-page-custom .framer-1bffjxr > *,
    .home-page-custom .framer-6i9pkl > *,
    .home-page-custom .framer-1ld3y1i > *,
    .home-page-custom .framer-5iual9 > *,
    .home-page-custom .framer-a3oink > *,
    .home-page-custom .framer-xplmi1 > * {
      margin: 10px 0;
    }

    .home-page-custom .framer-1xr2u5k > * {
      margin: 0 5px;
    }

    .home-page-custom .framer-1xr2u5k > :first-child,
    .home-page-custom .framer-1ihpbf6 > :first-child,
    .home-page-custom .framer-10secyz > :first-child,
    .home-page-custom .framer-r6aav1 > :first-child {
      margin-left: 0;
    }

    .home-page-custom .framer-1xr2u5k > :last-child,
    .home-page-custom .framer-1ihpbf6 > :last-child,
    .home-page-custom .framer-10secyz > :last-child,
    .home-page-custom .framer-r6aav1 > :last-child {
      margin-right: 0;
    }

    .home-page-custom .framer-1ihpbf6 > * {
      margin: 0 4px;
    }

    .home-page-custom .framer-guky4z > *,
    .home-page-custom .framer-j6xwqh > * {
      margin: 0;
    }

    .home-page-custom .framer-1kjlstk > *,
    .home-page-custom .framer-i045tb > * {
      margin: 15px 0;
    }

    .home-page-custom .framer-10secyz > *,
    .home-page-custom .framer-r6aav1 > * {
      margin: 0 7.5px;
    }

    .home-page-custom .framer-1kp00hc > *,
    .home-page-custom .framer-1kp00hc > :first-child,
    .home-page-custom .framer-1kp00hc > :last-child {
      margin: 0;
    }

    .home-page-custom .framer-5oimzk > * {
      margin: 12.5px 0;
    }
  }
}

.home-page-custom-1 .framer-styles-preset-1crlhpy:not(.rich-text-wrapper),
.home-page-custom-1 .framer-styles-preset-1crlhpy.rich-text-wrapper h1 {
  --framer-font-family: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
  --framer-font-family-bold: 'Be Vietnam Pro', sans-serif;
  --framer-font-family-bold-italic: 'Be Vietnam Pro', sans-serif;
  --framer-font-family-italic: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
  --framer-font-size: 50px;
  --framer-font-style: normal;
  --framer-font-style-bold: normal;
  --framer-font-style-bold-italic: italic;
  --framer-font-style-italic: italic;
  --framer-font-weight: 700;
  --framer-font-weight-bold: 900;
  --framer-font-weight-bold-italic: 900;
  --framer-font-weight-italic: 700;
  --framer-letter-spacing: 0px;
  --framer-line-height: 1.2em;
  --framer-paragraph-spacing: 0px;
  --framer-text-alignment: start;
  --framer-text-color: #000000;
  --framer-text-decoration: none;
  --framer-text-transform: none;
}

@media (max-width: 1299px) and (min-width: 896px) {
  .home-page-custom-1 .framer-styles-preset-1crlhpy:not(.rich-text-wrapper),
  .home-page-custom-1 .framer-styles-preset-1crlhpy.rich-text-wrapper h1 {
    --framer-font-family: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
    --framer-font-family-bold: 'Be Vietnam Pro', sans-serif;
    --framer-font-family-bold-italic: 'Be Vietnam Pro', sans-serif;
    --framer-font-family-italic: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
    --framer-font-size: 45px;
    --framer-font-style: normal;
    --framer-font-style-bold: normal;
    --framer-font-style-bold-italic: italic;
    --framer-font-style-italic: italic;
    --framer-font-weight: 700;
    --framer-font-weight-bold: 900;
    --framer-font-weight-bold-italic: 900;
    --framer-font-weight-italic: 700;
    --framer-letter-spacing: 0px;
    --framer-line-height: 58px;
    --framer-paragraph-spacing: 0px;
    --framer-text-alignment: start;
    --framer-text-color: #000000;
    --framer-text-decoration: none;
    --framer-text-transform: none;
  }
}

@media (max-width: 1220px) and (min-width: 896px) {
  .home-page-custom-1 .framer-styles-preset-1crlhpy:not(.rich-text-wrapper),
  .home-page-custom-1 .framer-styles-preset-1crlhpy.rich-text-wrapper h1 {
    --framer-font-size: 42px;
  }
}

@media (max-width: 895px) and (min-width: 0px) {
  .home-page-custom-1 .framer-styles-preset-1crlhpy:not(.rich-text-wrapper),
  .home-page-custom-1 .framer-styles-preset-1crlhpy.rich-text-wrapper h1 {
    --framer-font-family: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
    --framer-font-family-bold: 'Be Vietnam Pro', sans-serif;
    --framer-font-family-bold-italic: 'Be Vietnam Pro', sans-serif;
    --framer-font-family-italic: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
    --framer-font-size: 33px;
    --framer-font-style: normal;
    --framer-font-style-bold: normal;
    --framer-font-style-bold-italic: italic;
    --framer-font-style-italic: italic;
    --framer-font-weight: 700;
    --framer-font-weight-bold: 900;
    --framer-font-weight-bold-italic: 900;
    --framer-font-weight-italic: 700;
    --framer-letter-spacing: 0px;
    --framer-line-height: 42px;
    --framer-paragraph-spacing: 0px;
    --framer-text-alignment: start;
    --framer-text-color: #000000;
    --framer-text-decoration: none;
    --framer-text-transform: none;
  }
}

.home-page-custom-2 .framer-styles-preset-xtsc6h:not(.rich-text-wrapper),
.home-page-custom-2 .framer-styles-preset-xtsc6h.rich-text-wrapper p {
  --framer-font-family: 'Be Vietnam Pro', sans-serif;
  --framer-font-family-bold: 'Be Vietnam Pro', 'Inter', 'Inter Placeholder', sans-serif;
  --framer-font-family-bold-italic: 'Be Vietnam Pro', 'Inter', 'Inter Placeholder', sans-serif;
  --framer-font-family-italic: 'Be Vietnam Pro', 'Inter', 'Inter Placeholder', sans-serif;
  --framer-font-size: 16px;
  --framer-font-style: normal;
  --framer-font-style-bold: normal;
  --framer-font-style-bold-italic: italic;
  --framer-font-style-italic: italic;
  --framer-font-weight: 400;
  --framer-font-weight-bold: 700;
  --framer-font-weight-bold-italic: 700;
  --framer-font-weight-italic: 400;
  --framer-letter-spacing: 0em;
  --framer-line-height: 1.3em;
  --framer-paragraph-spacing: 20px;
  --framer-text-alignment: start;
  --framer-text-color: #333333;
  --framer-text-decoration: none;
  --framer-text-transform: none;
}

.home-page-custom-4 .framer-styles-preset-shv5t8:not(.rich-text-wrapper),
.home-page-custom-4 .framer-styles-preset-shv5t8.rich-text-wrapper h2 {
  --framer-font-family: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
  --framer-font-family-bold: 'Be Vietnam Pro', sans-serif;
  --framer-font-family-bold-italic: 'Be Vietnam Pro', sans-serif;
  --framer-font-family-italic: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
  --framer-font-size: 40px;
  --framer-font-style: normal;
  --framer-font-style-bold: normal;
  --framer-font-style-bold-italic: italic;
  --framer-font-style-italic: italic;
  --framer-font-weight: 700;
  --framer-font-weight-bold: 900;
  --framer-font-weight-bold-italic: 900;
  --framer-font-weight-italic: 700;
  --framer-letter-spacing: 0em;
  --framer-line-height: 1.3em;
  --framer-paragraph-spacing: 20px;
  --framer-text-alignment: left;
  --framer-text-color: #000000;
  --framer-text-decoration: none;
  --framer-text-transform: none;
}

@media (max-width: 1199px) and (min-width: 896px) {
  .home-page-custom-4 .framer-styles-preset-shv5t8:not(.rich-text-wrapper),
  .home-page-custom-4 .framer-styles-preset-shv5t8.rich-text-wrapper h2 {
    --framer-font-family: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
    --framer-font-family-bold: 'Be Vietnam Pro', sans-serif;
    --framer-font-family-bold-italic: 'Be Vietnam Pro', sans-serif;
    --framer-font-family-italic: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
    --framer-font-size: 32px;
    --framer-font-style: normal;
    --framer-font-style-bold: normal;
    --framer-font-style-bold-italic: italic;
    --framer-font-style-italic: italic;
    --framer-font-weight: 700;
    --framer-font-weight-bold: 900;
    --framer-font-weight-bold-italic: 900;
    --framer-font-weight-italic: 700;
    --framer-letter-spacing: 0em;
    --framer-line-height: 1.3em;
    --framer-paragraph-spacing: 20px;
    --framer-text-alignment: left;
    --framer-text-color: #000000;
    --framer-text-decoration: none;
    --framer-text-transform: none;
  }
}

@media (max-width: 895px) and (min-width: 0px) {
  .home-page-custom-4 .framer-styles-preset-shv5t8:not(.rich-text-wrapper),
  .home-page-custom-4 .framer-styles-preset-shv5t8.rich-text-wrapper h2 {
    --framer-font-family: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
    --framer-font-family-bold: 'Be Vietnam Pro', sans-serif;
    --framer-font-family-bold-italic: 'Be Vietnam Pro', sans-serif;
    --framer-font-family-italic: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
    --framer-font-size: 26px;
    --framer-font-style: normal;
    --framer-font-style-bold: normal;
    --framer-font-style-bold-italic: italic;
    --framer-font-style-italic: italic;
    --framer-font-weight: 700;
    --framer-font-weight-bold: 900;
    --framer-font-weight-bold-italic: 900;
    --framer-font-weight-italic: 700;
    --framer-letter-spacing: 0em;
    --framer-line-height: 1.3em;
    --framer-paragraph-spacing: 20px;
    --framer-text-alignment: left;
    --framer-text-color: #000000;
    --framer-text-decoration: none;
    --framer-text-transform: none;
  }
}

.home-page-custom-6 .framer-styles-preset-jfhzbe:not(.rich-text-wrapper),
.home-page-custom-6 .framer-styles-preset-jfhzbe.rich-text-wrapper h4 {
  --framer-font-family: 'Be Vietnam Pro', 'Inter', 'Inter Placeholder', sans-serif;
  --framer-font-family-bold: 'Be Vietnam Pro', 'Inter', sans-serif;
  --framer-font-family-bold-italic: 'Be Vietnam Pro', 'Inter', sans-serif;
  --framer-font-family-italic: 'Be Vietnam Pro', 'Inter', 'Inter Placeholder', sans-serif;
  --framer-font-size: 20px;
  --framer-font-style: normal;
  --framer-font-style-bold: normal;
  --framer-font-style-bold-italic: italic;
  --framer-font-style-italic: italic;
  --framer-font-weight: 700;
  --framer-font-weight-bold: 900;
  --framer-font-weight-bold-italic: 900;
  --framer-font-weight-italic: 700;
  --framer-letter-spacing: 0em;
  --framer-line-height: 1.3em;
  --framer-paragraph-spacing: 20px;
  --framer-text-alignment: start;
  --framer-text-color: #333333;
  --framer-text-decoration: none;
  --framer-text-transform: none;
}

.home-page-custom[data-border='true']:after,
.home-page-custom [data-border='true']:after {
  content: '';
  border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0)
    var(--border-left-width, 0);
  border-color: var(--border-color, none);
  border-style: var(--border-style, none);
  width: 100%;
  height: 100%;
  position: absolute;
  box-sizing: border-box;
  left: 0;
  top: 0;
  border-radius: inherit;
  pointer-events: none;
}

.home-page-custom-3 .framer-styles-preset-wo1gvy:not(.rich-text-wrapper),
.home-page-custom-3 .framer-styles-preset-wo1gvy.rich-text-wrapper a {
  --framer-link-current-text-color: #ededed;
  --framer-link-current-text-decoration: none;
  --framer-link-hover-text-color: #ededed;
  --framer-link-hover-text-decoration: none;
  --framer-link-text-color: #ffffff;
  --framer-link-text-decoration: none;
}

.home-page-custom-5 .framer-styles-preset-x7mwn6:not(.rich-text-wrapper),
.home-page-custom-5 .framer-styles-preset-x7mwn6.rich-text-wrapper h3 {
  --framer-font-family: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
  --framer-font-family-bold: 'Be Vietnam Pro', sans-serif;
  --framer-font-family-bold-italic: 'Be Vietnam Pro', sans-serif;
  --framer-font-family-italic: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
  --framer-font-size: 24px;
  --framer-font-style: normal;
  --framer-font-style-bold: normal;
  --framer-font-style-bold-italic: italic;
  --framer-font-style-italic: italic;
  --framer-font-weight: 700;
  --framer-font-weight-bold: 900;
  --framer-font-weight-bold-italic: 900;
  --framer-font-weight-italic: 700;
  --framer-letter-spacing: 0em;
  --framer-line-height: 1.3em;
  --framer-paragraph-spacing: 20px;
  --framer-text-alignment: start;
  --framer-text-color: #000000;
  --framer-text-decoration: none;
  --framer-text-transform: none;
}

@media (max-width: 1199px) and (min-width: 896px) {
  .home-page-custom-5 .framer-styles-preset-x7mwn6:not(.rich-text-wrapper),
  .home-page-custom-5 .framer-styles-preset-x7mwn6.rich-text-wrapper h3 {
    --framer-font-family: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
    --framer-font-family-bold: 'Be Vietnam Pro', sans-serif;
    --framer-font-family-bold-italic: 'Be Vietnam Pro', sans-serif;
    --framer-font-family-italic: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
    --framer-font-size: 20px;
    --framer-font-style: normal;
    --framer-font-style-bold: normal;
    --framer-font-style-bold-italic: italic;
    --framer-font-style-italic: italic;
    --framer-font-weight: 700;
    --framer-font-weight-bold: 900;
    --framer-font-weight-bold-italic: 900;
    --framer-font-weight-italic: 700;
    --framer-letter-spacing: 0em;
    --framer-line-height: 1.3em;
    --framer-paragraph-spacing: 20px;
    --framer-text-alignment: start;
    --framer-text-color: #000000;
    --framer-text-decoration: none;
    --framer-text-transform: none;
  }
}

@media (max-width: 895px) and (min-width: 0px) {
  .home-page-custom-5 .framer-styles-preset-x7mwn6:not(.rich-text-wrapper),
  .home-page-custom-5 .framer-styles-preset-x7mwn6.rich-text-wrapper h3 {
    --framer-font-family: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
    --framer-font-family-bold: 'Be Vietnam Pro', sans-serif;
    --framer-font-family-bold-italic: 'Be Vietnam Pro', sans-serif;
    --framer-font-family-italic: 'Be Vietnam Pro', 'DM Sans Placeholder', sans-serif;
    --framer-font-size: 18px;
    --framer-font-style: normal;
    --framer-font-style-bold: normal;
    --framer-font-style-bold-italic: italic;
    --framer-font-style-italic: italic;
    --framer-font-weight: 700;
    --framer-font-weight-bold: 900;
    --framer-font-weight-bold-italic: 900;
    --framer-font-weight-italic: 700;
    --framer-letter-spacing: 0em;
    --framer-line-height: 1.3em;
    --framer-paragraph-spacing: 20px;
    --framer-text-alignment: start;
    --framer-text-color: #000000;
    --framer-text-decoration: none;
    --framer-text-transform: none;
  }
}

.ssr-variant {
  display: contents;
}

@supports (aspect-ratio: 1) {
  body {
    --framer-aspect-ratio-supported: auto;
  }
}

@supports (background: -webkit-named-image(i)) and (not (font-palette: dark)) {
  .home-page-custom .my-courses-section {
    img {
      width: 50%;
      height: 220px;
      object-fit: cover;
    }

    .course-info {
      width: 50%;
    }

    @media (max-width: 1400px) {
      img,
      .course-info {
        width: 100%;
      }
    }
  }
}
