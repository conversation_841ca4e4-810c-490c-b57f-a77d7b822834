@font-face
{
    font-family: 'boxicons';
    font-weight: normal;
    font-style: normal;

    src: url('../fonts/boxicons.eot');
    src: url('../fonts/boxicons.eot') format('embedded-opentype'),
    url('../fonts/boxicons.woff2') format('woff2'),
    url('../fonts/boxicons.woff') format('woff'),
    url('../fonts/boxicons.ttf') format('truetype'),
    url('../fonts/boxicons.svg?#boxicons') format('svg');
}
.bx
{
    font-family: 'boxicons' !important;
    font-weight: normal;
    font-style: normal;
    font-variant: normal;
    line-height: 1;

    display: inline-block;

    text-transform: none;

    speak: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.bx-ul
{
    margin-left: 2em;
    padding-left: 0;

    list-style: none;
}
.bx-ul > li
{
    position: relative;
}
.bx-ul .bx
{
    font-size: inherit;
    line-height: inherit;

    position: absolute;
    left: -2em;

    width: 2em;

    text-align: center;
}
@-webkit-keyframes spin
{
    0%
    {
        -webkit-transform: rotate(0);
                transform: rotate(0);
    }
    100%
    {
        -webkit-transform: rotate(359deg);
                transform: rotate(359deg);
    }
}
@keyframes spin
{
    0%
    {
        -webkit-transform: rotate(0);
                transform: rotate(0);
    }
    100%
    {
        -webkit-transform: rotate(359deg);
                transform: rotate(359deg);
    }
}
@-webkit-keyframes burst
{
    0%
    {
        -webkit-transform: scale(1);
                transform: scale(1);

        opacity: 1;
    }
    90%
    {
        -webkit-transform: scale(1.5);
                transform: scale(1.5);

        opacity: 0;
    }
}
@keyframes burst
{
    0%
    {
        -webkit-transform: scale(1);
                transform: scale(1);

        opacity: 1;
    }
    90%
    {
        -webkit-transform: scale(1.5);
                transform: scale(1.5);

        opacity: 0;
    }
}
@-webkit-keyframes flashing
{
    0%
    {
        opacity: 1;
    }
    45%
    {
        opacity: 0;
    }
    90%
    {
        opacity: 1;
    }
}
@keyframes flashing
{
    0%
    {
        opacity: 1;
    }
    45%
    {
        opacity: 0;
    }
    90%
    {
        opacity: 1;
    }
}
@-webkit-keyframes fade-left
{
    0%
    {
        -webkit-transform: translateX(0);
                transform: translateX(0);

        opacity: 1;
    }
    75%
    {
        -webkit-transform: translateX(-20px);
                transform: translateX(-20px);

        opacity: 0;
    }
}
@keyframes fade-left
{
    0%
    {
        -webkit-transform: translateX(0);
                transform: translateX(0);

        opacity: 1;
    }
    75%
    {
        -webkit-transform: translateX(-20px);
                transform: translateX(-20px);

        opacity: 0;
    }
}
@-webkit-keyframes fade-right
{
    0%
    {
        -webkit-transform: translateX(0);
                transform: translateX(0);

        opacity: 1;
    }
    75%
    {
        -webkit-transform: translateX(20px);
                transform: translateX(20px);

        opacity: 0;
    }
}
@keyframes fade-right
{
    0%
    {
        -webkit-transform: translateX(0);
                transform: translateX(0);

        opacity: 1;
    }
    75%
    {
        -webkit-transform: translateX(20px);
                transform: translateX(20px);

        opacity: 0;
    }
}
@-webkit-keyframes fade-up
{
    0%
    {
        -webkit-transform: translateY(0);
                transform: translateY(0);

        opacity: 1;
    }
    75%
    {
        -webkit-transform: translateY(-20px);
                transform: translateY(-20px);

        opacity: 0;
    }
}
@keyframes fade-up
{
    0%
    {
        -webkit-transform: translateY(0);
                transform: translateY(0);

        opacity: 1;
    }
    75%
    {
        -webkit-transform: translateY(-20px);
                transform: translateY(-20px);

        opacity: 0;
    }
}
@-webkit-keyframes fade-down
{
    0%
    {
        -webkit-transform: translateY(0);
                transform: translateY(0);

        opacity: 1;
    }
    75%
    {
        -webkit-transform: translateY(20px);
                transform: translateY(20px);

        opacity: 0;
    }
}
@keyframes fade-down
{
    0%
    {
        -webkit-transform: translateY(0);
                transform: translateY(0);

        opacity: 1;
    }
    75%
    {
        -webkit-transform: translateY(20px);
                transform: translateY(20px);

        opacity: 0;
    }
}
@-webkit-keyframes tada
{
    from
    {
        -webkit-transform: scale3d(1, 1, 1);
                transform: scale3d(1, 1, 1);
    }

    10%,
    20%
    {
        -webkit-transform: scale3d(.95, .95, .95) rotate3d(0, 0, 1, -10deg);
                transform: scale3d(.95, .95, .95) rotate3d(0, 0, 1, -10deg);
    }

    30%,
    50%,
    70%,
    90%
    {
        -webkit-transform: scale3d(1, 1, 1) rotate3d(0, 0, 1, 10deg);
                transform: scale3d(1, 1, 1) rotate3d(0, 0, 1, 10deg);
    }

    40%,
    60%,
    80%
    {
        -webkit-transform: scale3d(1, 1, 1) rotate3d(0, 0, 1, -10deg);
                transform: scale3d(1, 1, 1) rotate3d(0, 0, 1, -10deg);
    }

    to
    {
        -webkit-transform: scale3d(1, 1, 1);
                transform: scale3d(1, 1, 1);
    }
}

@keyframes tada
{
    from
    {
        -webkit-transform: scale3d(1, 1, 1);
                transform: scale3d(1, 1, 1);
    }

    10%,
    20%
    {
        -webkit-transform: scale3d(.95, .95, .95) rotate3d(0, 0, 1, -10deg);
                transform: scale3d(.95, .95, .95) rotate3d(0, 0, 1, -10deg);
    }

    30%,
    50%,
    70%,
    90%
    {
        -webkit-transform: scale3d(1, 1, 1) rotate3d(0, 0, 1, 10deg);
                transform: scale3d(1, 1, 1) rotate3d(0, 0, 1, 10deg);
    }

    40%,
    60%,
    80%
    {
        -webkit-transform: rotate3d(0, 0, 1, -10deg);
                transform: rotate3d(0, 0, 1, -10deg);
    }

    to
    {
        -webkit-transform: scale3d(1, 1, 1);
                transform: scale3d(1, 1, 1);
    }
}
.bx-spin
{
    -webkit-animation: spin 2s linear infinite;
            animation: spin 2s linear infinite;
}
.bx-spin-hover:hover
{
    -webkit-animation: spin 2s linear infinite;
            animation: spin 2s linear infinite;
}

.bx-tada
{
    -webkit-animation: tada 1.5s ease infinite;
            animation: tada 1.5s ease infinite;
}
.bx-tada-hover:hover
{
    -webkit-animation: tada 1.5s ease infinite;
            animation: tada 1.5s ease infinite;
}

.bx-flashing
{
    -webkit-animation: flashing 1.5s infinite linear;
            animation: flashing 1.5s infinite linear;
}
.bx-flashing-hover:hover
{
    -webkit-animation: flashing 1.5s infinite linear;
            animation: flashing 1.5s infinite linear;
}

.bx-burst
{
    -webkit-animation: burst 1.5s infinite linear;
            animation: burst 1.5s infinite linear;
}
.bx-burst-hover:hover
{
    -webkit-animation: burst 1.5s infinite linear;
            animation: burst 1.5s infinite linear;
}
.bx-fade-up
{
    -webkit-animation: fade-up 1.5s infinite linear;
            animation: fade-up 1.5s infinite linear;
}
.bx-fade-up-hover:hover
{
    -webkit-animation: fade-up 1.5s infinite linear;
            animation: fade-up 1.5s infinite linear;
}
.bx-fade-down
{
    -webkit-animation: fade-down 1.5s infinite linear;
            animation: fade-down 1.5s infinite linear;
}
.bx-fade-down-hover:hover
{
    -webkit-animation: fade-down 1.5s infinite linear;
            animation: fade-down 1.5s infinite linear;
}
.bx-fade-left
{
    -webkit-animation: fade-left 1.5s infinite linear;
            animation: fade-left 1.5s infinite linear;
}
.bx-fade-left-hover:hover
{
    -webkit-animation: fade-left 1.5s infinite linear;
            animation: fade-left 1.5s infinite linear;
}
.bx-fade-right
{
    -webkit-animation: fade-right 1.5s infinite linear;
            animation: fade-right 1.5s infinite linear;
}
.bx-fade-right-hover:hover
{
    -webkit-animation: fade-right 1.5s infinite linear;
            animation: fade-right 1.5s infinite linear;
}
.bx-xs
{
    font-size: 1rem!important;
}
.bx-sm
{
    font-size: 1.55rem!important;
}
.bx-md
{
    font-size: 2.25rem!important;
}
.bx-fw
{
    font-size: 1.2857142857em;
    line-height: .8em;

    width: 1.2857142857em;
    height: .8em;
    margin-top: -.2em!important;

    vertical-align: middle;
}

.bx-lg
{
    font-size: 3.0rem!important;
}
.bx-pull-left
{
    float: left;

    margin-right: .3em!important;
}

.bx-pull-right
{
    float: right;

    margin-left: .3em!important;
}
.bx-rotate-90
{
    transform: rotate(90deg);

    -ms-filter: 'progid:DXImageTransform.Microsoft.BasicImage(rotation=1)';
}
.bx-rotate-180
{
    transform: rotate(180deg);

    -ms-filter: 'progid:DXImageTransform.Microsoft.BasicImage(rotation=2)';
}
.bx-rotate-270
{
    transform: rotate(270deg);

    -ms-filter: 'progid:DXImageTransform.Microsoft.BasicImage(rotation=3)';
}
.bx-flip-horizontal
{
    transform: scaleX(-1);

    -ms-filter: 'progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)';
}
.bx-flip-vertical
{
    transform: scaleY(-1);

    -ms-filter: 'progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)';
}
.bx-border
{
    padding: .25em;

    border: .07em solid rgba(0,0,0,.1);
    border-radius: .25em;
}
.bx-border-circle
{
    padding: .25em;

    border: .07em solid rgba(0,0,0,.1);
    border-radius: 50%;
}

.bxl-adobe:before {
  content: "\e900";
}
.bxl-algolia:before {
  content: "\e901";
}
.bxl-audible:before {
  content: "\e902";
}
.bxl-figma:before {
  content: "\e903";
}
.bxl-redbubble:before {
  content: "\e904";
}
.bxl-etsy:before {
  content: "\e905";
}
.bxl-gitlab:before {
  content: "\e906";
}
.bxl-patreon:before {
  content: "\e907";
}
.bxl-facebook-circle:before {
  content: "\e908";
}
.bxl-imdb:before {
  content: "\e909";
}
.bxl-jquery:before {
  content: "\e90a";
}
.bxl-pinterest-alt:before {
  content: "\e90b";
}
.bxl-500px:before {
  content: "\e90c";
}
.bxl-airbnb:before {
  content: "\e90d";
}
.bxl-amazon:before {
  content: "\e90e";
}
.bxl-android:before {
  content: "\e90f";
}
.bxl-angular:before {
  content: "\e910";
}
.bxl-apple:before {
  content: "\e911";
}
.bxl-baidu:before {
  content: "\e912";
}
.bxl-behance:before {
  content: "\e913";
}
.bxl-bing:before {
  content: "\e914";
}
.bxl-bitcoin:before {
  content: "\e915";
}
.bxl-blogger:before {
  content: "\e916";
}
.bxl-bootstrap:before {
  content: "\e917";
}
.bxl-chrome:before {
  content: "\e918";
}
.bxl-codepen:before {
  content: "\e919";
}
.bxl-creative-commons:before {
  content: "\e91a";
}
.bxl-css3:before {
  content: "\e91b";
}
.bxl-dailymotion:before {
  content: "\e91c";
}
.bxl-deviantart:before {
  content: "\e91d";
}
.bxl-digg:before {
  content: "\e91e";
}
.bxl-digitalocean:before {
  content: "\e91f";
}
.bxl-discord:before {
  content: "\e920";
}
.bxl-discourse:before {
  content: "\e921";
}
.bxl-dribbble:before {
  content: "\e922";
}
.bxl-dropbox:before {
  content: "\e923";
}
.bxl-drupal:before {
  content: "\e924";
}
.bxl-ebay:before {
  content: "\e925";
}
.bxl-edge:before {
  content: "\e926";
}
.bxl-facebook:before {
  content: "\e927";
}
.bxl-facebook-square:before {
  content: "\e928";
}
.bxl-firefox:before {
  content: "\e929";
}
.bxl-flickr:before {
  content: "\e92a";
}
.bxl-flickr-square:before {
  content: "\e92b";
}
.bxl-foursquare:before {
  content: "\e92c";
}
.bxl-git:before {
  content: "\e92d";
}
.bxl-github:before {
  content: "\e92e";
}
.bxl-google:before {
  content: "\e92f";
}
.bxl-google-plus:before {
  content: "\e930";
}
.bxl-google-plus-circle:before {
  content: "\e931";
}
.bxl-html5:before {
  content: "\e932";
}
.bxl-instagram:before {
  content: "\e933";
}
.bxl-instagram-alt:before {
  content: "\e934";
}
.bxl-internet-explorer:before {
  content: "\e935";
}
.bxl-invision:before {
  content: "\e936";
}
.bxl-javascript:before {
  content: "\e937";
}
.bxl-joomla:before {
  content: "\e938";
}
.bxl-jsfiddle:before {
  content: "\e939";
}
.bxl-kickstarter:before {
  content: "\e93a";
}
.bxl-less:before {
  content: "\e93b";
}
.bxl-linkedin:before {
  content: "\e93c";
}
.bxl-linkedin-square:before {
  content: "\e93d";
}
.bxl-magento:before {
  content: "\e93e";
}
.bxl-mailchimp:before {
  content: "\e93f";
}
.bxl-mastercard:before {
  content: "\e940";
}
.bxl-medium:before {
  content: "\e941";
}
.bxl-medium-old:before {
  content: "\e942";
}
.bxl-medium-square:before {
  content: "\e943";
}
.bxl-messenger:before {
  content: "\e944";
}
.bxl-microsoft:before {
  content: "\e945";
}
.bxl-nodejs:before {
  content: "\e946";
}
.bxl-opera:before {
  content: "\e947";
}
.bxl-paypal:before {
  content: "\e948";
}
.bxl-periscope:before {
  content: "\e949";
}
.bxl-pinterest:before {
  content: "\e94a";
}
.bxl-play-store:before {
  content: "\e94b";
}
.bxl-pocket:before {
  content: "\e94c";
}
.bxl-product-hunt:before {
  content: "\e94d";
}
.bxl-quora:before {
  content: "\e94e";
}
.bxl-react:before {
  content: "\e94f";
}
.bxl-reddit:before {
  content: "\e950";
}
.bxl-redux:before {
  content: "\e951";
}
.bxl-sass:before {
  content: "\e952";
}
.bxl-shopify:before {
  content: "\e953";
}
.bxl-skype:before {
  content: "\e954";
}
.bxl-slack:before {
  content: "\e955";
}
.bxl-slack-old:before {
  content: "\e956";
}
.bxl-snapchat:before {
  content: "\e957";
}
.bxl-soundcloud:before {
  content: "\e958";
}
.bxl-spotify:before {
  content: "\e959";
}
.bxl-squarespace:before {
  content: "\e95a";
}
.bxl-stack-overflow:before {
  content: "\e95b";
}
.bxl-stripe:before {
  content: "\e95c";
}
.bxl-telegram:before {
  content: "\e95d";
}
.bxl-trello:before {
  content: "\e95e";
}
.bxl-tumblr:before {
  content: "\e95f";
}
.bxl-twitch:before {
  content: "\e960";
}
.bxl-twitter:before {
  content: "\e961";
}
.bxl-unsplash:before {
  content: "\e962";
}
.bxl-vimeo:before {
  content: "\e963";
}
.bxl-visa:before {
  content: "\e964";
}
.bxl-vk:before {
  content: "\e965";
}
.bxl-vuejs:before {
  content: "\e966";
}
.bxl-whatsapp:before {
  content: "\e967";
}
.bxl-whatsapp-square:before {
  content: "\e968";
}
.bxl-wikipedia:before {
  content: "\e969";
}
.bxl-windows:before {
  content: "\e96a";
}
.bxl-wix:before {
  content: "\e96b";
}
.bxl-wordpress:before {
  content: "\e96c";
}
.bxl-yahoo:before {
  content: "\e96d";
}
.bxl-yelp:before {
  content: "\e96e";
}
.bxl-youtube:before {
  content: "\e96f";
}
.bx-accessibility:before {
  content: "\e970";
}
.bx-add-to-queue:before {
  content: "\e971";
}
.bx-adjust:before {
  content: "\e972";
}
.bx-alarm:before {
  content: "\e973";
}
.bx-alarm-add:before {
  content: "\e974";
}
.bx-alarm-off:before {
  content: "\e975";
}
.bx-album:before {
  content: "\e976";
}
.bx-align-justify:before {
  content: "\e977";
}
.bx-align-left:before {
  content: "\e978";
}
.bx-align-middle:before {
  content: "\e979";
}
.bx-align-right:before {
  content: "\e97a";
}
.bx-analyse:before {
  content: "\e97b";
}
.bx-anchor:before {
  content: "\e97c";
}
.bx-angry:before {
  content: "\e97d";
}
.bx-aperture:before {
  content: "\e97e";
}
.bx-archive:before {
  content: "\e97f";
}
.bx-archive-in:before {
  content: "\e980";
}
.bx-archive-out:before {
  content: "\e981";
}
.bx-area:before {
  content: "\e982";
}
.bx-arrow-back:before {
  content: "\e983";
}
.bx-at:before {
  content: "\e984";
}
.bx-award:before {
  content: "\e985";
}
.bx-badge:before {
  content: "\e986";
}
.bx-badge-check:before {
  content: "\e987";
}
.bx-ball:before {
  content: "\e988";
}
.bx-band-aid:before {
  content: "\e989";
}
.bx-bar-chart:before {
  content: "\e98a";
}
.bx-bar-chart-alt:before {
  content: "\e98b";
}
.bx-bar-chart-alt-2:before {
  content: "\e98c";
}
.bx-bar-chart-square:before {
  content: "\e98d";
}
.bx-barcode:before {
  content: "\e98e";
}
.bx-basket:before {
  content: "\e98f";
}
.bx-basketball:before {
  content: "\e990";
}
.bx-bath:before {
  content: "\e991";
}
.bx-battery:before {
  content: "\e992";
}
.bx-bed:before {
  content: "\e993";
}
.bx-bell:before {
  content: "\e994";
}
.bx-bell-minus:before {
  content: "\e995";
}
.bx-bell-off:before {
  content: "\e996";
}
.bx-bell-plus:before {
  content: "\e997";
}
.bx-bitcoin:before {
  content: "\e998";
}
.bx-block:before {
  content: "\e999";
}
.bx-bluetooth:before {
  content: "\e99a";
}
.bx-body:before {
  content: "\e99b";
}
.bx-bold:before {
  content: "\e99c";
}
.bx-bolt-circle:before {
  content: "\e99d";
}
.bx-book:before {
  content: "\e99e";
}
.bx-book-bookmark:before {
  content: "\e99f";
}
.bx-book-content:before {
  content: "\e9a0";
}
.bx-bookmark:before {
  content: "\e9a1";
}
.bx-bookmark-minus:before {
  content: "\e9a2";
}
.bx-bookmark-plus:before {
  content: "\e9a3";
}
.bx-bookmarks:before {
  content: "\e9a4";
}
.bx-book-open:before {
  content: "\e9a5";
}
.bx-border-all:before {
  content: "\e9a6";
}
.bx-border-bottom:before {
  content: "\e9a7";
}
.bx-border-left:before {
  content: "\e9a8";
}
.bx-border-radius:before {
  content: "\e9a9";
}
.bx-border-right:before {
  content: "\e9aa";
}
.bx-border-top:before {
  content: "\e9ab";
}
.bx-bot:before {
  content: "\e9ac";
}
.bx-bowling-ball:before {
  content: "\e9ad";
}
.bx-box:before {
  content: "\e9ae";
}
.bx-briefcase:before {
  content: "\e9af";
}
.bx-briefcase-alt:before {
  content: "\e9b0";
}
.bx-briefcase-alt-2:before {
  content: "\e9b1";
}
.bx-brightness:before {
  content: "\e9b2";
}
.bx-brightness-half:before {
  content: "\e9b3";
}
.bx-broadcast:before {
  content: "\e9b4";
}
.bx-brush:before {
  content: "\e9b5";
}
.bx-brush-alt:before {
  content: "\e9b6";
}
.bx-bug:before {
  content: "\e9b7";
}
.bx-bug-alt:before {
  content: "\e9b8";
}
.bx-building:before {
  content: "\e9b9";
}
.bx-building-house:before {
  content: "\e9ba";
}
.bx-buildings:before {
  content: "\e9bb";
}
.bx-bulb:before {
  content: "\e9bc";
}
.bx-bullseye:before {
  content: "\e9bd";
}
.bx-buoy:before {
  content: "\e9be";
}
.bx-bus:before {
  content: "\e9bf";
}
.bx-cake:before {
  content: "\e9c0";
}
.bx-calculator:before {
  content: "\e9c1";
}
.bx-calendar:before {
  content: "\e9c2";
}
.bx-calendar-alt:before {
  content: "\e9c3";
}
.bx-calendar-check:before {
  content: "\e9c4";
}
.bx-calendar-event:before {
  content: "\e9c5";
}
.bx-calendar-minus:before {
  content: "\e9c6";
}
.bx-calendar-plus:before {
  content: "\e9c7";
}
.bx-calendar-x:before {
  content: "\e9c8";
}
.bx-camera:before {
  content: "\e9c9";
}
.bx-camera-off:before {
  content: "\e9ca";
}
.bx-captions:before {
  content: "\e9cb";
}
.bx-car:before {
  content: "\e9cc";
}
.bx-card:before {
  content: "\e9cd";
}
.bx-caret-down:before {
  content: "\e9ce";
}
.bx-caret-left:before {
  content: "\e9cf";
}
.bx-caret-right:before {
  content: "\e9d0";
}
.bx-caret-up:before {
  content: "\e9d1";
}
.bx-carousel:before {
  content: "\e9d2";
}
.bx-cart:before {
  content: "\e9d3";
}
.bx-cart-alt:before {
  content: "\e9d4";
}
.bx-cast:before {
  content: "\e9d5";
}
.bx-certification:before {
  content: "\e9d6";
}
.bx-chalkboard:before {
  content: "\e9d7";
}
.bx-chart:before {
  content: "\e9d8";
}
.bx-chat:before {
  content: "\e9d9";
}
.bx-check:before {
  content: "\e9da";
}
.bx-checkbox:before {
  content: "\e9db";
}
.bx-checkbox-checked:before {
  content: "\e9dc";
}
.bx-checkbox-square:before {
  content: "\e9dd";
}
.bx-check-circle:before {
  content: "\e9de";
}
.bx-check-double:before {
  content: "\e9df";
}
.bx-check-shield:before {
  content: "\e9e0";
}
.bx-check-square:before {
  content: "\e9e1";
}
.bx-chevron-down:before {
  content: "\e9e2";
}
.bx-chevron-left:before {
  content: "\e9e3";
}
.bx-chevron-right:before {
  content: "\e9e4";
}
.bx-chevrons-down:before {
  content: "\e9e5";
}
.bx-chevrons-left:before {
  content: "\e9e6";
}
.bx-chevrons-right:before {
  content: "\e9e7";
}
.bx-chevrons-up:before {
  content: "\e9e8";
}
.bx-chevron-up:before {
  content: "\e9e9";
}
.bx-chip:before {
  content: "\e9ea";
}
.bx-circle:before {
  content: "\e9eb";
}
.bx-clinic:before {
  content: "\e9ec";
}
.bx-clipboard:before {
  content: "\e9ed";
}
.bx-closet:before {
  content: "\e9ee";
}
.bx-cloud:before {
  content: "\e9ef";
}
.bx-cloud-download:before {
  content: "\e9f0";
}
.bx-cloud-drizzle:before {
  content: "\e9f1";
}
.bx-cloud-lightning:before {
  content: "\e9f2";
}
.bx-cloud-light-rain:before {
  content: "\e9f3";
}
.bx-cloud-rain:before {
  content: "\e9f4";
}
.bx-cloud-snow:before {
  content: "\e9f5";
}
.bx-cloud-upload:before {
  content: "\e9f6";
}
.bx-code:before {
  content: "\e9f7";
}
.bx-code-alt:before {
  content: "\e9f8";
}
.bx-code-block:before {
  content: "\e9f9";
}
.bx-code-curly:before {
  content: "\e9fa";
}
.bx-coffee:before {
  content: "\e9fb";
}
.bx-cog:before {
  content: "\e9fc";
}
.bx-collapse:before {
  content: "\e9fd";
}
.bx-collection:before {
  content: "\e9fe";
}
.bx-columns:before {
  content: "\e9ff";
}
.bx-command:before {
  content: "\ea00";
}
.bx-comment:before {
  content: "\ea01";
}
.bx-comment-dots:before {
  content: "\ea02";
}
.bx-compass:before {
  content: "\ea03";
}
.bx-confused:before {
  content: "\ea04";
}
.bx-conversation:before {
  content: "\ea05";
}
.bx-cool:before {
  content: "\ea06";
}
.bx-copy:before {
  content: "\ea07";
}
.bx-copy-alt:before {
  content: "\ea08";
}
.bx-copyright:before {
  content: "\ea09";
}
.bx-credit-card:before {
  content: "\ea0a";
}
.bx-credit-card-alt:before {
  content: "\ea0b";
}
.bx-crop:before {
  content: "\ea0c";
}
.bx-crosshair:before {
  content: "\ea0d";
}
.bx-crown:before {
  content: "\ea0e";
}
.bx-cube:before {
  content: "\ea0f";
}
.bx-cube-alt:before {
  content: "\ea10";
}
.bx-cuboid:before {
  content: "\ea11";
}
.bx-customize:before {
  content: "\ea12";
}
.bx-cut:before {
  content: "\ea13";
}
.bx-cycling:before {
  content: "\ea14";
}
.bx-cylinder:before {
  content: "\ea15";
}
.bx-data:before {
  content: "\ea16";
}
.bx-desktop:before {
  content: "\ea17";
}
.bx-detail:before {
  content: "\ea18";
}
.bx-devices:before {
  content: "\ea19";
}
.bx-dialpad:before {
  content: "\ea1a";
}
.bx-dialpad-alt:before {
  content: "\ea1b";
}
.bx-diamond:before {
  content: "\ea1c";
}
.bx-directions:before {
  content: "\ea1d";
}
.bx-disc:before {
  content: "\ea1e";
}
.bx-dish:before {
  content: "\ea1f";
}
.bx-dislike:before {
  content: "\ea20";
}
.bx-dizzy:before {
  content: "\ea21";
}
.bx-dna:before {
  content: "\ea22";
}
.bx-dock-bottom:before {
  content: "\ea23";
}
.bx-dock-left:before {
  content: "\ea24";
}
.bx-dock-right:before {
  content: "\ea25";
}
.bx-dock-top:before {
  content: "\ea26";
}
.bx-dollar:before {
  content: "\ea27";
}
.bx-dollar-circle:before {
  content: "\ea28";
}
.bx-dots-horizontal:before {
  content: "\ea29";
}
.bx-dots-horizontal-rounded:before {
  content: "\ea2a";
}
.bx-dots-vertical:before {
  content: "\ea2b";
}
.bx-dots-vertical-rounded:before {
  content: "\ea2c";
}
.bx-doughnut-chart:before {
  content: "\ea2d";
}
.bx-down-arrow:before {
  content: "\ea2e";
}
.bx-down-arrow-alt:before {
  content: "\ea2f";
}
.bx-down-arrow-circle:before {
  content: "\ea30";
}
.bx-download:before {
  content: "\ea31";
}
.bx-downvote:before {
  content: "\ea32";
}
.bx-droplet:before {
  content: "\ea33";
}
.bx-dumbbell:before {
  content: "\ea34";
}
.bx-duplicate:before {
  content: "\ea35";
}
.bx-edit:before {
  content: "\ea36";
}
.bx-edit-alt:before {
  content: "\ea37";
}
.bx-envelope:before {
  content: "\ea38";
}
.bx-equalizer:before {
  content: "\ea39";
}
.bx-error:before {
  content: "\ea3a";
}
.bx-error-alt:before {
  content: "\ea3b";
}
.bx-error-circle:before {
  content: "\ea3c";
}
.bx-euro:before {
  content: "\ea3d";
}
.bx-exit:before {
  content: "\ea3e";
}
.bx-exit-fullscreen:before {
  content: "\ea3f";
}
.bx-expand:before {
  content: "\ea40";
}
.bx-export:before {
  content: "\ea41";
}
.bx-extension:before {
  content: "\ea42";
}
.bx-face:before {
  content: "\ea43";
}
.bx-fast-forward:before {
  content: "\ea44";
}
.bx-fast-forward-circle:before {
  content: "\ea45";
}
.bx-female:before {
  content: "\ea46";
}
.bx-female-sign:before {
  content: "\ea47";
}
.bx-file:before {
  content: "\ea48";
}
.bx-file-blank:before {
  content: "\ea49";
}
.bx-file-find:before {
  content: "\ea4a";
}
.bx-film:before {
  content: "\ea4b";
}
.bx-filter:before {
  content: "\ea4c";
}
.bx-filter-alt:before {
  content: "\ea4d";
}
.bx-fingerprint:before {
  content: "\ea4e";
}
.bx-first-aid:before {
  content: "\ea4f";
}
.bx-first-page:before {
  content: "\ea50";
}
.bx-flag:before {
  content: "\ea51";
}
.bx-folder:before {
  content: "\ea52";
}
.bx-folder-minus:before {
  content: "\ea53";
}
.bx-folder-open:before {
  content: "\ea54";
}
.bx-folder-plus:before {
  content: "\ea55";
}
.bx-font:before {
  content: "\ea56";
}
.bx-font-color:before {
  content: "\ea57";
}
.bx-font-family:before {
  content: "\ea58";
}
.bx-font-size:before {
  content: "\ea59";
}
.bx-food-menu:before {
  content: "\ea5a";
}
.bx-food-tag:before {
  content: "\ea5b";
}
.bx-football:before {
  content: "\ea5c";
}
.bx-fridge:before {
  content: "\ea5d";
}
.bx-fullscreen:before {
  content: "\ea5e";
}
.bx-gas-pump:before {
  content: "\ea5f";
}
.bx-ghost:before {
  content: "\ea60";
}
.bx-gift:before {
  content: "\ea61";
}
.bx-git-branch:before {
  content: "\ea62";
}
.bx-git-commit:before {
  content: "\ea63";
}
.bx-git-compare:before {
  content: "\ea64";
}
.bx-git-merge:before {
  content: "\ea65";
}
.bx-git-pull-request:before {
  content: "\ea66";
}
.bx-git-repo-forked:before {
  content: "\ea67";
}
.bx-globe:before {
  content: "\ea68";
}
.bx-globe-alt:before {
  content: "\ea69";
}
.bx-grid:before {
  content: "\ea6a";
}
.bx-grid-alt:before {
  content: "\ea6b";
}
.bx-grid-horizontal:before {
  content: "\ea6c";
}
.bx-grid-small:before {
  content: "\ea6d";
}
.bx-grid-vertical:before {
  content: "\ea6e";
}
.bx-group:before {
  content: "\ea6f";
}
.bx-handicap:before {
  content: "\ea70";
}
.bx-happy:before {
  content: "\ea71";
}
.bx-happy-alt:before {
  content: "\ea72";
}
.bx-happy-beaming:before {
  content: "\ea73";
}
.bx-happy-heart-eyes:before {
  content: "\ea74";
}
.bx-hash:before {
  content: "\ea75";
}
.bx-hdd:before {
  content: "\ea76";
}
.bx-heading:before {
  content: "\ea77";
}
.bx-headphone:before {
  content: "\ea78";
}
.bx-health:before {
  content: "\ea79";
}
.bx-heart:before {
  content: "\ea7a";
}
.bx-help-circle:before {
  content: "\ea7b";
}
.bx-hide:before {
  content: "\ea7c";
}
.bx-highlight:before {
  content: "\ea7d";
}
.bx-history:before {
  content: "\ea7e";
}
.bx-hive:before {
  content: "\ea7f";
}
.bx-home:before {
  content: "\ea80";
}
.bx-home-alt:before {
  content: "\ea81";
}
.bx-home-circle:before {
  content: "\ea82";
}
.bx-horizontal-center:before {
  content: "\ea83";
}
.bx-hotel:before {
  content: "\ea84";
}
.bx-hourglass:before {
  content: "\ea85";
}
.bx-id-card:before {
  content: "\ea86";
}
.bx-image:before {
  content: "\ea87";
}
.bx-image-add:before {
  content: "\ea88";
}
.bx-image-alt:before {
  content: "\ea89";
}
.bx-images:before {
  content: "\ea8a";
}
.bx-import:before {
  content: "\ea8b";
}
.bx-infinite:before {
  content: "\ea8c";
}
.bx-info-circle:before {
  content: "\ea8d";
}
.bx-italic:before {
  content: "\ea8e";
}
.bx-joystick:before {
  content: "\ea8f";
}
.bx-joystick-alt:before {
  content: "\ea90";
}
.bx-joystick-button:before {
  content: "\ea91";
}
.bx-key:before {
  content: "\ea92";
}
.bx-label:before {
  content: "\ea93";
}
.bx-landscape:before {
  content: "\ea94";
}
.bx-laptop:before {
  content: "\ea95";
}
.bx-last-page:before {
  content: "\ea96";
}
.bx-laugh:before {
  content: "\ea97";
}
.bx-layer:before {
  content: "\ea98";
}
.bx-layout:before {
  content: "\ea99";
}
.bx-left-arrow:before {
  content: "\ea9a";
}
.bx-left-arrow-alt:before {
  content: "\ea9b";
}
.bx-left-arrow-circle:before {
  content: "\ea9c";
}
.bx-left-down-arrow-circle:before {
  content: "\ea9d";
}
.bx-left-indent:before {
  content: "\ea9e";
}
.bx-left-top-arrow-circle:before {
  content: "\ea9f";
}
.bx-like:before {
  content: "\eaa0";
}
.bx-line-chart:before {
  content: "\eaa1";
}
.bx-link:before {
  content: "\eaa2";
}
.bx-link-alt:before {
  content: "\eaa3";
}
.bx-link-external:before {
  content: "\eaa4";
}
.bx-lira:before {
  content: "\eaa5";
}
.bx-list-check:before {
  content: "\eaa6";
}
.bx-list-minus:before {
  content: "\eaa7";
}
.bx-list-ol:before {
  content: "\eaa8";
}
.bx-list-plus:before {
  content: "\eaa9";
}
.bx-list-ul:before {
  content: "\eaaa";
}
.bx-loader:before {
  content: "\eaab";
}
.bx-loader-alt:before {
  content: "\eaac";
}
.bx-loader-circle:before {
  content: "\eaad";
}
.bx-lock:before {
  content: "\eaae";
}
.bx-lock-alt:before {
  content: "\eaaf";
}
.bx-lock-open:before {
  content: "\eab0";
}
.bx-lock-open-alt:before {
  content: "\eab1";
}
.bx-log-in:before {
  content: "\eab2";
}
.bx-log-in-circle:before {
  content: "\eab3";
}
.bx-log-out:before {
  content: "\eab4";
}
.bx-log-out-circle:before {
  content: "\eab5";
}
.bx-magnet:before {
  content: "\eab6";
}
.bx-mail-send:before {
  content: "\eab7";
}
.bx-male:before {
  content: "\eab8";
}
.bx-male-sign:before {
  content: "\eab9";
}
.bx-map:before {
  content: "\eaba";
}
.bx-map-alt:before {
  content: "\eabb";
}
.bx-map-pin:before {
  content: "\eabc";
}
.bx-meh:before {
  content: "\eabd";
}
.bx-meh-alt:before {
  content: "\eabe";
}
.bx-meh-blank:before {
  content: "\eabf";
}
.bx-memory-card:before {
  content: "\eac0";
}
.bx-menu:before {
  content: "\eac1";
}
.bx-menu-alt-left:before {
  content: "\eac2";
}
.bx-menu-alt-right:before {
  content: "\eac3";
}
.bx-message:before {
  content: "\eac4";
}
.bx-message-alt:before {
  content: "\eac5";
}
.bx-message-alt-dots:before {
  content: "\eac6";
}
.bx-message-dots:before {
  content: "\eac7";
}
.bx-message-rounded:before {
  content: "\eac8";
}
.bx-message-rounded-dots:before {
  content: "\eac9";
}
.bx-message-square:before {
  content: "\eaca";
}
.bx-message-square-dots:before {
  content: "\eacb";
}
.bx-microphone:before {
  content: "\eacc";
}
.bx-microphone-off:before {
  content: "\eacd";
}
.bx-minus:before {
  content: "\eace";
}
.bx-minus-circle:before {
  content: "\eacf";
}
.bx-mobile:before {
  content: "\ead0";
}
.bx-mobile-alt:before {
  content: "\ead1";
}
.bx-mobile-landscape:before {
  content: "\ead2";
}
.bx-mobile-vibration:before {
  content: "\ead3";
}
.bx-money:before {
  content: "\ead4";
}
.bx-moon:before {
  content: "\ead5";
}
.bx-mouse:before {
  content: "\ead6";
}
.bx-mouse-alt:before {
  content: "\ead7";
}
.bx-move:before {
  content: "\ead8";
}
.bx-move-horizontal:before {
  content: "\ead9";
}
.bx-move-vertical:before {
  content: "\eada";
}
.bx-movie:before {
  content: "\eadb";
}
.bx-music:before {
  content: "\eadc";
}
.bx-navigation:before {
  content: "\eadd";
}
.bx-news:before {
  content: "\eade";
}
.bx-no-entry:before {
  content: "\eadf";
}
.bx-note:before {
  content: "\eae0";
}
.bx-notepad:before {
  content: "\eae1";
}
.bx-notification:before {
  content: "\eae2";
}
.bx-notification-off:before {
  content: "\eae3";
}
.bx-package:before {
  content: "\eae4";
}
.bx-paint:before {
  content: "\eae5";
}
.bx-paint-roll:before {
  content: "\eae6";
}
.bx-palette:before {
  content: "\eae7";
}
.bx-paperclip:before {
  content: "\eae8";
}
.bx-paper-plane:before {
  content: "\eae9";
}
.bx-paragraph:before {
  content: "\eaea";
}
.bx-paste:before {
  content: "\eaeb";
}
.bx-pause:before {
  content: "\eaec";
}
.bx-pause-circle:before {
  content: "\eaed";
}
.bx-pen:before {
  content: "\eaee";
}
.bx-pencil:before {
  content: "\eaef";
}
.bx-phone:before {
  content: "\eaf0";
}
.bx-phone-call:before {
  content: "\eaf1";
}
.bx-phone-incoming:before {
  content: "\eaf2";
}
.bx-phone-outgoing:before {
  content: "\eaf3";
}
.bx-photo-album:before {
  content: "\eaf4";
}
.bx-pie-chart:before {
  content: "\eaf5";
}
.bx-pie-chart-alt:before {
  content: "\eaf6";
}
.bx-pie-chart-alt-2:before {
  content: "\eaf7";
}
.bx-pin:before {
  content: "\eaf8";
}
.bx-planet:before {
  content: "\eaf9";
}
.bx-play:before {
  content: "\eafa";
}
.bx-play-circle:before {
  content: "\eafb";
}
.bx-plug:before {
  content: "\eafc";
}
.bx-plus:before {
  content: "\eafd";
}
.bx-plus-circle:before {
  content: "\eafe";
}
.bx-plus-medical:before {
  content: "\eaff";
}
.bx-poll:before {
  content: "\eb00";
}
.bx-polygon:before {
  content: "\eb01";
}
.bx-pound:before {
  content: "\eb02";
}
.bx-power-off:before {
  content: "\eb03";
}
.bx-printer:before {
  content: "\eb04";
}
.bx-pulse:before {
  content: "\eb05";
}
.bx-purchase-tag:before {
  content: "\eb06";
}
.bx-purchase-tag-alt:before {
  content: "\eb07";
}
.bx-pyramid:before {
  content: "\eb08";
}
.bx-question-mark:before {
  content: "\eb09";
}
.bx-radar:before {
  content: "\eb0a";
}
.bx-radio:before {
  content: "\eb0b";
}
.bx-radio-circle:before {
  content: "\eb0c";
}
.bx-radio-circle-marked:before {
  content: "\eb0d";
}
.bx-receipt:before {
  content: "\eb0e";
}
.bx-rectangle:before {
  content: "\eb0f";
}
.bx-redo:before {
  content: "\eb10";
}
.bx-rename:before {
  content: "\eb11";
}
.bx-repeat:before {
  content: "\eb12";
}
.bx-reply:before {
  content: "\eb13";
}
.bx-reply-all:before {
  content: "\eb14";
}
.bx-repost:before {
  content: "\eb15";
}
.bx-reset:before {
  content: "\eb16";
}
.bx-restaurant:before {
  content: "\eb17";
}
.bx-revision:before {
  content: "\eb18";
}
.bx-rewind:before {
  content: "\eb19";
}
.bx-rewind-circle:before {
  content: "\eb1a";
}
.bx-right-arrow:before {
  content: "\eb1b";
}
.bx-right-arrow-alt:before {
  content: "\eb1c";
}
.bx-right-arrow-circle:before {
  content: "\eb1d";
}
.bx-right-down-arrow-circle:before {
  content: "\eb1e";
}
.bx-right-indent:before {
  content: "\eb1f";
}
.bx-right-top-arrow-circle:before {
  content: "\eb20";
}
.bx-rocket:before {
  content: "\eb21";
}
.bx-rotate-left:before {
  content: "\eb22";
}
.bx-rotate-right:before {
  content: "\eb23";
}
.bx-rss:before {
  content: "\eb24";
}
.bx-ruble:before {
  content: "\eb25";
}
.bx-ruler:before {
  content: "\eb26";
}
.bx-run:before {
  content: "\eb27";
}
.bx-rupee:before {
  content: "\eb28";
}
.bx-sad:before {
  content: "\eb29";
}
.bx-save:before {
  content: "\eb2a";
}
.bx-screenshot:before {
  content: "\eb2b";
}
.bx-search:before {
  content: "\eb2c";
}
.bx-search-alt:before {
  content: "\eb2d";
}
.bx-search-alt-2:before {
  content: "\eb2e";
}
.bx-selection:before {
  content: "\eb2f";
}
.bx-select-multiple:before {
  content: "\eb30";
}
.bx-send:before {
  content: "\eb31";
}
.bx-server:before {
  content: "\eb32";
}
.bx-shape-circle:before {
  content: "\eb33";
}
.bx-shape-square:before {
  content: "\eb34";
}
.bx-shape-triangle:before {
  content: "\eb35";
}
.bx-share:before {
  content: "\eb36";
}
.bx-share-alt:before {
  content: "\eb37";
}
.bx-shekel:before {
  content: "\eb38";
}
.bx-shield:before {
  content: "\eb39";
}
.bx-shield-alt:before {
  content: "\eb3a";
}
.bx-shield-alt-2:before {
  content: "\eb3b";
}
.bx-shield-quarter:before {
  content: "\eb3c";
}
.bx-shocked:before {
  content: "\eb3d";
}
.bx-shopping-bag:before {
  content: "\eb3e";
}
.bx-show:before {
  content: "\eb3f";
}
.bx-show-alt:before {
  content: "\eb40";
}
.bx-shuffle:before {
  content: "\eb41";
}
.bx-sidebar:before {
  content: "\eb42";
}
.bx-sitemap:before {
  content: "\eb43";
}
.bx-skip-next:before {
  content: "\eb44";
}
.bx-skip-next-circle:before {
  content: "\eb45";
}
.bx-skip-previous:before {
  content: "\eb46";
}
.bx-skip-previous-circle:before {
  content: "\eb47";
}
.bx-sleepy:before {
  content: "\eb48";
}
.bx-slider:before {
  content: "\eb49";
}
.bx-slider-alt:before {
  content: "\eb4a";
}
.bx-slideshow:before {
  content: "\eb4b";
}
.bx-smile:before {
  content: "\eb4c";
}
.bx-sort:before {
  content: "\eb4d";
}
.bx-sort-a-z:before {
  content: "\eb4e";
}
.bx-sort-down:before {
  content: "\eb4f";
}
.bx-sort-up:before {
  content: "\eb50";
}
.bx-sort-z-a:before {
  content: "\eb51";
}
.bx-spa:before {
  content: "\eb52";
}
.bx-space-bar:before {
  content: "\eb53";
}
.bx-spreadsheet:before {
  content: "\eb54";
}
.bx-square:before {
  content: "\eb55";
}
.bx-square-rounded:before {
  content: "\eb56";
}
.bx-star:before {
  content: "\eb57";
}
.bx-station:before {
  content: "\eb58";
}
.bx-stats:before {
  content: "\eb59";
}
.bx-sticker:before {
  content: "\eb5a";
}
.bx-stop:before {
  content: "\eb5b";
}
.bx-stop-circle:before {
  content: "\eb5c";
}
.bx-stopwatch:before {
  content: "\eb5d";
}
.bx-store:before {
  content: "\eb5e";
}
.bx-store-alt:before {
  content: "\eb5f";
}
.bx-street-view:before {
  content: "\eb60";
}
.bx-strikethrough:before {
  content: "\eb61";
}
.bx-subdirectory-left:before {
  content: "\eb62";
}
.bx-subdirectory-right:before {
  content: "\eb63";
}
.bx-sun:before {
  content: "\eb64";
}
.bx-support:before {
  content: "\eb65";
}
.bx-swim:before {
  content: "\eb66";
}
.bx-sync:before {
  content: "\eb67";
}
.bx-tab:before {
  content: "\eb68";
}
.bx-table:before {
  content: "\eb69";
}
.bx-tag:before {
  content: "\eb6a";
}
.bx-target-lock:before {
  content: "\eb6b";
}
.bx-task:before {
  content: "\eb6c";
}
.bx-taxi:before {
  content: "\eb6d";
}
.bx-tennis-ball:before {
  content: "\eb6e";
}
.bx-terminal:before {
  content: "\eb6f";
}
.bx-test-tube:before {
  content: "\eb70";
}
.bx-text:before {
  content: "\eb71";
}
.bx-time:before {
  content: "\eb72";
}
.bx-time-five:before {
  content: "\eb73";
}
.bx-timer:before {
  content: "\eb74";
}
.bx-tired:before {
  content: "\eb75";
}
.bx-toggle-left:before {
  content: "\eb76";
}
.bx-toggle-right:before {
  content: "\eb77";
}
.bx-tone:before {
  content: "\eb78";
}
.bx-train:before {
  content: "\eb79";
}
.bx-transfer:before {
  content: "\eb7a";
}
.bx-transfer-alt:before {
  content: "\eb7b";
}
.bx-trash:before {
  content: "\eb7c";
}
.bx-trash-alt:before {
  content: "\eb7d";
}
.bx-trending-down:before {
  content: "\eb7e";
}
.bx-trending-up:before {
  content: "\eb7f";
}
.bx-trophy:before {
  content: "\eb80";
}
.bx-tv:before {
  content: "\eb81";
}
.bx-underline:before {
  content: "\eb82";
}
.bx-undo:before {
  content: "\eb83";
}
.bx-unlink:before {
  content: "\eb84";
}
.bx-up-arrow:before {
  content: "\eb85";
}
.bx-up-arrow-alt:before {
  content: "\eb86";
}
.bx-up-arrow-circle:before {
  content: "\eb87";
}
.bx-upload:before {
  content: "\eb88";
}
.bx-upside-down:before {
  content: "\eb89";
}
.bx-upvote:before {
  content: "\eb8a";
}
.bx-usb:before {
  content: "\eb8b";
}
.bx-user:before {
  content: "\eb8c";
}
.bx-user-check:before {
  content: "\eb8d";
}
.bx-user-circle:before {
  content: "\eb8e";
}
.bx-user-minus:before {
  content: "\eb8f";
}
.bx-user-pin:before {
  content: "\eb90";
}
.bx-user-plus:before {
  content: "\eb91";
}
.bx-user-voice:before {
  content: "\eb92";
}
.bx-user-x:before {
  content: "\eb93";
}
.bx-vertical-center:before {
  content: "\eb94";
}
.bx-video:before {
  content: "\eb95";
}
.bx-video-off:before {
  content: "\eb96";
}
.bx-video-plus:before {
  content: "\eb97";
}
.bx-video-recording:before {
  content: "\eb98";
}
.bx-voicemail:before {
  content: "\eb99";
}
.bx-volume:before {
  content: "\eb9a";
}
.bx-volume-full:before {
  content: "\eb9b";
}
.bx-volume-low:before {
  content: "\eb9c";
}
.bx-volume-mute:before {
  content: "\eb9d";
}
.bx-walk:before {
  content: "\eb9e";
}
.bx-wallet:before {
  content: "\eb9f";
}
.bx-wallet-alt:before {
  content: "\eba0";
}
.bx-water:before {
  content: "\eba1";
}
.bx-wifi:before {
  content: "\eba2";
}
.bx-wifi-off:before {
  content: "\eba3";
}
.bx-wind:before {
  content: "\eba4";
}
.bx-window:before {
  content: "\eba5";
}
.bx-window-close:before {
  content: "\eba6";
}
.bx-window-open:before {
  content: "\eba7";
}
.bx-windows:before {
  content: "\eba8";
}
.bx-wink-smile:before {
  content: "\eba9";
}
.bx-wink-tongue:before {
  content: "\ebaa";
}
.bx-won:before {
  content: "\ebab";
}
.bx-world:before {
  content: "\ebac";
}
.bx-wrench:before {
  content: "\ebad";
}
.bx-x:before {
  content: "\ebae";
}
.bx-x-circle:before {
  content: "\ebaf";
}
.bx-yen:before {
  content: "\ebb0";
}
.bx-zoom-in:before {
  content: "\ebb1";
}
.bx-zoom-out:before {
  content: "\ebb2";
}
.bxs-add-to-queue:before {
  content: "\ebb3";
}
.bxs-adjust:before {
  content: "\ebb4";
}
.bxs-adjust-alt:before {
  content: "\ebb5";
}
.bxs-alarm:before {
  content: "\ebb6";
}
.bxs-alarm-add:before {
  content: "\ebb7";
}
.bxs-alarm-off:before {
  content: "\ebb8";
}
.bxs-album:before {
  content: "\ebb9";
}
.bxs-ambulance:before {
  content: "\ebba";
}
.bxs-analyse:before {
  content: "\ebbb";
}
.bxs-angry:before {
  content: "\ebbc";
}
.bxs-archive:before {
  content: "\ebbd";
}
.bxs-archive-in:before {
  content: "\ebbe";
}
.bxs-archive-out:before {
  content: "\ebbf";
}
.bxs-area:before {
  content: "\ebc0";
}
.bxs-award:before {
  content: "\ebc1";
}
.bxs-baby-carriage:before {
  content: "\ebc2";
}
.bxs-badge:before {
  content: "\ebc3";
}
.bxs-badge-check:before {
  content: "\ebc4";
}
.bxs-ball:before {
  content: "\ebc5";
}
.bxs-band-aid:before {
  content: "\ebc6";
}
.bxs-bank:before {
  content: "\ebc7";
}
.bxs-bar-chart-alt-2:before {
  content: "\ebc8";
}
.bxs-bar-chart-square:before {
  content: "\ebc9";
}
.bxs-barcode:before {
  content: "\ebca";
}
.bxs-basket:before {
  content: "\ebcb";
}
.bxs-bath:before {
  content: "\ebcc";
}
.bxs-battery:before {
  content: "\ebcd";
}
.bxs-battery-charging:before {
  content: "\ebce";
}
.bxs-battery-full:before {
  content: "\ebcf";
}
.bxs-battery-low:before {
  content: "\ebd0";
}
.bxs-bed:before {
  content: "\ebd1";
}
.bxs-bell:before {
  content: "\ebd2";
}
.bxs-bell-minus:before {
  content: "\ebd3";
}
.bxs-bell-off:before {
  content: "\ebd4";
}
.bxs-bell-plus:before {
  content: "\ebd5";
}
.bxs-bell-ring:before {
  content: "\ebd6";
}
.bxs-bolt:before {
  content: "\ebd7";
}
.bxs-bolt-circle:before {
  content: "\ebd8";
}
.bxs-book:before {
  content: "\ebd9";
}
.bxs-book-bookmark:before {
  content: "\ebda";
}
.bxs-book-content:before {
  content: "\ebdb";
}
.bxs-bookmark:before {
  content: "\ebdc";
}
.bxs-bookmark-minus:before {
  content: "\ebdd";
}
.bxs-bookmark-plus:before {
  content: "\ebde";
}
.bxs-bookmarks:before {
  content: "\ebdf";
}
.bxs-bookmark-star:before {
  content: "\ebe0";
}
.bxs-book-open:before {
  content: "\ebe1";
}
.bxs-bot:before {
  content: "\ebe2";
}
.bxs-bowling-ball:before {
  content: "\ebe3";
}
.bxs-box:before {
  content: "\ebe4";
}
.bxs-briefcase:before {
  content: "\ebe5";
}
.bxs-briefcase-alt:before {
  content: "\ebe6";
}
.bxs-briefcase-alt-2:before {
  content: "\ebe7";
}
.bxs-brightness:before {
  content: "\ebe8";
}
.bxs-brightness-half:before {
  content: "\ebe9";
}
.bxs-brush:before {
  content: "\ebea";
}
.bxs-brush-alt:before {
  content: "\ebeb";
}
.bxs-bug:before {
  content: "\ebec";
}
.bxs-bug-alt:before {
  content: "\ebed";
}
.bxs-building:before {
  content: "\ebee";
}
.bxs-building-house:before {
  content: "\ebef";
}
.bxs-buildings:before {
  content: "\ebf0";
}
.bxs-bulb:before {
  content: "\ebf1";
}
.bxs-buoy:before {
  content: "\ebf2";
}
.bxs-bus:before {
  content: "\ebf3";
}
.bxs-business:before {
  content: "\ebf4";
}
.bxs-cake:before {
  content: "\ebf5";
}
.bxs-calculator:before {
  content: "\ebf6";
}
.bxs-calendar:before {
  content: "\ebf7";
}
.bxs-calendar-alt:before {
  content: "\ebf8";
}
.bxs-calendar-check:before {
  content: "\ebf9";
}
.bxs-calendar-event:before {
  content: "\ebfa";
}
.bxs-calendar-minus:before {
  content: "\ebfb";
}
.bxs-calendar-plus:before {
  content: "\ebfc";
}
.bxs-calendar-x:before {
  content: "\ebfd";
}
.bxs-camera:before {
  content: "\ebfe";
}
.bxs-camera-off:before {
  content: "\ebff";
}
.bxs-camera-plus:before {
  content: "\ec00";
}
.bxs-capsule:before {
  content: "\ec01";
}
.bxs-captions:before {
  content: "\ec02";
}
.bxs-car:before {
  content: "\ec03";
}
.bxs-card:before {
  content: "\ec04";
}
.bxs-caret-down-circle:before {
  content: "\ec05";
}
.bxs-caret-left-circle:before {
  content: "\ec06";
}
.bxs-caret-right-circle:before {
  content: "\ec07";
}
.bxs-caret-up-circle:before {
  content: "\ec08";
}
.bxs-carousel:before {
  content: "\ec09";
}
.bxs-cart:before {
  content: "\ec0a";
}
.bxs-cart-alt:before {
  content: "\ec0b";
}
.bxs-certification:before {
  content: "\ec0c";
}
.bxs-chalkboard:before {
  content: "\ec0d";
}
.bxs-chart:before {
  content: "\ec0e";
}
.bxs-chat:before {
  content: "\ec0f";
}
.bxs-checkbox:before {
  content: "\ec10";
}
.bxs-checkbox-checked:before {
  content: "\ec11";
}
.bxs-check-circle:before {
  content: "\ec12";
}
.bxs-check-shield:before {
  content: "\ec13";
}
.bxs-check-square:before {
  content: "\ec14";
}
.bxs-chip:before {
  content: "\ec15";
}
.bxs-circle:before {
  content: "\ec16";
}
.bxs-city:before {
  content: "\ec17";
}
.bxs-clinic:before {
  content: "\ec18";
}
.bxs-cloud:before {
  content: "\ec19";
}
.bxs-cloud-download:before {
  content: "\ec1a";
}
.bxs-cloud-lightning:before {
  content: "\ec1b";
}
.bxs-cloud-rain:before {
  content: "\ec1c";
}
.bxs-cloud-upload:before {
  content: "\ec1d";
}
.bxs-coffee:before {
  content: "\ec1e";
}
.bxs-coffee-alt:before {
  content: "\ec1f";
}
.bxs-cog:before {
  content: "\ec20";
}
.bxs-collection:before {
  content: "\ec21";
}
.bxs-color-fill:before {
  content: "\ec22";
}
.bxs-comment:before {
  content: "\ec23";
}
.bxs-comment-add:before {
  content: "\ec24";
}
.bxs-comment-detail:before {
  content: "\ec25";
}
.bxs-comment-dots:before {
  content: "\ec26";
}
.bxs-comment-error:before {
  content: "\ec27";
}
.bxs-compass:before {
  content: "\ec28";
}
.bxs-component:before {
  content: "\ec29";
}
.bxs-confused:before {
  content: "\ec2a";
}
.bxs-contact:before {
  content: "\ec2b";
}
.bxs-conversation:before {
  content: "\ec2c";
}
.bxs-cool:before {
  content: "\ec2d";
}
.bxs-copy:before {
  content: "\ec2e";
}
.bxs-copy-alt:before {
  content: "\ec2f";
}
.bxs-coupon:before {
  content: "\ec30";
}
.bxs-credit-card:before {
  content: "\ec31";
}
.bxs-credit-card-alt:before {
  content: "\ec32";
}
.bxs-crown:before {
  content: "\ec33";
}
.bxs-cube:before {
  content: "\ec34";
}
.bxs-cube-alt:before {
  content: "\ec35";
}
.bxs-cuboid:before {
  content: "\ec36";
}
.bxs-customize:before {
  content: "\ec37";
}
.bxs-cylinder:before {
  content: "\ec38";
}
.bxs-dashboard:before {
  content: "\ec39";
}
.bxs-data:before {
  content: "\ec3a";
}
.bxs-detail:before {
  content: "\ec3b";
}
.bxs-devices:before {
  content: "\ec3c";
}
.bxs-direction-left:before {
  content: "\ec3d";
}
.bxs-direction-right:before {
  content: "\ec3e";
}
.bxs-directions:before {
  content: "\ec3f";
}
.bxs-disc:before {
  content: "\ec40";
}
.bxs-discount:before {
  content: "\ec41";
}
.bxs-dish:before {
  content: "\ec42";
}
.bxs-dislike:before {
  content: "\ec43";
}
.bxs-dizzy:before {
  content: "\ec44";
}
.bxs-dock-bottom:before {
  content: "\ec45";
}
.bxs-dock-left:before {
  content: "\ec46";
}
.bxs-dock-right:before {
  content: "\ec47";
}
.bxs-dock-top:before {
  content: "\ec48";
}
.bxs-dollar-circle:before {
  content: "\ec49";
}
.bxs-doughnut-chart:before {
  content: "\ec4a";
}
.bxs-down-arrow:before {
  content: "\ec4b";
}
.bxs-down-arrow-circle:before {
  content: "\ec4c";
}
.bxs-down-arrow-square:before {
  content: "\ec4d";
}
.bxs-download:before {
  content: "\ec4e";
}
.bxs-downvote:before {
  content: "\ec4f";
}
.bxs-drink:before {
  content: "\ec50";
}
.bxs-droplet:before {
  content: "\ec51";
}
.bxs-droplet-half:before {
  content: "\ec52";
}
.bxs-duplicate:before {
  content: "\ec53";
}
.bxs-edit:before {
  content: "\ec54";
}
.bxs-edit-alt:before {
  content: "\ec55";
}
.bxs-eject:before {
  content: "\ec56";
}
.bxs-envelope:before {
  content: "\ec57";
}
.bxs-eraser:before {
  content: "\ec58";
}
.bxs-error:before {
  content: "\ec59";
}
.bxs-error-alt:before {
  content: "\ec5a";
}
.bxs-error-circle:before {
  content: "\ec5b";
}
.bxs-exit:before {
  content: "\ec5c";
}
.bxs-extension:before {
  content: "\ec5d";
}
.bxs-eyedropper:before {
  content: "\ec5e";
}
.bxs-face:before {
  content: "\ec5f";
}
.bxs-factory:before {
  content: "\ec60";
}
.bxs-fast-forward-circle:before {
  content: "\ec61";
}
.bxs-file:before {
  content: "\ec62";
}
.bxs-file-blank:before {
  content: "\ec63";
}
.bxs-file-css:before {
  content: "\ec64";
}
.bxs-file-doc:before {
  content: "\ec65";
}
.bxs-file-find:before {
  content: "\ec66";
}
.bxs-file-gif:before {
  content: "\ec67";
}
.bxs-file-html:before {
  content: "\ec68";
}
.bxs-file-image:before {
  content: "\ec69";
}
.bxs-file-jpg:before {
  content: "\ec6a";
}
.bxs-file-js:before {
  content: "\ec6b";
}
.bxs-file-json:before {
  content: "\ec6c";
}
.bxs-file-md:before {
  content: "\ec6d";
}
.bxs-file-pdf:before {
  content: "\ec6e";
}
.bxs-file-plus:before {
  content: "\ec6f";
}
.bxs-file-png:before {
  content: "\ec70";
}
.bxs-file-txt:before {
  content: "\ec71";
}
.bxs-film:before {
  content: "\ec72";
}
.bxs-filter-alt:before {
  content: "\ec73";
}
.bxs-first-aid:before {
  content: "\ec74";
}
.bxs-flag:before {
  content: "\ec75";
}
.bxs-flag-alt:before {
  content: "\ec76";
}
.bxs-flame:before {
  content: "\ec77";
}
.bxs-flask:before {
  content: "\ec78";
}
.bxs-folder:before {
  content: "\ec79";
}
.bxs-folder-minus:before {
  content: "\ec7a";
}
.bxs-folder-open:before {
  content: "\ec7b";
}
.bxs-folder-plus:before {
  content: "\ec7c";
}
.bxs-food-menu:before {
  content: "\ec7d";
}
.bxs-fridge:before {
  content: "\ec7e";
}
.bxs-gas-pump:before {
  content: "\ec7f";
}
.bxs-ghost:before {
  content: "\ec80";
}
.bxs-gift:before {
  content: "\ec81";
}
.bxs-graduation:before {
  content: "\ec82";
}
.bxs-grid:before {
  content: "\ec83";
}
.bxs-grid-alt:before {
  content: "\ec84";
}
.bxs-group:before {
  content: "\ec85";
}
.bxs-hand-down:before {
  content: "\ec86";
}
.bxs-hand-left:before {
  content: "\ec87";
}
.bxs-hand-right:before {
  content: "\ec88";
}
.bxs-hand-up:before {
  content: "\ec89";
}
.bxs-happy:before {
  content: "\ec8a";
}
.bxs-happy-alt:before {
  content: "\ec8b";
}
.bxs-happy-beaming:before {
  content: "\ec8c";
}
.bxs-happy-heart-eyes:before {
  content: "\ec8d";
}
.bxs-hdd:before {
  content: "\ec8e";
}
.bxs-heart:before {
  content: "\ec8f";
}
.bxs-help-circle:before {
  content: "\ec90";
}
.bxs-hide:before {
  content: "\ec91";
}
.bxs-home:before {
  content: "\ec92";
}
.bxs-home-circle:before {
  content: "\ec93";
}
.bxs-hot:before {
  content: "\ec94";
}
.bxs-hotel:before {
  content: "\ec95";
}
.bxs-hourglass:before {
  content: "\ec96";
}
.bxs-hourglass-bottom:before {
  content: "\ec97";
}
.bxs-hourglass-top:before {
  content: "\ec98";
}
.bxs-id-card:before {
  content: "\ec99";
}
.bxs-image:before {
  content: "\ec9a";
}
.bxs-image-add:before {
  content: "\ec9b";
}
.bxs-image-alt:before {
  content: "\ec9c";
}
.bxs-inbox:before {
  content: "\ec9d";
}
.bxs-info-circle:before {
  content: "\ec9e";
}
.bxs-institution:before {
  content: "\ec9f";
}
.bxs-joystick:before {
  content: "\eca0";
}
.bxs-joystick-alt:before {
  content: "\eca1";
}
.bxs-joystick-button:before {
  content: "\eca2";
}
.bxs-key:before {
  content: "\eca3";
}
.bxs-keyboard:before {
  content: "\eca4";
}
.bxs-label:before {
  content: "\eca5";
}
.bxs-landmark:before {
  content: "\eca6";
}
.bxs-landscape:before {
  content: "\eca7";
}
.bxs-laugh:before {
  content: "\eca8";
}
.bxs-layer:before {
  content: "\eca9";
}
.bxs-layout:before {
  content: "\ecaa";
}
.bxs-left-arrow:before {
  content: "\ecab";
}
.bxs-left-arrow-circle:before {
  content: "\ecac";
}
.bxs-left-arrow-square:before {
  content: "\ecad";
}
.bxs-left-down-arrow-circle:before {
  content: "\ecae";
}
.bxs-left-top-arrow-circle:before {
  content: "\ecaf";
}
.bxs-like:before {
  content: "\ecb0";
}
.bxs-lock:before {
  content: "\ecb1";
}
.bxs-lock-alt:before {
  content: "\ecb2";
}
.bxs-lock-open:before {
  content: "\ecb3";
}
.bxs-lock-open-alt:before {
  content: "\ecb4";
}
.bxs-log-in:before {
  content: "\ecb5";
}
.bxs-log-in-circle:before {
  content: "\ecb6";
}
.bxs-log-out:before {
  content: "\ecb7";
}
.bxs-log-out-circle:before {
  content: "\ecb8";
}
.bxs-magic-wand:before {
  content: "\ecb9";
}
.bxs-magnet:before {
  content: "\ecba";
}
.bxs-map:before {
  content: "\ecbb";
}
.bxs-map-alt:before {
  content: "\ecbc";
}
.bxs-map-pin:before {
  content: "\ecbd";
}
.bxs-megaphone:before {
  content: "\ecbe";
}
.bxs-meh:before {
  content: "\ecbf";
}
.bxs-meh-alt:before {
  content: "\ecc0";
}
.bxs-meh-blank:before {
  content: "\ecc1";
}
.bxs-memory-card:before {
  content: "\ecc2";
}
.bxs-message:before {
  content: "\ecc3";
}
.bxs-message-alt:before {
  content: "\ecc4";
}
.bxs-message-alt-dots:before {
  content: "\ecc5";
}
.bxs-message-dots:before {
  content: "\ecc6";
}
.bxs-message-rounded:before {
  content: "\ecc7";
}
.bxs-message-rounded-dots:before {
  content: "\ecc8";
}
.bxs-message-square:before {
  content: "\ecc9";
}
.bxs-message-square-dots:before {
  content: "\ecca";
}
.bxs-microphone:before {
  content: "\eccb";
}
.bxs-microphone-alt:before {
  content: "\eccc";
}
.bxs-microphone-off:before {
  content: "\eccd";
}
.bxs-minus-circle:before {
  content: "\ecce";
}
.bxs-minus-square:before {
  content: "\eccf";
}
.bxs-mobile:before {
  content: "\ecd0";
}
.bxs-mobile-vibration:before {
  content: "\ecd1";
}
.bxs-moon:before {
  content: "\ecd2";
}
.bxs-mouse:before {
  content: "\ecd3";
}
.bxs-mouse-alt:before {
  content: "\ecd4";
}
.bxs-movie:before {
  content: "\ecd5";
}
.bxs-music:before {
  content: "\ecd6";
}
.bxs-navigation:before {
  content: "\ecd7";
}
.bxs-news:before {
  content: "\ecd8";
}
.bxs-no-entry:before {
  content: "\ecd9";
}
.bxs-note:before {
  content: "\ecda";
}
.bxs-notepad:before {
  content: "\ecdb";
}
.bxs-notification:before {
  content: "\ecdc";
}
.bxs-notification-off:before {
  content: "\ecdd";
}
.bxs-offer:before {
  content: "\ecde";
}
.bxs-package:before {
  content: "\ecdf";
}
.bxs-paint:before {
  content: "\ece0";
}
.bxs-paint-roll:before {
  content: "\ece1";
}
.bxs-palette:before {
  content: "\ece2";
}
.bxs-paper-plane:before {
  content: "\ece3";
}
.bxs-parking:before {
  content: "\ece4";
}
.bxs-paste:before {
  content: "\ece5";
}
.bxs-pen:before {
  content: "\ece6";
}
.bxs-pencil:before {
  content: "\ece7";
}
.bxs-phone:before {
  content: "\ece8";
}
.bxs-phone-call:before {
  content: "\ece9";
}
.bxs-phone-incoming:before {
  content: "\ecea";
}
.bxs-phone-outgoing:before {
  content: "\eceb";
}
.bxs-photo-album:before {
  content: "\ecec";
}
.bxs-pie-chart:before {
  content: "\eced";
}
.bxs-pie-chart-alt:before {
  content: "\ecee";
}
.bxs-pie-chart-alt-2:before {
  content: "\ecef";
}
.bxs-pin:before {
  content: "\ecf0";
}
.bxs-plane:before {
  content: "\ecf1";
}
.bxs-plane-alt:before {
  content: "\ecf2";
}
.bxs-plane-land:before {
  content: "\ecf3";
}
.bxs-planet:before {
  content: "\ecf4";
}
.bxs-plane-take-off:before {
  content: "\ecf5";
}
.bxs-playlist:before {
  content: "\ecf6";
}
.bxs-plug:before {
  content: "\ecf7";
}
.bxs-plus-circle:before {
  content: "\ecf8";
}
.bxs-plus-square:before {
  content: "\ecf9";
}
.bxs-polygon:before {
  content: "\ecfa";
}
.bxs-printer:before {
  content: "\ecfb";
}
.bxs-purchase-tag:before {
  content: "\ecfc";
}
.bxs-purchase-tag-alt:before {
  content: "\ecfd";
}
.bxs-pyramid:before {
  content: "\ecfe";
}
.bxs-quote-alt-left:before {
  content: "\ecff";
}
.bxs-quote-alt-right:before {
  content: "\ed00";
}
.bxs-quote-left:before {
  content: "\ed01";
}
.bxs-quote-right:before {
  content: "\ed02";
}
.bxs-quote-single-left:before {
  content: "\ed03";
}
.bxs-quote-single-right:before {
  content: "\ed04";
}
.bxs-radio:before {
  content: "\ed05";
}
.bxs-receipt:before {
  content: "\ed06";
}
.bxs-rectangle:before {
  content: "\ed07";
}
.bxs-rename:before {
  content: "\ed08";
}
.bxs-report:before {
  content: "\ed09";
}
.bxs-rewind-circle:before {
  content: "\ed0a";
}
.bxs-right-arrow:before {
  content: "\ed0b";
}
.bxs-right-arrow-circle:before {
  content: "\ed0c";
}
.bxs-right-arrow-square:before {
  content: "\ed0d";
}
.bxs-right-down-arrow-circle:before {
  content: "\ed0e";
}
.bxs-right-top-arrow-circle:before {
  content: "\ed0f";
}
.bxs-rocket:before {
  content: "\ed10";
}
.bxs-ruler:before {
  content: "\ed11";
}
.bxs-sad:before {
  content: "\ed12";
}
.bxs-save:before {
  content: "\ed13";
}
.bxs-school:before {
  content: "\ed14";
}
.bxs-search:before {
  content: "\ed15";
}
.bxs-search-alt-2:before {
  content: "\ed16";
}
.bxs-select-multiple:before {
  content: "\ed17";
}
.bxs-send:before {
  content: "\ed18";
}
.bxs-server:before {
  content: "\ed19";
}
.bxs-share:before {
  content: "\ed1a";
}
.bxs-share-alt:before {
  content: "\ed1b";
}
.bxs-shield:before {
  content: "\ed1c";
}
.bxs-shield-alt-2:before {
  content: "\ed1d";
}
.bxs-ship:before {
  content: "\ed1e";
}
.bxs-shocked:before {
  content: "\ed1f";
}
.bxs-shopping-bag:before {
  content: "\ed20";
}
.bxs-shopping-bag-alt:before {
  content: "\ed21";
}
.bxs-show:before {
  content: "\ed22";
}
.bxs-skip-next-circle:before {
  content: "\ed23";
}
.bxs-skip-previous-circle:before {
  content: "\ed24";
}
.bxs-skull:before {
  content: "\ed25";
}
.bxs-sleepy:before {
  content: "\ed26";
}
.bxs-slideshow:before {
  content: "\ed27";
}
.bxs-smile:before {
  content: "\ed28";
}
.bxs-sort-alt:before {
  content: "\ed29";
}
.bxs-spa:before {
  content: "\ed2a";
}
.bxs-spreadsheet:before {
  content: "\ed2b";
}
.bxs-square:before {
  content: "\ed2c";
}
.bxs-square-rounded:before {
  content: "\ed2d";
}
.bxs-star:before {
  content: "\ed2e";
}
.bxs-star-half:before {
  content: "\ed2f";
}
.bxs-stopwatch:before {
  content: "\ed30";
}
.bxs-store:before {
  content: "\ed31";
}
.bxs-store-alt:before {
  content: "\ed32";
}
.bxs-sun:before {
  content: "\ed33";
}
.bxs-tag:before {
  content: "\ed34";
}
.bxs-tag-x:before {
  content: "\ed35";
}
.bxs-taxi:before {
  content: "\ed36";
}
.bxs-tennis-ball:before {
  content: "\ed37";
}
.bxs-terminal:before {
  content: "\ed38";
}
.bxs-time:before {
  content: "\ed39";
}
.bxs-time-five:before {
  content: "\ed3a";
}
.bxs-timer:before {
  content: "\ed3b";
}
.bxs-tired:before {
  content: "\ed3c";
}
.bxs-toggle-left:before {
  content: "\ed3d";
}
.bxs-toggle-right:before {
  content: "\ed3e";
}
.bxs-tone:before {
  content: "\ed3f";
}
.bxs-torch:before {
  content: "\ed40";
}
.bxs-to-top:before {
  content: "\ed41";
}
.bxs-traffic:before {
  content: "\ed42";
}
.bxs-traffic-barrier:before {
  content: "\ed43";
}
.bxs-train:before {
  content: "\ed44";
}
.bxs-trash:before {
  content: "\ed45";
}
.bxs-trash-alt:before {
  content: "\ed46";
}
.bxs-tree:before {
  content: "\ed47";
}
.bxs-trophy:before {
  content: "\ed48";
}
.bxs-truck:before {
  content: "\ed49";
}
.bxs-t-shirt:before {
  content: "\ed4a";
}
.bxs-up-arrow:before {
  content: "\ed4b";
}
.bxs-up-arrow-circle:before {
  content: "\ed4c";
}
.bxs-up-arrow-square:before {
  content: "\ed4d";
}
.bxs-upside-down:before {
  content: "\ed4e";
}
.bxs-upvote:before {
  content: "\ed4f";
}
.bxs-user:before {
  content: "\ed50";
}
.bxs-user-badge:before {
  content: "\ed51";
}
.bxs-user-check:before {
  content: "\ed52";
}
.bxs-user-circle:before {
  content: "\ed53";
}
.bxs-user-detail:before {
  content: "\ed54";
}
.bxs-user-minus:before {
  content: "\ed55";
}
.bxs-user-pin:before {
  content: "\ed56";
}
.bxs-user-plus:before {
  content: "\ed57";
}
.bxs-user-rectangle:before {
  content: "\ed58";
}
.bxs-user-voice:before {
  content: "\ed59";
}
.bxs-user-x:before {
  content: "\ed5a";
}
.bxs-vial:before {
  content: "\ed5b";
}
.bxs-video:before {
  content: "\ed5c";
}
.bxs-video-off:before {
  content: "\ed5d";
}
.bxs-video-plus:before {
  content: "\ed5e";
}
.bxs-video-recording:before {
  content: "\ed5f";
}
.bxs-videos:before {
  content: "\ed60";
}
.bxs-volume:before {
  content: "\ed61";
}
.bxs-volume-full:before {
  content: "\ed62";
}
.bxs-volume-low:before {
  content: "\ed63";
}
.bxs-volume-mute:before {
  content: "\ed64";
}
.bxs-wallet:before {
  content: "\ed65";
}
.bxs-wallet-alt:before {
  content: "\ed66";
}
.bxs-watch:before {
  content: "\ed67";
}
.bxs-watch-alt:before {
  content: "\ed68";
}
.bxs-widget:before {
  content: "\ed69";
}
.bxs-wine:before {
  content: "\ed6a";
}
.bxs-wink-smile:before {
  content: "\ed6b";
}
.bxs-wink-tongue:before {
  content: "\ed6c";
}
.bxs-wrench:before {
  content: "\ed6d";
}
.bxs-x-circle:before {
  content: "\ed6e";
}
.bxs-x-square:before {
  content: "\ed6f";
}
.bxs-yin-yang:before {
  content: "\ed70";
}
.bxs-zap:before {
  content: "\ed71";
}
.bxs-zoom-in:before {
  content: "\ed72";
}
.bxs-zoom-out:before {
  content: "\ed73";
}
