// 
// plugins-rtl.scss
//

// calendar

.lnb-calendars-item {
  margin-right: 0;
  margin-left: 7px;
}

// Select 2

.select2-container {
  width: 100% !important;
  .select2-selection--single {
    .select2-selection__rendered {
      padding-right: 12px;
    }

    .select2-selection__arrow {
      left: 3px;
      right: auto;
    }
  }

  .select2-selection--multiple {
    .select2-selection__choice {
      float: right;
      margin-left: 5px;
      margin-right: 0;
    }
  }

  .select2-search__field {
    text-align: right;
  }

  .select2-search--inline {
    float: right;
  }
}

// Bootstrap select

.bootstrap-select {
  .dropdown-toggle {
    &:before {
      float: left;
    }

    .filter-option {
      text-align: right;
    }

    .filter-option-inner {
      padding-right: 0;
      padding-left: inherit;
    }
  }
}

// datatable

.dataTables_wrapper {
  .dataTables_filter {
    text-align: left !important;

    input {
      margin-left: 0px !important;
      margin-right: 0.5em;
    }
  }
}

// Responsive Table

.table-rep-plugin {
  .btn-group.pull-right {
    float: left;
  }

  .checkbox-row {
    label {
      &:after {
        margin-left: -22px;
        top: -2px;
      }
    }
  }
}

// date-picker

.mx-icon-calendar {
  left: 10px !important;
  right: auto !important;
}

// range-slider

.vue-slide-bar-separate {
  direction: ltr;
}

// Floating

.form-floating {
  >label {
    right: 0;
    left: auto;
  }
}

// editor

.ck.ck-toolbar>.ck-toolbar__items {
  flex-direction: row-reverse !important;
}

.ck.ck-editor__editable_inline {
  text-align: right !important;
}

.ck.ck-dropdown .ck-dropdown__panel.ck-dropdown__panel_ne,
.ck.ck-dropdown .ck-dropdown__panel.ck-dropdown__panel_se {
  right: 0;
  left: auto !important;
}


// Hotel picker

// .vhd__datepicker__input:first-child{
//   background: transparent url(/img/ic-arrow-right-datepicker.regular.99ab0620.99ab0620.svg) no-repeat 0%/8px !important;
// }

.vhd__datepicker__input--single-date:first-child{
  background: none !important;
}



.wizard{
  .actions{
    ul{
      text-align: left;
    }
  }
}
