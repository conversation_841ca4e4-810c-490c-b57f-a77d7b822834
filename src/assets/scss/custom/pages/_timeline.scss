// 
// timeline.scss
//

/************** Horizontal timeline **************/


.hori-timeline {
    .events {
        .event-list {
            text-align: center;
            display: block;

            .event-down-icon {
                position: relative;

                &::before {
                    content: "";
                    position: absolute;
                    width: 100%;
                    top: 16px;
                    left: 0;
                    right: 0;
                    border-bottom: 3px dashed var(--#{$prefix}border-color);
                }

                .down-arrow-icon {
                    position: relative;
                    background-color: var(--#{$prefix}secondary-bg);
                    padding: 4px;
                }
            }

            &:hover {
                .down-arrow-icon {
                    animation: fade-down 1.5s infinite linear;
                }
            }

            &.active {
                .down-arrow-icon {
                    animation: fade-down 1.5s infinite linear;

                    &:before {
                        content: "\ec4c";
                    }
                }
            }
        }
    }
}

.owl-nav {
    display: flex;
    justify-content: center;

    button {
        width: 30px;
        height: 30px;
        line-height: 28px;
        font-size: 20px;
        border-radius: 50%;
        background-color: rgba(85, 110, 230, .25);
        border: 0;
        color: #556ee6;
        margin: 4px 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        box-shadow: none;
        right: -7px;

        &.active,
        &:hover,
        &:focus {
            background-color: rgba(85, 110, 230, .25) !important;
            box-shadow: none !important;
            color: $primary !important;
        }

        &:after {
            font-size: 12px !important;
        }
    }
}


/************** vertical timeline **************/

.verti-timeline {
    border-left: 3px dashed var(--#{$prefix}border-color);
    margin: 0 10px;

    .event-list {
        position: relative;
        padding: 0px 0px 40px 30px;

        .event-timeline-dot {
            position: absolute;
            left: -9px;
            top: 0px;
            z-index: 9;
            font-size: 16px;
        }

        .event-content {
            position: relative;
            border: 2px solid var(--#{$prefix}border-color);
            border-radius: 7px;
        }

        &.active {
            .event-timeline-dot {
                color: $primary;
            }
        }

        &:last-child {
            padding-bottom: 0px;
        }
    }
}