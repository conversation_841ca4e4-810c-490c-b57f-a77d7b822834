//
// _dropdown.scss
//

.dropdown-menu {
  box-shadow: $box-shadow-lg;
  // animation-name: DropDownSlide;
  // animation-duration: 0.3s;
  // animation-fill-mode: both;
  margin: 0;
  position: absolute;
  z-index: 1000;
  // &.show {
  //   top: 100% !important;
  // }
  &.public-menu {
    min-width: 250px;
    margin-top: 10px;
    border-radius: 10px;
    @media (max-width: 600px) {
      transform: translate(0, 51px) !important;
    }
  }
}

.dropdown-menu-end[style] {
  left: auto !important;
  right: 0 !important;
}

.dropdown-menu[data-popper-placement^='right'],
.dropdown-menu[data-popper-placement^='top'],
.dropdown-menu[data-popper-placement^='left'] {
  top: auto !important;
  animation: none !important;
}

@keyframes DropDownSlide {
  100% {
    transform: translateY(0);
  }

  0% {
    transform: translateY(10px);
  }
}

@media (min-width: 600px) {
  .dropdown-menu-lg {
    width: 320px;
  }

  .dropdown-menu-md {
    width: 240px;
  }
}

// Dropdown Mega Menu

.dropdown-mega {
  position: static !important;
}

.dropdown-megamenu[style] {
  padding: 20px;
  left: 20px !important;
  right: 20px !important;
}

// Dropdown size

.dropdown-mega-menu-xl {
  width: 40rem;
}

.dropdown-mega-menu-lg {
  width: 26rem;
}

.dropdown-menu-right {
  position: absolute;
  right: 0 !important;
}

.dropdown-toggle.dropdown-toggle-split span {
  content: '';
}

.dropdown-toggle.dropdown-toggle-split:before {
  display: block;

  content: '\f0140';
  font-family: 'Material Design Icons';
  font-size: 12px;
}

.job-list-dropdown {
  .dropdown-menu.show {
    left: -144px !important;
  }
}
