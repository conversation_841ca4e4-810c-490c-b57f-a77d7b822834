// 
// _nav.scss
// 

.nav-tabs,
.nav-pills {
  >li {
    >a {
      color: var(--#{$prefix}emphasis-color);
      font-weight: $font-weight-medium;
    }
  }
}

.nav-pills {
  >a {
    color: var(--#{$prefix}emphasis-color);
    font-weight: $font-weight-medium;
  }
}


.nav-tabs-custom {
  border-bottom: 2px solid var(--#{$prefix}border-color);

  .nav-item {
    position: relative;
    color: var(--#{$prefix}body-color);

    .nav-link {
      border: none;

      &::after {
        content: "";
        background: $primary;
        height: 2px;
        position: absolute;
        width: 100%;
        left: 0;
        bottom: -1px;
        transition: all 250ms ease 0s;
        transform: scale(0);
      }

      &.active {
        color: $primary;

        &:after {
          transform: scale(1);
        }
      }

    }
  }
}

// vertical nav

.vertical-nav {
  .nav {
    .nav-link {
      padding: 24px 16px;
      text-align: center;
      margin-bottom: 8px;

      .nav-icon {
        font-size: 24px;
      }
    }
  }
}

// tabs & accordion.scss

.default-tabs {
  .nav-tabs {
    .nav-item {
      .nav-link {
        color: var(--#{$prefix}body-color) !important;
        font-weight: 500 !important;

        &:hover {
          color: var(--#{$prefix}primary) !important;
        }
      }
    }
  }
}

.nav-pills {
  &.nav-justified {
    .nav-item {
      .nav-link {
        color: var(--#{$prefix}body-color) !important;
        font-weight: 500 !important;

        &:hover {
          color: var(--#{$prefix}primary) !important;
        }

        &.active {
          color: white !important;
        }
      }
    }
  }
}

.vertical-pills {
  .nav-item {
    .nav-link {
      color: var(--#{$prefix}body-color) !important;
      width: 100%;
      font-weight: 500 !important;

      &:hover {
        color: var(--#{$prefix}primary) !important;
      }

      &.active {
        color: white !important;
      }
    }
  }
}

.nav-tabs-custom {
  .nav-item {
    .nav-link {
      color: var(--#{$prefix}body-color) !important;
      font-weight: 500 !important;

      &:hover {
        color: var(--#{$prefix}primary) !important;
      }

      &.active {
        color: var(--#{$prefix}primary) !important;
      }
    }
  }
}