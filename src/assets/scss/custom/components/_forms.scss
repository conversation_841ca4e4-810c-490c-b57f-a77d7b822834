//
// Forms.scss
//

[type='tel'],
[type='url'],
[type='email'],
[type='number'] {
  &::placeholder {
    text-align: left /*rtl: right*/;
  }
}

.form-check {
  position: relative;
  text-align: left /*rtl: right*/;
}

// checkbox input right

.form-check-right {
  padding-left: 0;
  display: inline-block;
  padding-right: $form-check-padding-start;

  .form-check-input {
    float: right;
    margin-left: 0;
    margin-right: $form-check-padding-start * -1;
  }

  .form-check-label {
    display: block;
  }
}

// checkbox

.form-checkbox-outline {
  .form-check-input {
    border-width: 2px;
    background-color: var(--#{$prefix}secondary-bg);

    &:active {
      filter: none;
    }

    &:checked {
      background-color: var(--#{$prefix}secondary-bg) !important;

      &[type='checkbox'] {
        background-image: none;
      }

      &:after {
        position: absolute;
        content: '\F012C';
        font-family: 'Material Design Icons';
        top: -4px !important;
        left: 1px;
        /*rtl: -4px */
        font-size: 16px;
        color: var(--#{$prefix}body-color);
      }
    }
  }
}

// radio

.form-radio-outline {
  .form-check-input {
    background-color: var(--#{$prefix}secondary-bg);
    position: relative;

    &:active {
      filter: none;
    }

    &:checked {
      background-color: var(--#{$prefix}secondary-bg) !important;

      &[type='checkbox'] {
        background-image: none;
      }

      &:after {
        position: absolute;
        content: '';
        top: 3px !important;
        left: 3px;
        width: 5px;
        height: 5px;
        border-radius: 50%;
      }
    }
  }
}

// checkbox color

@each $color, $value in $theme-colors {
  .form-check-#{$color} {
    .form-check-input {
      &:checked {
        background-color: $value;
        border-color: $value;
      }
    }
  }

  .form-radio-#{$color} {
    .form-check-input {
      &:checked {
        border-color: $value;
        background-color: $value;

        &:after {
          background-color: $value;
        }
      }
    }
  }
}

.form-check,
.form-check-input,
.form-check-label {
  cursor: pointer;
  margin-bottom: 0;
}

// Switch sizes

.form-switch-md {
  padding-left: 2.5rem;
  min-height: 24px;
  line-height: 24px;

  .form-check-input {
    width: 40px;
    height: 20px;
    left: -0.5rem;
    position: relative;
  }

  .form-check-label {
    vertical-align: middle;
  }
}

.form-switch-lg {
  padding-left: 2.75rem;
  min-height: 28px;
  line-height: 28px;

  .form-check-input {
    width: 48px;
    height: 24px;
    left: -0.75rem;
    position: relative;
  }
}

.input-group-text {
  margin-bottom: 0px;
}

// form select

.multiselect {
  background-color: var(--#{$prefix}secondary-bg) !important;
  border: var(--#{$prefix}border-width) solid var(--#{$prefix}border-color-translucent) !important;
}

.multiselect.is-disabled {
  .multiselect-wrapper {
    cursor: not-allowed !important;
    background-color: var(--bs-body-bg);
    opacity: 0.5;
  }
}

.multiselect-dropdown {
  overflow-y: auto;
  background-color: var(--#{$prefix}secondary-bg) !important;
  color: $input-color !important;
  border: var(--#{$prefix}border-width) solid var(--#{$prefix}border-color-translucent) !important;

  .multiselect-option span {
    color: $input-color !important;
  }

  .multiselect-option:hover,
  .multiselect-option.is-pointed {
    background-color: var(--#{$prefix}light) !important;
    color: $input-color !important;
  }
}

.multiselect-single-label img,
.multiselect-option img {
  width: 22px;
  height: 22px;
  margin-right: 5px;
}

.multiselect-option.is-selected.is-pointed {
  background-color: $primary !important;
}

.multiselect-option.is-selected {
  background-color: $primary !important;
}

.multiselect.is-active {
  box-shadow: none !important;
}

.multiselect-single-label-text {
  font-size: small !important;
}

.multiselect-option {
  span {
    font-size: small !important;
  }
}

.multiselect-multiple-label {
  font-size: small !important;
}

// Multiselect
.multiselect-single-label-text {
  color: var(--bs-emphasis-color);
}

.multiselect-placeholder {
  color: var(--#{$prefix}secondary-color);
  font-size: 13px;
}
