.auth-submit-btn {
  background-color: #ecac24 !important;
  border-color: #ecac24 !important;
  color: #fff !important;
  height: 55px !important;
  font-size: 1.1rem !important;
  border-radius: 12px !important;
  padding: 0 40px !important;
}

.auth-submit-btn:hover {
  background-color: #d89b20 !important;
  border-color: #d89b20 !important;
}

.custom-input {
  height: auto !important;
  padding: 16px !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  background: #fff !important;
  transition: box-shadow 0.2s ease-out;
}

.custom-input:focus {
  box-shadow: inset 0px 0px 1px #d89b20, 0 0 8px #d89b20;
}

.link-text {
  font-size: 1rem !important;
  font-weight: 400 !important;
}

.auth-title {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
}

.custom-card-body {
  padding: 2rem !important;
}

.auth-card {
  border-radius: 15px !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e0e0e0 !important;
}
