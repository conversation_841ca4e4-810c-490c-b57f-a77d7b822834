.drill-list {
  .drill-card {
    position: relative;
    overflow: hidden;
    .drill-badge {
      position: absolute;
      top: 50px;
      right: 55px;
      padding: 7px 15px;
      filter: drop-shadow(1px 2px 2px gray);

      @media (max-width: 2000px) {
        top: 40px;
        right: 45px;
      }

      @media (max-width: 1400px) {
        top: 35px;
        right: 40px;
      }
    }

    .drill-image {
      transition: transform 0.3s ease;
      object-fit: cover;
      border-radius: 0.5rem;
    }

    .detail-btn,
    .video-btn {
      border: 1px solid #e8e8e8;
    }

    &:hover {
      .drill-image {
        transform: scale(1.03);
      }

      .detail-btn {
        color: white;
        background-color: #f1b44c;
      }

      .video-btn {
        background-color: aliceblue;
      }
    }
  }
}

.user-drill-list{
  .drill-card {
    position: relative;
    overflow: hidden;
    .drill-badge {
      position: absolute;
      top: 40px;
      right: 45px;
      padding: 7px 15px;
      filter: drop-shadow(1px 2px 2px gray);

      @media (max-width: 2000px) {
        top: 40px;
        right: 45px;
      }

      @media (max-width: 1400px) {
        top: 35px;
        right: 40px;
      }
    }

    .drill-image {
      transition: transform 0.3s ease;
      object-fit: cover;
      border-radius: 0.5rem;
    }

    .detail-btn,
    .video-btn {
      border: 1px solid #e8e8e8;
    }

    &:hover {
      .drill-image {
        transform: scale(1.03);
      }

      .detail-btn {
        color: white;
        background-color: #f1b44c;
      }

      .video-btn {
        background-color: aliceblue;
      }
    }
  }
}