.user-course-view {
  .course-card {
    .course-banner {
      transition: transform 0.3s ease;
    }
    .card-title {
      transition: transform 0.3s ease;
    }
    .card-body {
      @media (max-width: 992px) {
        min-height: auto;
      }
    }

    &:hover {
      .course-banner {
        transform: scale(1.05);
      }
      .card-title {
        color: #e1a53f;
      }
    }
  }
}

.card-image {
  position: relative;
  .teacher-section {
    position: absolute;
    bottom: 10px;
    left: 10px;
    gap: 10px;
    .avatar-teacher-section {
      position: relative;
      display: inline-block;

      .check-ic {
        position: absolute;
        bottom: -2px;
        right: -4px;
        background: white;
        border-radius: 50%;
        padding: 1px;
      }
    }
    .teacher-name {
      filter: drop-shadow(1px 1px 2px #363636);
    }
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, #ffffff, #000000);
    opacity: 0.2;
  }
}

.course-card {
  .rating {
    .star-container {
      width: 15px;
      height: 15px;
      font-size: 12px;
    }
    .value {
      width: auto;
      font-size: 12px;
      margin-left: 5px;
    }
  }
  .course-status {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 2px 10px;

    &.beginner {
      color: #9a6700;
      background-color: #fff8c5;
    }

    &.intermediate {
      color: #0a69da;
      background-color: #ddf4ff;
    }

    &.advanced {
      color: #bc4c00;
      background-color: #fff1e5;
    }

    &.allLevels {
      color: #1f883d;
      background-color: #dafbe1;
    }
  }
}
