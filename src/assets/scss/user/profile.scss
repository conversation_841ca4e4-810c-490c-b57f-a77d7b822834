.profile-user-view {
  .profile-user-card {
    border-top: 3px solid #eaaa17;
    .avatar-xs {
      .avatar-title {
        background-color: #f0b629 !important;
        i {
          color: white;
        }
      }
    }

    .avatar-lg {
      .avatar-title {
        box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1), 0 0 10px rgba(255, 193, 7, 0.2), 0 0 20px rgba(255, 193, 7, 0.15);
        img {
          width: 6rem;
          height: 6rem;
          object-fit: cover;
        }
      }
    }

    .action-btn {
      .btn {
        width: 210px;
        &.cancel-btn {
          color: black;
          background-color: white;
          border-color: #d1d5db;
          &:hover {
            background-color: #f9fafb;
          }
        }
        &.save-btn {
          color: white;
          background-color: #eaaa17;
          border-color: #eaaa17;
          &:hover {
            background-color: #dba11a;
          }
        }
        @media (max-width: 765px) {
          width: 50%;
        }
      }
    }
  }
}
