.category-icon {
  width: 30px;
  height: 30px;

  display: inline-flex;
  justify-content: center;
  align-items: center;

  border-radius: 50%;
  &.text-primary {
    background-color: #e0e5fa;
  }

  &.text-danger {
    background-color: #fde4e4;
  }

  &.text-success {
    background-color: #daf4eb;
  }

  &.text-orange {
    background-color: #fcf1df;
  }
}

.review-section {
  .rating {
    .star-container {
      width: 18px;
      height: 18px;
      font-size: 16px;
    }
    .value {
      font-size: 14px;
      width: auto;
    }
  }
  .rating-line {
    .star-number,
    .rating-count {
      width: 50px;
    }
  }
  @media (max-width: 765px) {
    .author-avatar {
      width: 30px;
      height: 30px;
    }
    .author-name {
      font-size: 14px;
    }
    .rating {
      .star-container {
        width: 15px;
        height: 15px;
        font-size: 14px;
      }
      .value {
        font-size: 12px;
        margin-left: 5px;
      }
    }
  }
}
