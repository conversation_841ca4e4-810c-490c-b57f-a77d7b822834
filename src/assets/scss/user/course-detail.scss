.course-detail {
  margin-top: 70px;

  .banner {
    position: relative;

    width: 100%;
    height: 250px;
    background-color: #101828;
    background-image: url('@/assets/images/banner-course-detail.png');

    .course-detail-banner {
      position: absolute;
      bottom: 0;
      left: 0;
    }

    .vector-billiard {
      position: absolute;
      top: 50%;
      transform: translate(0, -50%);
      height: 100%;

      @media (max-width: 1400px) {
        display: none;
      }

      &.left {
        left: 0;
      }

      &.right {
        right: 0;
      }

      &.sp {
        display: none;
        @media (max-width: 1400px) {
          display: block;
        }
      }
    }
  }

  .course-intro-sidebar {
    .alert {
      color: #dc2626;
      background-color: #fef2f2;
    }

    .course-image {
      width: 100%;
      object-fit: cover;
    }

    .status {
      display: inline-block;
      position: absolute;
      top: 7px;
      right: 5px;
      z-index: 1;

      color: white;
      background-color: #34c38f;
      font-weight: 500;
      padding: 3px 10px;
      border-radius: 20px;
      cursor: default;
    }

    .preview-btn {
      color: white;
      background-color: #34c38f;
    }

    .learn-now-btn {
      background-color: #f1b44c;
      &:hover,
      &:active {
        background-color: #c1903d;
      }
    }
  }

  .course-intro-content {
    .toggle-btn {
      color: #1d4ed8;
      background-color: #eff6ff;
    }
    .list-lesson {
      .lesson-card {
        border: 1px solid #e4e4e7;

        .mark-order {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 30px;
          height: 30px;
          background-color: #dbeafe;
          border-radius: 50%;
          line-height: 1;
        }

        .course-section-title {
          width: fit-content;
        }

        .user-site-badge {
          width: fit-content;
        }
      }

      .course-lesson-collapse {
        border: 1px solid #e4e4e7;
        li {
          border: 1px solid #80808047;
          background-color: #f9fafb;
        }
      }
    }
  }
}
