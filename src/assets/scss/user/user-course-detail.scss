.user-course-detail {
  margin-top: 100px;
  margin-bottom: 20px;
  @media (max-width: 992px) {
    margin-top: 70px;
    padding: 0;
  }
  .course-title {
    @media (max-width: 992px) {
      position: fixed;
      top: 70px;
      left: 0;
      width: 100%;
      z-index: 2;

      padding: 7px 15px;
      background-color: black;
      h3 {
        display: -webkit-box;
        font-size: 15px;
        color: white;
        margin-bottom: 0;
        line-height: normal;

        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .lesson-container {
    @media (max-width: 992px) {
      margin-top: 115px;
    }
  }

  .drill-step {
    padding-left: 50px;
    .step-item {
      img {
        width: 60%;
        @media (max-width: 675px) {
          width: 100%;
        }
      }
    }

    .step-ball {
      width: 32px;
      height: 32px;
      top: 9px;
      left: 20px;
      @media (max-width: 675px) {
        width: 25px;
        height: 25px;
        left: 15px;
      }
    }

    .line-ball {
      top: 32px;
      left: 20px;
      width: 2px;
      height: calc(100% - 32px);
      background-color: #ffe8a1;
      @media (max-width: 675px) {
        left: 15px;
      }
    }

    @media (max-width: 675px) {
      padding-left: 35px;
    }
  }

  .course-content-tabs {
    .nav-tabs-custom {
      .nav-item {
        button {
          width: 180px;

          @media (max-width: 992px) {
            width: 120px;
          }

          @media (max-width: 675px) {
            width: 100%;
            padding: 5px 15px;
          }

          @media (max-width: 460px) {
            padding: 5px 10px;
          }

          &:hover {
            color: #ecac24 !important;
          }
        }

        .nav-link.active {
          color: #ecac24 !important;
        }

        .nav-link::after {
          background: #ecac24;
        }
      }

      @media (max-width: 390px) {
        gap: 5px;
        justify-content: center;
      }
    }

    .practice-card-header {
      background-color: #f4f4f5;
    }

    .submissions-data-empty {
      i {
        color: #e3e3e4;
        font-size: 36px;
      }
    }

    .video-card {
      background-color: #f8f9fa;
    }

    .review-section {
      .review-title {
        font-size: 16px;
      }
    }
  }

  .course-side-bar {
    @media (max-width: 992px) {
      height: 100%;
      box-shadow: none;
    }

    .list-lesson {
      height: 60vh;
      overflow-y: scroll;
      .lesson-card {
        box-shadow: none;

        .complete-icon {
          font-size: 16px;
          color: #d1d5db;
          input {
            margin-top: 0;
          }
        }

        .action-icon {
          display: none;
          font-size: 22px;
          line-height: 1;
          @media (max-width: 992px) {
            font-size: 19px;
          }
        }

        &:hover,
        .active {
          background-color: #fff8f3;
        }

        .active {
          .mdi-folder-open-outline {
            color: #556ee6 !important;
          }
        }
      }

      @media (max-width: 992px) {
        height: 100%;
      }
    }
  }

  .course-side-bar::-webkit-scrollbar {
    width: 6px;
  }

  .course-side-bar::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 10px;
  }

  .content-course {
    .default-tabs {
      .nav-link {
        &.active,
        &:hover {
          color: #ecac24 !important;
        }
      }
    }

    .lesson-content {
      overflow-wrap: anywhere;
    }

    .drill-section {
      .custom-select-option {
        width: 25%;
        @media (max-width: 675px) {
          width: 50%;
        }
      }

      .button-toggle-group {
        display: flex;
        background-color: #f4f6f8;
        border-radius: 999px;
        overflow: hidden;

        .toggle-btn {
          min-width: 245px;
          background: transparent;
          cursor: pointer;
          transition: all 0.3s ease-in-out;
          .action-ic {
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #dbeafe;
            margin-right: 10px;
            i {
              color: #2563eb;
            }
          }
          .count-badge {
            padding: 0 7px;
            background-color: #ffffffc2;
            border-radius: 20px;
          }
          &.active {
            color: black;
            background-color: #dbeafe;
            .action-ic {
              background-color: transparent;
              margin-right: 5px;
            }
          }

          @media (max-width: 675px) {
            min-width: auto;
            .action-ic {
              margin-right: 5px;
            }
          }
        }
      }

      .tab-list-diagram {
        button {
          width: 25px;
          height: 25px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }

    .step-action {
      display: inline-flex;
      align-items: center;
      gap: 2px;
      padding: 5px 10px;
      border-radius: 5px;
      cursor: pointer;

      i {
        font-size: 18px;
      }

      &.back-step {
        color: black;
        background-color: white;
        border: 1px solid lightgray;
        &:hover {
          background-color: #f9f9f9;
        }
      }

      &.next-step {
        color: white;
        background-color: #ecac24;
        border: 1px solid #ecac24;
        &:hover {
          background-color: #db9f20;
        }
      }

      @media (max-width: 675px) {
        padding: 5px 5px;
        font-size: 12px;
        i {
          font-size: 14px;
        }
      }
    }
  }
}
