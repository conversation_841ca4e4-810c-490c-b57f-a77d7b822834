{"back_to_drill": "Back to drills", "back_to_course": "Back to courses", "title": "First, let's find out what type of media you're making.", "course": {"label": "Course", "description": "Create rich learning experiences with the help of video lectures, practice drills, diagrams, etc.", "init_step": {"label": "How about a working title for your course?", "description": "It's ok if you can't think of a good title now. You can change it later.", "form": {"title": "Title", "placeholder": "e.g. Fundamentals of cue control", "button": {"next": "Continue"}}}, "status": {"modal": {"title": "Course status history"}}, "steps": {"planning_tip": {"label": "Planning", "title": "There's a course in you. Plan it out.", "description": "Planning your course carefully will create a clear learning path for students and help you once you film. Think down to the details of each lecture including the skill you’ll teach, estimated video length, practical activities to include, and how you’ll create introductions and summaries.", "content": {"tip": {"title": "Tips", "list": {"1": {"title": "Course structure", "description": "Think through every detail of each exercise, including the skill you'll teach, estimated video length, practice activities you'll include, and how you'll introduce and conclude the content.", "sub_list": {"1": {"label": "Start with your goals.", "description": "Setting goals for what learners will accomplish in your course (also known as learning objectives) at the beginning will help you determine what content to include in your course and how you will teach the content to help your learners achieve the goals."}, "2": {"label": "Create an outline.", "description": "Decide what skills you’ll teach and how you’ll teach them. Group related lectures into sections. Each section should have at least 3 lectures, and include at least one assignment or practical activity."}, "3": {"label": "Introduce yourself and create momentum.", "description": "People online want to start learning quickly. Make an introduction section that gives learners something to be excited about in the first 10 minutes."}, "4": {"label": "Sections have a clear learning objective.", "description": "Introduce each section by describing the section's goal and why it’s important. Give lectures and sections titles that reflect their content and have a logical flow."}, "5": {"label": "Lectures cover one concept.", "description": "A good lecture length is 2-7 minutes to keep students interested and help them study in short bursts. Cover a single topic in each lecture so learners can easily find and re-watch them later."}, "6": {"label": "Mix and match your lecture types.", "description": "Alternate between filming yourself, your screen, and slides or other visuals. Showing yourself can help learners feel connected."}, "7": {"label": "Practice activities create hands-on learning.", "description": "Help learners apply your lessons to their real world with projects, assignments, coding exercises, or worksheets."}}}, "2": {"title": "Setup & test video", "description": "It's important to get your audio and video set up correctly now, because it's much more difficult to fix your videos after you’ve recorded. There are many creative ways to use what you have to create professional looking video.", "sub_list": {"1": {"label": "Equipment can be easy.", "description": "You don’t need to buy fancy equipment. Most smartphone cameras can capture video in HD, and you can record audio on another phone or external microphone."}, "2": {"label": "Students need to hear you.", "description": "A good microphone is the most important piece of equipment you will choose. There are lot of affordable options.. Make sure it’s correctly plugged in and 6-12 inches (15-30 cm) from you."}, "3": {"label": "Set up your shooting space.", "description": "Clear the area around your table and arrange any billiard props you’ll use (cues, racks, chalk). Position your camera so it captures both the entire table and close-ups of your cue action. A simple backdrop—like a plain wall or a draped cloth—helps viewers focus on the balls and your technique."}, "4": {"label": "Light the table and your demonstration.", "description": "Good lighting is key to showing spin, angles, and ball paths. Turn off harsh overhead lights that cast shadows. Instead, aim two soft lights at the table from opposite sides and one behind you shining onto the table surface. This three-point setup highlights the table’s contours and keeps your face visible when you explain shot setup."}, "5": {"label": "Reduce noise and echo.", "description": "Turn off fans or air vents, and record at a time when it’s quiet. Place acoustic foam or blankets on the walls, and bring in rugs or furniture to dampen echo."}, "6": {"label": "Be creative.", "description": "Students won’t see behind the scenes. No one will know if you’re surrounded by pillows for soundproofing...unless you tell other instructors in the community."}}}, "3": {"title": "Film & edit", "description": "This is your moment! If you’ve structured your course and used our guides, you're well prepared for the actual shoot. Pace yourself, take time to make it just right, and fine-tune when you edit.", "sub_list": {"1": {"label": "Take breaks and review frequently.", "description": "Keep an eye (and ear) on your recording for unexpected noises—rolling balls, clacking cues, or room sounds. Filming can be tiring; short pauses help you stay energetic and focused on screen."}, "2": {"label": "Build rapport with students.", "description": "Players want to know who’s teaching them. Even if most of your course is table demonstrations, film yourself introducing each section—talk about why the shot matters and what skills they’ll master."}, "3": {"label": "Practice being on camera.", "description": "Look into the lens, speak clearly, and demonstrate your stance and cue grip. Do as many retakes as needed to show smooth, confident instruction."}, "4": {"label": "Set yourself up for editing success.", "description": "Film a little extra before and after each drill or shot demonstration. That way you can trim mistakes, long pauses without losing flow."}, "5": {"label": "Create audio marks.", "description": "Clap or strike a cue on the table at the start of each take. This spike in the audio track makes it easy to align video clips when editing."}}}}}, "requirement": {"title": "Requirements", "list": {"1": {"description": "Your course must have at least five lectures."}, "2": {"description": "All lectures must add up to at least 30+ minutes of total video."}, "3": {"description": "Your course is composed of valuable educational content and free of promotional or distracting materials."}, "4": {"description": "Film and export in HD to create videos of at least 720p, or 1080p if possible."}, "5": {"description": "Audio should come out of both the left and right channels and be synced to your video."}, "6": {"description": "Audio should be free of echo and background noise so as not to be distracting to students."}}}}}, "sections": {"label": "Sections", "title": "Sections", "description": "Start putting together your course by creating sections, lectures and practice activities (quizzes, exercises and assignments). Use your course outline to structure your content and label your sections and lectures clearly.", "action_btn": {"add_text": "Add text", "add_video": "Add video", "add_drill": "Add drill"}, "content_type": {"text": {"title": "Text", "remove_text_message": "Are you sure you want to delete this text content?"}, "video": {"title": "Video"}, "drill": {"title": "Drill"}}}, "certificate": {"label": "Certificate", "title": "Certificate", "description": "A certificate is an important part of your course. It helps learners better understand the course content and why they should enroll. Consider creating a compelling certificate that shows why someone should take your course.", "form": {"title": {"title": "Certificate Title", "subtitle": "Your certificate title should be concise, clear, and describe the course content accurately.", "placeholder": "Enter certificate title"}, "is_active": {"title": "Issue Certificate", "tooltip": "When enabled, learners will receive a certificate upon completing the course."}, "description": {"title": "Certificate Description", "subtitle": "Your certificate description should give an overview of the course content, what learners will gain, and why they should enroll.", "placeholder": "Enter certificate description"}}}, "landing_page": {"label": "Landing page", "title": "Landing page", "description": "Your course landing page is crucial to your success on Vibico. If it’s done right, it can also help you gain visibility in search engines like Google. As you complete this section, think about creating a compelling Course Landing Page that demonstrates why someone would want to enroll in your course.", "form": {"title": {"title": "Course Title", "subtitle": "Your course title should be short, concise, and clearly describe the course content.", "placeholder": "Enter course title"}, "description": {"title": "Course Description", "subtitle": "Your course description should provide an overview of the course content, what learners will achieve, and why they should enroll.", "placeholder": "Enter course description"}, "instructional_level": {"title": "Course Level", "subtitle": "The course level should be chosen to match the target learners’ proficiency (e.g., Beginner, Intermediate, Advanced)", "placeholder": "Select course level"}, "banner": {"title": "Course Image"}, "highlight": {"title": "Highlight", "placeholder": "Enter highlight", "no_results": "No results found", "add_highlight": "Create new highlight"}, "status": {"title": "Status"}, "course_setting": {"title": "Course setting", "subtitle": "Course setting should be chosen to match the content of your course", "locking_item": {"title": "Locking item", "tooltip": "When locking a lesson, students will not be able to view all the lessons freely. Instead, students will have to complete the lesson, submit assignments, and you are responsible for checking and approving submitted assignments. At that time, the student's next lesson will be unlocked."}}}}, "pricing": {"label": "Pricing", "title": "Set a price for your course", "description": "Please choose the price tier for your course. You can offer your course for free or set a price—there are no restrictions based on video length but it must follow the video requirements. However, if your course includes practice tests or premium content, consider a pricing tier that reflects the value you’re offering to learners.", "form": {"price": {"title": "Price", "placeholder": "Enter price"}, "sale_price": {"title": "Sale price", "subtitle": {"1": "If no sale price is entered or the sale price is set equal to the original price, the system will display the original price to students", "2": "If the sale price is set lower than the original price, the system will display the sale price to students", "3": "If both the original price and sale price are set to 0, your course will be considered free"}, "placeholder": "Enter sale price"}}, "add_package": "Add course package"}, "preview": {"label": "Preview", "title": "Preview", "description": "Please review all information before submitting your course. Ensure everything is accurate and complete, from the title to the description and lesson content. If you need to make any changes, go back to the previous steps to edit."}}, "actions": {"save": "Save course"}}, "drill": {"label": "Drill", "description": "Help students prepare for certification course by providing practice drills."}, "censor": {"label": "Censor History", "status": {"modal": {"title": "Censor History"}}}}