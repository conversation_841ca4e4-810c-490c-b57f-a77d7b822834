const locales: Record<string, any> = {};

const modules = import.meta.glob('./**/*.json', { eager: true });

function setNestedProperty(obj: Record<string, any>, keys: string[], value: any) {
  let current = obj;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];

    if (!current[key]) {
      current[key] = {};
    }
    current = current[key];
  }

  current[keys[keys.length - 1]] = value['default'];
}

function mergeIntoParent(obj: Record<string, any>, keys: string[], value: any) {
  const parentKeys = keys.slice(0, -1); // Get the parent keys (excluding "index")
  let parentObj = obj;

  for (const key of parentKeys) {
    if (!parentObj[key]) {
      parentObj[key] = {};
    }
    parentObj = parentObj[key];
  }

  Object.assign(parentObj, value['default']); // Merge the content of index.json into the parent directory
}

for (const path in modules) {
  const match = path.match(/\.\/(\w+)\/(.+)\.json$/); // Extract the language and inner path
  if (!match) continue;

  const [, lang, subPath] = match;
  const keys = subPath.split('/');

  if (!locales[lang]) {
    locales[lang] = {};
  }

  const jsonData = modules[path];

  if (keys[keys.length - 1] === 'index') {
    mergeIntoParent(locales[lang], keys, jsonData); // Handle index.json files
  } else {
    setNestedProperty(locales[lang], keys, jsonData); // Handle regular files
  }
}

export default locales;
