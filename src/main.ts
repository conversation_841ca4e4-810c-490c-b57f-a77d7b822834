import { createApp } from 'vue';
import App from './App.vue';

import router from './router';

import 'bootstrap-vue-next/dist/bootstrap-vue-next.css';
import 'bootstrap/dist/css/bootstrap.css';

import 'vue-advanced-cropper/dist/style.css';

import '@/assets/scss/app.scss';

import 'quill/dist/quill.bubble.css';
import 'quill/dist/quill.snow.css';

import 'swiper/css';
import 'swiper/css/free-mode';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/thumbs';
import 'swiper/css/effect-cards';

import pinia from './store/index';
import { createBootstrap } from 'bootstrap-vue-next';

import vClickOutside from 'click-outside-vue3';
import VueApexCharts from 'vue3-apexcharts';
import i18n from './plugin/i18n';

const app = createApp(App);

import '@/utils/javascript/index';

import components from '@/components/base/index';
for (const appKey in components) {
  app.component(appKey, components[appKey]);
}

app.use(pinia);
app.use(router);
app.use(createBootstrap());
app.use(vClickOutside);
app.use(VueApexCharts);
app.use(i18n);

app.mount('#app');
