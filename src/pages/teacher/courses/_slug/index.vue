<template>
  <div v-if="courseForm" class="course-preview">
    <div
      v-if="courseForm.status == 'rejected'"
      class="alert alert-warning d-flex justify-content-between align-items-center mb-3"
    >
      <div>
        <i class="mdi mdi-alert-circle me-2"></i>
        {{ $t('teacher.course.feedback_message.rejected') }}
      </div>
      <Button variant="warning" icon="bx-pencil" @click="toEdit()">
        {{ $t('common.edit') }}
      </Button>
    </div>

    <div v-else class="d-flex align-items-center justify-content-end pb-2">
      <Button icon="bx-pencil" @click="toEdit()">
        {{ $t('common.edit') }}
      </Button>
    </div>
    <Loader :loading="loading">
      <CourseDetailSection
        v-if="isPreview && currentLesson"
        :course="courseForm"
        :currentLesson="currentLesson as any"
        :loading="loading"
        :isPreview="isPreview"
      />
      <CourseInfoForm v-else v-model:course-form="courseForm" />
    </Loader>
  </div>
</template>

<script setup lang="ts">
  import i18n from '@/plugin/i18n';
  import Button from '@/components/base/Button.vue';
  import CourseInfoForm from '@/components/teacher/courses/CourseInfoForm.vue';
  import CourseDetailSection from '@/components/user/course/CourseDetailSection.vue';

  import { courseSectionItemDetail, showCourse } from '@/services/teacher';
  import { useBreadcrumb } from '@bachdx/b-vuse';
  import { CourseSectionItemInterface } from '@/utils/interface/teacher/courseSectionItem';

  const route = useRoute();
  const router = useRouter();

  const { setBreadcrumb } = useBreadcrumb({});

  const currentLesson = ref<CourseSectionItemInterface | null>(null);
  const currentItemId = ref();
  const currentSectionId = ref();
  const courseForm = ref();
  const loading = ref(false);

  const courseSlug = computed(() => route.params.slug?.toString());
  const lessonSlug = computed(() => route.query.courseContentSlug?.toString());
  const isPreview = computed(() => route.query.preview === 'true');

  setBreadcrumb({
    title: i18n.global.t('teacher.course.title'),
    items: [
      {
        text: i18n.global.t('common.dashboard'),
        href: 'dashboard'
      },
      {
        text: i18n.global.t('teacher.course.title'),
        href: '/teacher/courses'
      },
      {
        text: courseSlug.value
      }
    ]
  });

  const fetchLesson = async () => {
    loading.value = true;
    try {
      if (currentItemId.value && currentSectionId.value) {
        const result = await courseSectionItemDetail(
          String(currentItemId.value),
          String(courseForm.value?.id),
          String(currentSectionId.value)
        );

        currentLesson.value = result.data.courseSectionItem;
      }
    } catch (error) {
      console.error(error);
    }
    loading.value = false;
  };

  const toEdit = () => {
    router.push(`/teacher/editor/course/${courseSlug.value}?tabIndex=1`);
  };

  const fetchCourse = async () => {
    await showCourse(courseSlug.value).then(res => {
      courseForm.value = res.data.course;
    });
  };

  watch(lessonSlug, (newVal, oldVal) => {
    if (newVal !== oldVal && currentLesson.value?.slug != newVal) {
      for (const section of courseForm.value?.courseSections || []) {
        const item = section.courseSectionItems?.find((item: CourseSectionItemInterface) => item.slug === newVal);
        if (item) {
          currentSectionId.value = section.id;
          currentItemId.value = item.id;
          fetchLesson();
          break;
        }
      }
    }
  });

  onMounted(async () => {
    await fetchCourse();

    if (
      courseForm.value?.courseSections?.length &&
      courseForm.value.courseSections.every(
        (section: { courseSectionItems?: any[] }) =>
          section.courseSectionItems && section.courseSectionItems?.length > 0
      )
    ) {
      currentSectionId.value = courseForm.value.courseSections[0]?.id;
      currentItemId.value = courseForm.value.courseSections[0]?.courseSectionItems?.[0]?.id;

      await fetchLesson();

      const initialItemSlug = courseForm.value.courseSections[0]?.courseSectionItems?.[0]?.slug;
      if (initialItemSlug) {
        router.replace({
          query: { ...route.query, courseContentSlug: initialItemSlug }
        });
      }
    }
  });
</script>
