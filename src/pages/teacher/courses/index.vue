<template>
  <BRow>
    <BCol lg="12">
      <BCard no-body>
        <BCardBody class="border-bottom">
          <div class="d-flex align-items-center">
            <BCardTitle class="mb-0 flex-grow-1">
              {{ $t('teacher.course.title') }}
            </BCardTitle>

            <div class="flex-shrink-0">
              <Button icon="bx-plus" @click="toCreate">
                {{ $t('common.create_btn') }}
              </Button>
            </div>
          </div>
        </BCardBody>

        <BCardBody class="border-bottom">
          <SearchForm
            v-model:query="query"
            :disabled="isTableLoading"
            :loading="isTableLoading"
            :search-fields-list="searchFieldsList"
            @reset="handleReset"
            @search="handleSearch"
          ></SearchForm>
        </BCardBody>

        <BCardBody>
          <CourseList
            v-if="items.length > 0"
            :courses="items"
            :loading="isTableLoading"
            :metadata="metadata"
            @fetch-list="changePage"
          ></CourseList>

          <DataEmpty v-else :message="$t('teacher.course.data_empty')" height="150" />
        </BCardBody>
      </BCard>
    </BCol>
  </BRow>
</template>
<script lang="ts" setup>
  import i18n from '@/plugin/i18n';

  import { useGoList, useBreadcrumb } from '@bachdx/b-vuse';

  import { courseList } from '@/services/teacher';

  import { CourseListQueryFormModel } from '@/forms/teacher/course';

  import SearchField, { SearchFieldOptions } from '@/utils/search-fields';

  import Button from '@/components/base/Button.vue';
  import CourseList from '@/components/teacher/courses/CourseList.vue';
  import SearchForm from '@/components/base/SearchForm.vue';
  import DataEmpty from '@/components/utility/DataEmpty.vue';

  import useDynamicSearch from '@/composable/dynamicSearch';

  const route = useRoute();
  const router = useRouter();

  const { searchFieldsList, searchComponents } = useDynamicSearch();
  const { setBreadcrumb } = useBreadcrumb({});

  const { items, metadata, changePage, parseQueryAndFetch, query, reset, search } = useGoList({
    fetchListFnc: courseList,
    fetchKey: 'courses',
    route: route,
    router: router,
    queryFormModels: CourseListQueryFormModel,
    perPage: 12
  });

  setBreadcrumb({
    title: i18n.global.t('teacher.course.title'),
    items: [
      {
        text: i18n.global.t('teacher.course.title'),
        href: ''
      }
    ]
  });

  const isTableLoading = ref(true);

  const statusOptions = [
    { value: 'draft', label: i18n.global.t('teacher.course.search_form.status.draft') },
    { value: 'submitted', label: i18n.global.t('teacher.course.search_form.status.submitted') },
    { value: 'approved', label: i18n.global.t('teacher.course.search_form.status.approved') },
    { value: 'rejected', label: i18n.global.t('teacher.course.search_form.status.rejected') }
  ];

  searchFieldsList.value = [
    new SearchField(
      i18n.global.t('teacher.course.search_form.title.label'),
      'titleCont',
      'bx bx-search-alt',
      searchComponents.TextInputField,
      { xxl: 4, lg: 12 }
    ),
    new SearchField(
      i18n.global.t('teacher.course.search_form.description.label'),
      'descriptionCont',
      'bx bx-search-alt',
      searchComponents.TextInputField,
      { xxl: 4, lg: 6 }
    ),
    new SearchField(
      i18n.global.t('teacher.course.search_form.status.label'),
      'statusEq',
      'bx bx-upload',
      searchComponents.SingleSelectField,
      { xxl: 4, lg: 6 },
      new SearchFieldOptions({
        selectOptions: statusOptions
      })
    )
  ];

  const toCreate = () => {
    router.push({ path: '/teacher/editor/course/new' });
  };

  const handleSearch = async () => {
    isTableLoading.value = true;
    await search();
    isTableLoading.value = false;
  };

  const handleReset = async () => {
    isTableLoading.value = true;
    await reset({});
    isTableLoading.value = false;
  };

  onMounted(async () => {
    isTableLoading.value = true;
    await parseQueryAndFetch();
    isTableLoading.value = false;
  });
</script>
