<template>
  <Loader :loading="loading">
    <BContainer class="mt-4">
      <BCard>
        <BCardBody class="border-bottom">
          <SearchForm
            v-model:query="query"
            :search-fields-list="searchFieldsList"
            @reset="reset({})"
            @search="search"
          ></SearchForm>
        </BCardBody>
      </BCard>

      <!-- TODO: Add assets/images/data_empty.png -->

      <BCard v-for="item in submissions" :key="item.id" class="mb-4 shadow-sm">
        <BCardBody>
          <div class="mb-3">
            <h5>{{ item.content }}</h5>

            <VideoList :videos="item.videos" />
          </div>

          <div class="d-flex justify-content-end mt-2">
            <p class="text-primary read-more-btn" @click="openDetailModal(item)">
              {{ $t(`teacher.practice_submission.read_more_btn`) }}
              <i class="mdi mdi-arrow-right" />
            </p>
          </div>
        </BCardBody>
      </BCard>

      <Pagination :metadata="metadata" @change="changePage($event)" />
    </BContainer>

    <BModal
      v-model="isDetailModalOpen"
      :title="$t(`teacher.practice_submission.modal_detail.title`)"
      size="xl"
      title-class="font-18"
      no-footer
      lazy
      unmount-lazy
      @hide="closeDetailModal()"
    >
      <SubmissionDetail v-model:submission="submissionDetail" />
    </BModal>
  </Loader>
</template>

<script lang="ts" setup>
  import { useGoList, useBreadcrumb } from '@bachdx/b-vuse';

  import i18n from '@/plugin/i18n';

  import SearchField, { SearchFieldOptions } from '@/utils/search-fields';

  import Pagination from '@/components/base/Pagination.vue';
  import SearchForm from '@/components/base/SearchForm.vue';
  import VideoList from '@/components/base/video/VideoList.vue';
  import SubmissionDetail from '@/components/teacher/practiceSubmissions/Detail.vue';

  import useDynamicSearch from '@/composable/dynamicSearch';

  import { PracticeSubmissionInterface } from '@/utils/interface/teacher/practiceSubmission';

  import { practiceSubmissionList, practiceSubmissionDetail } from '@/services/teacher';
  import { PracticeSubmissionQueryFormModel } from '@/forms/teacher/practiceSubmission';

  const route = useRoute();
  const router = useRouter();

  const { searchFieldsList, searchComponents } = useDynamicSearch();
  const { setBreadcrumb } = useBreadcrumb({});

  setBreadcrumb({
    title: i18n.global.t('teacher.practice_submission.title'),
    items: [
      {
        text: i18n.global.t('teacher.practice_submission.title'),
        href: ''
      }
    ]
  });

  const {
    items: submissions,
    metadata,
    query,
    search,
    changePage,
    reset,
    parseQueryAndFetch
  } = useGoList({
    fetchListFnc: practiceSubmissionList,
    fetchKey: 'practiceSubmissions',
    route: route,
    queryFormModels: PracticeSubmissionQueryFormModel,
    router: router,
    perPage: 10
  });

  const loading = ref(false);
  const isDetailModalOpen = ref(false);
  const submissionDetail = ref<PracticeSubmissionInterface | undefined>(undefined);

  const statusOptions = [
    { value: 'submitted', label: i18n.global.t('teacher.practice_submission.search_form.status.submitted') },
    { value: 'in_review', label: i18n.global.t('teacher.practice_submission.search_form.status.in_review') }
  ];

  searchFieldsList.value = [
    new SearchField(
      i18n.global.t('teacher.practice_submission.search_form.status.label'),
      'statusEq',
      'bx bx-upload',
      searchComponents.SingleSelectField,
      { lg: 8 },
      new SearchFieldOptions({
        selectOptions: statusOptions
      })
    )
  ];

  async function openDetailModal(submission: PracticeSubmissionInterface) {
    const res = await practiceSubmissionDetail(submission.id.toString());
    submissionDetail.value = res.practiceSubmission;
    isDetailModalOpen.value = true;
  }

  function closeDetailModal() {
    isDetailModalOpen.value = false;
    submissionDetail.value = undefined;
  }

  onBeforeMount(async () => {
    loading.value = true;
    await parseQueryAndFetch();
    loading.value = false;
  });
</script>

<style lang="scss" scoped>
  .comment-content {
    white-space: pre-wrap;
  }

  .read-more-btn {
    cursor: pointer;
  }
</style>
