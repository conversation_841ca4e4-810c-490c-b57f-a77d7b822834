<template>
  <div class="d-flex align-items-center justify-content-end pb-2">
    <Button icon="bx-pencil" @click="toEdit()">
      {{ $t('common.edit') }}
    </Button>
  </div>

  <BContainer v-if="teacherProfile" class="py-4">
    <BCard class="mb-4 border-0 gradient-background">
      <BCardBody class="p-4">
        <span class="badge bg-primary mb-3">{{ $t('public.teacher.title') }}</span>

        <BRow class="g-4">
          <BCol md="4" class="text-center">
            <BImg
              :src="teacherProfile.imageUrl ? teacherProfile.imageUrl : dummyAvatar"
              :alt="teacherProfile.name"
              rounded="circle"
              class="teacher-avatar shadow"
              width="200"
              height="200"
            />
          </BCol>

          <BCol md="8" class="profile">
            <h1 class="display-5 fw-bold mb-2 text-white">{{ teacherProfile.name }}</h1>
            <p class="text-muted h5 mb-4" v-if="teacherProfile.award">{{ teacherProfile.award }}</p>

            <div class="about-section">
              <h3 class="h4 fw-bold mb-3 text-white">{{ $t('public.teacher.about_me') }}</h3>
              <pre class="mb-4 text-white description-text pre-content">{{ teacherProfile.description }}</pre>

              <div v-if="teacherProfile.contactEmail || teacherProfile.address" class="contact-info">
                <div v-if="teacherProfile.address" class="contact-item mb-3">
                  <i class="bx bx-map-pin contact-icon"></i>
                  <span class="contact-text">{{ teacherProfile.address }}</span>
                </div>
                <div v-if="teacherProfile.contactEmail" class="contact-item">
                  <i class="bx bx-envelope contact-icon"></i>
                  <span class="contact-text">{{ teacherProfile.contactEmail }}</span>
                </div>
              </div>
            </div>
          </BCol>
        </BRow>
      </BCardBody>
    </BCard>
  </BContainer>
</template>

<script setup lang="ts">
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';

  import { useTeacherAuthStore } from '@/store/teacher/auth';
  import { useBreadcrumb } from '@bachdx/b-vuse';
  import Button from '@/components/base/Button.vue';
  import i18n from '@/plugin/i18n';

  const teacherAuthStore = useTeacherAuthStore();
  const { teacherProfile } = storeToRefs(teacherAuthStore);
  const { setBreadcrumb } = useBreadcrumb({});
  const router = useRouter();

  const toEdit = () => {
    router.push(`/teacher/profile/edit`);
  };

  setBreadcrumb({
    title: i18n.global.t('teacher.setup.profile'),
    items: [
      {
        text: i18n.global.t('public.teacher.title'),
        href: '/teacher'
      },
      {
        text: i18n.global.t('teacher.setup.profile')
      }
    ]
  });
</script>

<style scoped lang="scss">
  $primary: #556ee6;
  $primary-light: #a8b3e8;
  $shadow-light: rgba(85, 110, 230, 0.08);
  $shadow-medium: rgba(85, 110, 230, 0.12);
  $shadow-strong: rgba(85, 110, 230, 0.16);
  $border-accent: rgba(241, 180, 76, 0.2);
  $transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .teacher-avatar {
    border: 4px solid rgba(255, 255, 255, 0.9);
    object-fit: cover;
    box-shadow: 0 8px 32px $shadow-light;
    transition: $transition-smooth;
    backdrop-filter: blur(10px);

    &:hover {
      transform: translateY(-4px) scale(1.02);
      box-shadow: 0 16px 48px $shadow-medium;
      border-color: rgba(255, 255, 255, 1);
    }
  }

  .badge {
    &.bg-primary {
      background: linear-gradient(135deg, $primary, $primary-light) !important;
      padding: 8px 20px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 600;
      letter-spacing: 0.5px;
      box-shadow: 0 4px 16px $shadow-medium;
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: $transition-smooth;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 24px $shadow-strong;
      }
    }
  }

  .gradient-background {
    background: linear-gradient(135deg, $primary 0%, $primary-light 100%);
    box-shadow: 0 8px 32px $shadow-light;
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    }
  }

  .about-section {
    margin-top: 32px;
    padding: 24px 0 0 0;
    border-top: 2px solid $border-accent;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: -1px;
      left: 0;
      width: 60px;
      height: 2px;
      background: linear-gradient(90deg, rgba(255, 255, 255, 0.6), transparent);
    }
  }

  .description-text {
    line-height: 1.7;
    font-size: 16px;
    opacity: 0.95;
  }

  .contact-info {
    margin-top: 24px;

    .contact-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-radius: 12px;
      transition: $transition-smooth;

      &:hover {
        background: rgba(255, 255, 255, 0.05);
        padding-left: 8px;
      }
    }

    .contact-icon {
      color: rgba(255, 255, 255, 0.9);
      font-size: 20px;
      margin-right: 12px;
      min-width: 24px;
      transition: $transition-smooth;
    }

    .contact-text {
      color: rgba(255, 255, 255, 0.9);
      font-size: 16px;
      font-weight: 500;
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    .card-body {
      padding: 0px !important;
    }

    .teacher-avatar {
      width: 140px !important;
      height: 140px !important;
      margin-bottom: 24px;
    }

    .gradient-background {
      padding: 20px !important;
      border-radius: 20px;
      margin: 0 8px;
    }

    .profile {
      text-align: center;
      word-break: break-word;
    }

    .about-section {
      margin-top: 24px;
      padding-top: 20px;
    }

    .contact-item {
      flex-direction: column;
      justify-content: center;
      text-align: center;

      .contact-icon {
        margin-right: 8px;
        font-size: 30px;
      }
    }

    .description-text {
      font-size: 15px;
    }
  }

  @media (max-width: 576px) {
    .gradient-background {
      margin: 0 4px;
      padding: 16px !important;
    }

    .teacher-avatar {
      width: 120px !important;
      height: 120px !important;
    }
  }
</style>
