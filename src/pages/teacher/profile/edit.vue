<template>
  <div class="container">
    <BRow>
      <BCol cols="12">
        <BCard no-body>
          <BCardBody>
            <div class="d-flex justify-content-between">
              <BCardTitle>{{ $t('teacher.setup.form.title') }}</BCardTitle>

              <Button variant="outline-primary" icon="mdi mdi-key-chain-variant" @click="handleChangePassword">{{
                $t('user.profile.change_password.title')
              }}</Button>
            </div>

            <div class="text-center">
              <BaseFormValidator name="imageUrl" class="mb-3">
                <UploadAvatar v-model:url="input.imageUrl" :site="SITES.USER" />
              </BaseFormValidator>
            </div>

            <BForm class="form-horizontal" role="form">
              <BRow>
                <BCol md="6">
                  <BaseFormValidator name="name" :label="$t('teacher.setup.form.name')" class="mb-3" required>
                    <BFormInput
                      v-model="input.name"
                      type="text"
                      :placeholder="$t('teacher.setup.form.name_placeholder')"
                    ></BFormInput>
                  </BaseFormValidator>

                  <BaseFormValidator
                    name="contactEmail"
                    :label="$t('teacher.setup.form.contact_email')"
                    class="mb-3"
                    required
                  >
                    <BFormInput
                      v-model="input.contactEmail"
                      type="email"
                      :placeholder="$t('teacher.setup.form.contact_email_placeholder')"
                    ></BFormInput>
                  </BaseFormValidator>

                  <BaseFormValidator
                    name="phoneNumber"
                    :label="$t('teacher.setup.form.phone_number')"
                    class="mb-3"
                    required
                  >
                    <BFormInput
                      v-model="input.phoneNumber"
                      type="tel"
                      disabled
                      :placeholder="$t('teacher.setup.form.phone_number_placeholder')"
                    ></BFormInput>
                  </BaseFormValidator>
                </BCol>

                <BCol md="6">
                  <BaseFormValidator name="description" :label="$t('teacher.setup.form.description')" class="mb-3">
                    <BFormTextarea
                      v-model="input.description"
                      rows="3"
                      :placeholder="$t('teacher.setup.form.description_placeholder')"
                    ></BFormTextarea>
                  </BaseFormValidator>

                  <BaseFormValidator name="address" :label="$t('teacher.setup.form.address')" class="mb-3">
                    <BFormInput
                      v-model="input.address"
                      type="text"
                      :placeholder="$t('teacher.setup.form.address_placeholder')"
                    ></BFormInput>
                  </BaseFormValidator>
                </BCol>
              </BRow>

              <div class="d-flex justify-content-end mt-4">
                <Button v-if="!blockExperimentFeatures" variant="secondary" @click="cancel" classes="me-2">
                  {{ $t('common.cancel') }}
                </Button>
                <Button @click="save">
                  {{ $t('common.save') }}
                </Button>
              </div>
            </BForm>
          </BCardBody>
        </BCard>
      </BCol>
    </BRow>
  </div>
</template>

<script setup lang="ts">
  import i18n from '@/plugin/i18n';

  import { useBreadcrumb } from '@bachdx/b-vuse';

  import { selfUpdateInfo } from '@/services/teacher';

  import { useTeacherAuthStore } from '@/store/teacher/auth';

  import { BasicFormModel } from '@/forms/teacher/setup';

  import Button from '@/components/base/Button.vue';
  import UploadAvatar from '@/components/base/UploadAvatar.vue';

  import { SITES } from '@/utils/constant';

  const router = useRouter();
  const teacherAuthStore = useTeacherAuthStore();
  const { setBreadcrumb } = useBreadcrumb({});

  const { teacherProfile } = storeToRefs(teacherAuthStore);

  setBreadcrumb({
    title: i18n.global.t('teacher.setup.profile'),
    items: [
      {
        text: i18n.global.t('public.teacher.title'),
        href: '/teacher'
      },
      {
        text: i18n.global.t('teacher.setup.profile'),
        href: '/teacher/profile/edit'
      }
      // {
      //   text: i18n.global.t('common.edit')
      // }
    ]
  });

  const input = ref(new BasicFormModel());
  const blockExperimentFeatures = import.meta.env.VITE_APP_BLOCK_EXPERIMENTAL_FEATURES.toLowerCase() === 'true';

  async function save() {
    try {
      selfUpdateInfo(input.value).then(() => {
        if (!blockExperimentFeatures) {
          router.push('/teacher/profile');
        }
      });
    } catch (error) {
      console.log(error);
    }
  }

  function cancel() {
    router.push('/teacher/profile');
  }

  function handleChangePassword() {
    router.push('/change-password');
  }

  onMounted(() => {
    input.value.assignAttributes(teacherProfile.value);
  });
</script>
