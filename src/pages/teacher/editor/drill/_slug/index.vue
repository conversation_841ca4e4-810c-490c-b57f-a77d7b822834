<template>
  <div class="position-relative">
    <DrillEditorStepper
      :editor-title="$t('teacher.top_menu.editor.drill.edit_label')"
      :is-admin="false"
      :level-options="selectOptions ? selectOptions.levelOptions : []"
      :skill-options="selectOptions ? selectOptions.skillOptions : []"
    />
  </div>
</template>

<script lang="ts" setup>
  import useSelectOptions from '@/composable/useSelectOptions';

  import DrillEditorStepper from '@/components/base/editor/drill/DrillEditorStepper.vue';

  const { fetchSelectOptions, selectOptions } = useSelectOptions();

  onMounted(async () => {
    fetchSelectOptions(['skillOptions', 'levelOptions']);
  });
</script>
