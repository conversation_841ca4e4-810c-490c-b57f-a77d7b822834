<template>
  <BRow class="justify-content-center">
    <BCol lg="6">
      <div class="text-center mb-5">
        <h2>
          {{ $t('teacher.editor.title') }}
        </h2>
      </div>
    </BCol>
  </BRow>

  <BRow class="justify-content-center">
    <BCol xxl="2" xl="3" lg="4" md="5" sm="6">
      <router-link to="editor/course/new">
        <BCard no-body class="plan-box h-100">
          <BCardBody class="p-4">
            <div class="d-flex align-items-center justify-content-center flex-column selection-container">
              <i class="bx bx-food-menu"></i>
              <h5 class="my-4">
                {{ $t('teacher.editor.course.label') }}
              </h5>
              <span class="text-muted text-center">
                {{ $t('teacher.editor.course.description') }}
              </span>
            </div>
          </BCardBody>
        </BCard>
      </router-link>
    </BCol>
    <BCol xxl="2" xl="3" lg="4" md="5" sm="6">
      <router-link to="editor/drill/new">
        <BCard no-body class="plan-box h-100">
          <BCardBody class="p-4">
            <div class="d-flex align-items-center justify-content-center flex-column selection-container">
              <i class="bx bx-border-all"></i>
              <h5 class="my-4">
                {{ $t('teacher.editor.drill.label') }}
              </h5>
              <span class="text-muted text-center">
                {{ $t('teacher.editor.drill.description') }}
              </span>
            </div>
          </BCardBody>
        </BCard>
      </router-link>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';

  import { useBreadcrumb } from '@bachdx/b-vuse';

  const { setBreadcrumb } = useBreadcrumb({});

  setBreadcrumb({
    title: i18n.global.t('teacher.top_menu.editor.label'),
    items: [
      {
        text: i18n.global.t('teacher.top_menu.editor.label'),
        href: ''
      }
    ]
  });
</script>

<style lang="scss" scoped>
  .plan-box {
    transition: all 0.2s ease-in-out;
    &:hover {
      border: 1px solid black;
      transform: scale(1.05);
    }
  }
  .selection-container {
    i {
      &::before {
        font-size: 50px;
      }
    }
  }
</style>
