<template>
  <BRow class="justify-content-center">
    <BCol lg="6">
      <div class="text-center mb-5">
        <h2>
          {{ $t('teacher.editor.course.init_step.label') }}
        </h2>
        <p class="text-muted">
          {{ $t('teacher.editor.course.init_step.description') }}
        </p>
      </div>
    </BCol>
  </BRow>

  <BRow class="justify-content-center">
    <BCol xxl="6" xl="7" lg="8" md="9" sm="10">
      <BCard no-body>
        <BCardBody class="p-4">
          <BaseFormValidator :label="$t('teacher.editor.course.init_step.form.title')" name="title" required>
            <input
              v-model="courseForm.title"
              class="form-control"
              id="title"
              name="title"
              required
              type="text"
              :placeholder="$t(`teacher.editor.course.init_step.form.placeholder`)"
            />
          </BaseFormValidator>

          <div class="mt-3 w-100 text-end">
            <Button
              append-icon="bx-right-arrow-alt"
              :disabled="isCourseSubmitButtonLoading"
              :loading="isCourseSubmitButtonLoading"
              @click="handleSubmitCourse"
            >
              {{ $t('teacher.editor.course.init_step.form.button.next') }}
            </Button>
          </div>
        </BCardBody>
      </BCard>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';

  import { useBreadcrumb } from '@bachdx/b-vuse';

  import { CourseFormModel } from '@/forms/teacher/course';

  import { courseCreate } from '@/services/teacher';

  import Button from '@/components/base/Button.vue';

  const router = useRouter();

  const { setBreadcrumb } = useBreadcrumb({});

  const courseForm = ref(new CourseFormModel());
  const isCourseSubmitButtonLoading = ref<boolean>(false);

  setBreadcrumb({
    title: i18n.global.t('teacher.top_menu.editor.course.new_label'),
    items: [
      {
        text: i18n.global.t('teacher.top_menu.editor.label'),
        href: '/teacher/editor'
      },
      {
        text: i18n.global.t('teacher.top_menu.editor.course.new_label'),
        href: ''
      }
    ]
  });

  const handleSubmitCourse = async () => {
    isCourseSubmitButtonLoading.value = true;

    try {
      const res = await courseCreate(courseForm.value.createInput());
      router.push(`/teacher/editor/course/${res.courseCreate.course.slug}`);
    } catch {
      isCourseSubmitButtonLoading.value = false;
      return;
    }

    isCourseSubmitButtonLoading.value = false;
  };
</script>
