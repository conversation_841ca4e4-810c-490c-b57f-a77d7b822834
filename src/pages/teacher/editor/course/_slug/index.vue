<template>
  <div class="position-relative">
    <CourseEditorStepper
      :course-instructional-level-options="selectOptions.courseInstructionalLevelOptions"
      :editor-title="$t('teacher.top_menu.editor.course.edit_label')"
      :level-options="selectOptions ? selectOptions.levelOptions : []"
      :package-deal-options="packageDealOptions"
      :skill-options="selectOptions ? selectOptions.skillOptions : []"
    ></CourseEditorStepper>
  </div>
</template>

<script lang="ts" setup>
  import useSelectOptions from '@/composable/useSelectOptions';

  import CourseEditorStepper from '@/components/base/editor/course/CourseEditorStepper.vue';
  import { PACKAGES_NAMES } from '@/utils/constant';

  const blockExperimentFeatures = import.meta.env.VITE_APP_BLOCK_EXPERIMENTAL_FEATURES.toLowerCase() === 'true';

  const { fetchSelectOptions, selectOptions } = useSelectOptions();

  const packageDealOptions = computed(() => {
    if (blockExperimentFeatures) {
      return selectOptions.value.packageDealOptions?.filter(option => option.label !== PACKAGES_NAMES.OFFLINE) || [];
    }
    return selectOptions.value.packageDealOptions;
  });

  onMounted(async () => {
    fetchSelectOptions(['courseInstructionalLevelOptions', 'skillOptions', 'levelOptions', 'packageDealOptions']);
  });
</script>
