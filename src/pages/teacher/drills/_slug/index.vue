<template>
  <DrillDetail v-if="drill" :drill="drill" @edit="handleEdit" :site="SITES.TEACHER" />
</template>

<script lang="ts" setup>
  import { drillShow } from '@/services/teacher/repositories/drill';

  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { SITES } from '@/utils/constant';

  import DrillDetail from '@/components/base/DrillDetail.vue';

  const route = useRoute();
  const router = useRouter();

  const drill = ref<DrillInterface>();
  const publishStatusText = ref('');

  const drillSlug = computed(() => route.params.slug.toString());

  const handleEdit = () => {
    router.push({ path: `/teacher/editor/drill/${drillSlug.value}` });
  };

  onMounted(async () => {
    const { data } = await drillShow(drillSlug.value);
    drill.value = data.drill;
    publishStatusText.value = drill.value?.status;
  });
</script>
