<template>
  <div>
    <!-- Button to open modal -->
    <button @click="openModal" class="open-btn">Open Celebration Modal</button>

    <!-- Modal -->
    <div v-if="isModalOpen" class="modal">
      <div class="modal-content">
        <canvas ref="fireworkCanvas"></canvas>
        <button @click="closeModal" class="close-btn">×</button>
        <div class="celebration-icon">🎉</div>
        <h2>Congratulations!</h2>
        <p>Celebrate your amazing achievement!</p>
      </div>
    </div>
  </div>
</template>

<script>
import confetti from 'canvas-confetti';

export default {
  data() {
    return {
      isModalOpen: false,
      fireworkInterval: null,
      confettiInterval: null,
    };
  },
  methods: {
    openModal() {
      this.isModalOpen = true;
      this.$nextTick(() => {
        this.startFireworks();
        this.startConfetti();
      });
    },
    closeModal() {
      this.isModalOpen = false;
      this.stopFireworks();
      this.stopConfetti();
    },
    startFireworks() {
      const canvas = this.$refs.fireworkCanvas;
      if (!canvas) return;
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      canvas.style.position = 'fixed';
      canvas.style.top = '0';
      canvas.style.left = '0';
      canvas.style.zIndex = '1000';

      const confettiInstance = confetti.create(canvas, {
        resize: true,
        useWorker: false,
      });

      // Immediate fireworks from multiple positions
      for (let i = 0; i < 5; i++) {
        const x = (i + 1) / 6;
        this.launchFirework(confettiInstance, x);
      }

      this.fireworkInterval = setInterval(() => {
        const x = Math.random() * (0.9 - 0.1) + 0.1;
        this.launchFirework(confettiInstance, x);
      }, 1500);

      setTimeout(() => this.stopFireworks(), 15000);
    },
    launchFirework(confettiInstance, x) {
      confettiInstance({
        particleCount: 1,
        origin: { x, y: 0.9 },
        startVelocity: 40,
        gravity: 1.2,
        scalar: 1,
        colors: ['#FFD700'],
        shapes: ['circle'],
        flat: true,
      });

      setTimeout(() => {
        confettiInstance({
          particleCount: 80,
          origin: { x, y: 0.6 },
          startVelocity: 25,
          gravity: 0.9,
          spread: 100,
          colors: ['#FF4500', '#FF69B4', '#00FF7F', '#1E90FF', '#FFFFFF'],
          shapes: ['circle', 'star'],
          scalar: 1.1,
          decay: 0.95,
        });
      }, 500);
    },
    stopFireworks() {
      if (this.fireworkInterval) {
        clearInterval(this.fireworkInterval);
        const canvas = this.$refs.fireworkCanvas;
        if (canvas) {
          const confettiInstance = confetti.create(canvas, { resize: true, useWorker: false });
          confettiInstance.reset();
        }
      }
    },
    startConfetti() {
      const canvas = this.$refs.fireworkCanvas;
      if (!canvas) return;
      const confettiInstance = confetti.create(canvas, {
        resize: true,
        useWorker: false,
      });

      this.confettiInterval = setInterval(() => {
        confettiInstance({
          particleCount: 30,
          spread: 80,
          startVelocity: 15,
          gravity: 0.6,
          origin: { x: Math.random(), y: 0 },
          colors: ['#FF4500', '#FFFF00', '#00FF00', '#0000FF', '#FFFFFF'],
          shapes: ['rectangle'],
          scalar: 0.8,
          drift: Math.random() * 0.4 - 0.2,
        });
      }, 300);

      setTimeout(() => this.stopConfetti(), 15000);
    },
    stopConfetti() {
      if (this.confettiInterval) {
        clearInterval(this.confettiInterval);
        const canvas = this.$refs.fireworkCanvas;
        if (canvas) {
          const confettiInstance = confetti.create(canvas, { resize: true, useWorker: false });
          confettiInstance.reset();
        }
      }
    },
  },
  mounted() {
    window.addEventListener('resize', () => {
      if (this.isModalOpen) {
        const canvas = this.$refs.fireworkCanvas;
        if (canvas) {
          canvas.width = window.innerWidth;
          canvas.height = window.innerHeight;
          if (this.fireworkInterval || this.confettiInterval) {
            this.startFireworks();
            this.startConfetti();
          }
        }
      }
    });
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  },
};
</script>

<style scoped>
body {
  font-family: 'Poppins', sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  margin: 0;
  background-color: #f0f0f0;
}

.open-btn {
  padding: 15px 35px;
  font-size: 18px;
  background: linear-gradient(90deg, #ff6200, #ff8c00);
  color: white;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(255, 98, 0, 0.3);
  transition: transform 0.3s, box-shadow 0.3s;
}

.open-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 98, 0, 0.4);
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001;
}

.modal-content {
  background: linear-gradient(135deg, #ff5722, #ffca28);
  padding: 40px;
  border-radius: 20px;
  text-align: center;
  color: white;
  max-width: 450px;
  width: 90%;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
  animation: scaleUpFadeIn 0.5s ease-out;
  border: 2px solid rgba(255, 215, 0, 0.4);
}

@keyframes scaleUpFadeIn {
  0% { transform: scale(0.6); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

.close-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 15px;
  width: 35px;
  height: 35px;
  font-size: 20px;
  color: white;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: background 0.3s, transform 0.3s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.4);
  transform: scale(1.1);
}

.celebration-icon {
  font-size: 90px;
  margin-bottom: 20px;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 0 0 10px rgba(255, 69, 0, 0.6);
}

h2 {
  margin: 0 0 15px;
  font-size: 36px;
  font-weight: 700;
  color: #fff3e0;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.7), 0 0 5px rgba(255, 69, 0, 0.5);
}

p {
  margin: 0 0 25px;
  font-size: 18px;
  line-height: 1.6;
  color: #fff9e6;
}

canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 1000;
  background: radial-gradient(circle, rgba(255, 87, 34, 0.1) 0%, rgba(0, 0, 0, 0) 70%); /* Subtle particle glow */
}
</style>