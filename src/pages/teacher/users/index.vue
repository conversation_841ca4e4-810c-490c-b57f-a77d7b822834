<template>
  <BRow>
    <BCol lg="12">
      <BCard no-body>
        <BCardBody class="border-bottom">
          <div class="d-flex align-items-center">
            <BCardTitle class="mb-0 flex-grow-1">
              {{ $t('teacher.user.list_title') }}
            </BCardTitle>
          </div>
        </BCardBody>

        <BCardBody class="border-bottom">
          <SearchForm
            v-model:query="query"
            :disabled="isTableLoading"
            :search-fields-list="searchFieldsList"
            @reset="handleReset"
            @search="handleSearch"
          />
        </BCardBody>

        <BCardBody>
          <UserList
            v-if="items.length > 0"
            :loading="isTableLoading"
            :metadata="metadata"
            :users="items"
            @changePage="handleChangePage"
          />

          <DataEmpty v-else-if="!isTableLoading" :message="$t('teacher.user.data_empty')" height="150" />

          <div v-if="isTableLoading" class="text-center py-5">
            <BSpinner variant="primary" />
            <p class="mt-3">{{ $t('common.loading') }}</p>
          </div>
        </BCardBody>
      </BCard>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';

  import { useGoList, useBreadcrumb } from '@bachdx/b-vuse';

  import { userList } from '@/services/teacher';

  import { UserListQueryFormModel } from '@/forms/teacher/user';

  import SearchField from '@/utils/search-fields';

  import DataEmpty from '@/components/utility/DataEmpty.vue';
  import SearchForm from '@/components/base/SearchForm.vue';
  import UserList from '@/components/teacher/users/UserList.vue';

  import useDynamicSearch from '@/composable/dynamicSearch';

  const route = useRoute();
  const router = useRouter();

  const { searchFieldsList, searchComponents } = useDynamicSearch();
  const { setBreadcrumb } = useBreadcrumb({});

  setBreadcrumb({
    title: i18n.global.t('teacher.user.title'),
    items: [
      {
        text: i18n.global.t('teacher.user.title'),
        href: '/teacher/users'
      }
    ]
  });

  searchFieldsList.value = [
    new SearchField(
      i18n.global.t('teacher.user.search_form.fields.name'),
      'nameCont',
      'bx bx-search-alt',
      searchComponents.TextInputField,
      { lg: 6 }
    ),
    new SearchField(
      i18n.global.t('teacher.user.search_form.fields.phone_number'),
      'phoneNumberCont',
      'bx bx-phone',
      searchComponents.TextInputField,
      { lg: 6 }
    )
  ];

  const { items, metadata, search, changePage, query, reset, parseQueryAndFetch } = useGoList({
    fetchListFnc: userList,
    fetchKey: 'users',
    route: route,
    router: router,
    queryFormModels: UserListQueryFormModel,
    perPage: 10
  });

  const isTableLoading = ref<boolean>(false);

  const handleSearch = async () => {
    isTableLoading.value = true;
    try {
      await search();
    } finally {
      isTableLoading.value = false;
    }
  };

  const handleReset = async () => {
    isTableLoading.value = true;
    try {
      await reset({});
    } finally {
      isTableLoading.value = false;
    }
  };

  const handleChangePage = async (pageData: { page: number }) => {
    isTableLoading.value = true;
    try {
      await changePage(pageData);
    } finally {
      isTableLoading.value = false;
    }
  };

  onMounted(async () => {
    isTableLoading.value = true;
    try {
      await parseQueryAndFetch();
    } finally {
      isTableLoading.value = false;
    }
  });
</script>
