<template>
  <div class="container">
    <BRow class="mb-4">
      <BCol cols="12">
        <h2 class="mb-3">{{ $t('teacher.setup.title') }}</h2>
      </BCol>
    </BRow>
    <BRow>
      <BCol cols="12">
        <BCard no-body>
          <BCardBody>
            <BCardTitle>{{ $t('teacher.setup.form.title') }}</BCardTitle>
            <p class="card-title-desc mb-4">
              {{ $t('teacher.setup.form.description_text') }}
            </p>

            <div class="text-center">
              <BaseFormValidator name="imageUrl" class="mb-3">
                <UploadAvatar v-model:url="input.imageUrl" :site="SITES.USER" />
              </BaseFormValidator>
            </div>

            <BForm class="form-horizontal" role="form">
              <BRow>
                <BCol md="6">
                  <BaseFormValidator name="name" :label="$t('teacher.setup.form.name')" class="mb-3" required>
                    <BFormInput
                      v-model="input.name"
                      type="text"
                      :placeholder="$t('teacher.setup.form.name_placeholder')"
                    ></BFormInput>
                  </BaseFormValidator>

                  <BaseFormValidator
                    name="contactEmail"
                    :label="$t('teacher.setup.form.contact_email')"
                    class="mb-3"
                    required
                  >
                    <BFormInput
                      v-model="input.contactEmail"
                      type="email"
                      :placeholder="$t('teacher.setup.form.contact_email_placeholder')"
                    ></BFormInput>
                  </BaseFormValidator>

                  <BaseFormValidator
                    name="phoneNumber"
                    :label="$t('teacher.setup.form.phone_number')"
                    class="mb-3"
                    required
                  >
                    <BFormInput
                      v-model="input.phoneNumber"
                      type="tel"
                      disabled
                      :placeholder="$t('teacher.setup.form.phone_number_placeholder')"
                    ></BFormInput>
                  </BaseFormValidator>
                </BCol>

                <BCol md="6">
                  <BaseFormValidator name="description" :label="$t('teacher.setup.form.description')" class="mb-3">
                    <BFormTextarea
                      v-model="input.description"
                      rows="3"
                      :placeholder="$t('teacher.setup.form.description_placeholder')"
                    ></BFormTextarea>
                  </BaseFormValidator>

                  <BaseFormValidator name="address" :label="$t('teacher.setup.form.address')" class="mb-3">
                    <BFormInput
                      v-model="input.address"
                      type="text"
                      :placeholder="$t('teacher.setup.form.address_placeholder')"
                    ></BFormInput>
                  </BaseFormValidator>
                </BCol>
              </BRow>

              <div class="d-flex justify-content-end mt-4">
                <Button variant="secondary" @click="cancel" classes="me-2">
                  {{ $t('common.cancel') }}
                </Button>
                <Button @click="save">
                  {{ $t('common.save') }}
                </Button>
              </div>
            </BForm>
          </BCardBody>
        </BCard>
      </BCol>
    </BRow>
  </div>
</template>

<script setup lang="ts">
  import { setupInfo } from '@/services/teacher';

  import { ROUTE_PATH, SITES } from '@/utils/constant';

  import { useTeacherAuthStore } from '@/store/teacher/auth';

  import { BasicFormModel } from '@/forms/teacher/setup';

  import Button from '@/components/base/Button.vue';
  import UploadAvatar from '@/components/base/UploadAvatar.vue';

  const router = useRouter();
  const teacherAuthStore = useTeacherAuthStore();

  const { teacherProfile } = storeToRefs(teacherAuthStore);

  const input = ref(new BasicFormModel());

  async function save() {
    try {
      setupInfo(input.value).then(() => {
        router.push(ROUTE_PATH.TEACHER_COURSES);
      });
    } catch (error) {
      console.log(error);
    }
  }

  function cancel() {
    router.push('/');
  }

  onMounted(async () => {
    await teacherAuthStore.getProfile();
    input.value.assignAttributes(teacherProfile.value);
  });
</script>
