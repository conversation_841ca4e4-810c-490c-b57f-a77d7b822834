<template>
  <BRow>
    <BCol lg="12">
      <BCard no-body>
        <BCardBody class="border-bottom">
          <div class="d-flex align-items-center">
            <BCardTitle class="mb-0 flex-grow-1">
              {{ $t('admin.user.list_title') }}
            </BCardTitle>
          </div>
        </BCardBody>

        <BCardBody class="border-bottom">
          <SearchForm
            v-model:query="query"
            :disabled="isTableLoading"
            :loading="isTableLoading"
            :search-fields-list="searchFieldsList"
            @reset="handleReset"
            @search="handleSearch"
          />
        </BCardBody>

        <BCardBody>
          <UserList
            v-if="items.length > 0"
            :loading="isTableLoading"
            :metadata="metadata"
            :users="items"
            @changePage="changePage"
          />

          <DataEmpty v-else :message="$t('admin.user.data_empty')" height="150" />
        </BCardBody>
      </BCard>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';

  import { useGoList, useBreadcrumb } from '@bachdx/b-vuse';

  import { userList } from '@/services/admin';

  import { UserListQueryFormModel } from '@/forms/admin/user';

  import SearchField from '@/utils/search-fields';

  import DataEmpty from '@/components/utility/DataEmpty.vue';
  import SearchForm from '@/components/base/SearchForm.vue';
  import UserList from '@/components/admin/users/UserList.vue';

  import useDynamicSearch from '@/composable/dynamicSearch';

  const route = useRoute();
  const router = useRouter();

  const { searchFieldsList, searchComponents } = useDynamicSearch();
  const { setBreadcrumb } = useBreadcrumb({});

  setBreadcrumb({
    title: i18n.global.t('admin.user.title'),
    items: [
      {
        text: i18n.global.t('admin.user.title'),
        href: '/admin/users'
      }
    ]
  });

  searchFieldsList.value = [
    new SearchField(
      i18n.global.t('admin.user.search_form.fields.name'),
      'nameCont',
      'bx bx-search-alt',
      searchComponents.TextInputField,
      { lg: 6 }
    ),
    new SearchField(
      i18n.global.t('admin.user.search_form.fields.phone_number'),
      'phoneNumberCont',
      'bx bx-phone',
      searchComponents.TextInputField,
      { lg: 6 }
    )
  ];

  const { items, metadata, search, changePage, query, reset, parseQueryAndFetch } = useGoList({
    fetchListFnc: userList,
    fetchKey: 'users',
    route: route,
    router: router,
    queryFormModels: UserListQueryFormModel,
    perPage: 10
  });

  const isTableLoading = ref<boolean>(false);

  const handleSearch = async () => {
    isTableLoading.value = true;
    await search();
    isTableLoading.value = false;
  };

  const handleReset = async () => {
    isTableLoading.value = true;
    await reset({});
    isTableLoading.value = false;
  };

  onMounted(async () => {
    isTableLoading.value = true;
    await parseQueryAndFetch();
    isTableLoading.value = false;
  });
</script>
