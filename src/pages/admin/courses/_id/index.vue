<template>
  <BRow>
    <BCol lg="3">
      <BCard no-body>
        <BCardBody>
          <BCardTitle class="mb-4 pb-3 border-bottom">
            <span>{{ $t('admin.course.tabs.basic_info') }}</span>
          </BCardTitle>
          <div>
            <CourseView :course="course || {}" @refresh="fetchCourse()" />
          </div>
        </BCardBody>
      </BCard>

      <CensorHistoryList :histories="course?.courseCensorHistories" />
    </BCol>

    <BCol lg="9">
      <BCard no-body>
        <BCardBody>
          <BCardTitle class="mb-4 pb-3 border-bottom">
            <span>{{ $t('admin.course.tabs.course_content') }}</span>
          </BCardTitle>
          <div>
            <CourseSectionList :list="items" :loading="false" @refresh="fetchCourse" :courseId="courseId" />
          </div>
        </BCardBody>
      </BCard>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  // ==== Utilities =====
  import { useBreadcrumb } from '@bachdx/b-vuse';
  import CourseView from '@/components/admin/courses/CourseView.vue';
  import i18n from '@/plugin/i18n';
  import CensorHistoryList from '@/components/admin/censorHistories/CensorHistoryList.vue';

  // ===== Components =====
  import CourseSectionList from '@/components/admin/courses/course_section/CourseSectionList.vue';

  // ===== APIs =====
  import { adminCourseDetail } from '@/services/admin';

  // ===== Types =====
  import { CourseFormInterface } from '@/utils/interface/admin/course';
  import { CourseSectionForm } from '@/forms/admin/courseSection';

  const route = useRoute();

  const course = ref<CourseFormInterface>();
  const items = ref<CourseSectionForm[]>([]);
  const loading = ref(true);

  const { setBreadcrumb } = useBreadcrumb({});
  const courseId = computed(() => route.params.id.toString());

  setBreadcrumb({
    title: i18n.global.t('admin.course.title'),
    items: [
      {
        text: i18n.global.t('common.dashboard'),
        href: 'dashboard'
      },
      {
        text: i18n.global.t('admin.course.title'),
        href: '/admin/courses'
      },
      {
        text: `${courseId.value}`
      }
    ]
  });

  const fetchCourse = async () => {
    if (!courseId.value) return;
    loading.value = true;
    const res = await adminCourseDetail(courseId.value);

    if (res.course) {
      course.value = res.course;
      items.value = res.course.courseSections || [];
    }
  };

  onMounted(async () => {
    await fetchCourse();
    loading.value = false;
  });
</script>
