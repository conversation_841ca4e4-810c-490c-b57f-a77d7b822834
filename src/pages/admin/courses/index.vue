<template>
  <BRow>
    <BCol lg="12">
      <BCard no-body>
        <BCardBody class="border-bottom">
          <div class="d-flex align-items-center">
            <BCardTitle class="mb-0 flex-grow-1">
              {{ $t('admin.course.title') }}
            </BCardTitle>
          </div>
        </BCardBody>

        <BCardBody class="border-bottom">
          <SearchForm
            v-model:query="query"
            :disabled="isTableLoading"
            :loading="isTableLoading"
            :search-fields-list="searchFieldsList"
            @reset="handleReset"
            @search="handleSearch"
          ></SearchForm>
        </BCardBody>

        <BCardBody>
          <CourseList
            v-if="items.length > 0"
            :courses="items"
            :loading="isTableLoading"
            :metadata="metadata"
            @fetch-list="changePage"
          ></CourseList>

          <DataEmpty v-else :message="$t('admin.course.data_empty')" height="150" />
        </BCardBody>
      </BCard>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';

  import { useGoList, useBreadcrumb } from '@bachdx/b-vuse';

  import { adminCourseList } from '@/services/admin';

  import { CourseListQueryFormModel } from '@/forms/admin/course';

  import SearchField, { SearchFieldOptions } from '@/utils/search-fields';
  import DataEmpty from '@/components/utility/DataEmpty.vue';

  import CourseList from '@/components/admin/courses/CourseList.vue';
  import SearchForm from '@/components/base/SearchForm.vue';

  import useDynamicSearch from '@/composable/dynamicSearch';
  import useSelectOptions from '@/composable/useSelectOptions';

  const route = useRoute();
  const router = useRouter();

  const { searchFieldsList, searchComponents } = useDynamicSearch();
  const { setBreadcrumb } = useBreadcrumb({});
  const { fetchSelectOptions, selectOptions } = useSelectOptions();

  const { items, metadata, changePage, parseQueryAndFetch, query, reset, search } = useGoList({
    fetchListFnc: adminCourseList,
    fetchKey: 'courses',
    route: route,
    router: router,
    queryFormModels: CourseListQueryFormModel,
    perPage: 12
  });

  setBreadcrumb({
    title: i18n.global.t('admin.course.title'),
    items: [
      {
        text: i18n.global.t('admin.course.title'),
        href: ''
      }
    ]
  });

  const isTableLoading = ref(true);

  const updateSearchFieldsList = () => {
    searchFieldsList.value = [
      new SearchField(
        i18n.global.t('admin.course.search_form.title.label'),
        'titleCont',
        'bx bx-search-alt',
        searchComponents.TextInputField,
        { xxl: 4, lg: 12 }
      ),
      new SearchField(
        i18n.global.t('admin.course.search_form.teacher.label'),
        'teacherIdEq',
        'bx bx-user',
        searchComponents.SingleSelectField,
        { xxl: 4, lg: 6 },
        new SearchFieldOptions({
          selectOptions: selectOptions.value ? selectOptions.value.teacherOptions : []
        })
      ),
      new SearchField(
        i18n.global.t('admin.course.search_form.status.label'),
        'statusIn',
        'bx bx-upload',
        searchComponents.MultipleSelectField,
        { xxl: 4, lg: 6 },
        new SearchFieldOptions({
          selectOptions: selectOptions.value?.courseStatusOptions || []
        })
      )
    ];
  };

  const handleSearch = async () => {
    isTableLoading.value = true;
    await search();
    isTableLoading.value = false;
  };

  const handleReset = async () => {
    isTableLoading.value = true;
    await reset({});
    isTableLoading.value = false;
  };

  onMounted(async () => {
    isTableLoading.value = true;
    await Promise.all([
      fetchSelectOptions(['courseStatusOptions', 'teacherOptions'], { courseOptionExcludes: ['draft'] }),
      parseQueryAndFetch()
    ]);
    updateSearchFieldsList();
    isTableLoading.value = false;
  });
</script>
