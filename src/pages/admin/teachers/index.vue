<template>
  <BRow>
    <BCol lg="12">
      <BCard no-body>
        <BCardBody class="border-bottom">
          <div class="d-flex align-items-center">
            <BCardTitle class="mb-0 flex-grow-1">
              {{ $t('admin.teacher.list_title') }}
            </BCardTitle>
          </div>
        </BCardBody>

        <BCardBody class="border-bottom">
          <SearchForm
            v-model:query="query"
            :disabled="isTableLoading"
            :loading="isTableLoading"
            :search-fields-list="searchFieldsList"
            @reset="handleReset"
            @search="handleSearch"
          ></SearchForm>
        </BCardBody>

        <BCardBody>
          <TeacherList
            v-if="items.length > 0"
            :loading="isTableLoading"
            :metadata="metadata"
            :teachers="items"
            @fetch-list="changePage"
            @toggle-invite="handleToggleInvite"
          ></TeacherList>

          <DataEmpty v-else :message="$t('admin.teacher.data_empty')" height="150" />
        </BCardBody>
      </BCard>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';

  import { useGoList, useBreadcrumb } from '@bachdx/b-vuse';

  import { permitTeacherInviteStudent, teacherList } from '@/services/admin/repositories/teacher';

  import { TeacherListQueryFormModel } from '@/forms/admin/teacher';

  import SearchField from '@/utils/search-fields';
  import { SwalIconOptions, SwalOptions } from '@/utils/swal-options';

  import DataEmpty from '@/components/utility/DataEmpty.vue';
  import SearchForm from '@/components/base/SearchForm.vue';
  import TeacherList from '@/components/admin/teachers/TeacherList.vue';

  import useDynamicSearch from '@/composable/dynamicSearch';
  import useSwal from '@/composable/swal';

  const route = useRoute();
  const router = useRouter();

  const { confirming } = useSwal();
  const { searchFieldsList, searchComponents } = useDynamicSearch();
  const { setBreadcrumb } = useBreadcrumb({});

  const { items, metadata, search, changePage, query, reset, parseQueryAndFetch } = useGoList({
    fetchListFnc: teacherList,
    fetchKey: 'teachers',
    route: route,
    router: router,
    queryFormModels: TeacherListQueryFormModel,
    perPage: 10
  });

  setBreadcrumb({
    title: i18n.global.t('admin.teacher.title'),
    items: [
      {
        text: i18n.global.t('admin.teacher.title'),
        href: '/admin/teachers'
      }
    ]
  });

  const isTableLoading = ref<boolean>(false);

  searchFieldsList.value = [
    new SearchField(
      i18n.global.t('admin.teacher.search_form.fields.name'),
      'nameCont',
      'bx bx-search-alt',
      searchComponents.TextInputField,
      { lg: 6 }
    ),
    new SearchField(
      i18n.global.t('admin.teacher.search_form.fields.phone_number'),
      'phoneNumberCont',
      'bx bx-phone',
      searchComponents.TextInputField,
      { lg: 6 }
    )
  ];

  const handleToggleInvite = async (id: number) => {
    const title = i18n.global.t('admin.teacher.confirm_modal.invite_permission.title');
    const message = i18n.global.t('admin.teacher.confirm_modal.invite_permission.content');

    const confirmed = await confirming(
      new SwalOptions({
        title: title,
        html: message,
        icon: SwalIconOptions.Warning
      })
    );

    if (!confirmed) {
      await parseQueryAndFetch();
      return;
    }
    if (!id) return;

    await permitTeacherInviteStudent(String(id));
    await parseQueryAndFetch();
  };

  const handleSearch = async () => {
    isTableLoading.value = true;
    await search();
    isTableLoading.value = false;
  };

  const handleReset = async () => {
    isTableLoading.value = true;
    await reset({});
    isTableLoading.value = false;
  };

  onMounted(async () => {
    isTableLoading.value = true;
    await parseQueryAndFetch();
    isTableLoading.value = false;
  });
</script>
