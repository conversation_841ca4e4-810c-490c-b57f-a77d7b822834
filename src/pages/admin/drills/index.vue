<template>
  <BRow>
    <BCol lg="12">
      <BCard no-body>
        <BCardBody class="border-bottom">
          <div class="d-flex align-items-center">
            <BCardTitle class="mb-0 flex-grow-1">
              {{ $t('admin.drills.title') }}
            </BCardTitle>

            <div class="flex-shrink-0">
              <Button icon="bx-plus" @click="toEditor">
                {{ $t('common.create_btn') }}
              </Button>
            </div>
          </div>
        </BCardBody>

        <BCardBody class="border-bottom">
          <SearchForm
            v-model:query="query"
            :disabled="isTableLoading"
            :loading="isTableLoading"
            :search-fields-list="searchFieldsList"
            @reset="handleReset"
            @search="handleSearch"
          ></SearchForm>
        </BCardBody>

        <BCardBody>
          <DrillsList
            v-if="items.length > 0"
            :drills="items"
            :loading="isTableLoading"
            :metadata="metadata"
            @delete="handleDelete"
            @edit="handleEdit"
            @fetch-list="changePage"
          ></DrillsList>

          <DataEmpty v-else :message="$t('admin.drills.data_empty')" height="150" />
        </BCardBody>
      </BCard>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import i18n from '@/plugin/i18n';

  import { useGoList, useBreadcrumb } from '@bachdx/b-vuse';

  import { drillList, drillDelete } from '@/services/admin/repositories/drill';

  import { DrillListQueryFormModel } from '@/forms/admin/drill';

  import { SwalIconOptions, SwalOptions } from '@/utils/swal-options';
  import SearchField, { SearchFieldOptions } from '@/utils/search-fields';

  import Button from '@/components/base/Button.vue';
  import DataEmpty from '@/components/utility/DataEmpty.vue';
  import DrillsList from '@/components/admin/drills/DrillsList.vue';
  import SearchForm from '@/components/base/SearchForm.vue';

  import useDynamicSearch from '@/composable/dynamicSearch';
  import useSelectOptions from '@/composable/useSelectOptions';
  import useSwal from '@/composable/swal';

  const route = useRoute();
  const router = useRouter();

  const { confirming } = useSwal();
  const { fetchSelectOptions, selectOptions } = useSelectOptions();
  const { searchFieldsList, searchComponents } = useDynamicSearch();
  const { setBreadcrumb } = useBreadcrumb({});

  const { items, metadata, search, changePage, query, reset, parseQueryAndFetch } = useGoList({
    fetchListFnc: drillList,
    fetchKey: 'drills',
    route: route,
    queryFormModels: DrillListQueryFormModel,
    router: router,
    perPage: 9
  });

  setBreadcrumb({
    title: i18n.global.t('admin.top_menu.drills.label'),
    items: [
      {
        text: i18n.global.t('admin.top_menu.drills.label'),
        href: ''
      }
    ]
  });

  const isTableLoading = ref<boolean>(false);

  const updateSearchFieldsList = () => {
    searchFieldsList.value = [
      new SearchField(
        i18n.global.t('admin.drills.search_form.fields.title'),
        'titleCont',
        'bx bx-search-alt',
        searchComponents.TextInputField,
        { xxl: 4, lg: 12 }
      ),
      new SearchField(
        i18n.global.t('admin.drills.search_form.fields.level'),
        'levelIn',
        'bx bx-select-multiple',
        searchComponents.MultipleSelectField,
        { xxl: 4, lg: 6 },
        new SearchFieldOptions({
          selectOptions: selectOptions.value ? selectOptions.value.levelOptions : [],
          multipleSelectOptions: {
            label: 'description'
          }
        })
      ),
      new SearchField(
        i18n.global.t('admin.drills.search_form.fields.skill'),
        'skillIDIn',
        'bx bx-select-multiple',
        searchComponents.MultipleSelectField,
        { xxl: 4, lg: 6 },
        new SearchFieldOptions({
          selectOptions: selectOptions.value ? selectOptions.value.skillOptions : []
        })
      )
    ];
  };

  const handleSearch = async () => {
    isTableLoading.value = true;
    await search();
    isTableLoading.value = false;
  };

  const handleReset = async () => {
    isTableLoading.value = true;
    await reset({});
    isTableLoading.value = false;
  };

  watch(() => selectOptions.value, updateSearchFieldsList, { immediate: true });

  const toEditor = () => {
    router.push({ path: 'editor/drill/new' });
  };

  const handleEdit = (id: number) => {
    router.push({ path: `editor/drill/${id}` });
  };

  const handleDelete = async (id: number) => {
    const message = i18n.global.t('admin.drills.delete_modal.message');

    const confirmed = await confirming(
      new SwalOptions({
        html: message,
        icon: SwalIconOptions.Warning
      })
    );

    if (!confirmed) return;
    if (!id) return;

    await drillDelete(String(id));

    if (items.value.length <= 1 && metadata.value.page >= 2) {
      metadata.value.page--;
      changePage(metadata.value);
    } else {
      parseQueryAndFetch();
    }
  };

  onMounted(async () => {
    await fetchSelectOptions(['skillOptions', 'levelOptions']);
    isTableLoading.value = true;
    await parseQueryAndFetch();
    isTableLoading.value = false;
  });
</script>
