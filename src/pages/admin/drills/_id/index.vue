<template>
  <DrillDetail v-if="drill" :drill="drill" @edit="handleEdit(drill.id)" @fetch="fetchDrill" :site="SITES.ADMIN" />
</template>

<script lang="ts" setup>
  import { drillShow } from '@/services/admin/repositories/drill';

  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { SITES } from '@/utils/constant';

  import DrillDetail from '@/components/base/DrillDetail.vue';

  const route = useRoute();
  const router = useRouter();

  const drill = ref<DrillInterface>();

  const drillId = computed(() => route.params.id.toString());
  const publishStatusText = ref('');

  const handleEdit = (id: number) => {
    router.push({ path: `/admin/editor/drill/${id}` });
  };

  const fetchDrill = async () => {
    const { data } = await drillShow(drillId.value);
    drill.value = data.drill;
    publishStatusText.value = drill.value?.status;
  };

  onMounted(async () => {
    fetchDrill();
  });
</script>
