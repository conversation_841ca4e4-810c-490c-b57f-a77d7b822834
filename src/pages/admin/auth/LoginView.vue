<template>
  <BRow class="justify-content-center">
    <BCol md="8" lg="6" xl="5">
      <BCard no-body class="overflow-hidden">
        <div class="bg-primary-subtle">
          <BRow>
            <BCol cols="7">
              <div class="text-primary p-4">
                <h5 class="text-primary">{{ $t('public.login.title') }}</h5>
                <p>{{ $t('public.login.sub_title') }}</p>
              </div>
            </BCol>
            <BCol cols="5" class="align-self-end">
              <img src="@/assets/images/profile-img.png" class="img-fluid" />
            </BCol>
          </BRow>
        </div>
        <BCardBody class="pt-0">
          <BForm class="p-2">
            <BaseFormValidator name="identifier" :label="$t('public.login.form.username')" class="mb-3">
              <BFormInput
                v-model="input.identifier"
                autocomplete="username"
                type="text"
                :placeholder="$t('public.login.form.username_placeholder')"
              ></BFormInput>
            </BaseFormValidator>

            <BaseFormValidator name="password" :label="$t('public.login.form.password')" class="mb-3">
              <BFormInput
                v-model="input.password"
                autocomplete="current-password"
                type="password"
                :placeholder="$t('public.login.form.password_placeholder')"
              ></BFormInput>
            </BaseFormValidator>

            <!-- <BFormCheckboxGroup v-model="input.rememberMe" class="form-check me-2" :value="true" :unchecked-value="false"> Remember me </BFormCheckboxGroup> -->
            <div class="mt-4 d-grid">
              <BButton
                class="btn-block bg-gradient"
                loading-text=""
                type="submit"
                variant="primary"
                :disabled="isLoading"
                :loading="isLoading"
                @click.prevent="handleLogin"
              >
                {{ $t('public.login.login_btn') }}
              </BButton>
            </div>
          </BForm>
        </BCardBody>
      </BCard>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import { useAuthAdminStore } from '@/store/admin/auth';
  import { SignInInputInterface } from '@/utils/interface/user/auth';

  const authAdminStore = useAuthAdminStore();

  const isLoading = ref<boolean>(false);
  const input = ref<SignInInputInterface>({
    identifier: '',
    password: ''
  });

  const handleLogin = async () => {
    isLoading.value = true;
    await authAdminStore.login(input.value);
    isLoading.value = false;
  };
</script>
