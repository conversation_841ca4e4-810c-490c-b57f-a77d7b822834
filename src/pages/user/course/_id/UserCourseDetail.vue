<template>
  <BContainer fluid class="p-0">
    <template v-if="currentLesson">
      <CourseDetailSection
        v-if="course"
        :course="course"
        :currentLesson="currentLesson"
        :loading="loading"
        @refreshMyCourse="async () => await fetchMyCourse()"
      />
    </template>
    <template v-else-if="!loading">
      <BCol>
        <div class="alert alert-warning d-flex justify-content-between align-items-center mb-3">
          <div>
            <i class="mdi mdi-alert-circle me-2"></i>
            {{ $t('user.user_course.unlaunching_course') }}
          </div>
        </div>
      </BCol>
    </template>
  </BContainer>
</template>

<script lang="ts" setup>
  import CourseDetailSection from '@/components/user/course/CourseDetailSection.vue';

  import { showMyCourse } from '@/services/user/repositories/userCourse';

  import {
    courseUserSetLastAccessItemSlug,
    setSectionItemStatus,
    showMyCourseSectionItem
  } from '@/services/user/repositories/userCourseSectionItem';

  import type { CourseSectionItemInterface } from '@/utils/interface/user/courseSectionItem';
  import type { UserCourseInterface } from '@/utils/interface/user/userCourse';

  const route = useRoute();
  const router = useRouter();

  const course = ref<UserCourseInterface>();
  const currentLesson = ref<CourseSectionItemInterface>();
  const loading = ref(false);

  const courseSlug = computed(() => route.params.id.toString());
  const lessonSlug = computed(() => route.query.courseContentSlug?.toString());

  const fetchMyCourse = async () => {
    loading.value = true;

    const { myCourse } = await showMyCourse(courseSlug.value);
    course.value = myCourse;

    loading.value = false;
  };

  const fetchLesson = async () => {
    loading.value = true;

    const { myCourseSectionItem } = await showMyCourseSectionItem(courseSlug.value, lessonSlug.value);
    if (!myCourseSectionItem.currentUserSectionItem && course.value?.id) {
      await setSectionItemStatus(String(myCourseSectionItem.id), String(course.value.id), 'studying');
    }

    currentLesson.value = myCourseSectionItem;

    if (!lessonSlug.value) {
      router.replace({
        name: route.name as string,
        params: route.params,
        query: {
          ...route.query,
          courseContentSlug: myCourseSectionItem.slug
        }
      });
    }

    loading.value = false;
  };

  watch(lessonSlug, (newVal, oldVal) => {
    if (newVal !== oldVal && !!oldVal && typeof newVal === 'string') {
      fetchLesson().then(() => courseUserSetLastAccessItemSlug(newVal, String(course.value?.id)));
    }
  });

  onBeforeMount(async () => {
    await fetchMyCourse();
    if (course.value?.status === 'approved') {
      await fetchLesson();

      if (typeof lessonSlug.value === 'string') {
        courseUserSetLastAccessItemSlug(lessonSlug.value, String(course.value?.id));
      }
    }
  });
</script>

<style lang="scss" scoped>
  .alert {
    margin-top: 100px;
  }
</style>
