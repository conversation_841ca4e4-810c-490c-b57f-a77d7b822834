<template>
  <BContainer fluid class="p-0">
    <template v-if="currentLesson">
      <CourseDetailSection
        ref="courseDetailSectionRef"
        v-if="course"
        :course="course"
        :currentLesson="currentLesson"
        :loading="loading"
        isPreview
        blockAccess
        @fetchLesson="fetchLesson"
      />
    </template>
    <template v-else-if="!loading">
      <BCol>
        <div class="alert alert-warning d-flex justify-content-between align-items-center mb-3">
          <div>
            <i class="mdi mdi-alert-circle me-2"></i>
            {{ $t('user.user_course.unlaunching_course') }}
          </div>
        </div>
      </BCol>
    </template>
  </BContainer>
</template>

<script setup lang="ts">
  import CourseDetailSection from '@/components/user/course/CourseDetailSection.vue';
  import { coursePreview, courseSectionItemPreview } from '@/services/user/repositories/userCourse';

  const route = useRoute();
  const router = useRouter();
  const course = ref<any>();
  const currentLesson = ref<any>();
  const loading = ref(false);
  const lessonSlug = ref<string>('');
  const courseSlug = ref<string>('');

  const fetchLesson = async (lessonSlug: string) => {
    loading.value = true;

    const res = await courseSectionItemPreview(courseSlug.value, lessonSlug);

    currentLesson.value = res.courseSectionItemPreview;

    loading.value = false;
  };

  const findFirstFreeLesson = (sections: any[]) => {
    for (const section of sections) {
      if (section.courseSectionItems?.length > 0) {
        const freeItem = section.courseSectionItems.find((item: any) => item.isFree);
        if (freeItem) {
          return freeItem.slug;
        }
      }
    }

    for (const section of sections) {
      if (section.courseSectionItems?.length > 0) {
        return section.courseSectionItems[0].slug;
      }
    }

    return null;
  };

  onMounted(async () => {
    await coursePreview(route.params.id as string).then((res: any) => {
      course.value = res.coursePreview;
    });

    lessonSlug.value = findFirstFreeLesson(course.value.courseSections || []);

    courseSlug.value = route.params.id as string;

    if (lessonSlug.value) {
      await fetchLesson(lessonSlug.value);
      router.replace({
        query: { ...route.query, courseContentSlug: lessonSlug.value }
      });
    }
  });
</script>
