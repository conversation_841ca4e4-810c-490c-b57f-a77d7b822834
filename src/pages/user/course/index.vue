<template>
  <BContainer class="user-course-view pt-5">
    <template v-if="hasCourses">
      <BRow v-for="(category, index) in myCourseCategories" :key="index" class="mb-4">
        <div v-if="category.list.length">
          <FadeInUp>
            <h4 class="mb-2 mb-lg-3">
              <i class="mdi font-size-20 me-1 category-icon" :class="category.icon"></i>
              {{ $t(`user.user_course.category.${category.title}`) }}
            </h4>
          </FadeInUp>

          <FadeInLeft>
            <BaseCarousel :items="category.list">
              <template #default="{ item }">
                <CourseCard :course="item" is-my-course />
              </template>
            </BaseCarousel>
          </FadeInLeft>
        </div>
      </BRow>
    </template>
    <BRow v-if="hasDrills">
      <FadeInUp>
        <h4 class="mb-2 mb-lg-3">
          <span style="display: flex; align-items: center; gap: 0.5rem">
            <i class="mdi mdi-billiards-rack font-size-20 me-1 category-icon drill-icon"></i>
            <span>{{ $t('user.drill.drill_list_bought') }}</span>
          </span>
        </h4>
      </FadeInUp>
      <FadeInLeft>
        <BaseCarousel :items="myDrillList" class="user-drill-list">
          <template #default="{ item }">
            <BaseDrillCard
              :drill="item"
              :site="SITES.USER"
              @on-view-detail-click="onViewDetailDrill(item)"
              @on-teacher-click="onViewTeacherClick(item)"
            />
          </template>
        </BaseCarousel>
      </FadeInLeft>
    </BRow>
    <template v-else-if="!hasCourses && !hasDrills">
      <BRow>
        <DataEmpty :message="$t('user.user_course.data_course_drill_empty')" height="150" />
      </BRow>
    </template>
  </BContainer>
</template>

<script lang="ts" setup>
  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { UserCourseInterface } from '@/utils/interface/user/userCourse';

  import BaseCarousel from '@/components/base/BaseCarousel.vue';
  import DataEmpty from '@/components/utility/DataEmpty.vue';

  import CourseCard from '@/components/base/CourseCard.vue';
  import BaseDrillCard from '@/components/base/drill/BaseDrillCard.vue';

  import { drillListUserPurchased } from '@/services/user/repositories/drill';
  import { myCourseList } from '@/services/user/repositories/userCourse';
  import { OWNER_TYPE_ENUMS, SITES } from '@/utils/constant';
  const router = useRouter();

  const loading = ref(true);
  const myCourseCategories = ref<{ icon: string; title: string; list: UserCourseInterface[] }[]>([]);
  const myDrillList = ref<DrillInterface[]>([]);

  const categoryConfigs = [
    { icon: 'mdi-book-check-outline text-primary', title: 'recent_joined', queryKey: 'recentJoined' },
    { icon: 'mdi-progress-check text-orange', title: 'in_progress', queryKey: 'inProgress' },
    { icon: 'mdi-check-circle-outline text-success', title: 'completed', queryKey: 'completed' }
  ];

  const hasCourses = computed(() => {
    return myCourseCategories.value.some(category => category.list.length > 0);
  });
  const hasDrills = computed(() => {
    return myDrillList.value && myDrillList.value.length > 0;
  });

  const fetchCourses = async () => {
    const requests = categoryConfigs.map(config =>
      myCourseList({
        input: { page: 1, perPage: 20 },
        query: { categoryCont: config.queryKey }
      })
    );

    const results = await Promise.all(requests);

    myCourseCategories.value = results.map((res, i) => ({
      icon: categoryConfigs[i].icon,
      title: categoryConfigs[i].title,
      list: res.myCourses.collection ?? []
    }));
  };

  const fetchMyDrillList = async () => {
    const res = await drillListUserPurchased({
      input: { page: 1, perPage: 20 }
    });

    if (res && res.myDrills && res.myDrills.collection) {
      myDrillList.value = res.myDrills.collection;
    }
  };

  const onViewDetailDrill = (drill: DrillInterface) => {
    if (!drill.slug) {
      return;
    }
    router.push(`/drills/${drill.slug}`);
  };

  const onViewTeacherClick = (drill: DrillInterface) => {
    if (!drill || drill.ownerType !== OWNER_TYPE_ENUMS.TEACHER) {
      return;
    }
    router.push(`/teachers/${drill.owner.slug}`);
  };

  onMounted(async () => {
    await fetchCourses();
    await fetchMyDrillList();
    loading.value = false;
  });
</script>

<style scoped>
  .drill-icon {
    background-color: lightgray;
    color: rgb(83, 83, 83);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
