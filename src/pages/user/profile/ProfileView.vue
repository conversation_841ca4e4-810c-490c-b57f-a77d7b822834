<template>
  <div class="container profile-user-view">
    <BRow class="mb-4 text-center">
      <BCol cols="12">
        <h2 class="mb-3">{{ $t('user.profile.form.title') }}</h2>
        <div>{{ $t('user.profile.form.description_text') }}</div>
      </BCol>
    </BRow>
    <BRow>
      <BCol cols="12">
        <BCard no-body>
          <BCardBody class="py-4 profile-user-card">
            <BForm class="form-horizontal" role="form">
              <BRow class="justify-content-center">
                <BCol md="12" lg="12">
                  <div class="d-flex justify-content-end">
                    <Button variant="outline-warning" icon="mdi mdi-key-chain-variant" @click="handleChangePassword">{{
                      $t('user.profile.change_password.title')
                    }}</Button>
                  </div>
                </BCol>
                <BCol md="8" lg="7">
                  <FadeInDown>
                    <div class="text-center mb-2 mb-lg-4">
                      <BaseFormValidator name="imageUrl" class="mb-2 mb-lg-4 my-2">
                        <UploadAvatar v-model:url="input.imageUrl" :site="SITES.USER" />
                      </BaseFormValidator>
                      <h5 class="mb-2">{{ $t('user.profile.form.avatar.label') }}</h5>
                      <span>{{ $t('user.profile.form.avatar.placeholder') }}</span>
                    </div>
                  </FadeInDown>

                  <FadeInUp>
                    <BaseFormValidator
                      name="name"
                      :label="$t('user.profile.form.name.label')"
                      class="mb-3 mb-lg-4"
                      required
                    >
                      <BFormInput
                        v-model="input.name"
                        type="text"
                        :placeholder="$t('user.profile.form.name.placeholder')"
                      ></BFormInput>
                    </BaseFormValidator>

                    <BaseFormValidator
                      name="birthDate"
                      required
                      :label="$t('user.profile.form.birthDate.label')"
                      class="mb-3 mb-lg-4"
                    >
                      <BFormInput v-model="input.birthDate" type="date"></BFormInput>
                    </BaseFormValidator>

                    <BaseFormValidator name="gender" :label="$t('user.profile.form.gender.label')">
                      <BFormSelect v-model="input.gender" :options="genderOptions"></BFormSelect>
                    </BaseFormValidator>
                  </FadeInUp>
                </BCol>
              </BRow>

              <div class="action-btn d-flex justify-content-center gap-3 mt-4 mt-lg-5">
                <Button classes="cancel-btn" @click="cancel">
                  {{ $t('common.cancel') }}
                </Button>
                <Button class="save-btn" @click="save">
                  {{ $t('common.save') }}
                </Button>
              </div>
            </BForm>
          </BCardBody>
        </BCard>
      </BCol>
    </BRow>
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';
  import { useUserAuthStore } from '@/store/user/auth';

  import { updateProfile } from '@/services/user';

  import { UserProfileModel } from '@/forms/user/profile';

  import Button from '@/components/base/Button.vue';
  import UploadAvatar from '@/components/base/UploadAvatar.vue';

  import { SITES } from '@/utils/constant';

  const userAuthStore = useUserAuthStore();
  const router = useRouter();
  const { userProfile } = storeToRefs(userAuthStore);

  const input = ref(new UserProfileModel());

  const genderOptions = [
    { value: 'Male', text: 'Nam' },
    { value: 'Female', text: 'Nữ' }
  ];

  async function save() {
    try {
      await updateProfile(input.value);
      userAuthStore.getProfile();
    } catch (error) {
      console.log(error);
    }
  }

  function handleChangePassword() {
    router.push('/change-password');
  }

  function cancel() {
    router.push('/home');
  }

  onMounted(async () => {
    input.value.assignAttributes(userProfile.value);
    input.value.gender = userProfile.value?.gender ?? 'Male';
  });
</script>
