<template>
  <BRow class="justify-content-center">
    <BCol md="8" lg="7" xl="6" xxl="5">
      <div class="text-center mb-5">
        <h1 class="auth-title">
          {{ $t('public.login.login_btn') }}
        </h1>
      </div>

      <BCard no-body class="overflow-hidden auth-card">
        <BCardBody class="custom-card-body">
          <BForm class="p-2">
            <BaseFormValidator name="identifier" :label="$t('public.login.form.phone')" class="mb-4" required>
              <BFormInput
                v-model="input.identifier"
                autocomplete="phone"
                class="custom-input"
                type="text"
                :placeholder="$t('public.login.form.phone_placeholder')"
              ></BFormInput>
            </BaseFormValidator>

            <BaseFormValidator name="password" :label="$t('public.login.form.password')" class="mb-4" required>
              <div class="position-relative">
                <BFormInput
                  v-model="input.password"
                  autocomplete="current-password"
                  class="custom-input input-password"
                  :type="showPassword ? 'text' : 'password'"
                  :placeholder="$t('public.login.form.password_placeholder')"
                />

                <div
                  @click="showPassword = !showPassword"
                  class="eye-icon-action position-absolute top-50 end-0 translate-middle-y pe-3 cursor-pointer"
                >
                  <i class="mdi font-size-18" :class="showPassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"></i>
                </div>
              </div>

              <div class="d-flex justify-content-end pt-2">
                <router-link to="/recover-password">
                  <i class="mdi mdi-lock-question"></i>
                  {{ $t('public.login.forget_password') }}
                </router-link>
              </div>
            </BaseFormValidator>

            <div class="mt-6 d-grid">
              <BButton
                class="btn-block auth-submit-btn bg-gradient"
                loading-text=""
                type="submit"
                :disabled="isLoading"
                :loading="isLoading"
                @click.prevent="handleLogin"
              >
                {{ $t('public.login.login_btn') }}
              </BButton>
            </div>
          </BForm>
        </BCardBody>
      </BCard>

      <div class="text-center link-text">
        <p>
          {{ $t('public.login.signup_desc') }}
          <router-link to="register" class="fw-medium text-primary">
            {{ $t('public.login.signup_btn') }}
          </router-link>
        </p>
      </div>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import { SignInInputInterface } from '@/utils/interface/user/auth';

  import { useAuthPublicStore } from '@/store/public/auth';

  const authPublicStore = useAuthPublicStore();

  const isLoading = ref<boolean>(false);
  const showPassword = ref<boolean>(false);
  const input = ref<SignInInputInterface>({
    identifier: '',
    password: ''
  });

  const handleLogin = async () => {
    isLoading.value = true;
    await authPublicStore.login(input.value);
    isLoading.value = false;
  };
</script>

<style lang="scss" scoped>
  :deep(*) {
    .form-label {
      padding: 0px 0px 16px !important;
      margin-bottom: 0.25rem !important;
      font-size: 16px !important;
      font-weight: 400 !important;
    }

    .invalid-feedback {
      font-size: 0.8rem !important;
      font-weight: 200 !important;
      margin-bottom: 2px !important;
    }
  }
</style>
