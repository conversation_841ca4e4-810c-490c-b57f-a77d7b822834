<template>
  <div class="container profile-user-view">
    <BRow class="mb-4 text-center">
      <BCol cols="12">
        <h2 class="mb-3">{{ $t('user.profile.change_password.title') }}</h2>
      </BCol>
    </BRow>
    <BRow>
      <BCol cols="12">
        <BCard no-body>
          <BCardBody class="py-4 profile-user-card">
            <BForm class="form-horizontal" role="form">
              <BRow class="justify-content-center">
                <BCol md="8" lg="7">
                  <FadeInDown>
                    <div class="text-center my-2 mb-lg-2">
                      <div class="position-relative d-inline-block">
                        <div class="text-center mb-2 mb-lg-4">
                          <div class="avatar-lg">
                            <div class="avatar-title bg-light rounded-circle">
                              <img
                                v-if="userProfile?.imageUrl"
                                :src="userProfile.imageUrl"
                                id="projectlogo-img"
                                class="avatar-md rounded-circle"
                              />

                              <img
                                v-else
                                src="@/assets/images/users/user-dummy-img.png"
                                id="projectlogo-img"
                                class="avatar-md rounded-circle"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </FadeInDown>

                  <div class="censor-status-bg">
                    <div class="censor-status-overlay-bg"></div>
                    <div class="alert d-flex justify-content-between align-items-center mb-3 min-h-62 alert-info">
                      <div class="d-flex align-items-center">
                        <i class="bx me-2 bx bx-lock-alt"></i>
                        {{ $t('user.profile.secure_message.header') }}
                        <br />
                        {{ $t('user.profile.secure_message.message') }}
                      </div>
                    </div>
                  </div>

                  <FadeInUp>
                    <BaseFormValidator
                      name="password"
                      :label="$t('user.profile.form.password.label')"
                      class="mb-3 mb-lg-4"
                      required
                    >
                      <div class="position-relative">
                        <BFormInput
                          v-model="changePasswordInput.password"
                          :type="showPassword ? 'text' : 'password'"
                          :placeholder="$t('user.profile.form.password.placeholder')"
                        ></BFormInput>

                        <div
                          @click="showPassword = !showPassword"
                          class="eye-icon-action position-absolute top-50 end-0 translate-middle-y pe-3 cursor-pointer"
                        >
                          <i
                            class="mdi font-size-18"
                            :class="showPassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"
                          ></i>
                        </div>
                      </div>
                    </BaseFormValidator>

                    <BaseFormValidator
                      name="newPassword"
                      required
                      :label="$t('user.profile.form.new_password.label')"
                      class="mb-3 mb-lg-4"
                    >
                      <div class="position-relative">
                        <BFormInput
                          v-model="changePasswordInput.newPassword"
                          :placeholder="$t('user.profile.form.new_password.placeholder')"
                          :type="showNewPassword ? 'text' : 'password'"
                        ></BFormInput>

                        <div
                          @click="showNewPassword = !showNewPassword"
                          class="eye-icon-action position-absolute top-50 end-0 translate-middle-y pe-3 cursor-pointer"
                        >
                          <i
                            class="mdi font-size-18"
                            :class="showNewPassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"
                          ></i>
                        </div>
                      </div>
                    </BaseFormValidator>

                    <BaseFormValidator
                      name="confirmPassword"
                      required
                      :label="$t('user.profile.form.password_confirmation.label')"
                    >
                      <div class="position-relative">
                        <BFormInput
                          v-model="changePasswordInput.confirmPassword"
                          :placeholder="$t('user.profile.form.password_confirmation.placeholder')"
                          :type="showConfirmPassword ? 'text' : 'password'"
                        ></BFormInput>

                        <div
                          @click="showConfirmPassword = !showConfirmPassword"
                          class="eye-icon-action position-absolute top-50 end-0 translate-middle-y pe-3 cursor-pointer"
                        >
                          <i
                            class="mdi font-size-18"
                            :class="showConfirmPassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"
                          ></i>
                        </div>
                      </div>
                    </BaseFormValidator>
                  </FadeInUp>
                </BCol>
              </BRow>

              <div class="action-btn d-flex justify-content-center gap-3 mt-4 mt-lg-5">
                <Button classes="cancel-btn" @click="cancel">
                  {{ $t('common.cancel') }}
                </Button>
                <Button class="save-btn" @click="handleChangePassword" :loading="isLoading">
                  {{ $t('common.save') }}
                </Button>
              </div>
            </BForm>
          </BCardBody>
        </BCard>
      </BCol>
    </BRow>
  </div>
</template>

<script setup lang="ts">
  import { changePassword } from '@/services/user';

  import { ChangePasswordForm } from '@/forms/user/changePassword';

  import Button from '@/components/base/Button.vue';

  import { storeToRefs } from 'pinia';
  import { useUserAuthStore } from '@/store/user/auth';

  const userAuthStore = useUserAuthStore();
  const { userProfile } = storeToRefs(userAuthStore);

  const router = useRouter();

  const changePasswordInput = ref(new ChangePasswordForm());
  const showPassword = ref<boolean>(false);
  const showNewPassword = ref<boolean>(false);
  const showConfirmPassword = ref<boolean>(false);
  const isLoading = ref<boolean>(false);

  async function handleChangePassword() {
    isLoading.value = true;

    try {
      await changePassword(changePasswordInput.value);

      router.push('/profile');
    } catch (e) {
      console.error(e);
    }

    isLoading.value = false;
  }

  function cancel() {
    router.push('/home');
  }
</script>
