<template>
  <BRow class="register-verify justify-content-center">
    <BCol md="8" lg="7" xl="6" xxl="5">
      <div class="text-center mb-4">
        <i class="shield-ic mdi mdi-shield-outline lh-1"></i>
      </div>

      <div class="text-center mb-5">
        <h2 class="auth-title">{{ $t('public.login.forget_password_verify') }}</h2>
        <p class="text-center">
          {{ $t('public.register_verify.sub-title') }}
        </p>
      </div>

      <BCard no-body class="overflow-hidden auth-card">
        <BCardBody class="custom-card-body">
          <div class="d-flex justify-content-center align-items-center gap-2 mb-3">
            <i class="cellphone-ic mdi mdi-cellphone-settings lh-1"></i>
            <p class="fw-bold fs-5 text-center mb-0">
              {{ phoneNumber }}
            </p>
          </div>

          <BForm class="p-2">
            <OtpInput v-model="verifyCode" class="justify-content-center" :num-inputs="numInputs"></OtpInput>

            <div class="pt-5 d-grid">
              <Button
                class="auth-submit-btn"
                loading-text=""
                :disabled="isLoading || !isVerifyCodeFilled"
                :loading="isLoading"
                @click="registerVerify"
              >
                {{ $t('public.register_verify.verify') }}
              </Button>
            </div>
          </BForm>
        </BCardBody>
      </BCard>

      <div class="text-center link-text">
        <p>
          {{ $t('public.register_verify.not_received_sms') }}
          <template v-if="resendCountdown > 0">
            <span class="text-muted">
              {{ $t('public.register_verify.resend_code_message') }} {{ resendCountdown }}s
            </span>
          </template>
          <template v-else>
            <span class="text-primary cursor-pointer" @click="resendSMS">
              {{ $t('public.register_verify.resend_code') }}
            </span>
          </template>
        </p>

        <BProgress
          v-if="resendCountdown > 0"
          :max="COUNTDOWN_SECONDS"
          :value="resendCountdown"
          height="6px"
          class="mt-2 countdown-bar"
        />
      </div>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import Button from '@/components/base/Button.vue';
  import OtpInput from '@/components/base/OtpInput.vue';

  import { resetPasswordRequest, resetPasswordVerify } from '@/services/public/repositories/auth';

  import { COUNTDOWN_SECONDS } from '@/utils/constant';

  const route = useRoute();
  const router = useRouter();

  const isLoading = ref<boolean>(false);
  const phoneNumber = ref<string>(route.query.phoneNumber?.toString() || '');
  const resendCountdown = ref<number>(0);
  const verifyCode = ref<string>('');

  const numInputs = 6;
  let countdownTimer: number | null = null;

  const isVerifyCodeFilled = computed(() => verifyCode.value.length == numInputs);

  const registerVerify = async () => {
    if (!isVerifyCodeFilled.value || isLoading.value) return;

    isLoading.value = true;

    try {
      const res = await resetPasswordVerify({
        phoneNumber: phoneNumber.value,
        code: verifyCode.value
      });

      if (res && res.data) {
        window.location.href = res.data.resetPasswordVerify.resetPasswordUrl;
      }
    } catch (error) {
      console.log(error);
    }

    isLoading.value = false;
  };

  const startCountdown = () => {
    if (countdownTimer) {
      clearInterval(countdownTimer);
    }

    resendCountdown.value = COUNTDOWN_SECONDS;
    countdownTimer = setInterval(() => {
      if (resendCountdown.value > 0) {
        resendCountdown.value--;
      } else {
        clearInterval(countdownTimer!);
        countdownTimer = null;
      }
    }, 1000);
  };

  const resendSMS = async () => {
    try {
      await resetPasswordRequest(phoneNumber.value);
      startCountdown();
    } catch (error) {
      console.log(error);
    }
  };

  const redirectToLogin = () => {
    router.push('/login');
  };

  onBeforeMount(() => {
    if (!phoneNumber.value) redirectToLogin();
  });
</script>
