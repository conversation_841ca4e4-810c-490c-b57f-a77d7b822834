<template>
  <BRow class="justify-content-center">
    <BCol md="8" lg="7" xl="6" xxl="5">
      <div class="text-center mb-5">
        <h1 class="auth-title">
          {{ $t('public.login.forget_password') }}
        </h1>
      </div>

      <BCard no-body class="overflow-hidden auth-card">
        <BCardBody class="custom-card-body">
          <BForm class="p-2">
            <div class="censor-status-bg">
              <div class="censor-status-overlay-bg"></div>
              <div class="alert d-flex justify-content-between align-items-center mb-3 min-h-62 alert-info">
                <div class="d-flex align-items-center">
                  <i class="bx me-2 mdi mdi-information-outline"></i>
                  <br />
                  <span>{{ $t('public.login.recover_password_message') }}</span>
                </div>
              </div>
            </div>

            <BaseFormValidator name="identifier" :label="$t('public.login.form.phone')" class="mb-4" required>
              <BFormInput
                v-model="phoneNumber"
                autocomplete="phone"
                required
                class="custom-input"
                type="text"
                :placeholder="$t('public.login.form.phone_placeholder')"
              ></BFormInput>
            </BaseFormValidator>

            <div class="mt-6 d-grid">
              <BButton
                class="btn-block auth-submit-btn bg-gradient"
                loading-text=""
                type="submit"
                :disabled="isLoading"
                :loading="isLoading"
                @click.prevent="handleRequestResetPassword"
              >
                {{ $t('common.send') }}
              </BButton>
            </div>
          </BForm>
        </BCardBody>
      </BCard>

      <div class="text-center link-text">
        <p>
          {{ $t('public.login.sub_title') }}
          <router-link to="/login" class="fw-medium text-primary">
            {{ $t('public.login.login_btn') }}
          </router-link>
        </p>
      </div>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import { resetPasswordRequest } from '@/services/public';
  const router = useRouter();

  const isLoading = ref<boolean>(false);
  const phoneNumber = ref<string>('');

  const handleRequestResetPassword = async () => {
    isLoading.value = true;

    try {
      await resetPasswordRequest(phoneNumber.value);

      router.push({
        path: '/recover-password-verify',
        query: { phoneNumber: phoneNumber.value }
      });
    } catch (err) {
      console.error(err);
    }

    isLoading.value = false;
  };
</script>

<style lang="scss" scoped>
  :deep(*) {
    .form-label {
      padding: 0px 0px 16px !important;
      margin-bottom: 0.25rem !important;
      font-size: 16px !important;
      font-weight: 400 !important;
    }

    .invalid-feedback {
      font-size: 0.8rem !important;
      font-weight: 200 !important;
      margin-bottom: 2px !important;
    }
  }
</style>
