<template>
  <BRow class="justify-content-center">
    <BCol md="8" lg="7" xl="6" xxl="5">
      <div class="text-center mb-5">
        <h1 class="auth-title">{{ $t('public.register.register_btn') }}</h1>
      </div>

      <BCard no-body class="overflow-hidden auth-card">
        <BCardBody class="custom-card-body">
          <BForm class="p-2" @submit.prevent="handleRegister">
            <BaseFormValidator
              name="phoneNumber"
              :label="$t('public.register.form.phone', 'Phone Number')"
              class="mb-4"
              required
            >
              <BFormInput
                v-model="input.phoneNumber"
                autocomplete="phone"
                class="custom-input"
                :placeholder="$t('public.register.form.phone_placeholder')"
              ></BFormInput>
            </BaseFormValidator>

            <BaseFormValidator
              class="mb-4"
              name="password"
              required
              :label="$t('public.register.form.password', 'Password')"
            >
              <div class="position-relative">
                <BFormInput
                  v-model="input.password"
                  autocomplete="new-password"
                  class="custom-input input-password"
                  :type="showPassword ? 'text' : 'password'"
                  :placeholder="$t('public.register.form.password_placeholder')"
                ></BFormInput>

                <div
                  @click="showPassword = !showPassword"
                  class="eye-icon-action position-absolute top-50 end-0 translate-middle-y pe-3 cursor-pointer"
                >
                  <i class="mdi font-size-18" :class="showPassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"></i>
                </div>
              </div>
            </BaseFormValidator>

            <BaseFormValidator
              class="mb-4"
              name="passwordConfirmation"
              required
              :label="$t('public.register.form.passwordConfirmation', 'password Confirmation')"
            >
              <div class="position-relative">
                <BFormInput
                  v-model="input.passwordConfirmation"
                  autocomplete="new-password"
                  class="custom-input input-password"
                  :type="showPasswordConfirm ? 'text' : 'password'"
                  :placeholder="$t('public.register.form.passwordConfirmation_placeholder')"
                ></BFormInput>

                <div
                  @click="showPasswordConfirm = !showPasswordConfirm"
                  class="eye-icon-action position-absolute top-50 end-0 translate-middle-y pe-3 cursor-pointer"
                >
                  <i
                    class="mdi font-size-18"
                    :class="showPasswordConfirm ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"
                  ></i>
                </div>
              </div>
            </BaseFormValidator>

            <div class="mt-5 d-grid">
              <BButton
                class="btn-block auth-submit-btn bg-gradient"
                loading-text=""
                type="submit"
                :disabled="isLoading"
                :loading="isLoading"
                @click.prevent="handleRegister"
              >
                {{ $t('public.register.register_btn') }}
              </BButton>
            </div>

            <div class="mt-4 text-center link-text">
              <p class="mb-0">
                {{ $t('public.register.terms_text', 'By registering you agree to the') }}

                <router-link to="/terms-and-conditions" class="text-primary">
                  {{ $t('public.register.terms_link', 'Terms of Use') }}
                </router-link>
              </p>
            </div>
          </BForm>
        </BCardBody>
      </BCard>

      <div class="text-center link-text">
        <p>
          {{ $t('public.register.login_desc', 'Already have an account?') }}
          <router-link to="/login" class="fw-medium text-primary">
            {{ $t('public.register.login_btn', 'Login') }}
          </router-link>
        </p>
      </div>
    </BCol>
  </BRow>
</template>

<script lang="ts" setup>
  import { useAuthPublicStore } from '@/store/public/auth';

  import { SignUpInputInterface } from '@/utils/interface/user/auth';

  const authPublicStore = useAuthPublicStore();

  const isLoading = ref<boolean>(false);
  const showPassword = ref<boolean>(false);
  const showPasswordConfirm = ref<boolean>(false);
  const input = ref<SignUpInputInterface>({
    phoneNumber: '',
    password: '',
    passwordConfirmation: ''
  });

  const handleRegister = async () => {
    isLoading.value = true;
    await authPublicStore.register(input.value);
    isLoading.value = false;
  };
</script>

<style lang="scss" scoped>
  :deep(*) {
    .form-label {
      padding: 0px 0px 16px !important;
      margin-bottom: 0.25rem !important;
      font-size: 16px !important;
      font-weight: 400 !important;
    }

    .invalid-feedback {
      font-size: 0.8rem !important;
      font-weight: 200 !important;
      margin-bottom: 2px !important;
    }
  }
</style>
