<template>
  <BRow>
    <BCol lg="12">
      <BCard no-body>
        <BCardBody>
          <BAlert :model-value="showDrillRejectAlert" variant="warning" class="d-flex mb-4 mx-0 mx-lg-3">
            <i class="mdi mdi-alert-circle-outline lh-1 me-2 font-size-16"></i>
            <div class="d-flex flex-column">
              <h5 class="lh-1">{{ $t('user.practice_submission.form.alert.title') }}</h5>
              <span>{{ $t('public.drills.drill_alert_reject') }}</span>
            </div>
          </BAlert>
          <BRow>
            <BCol lg="7" md="12" sm="12" class="relative mb-4 mb-lg-0 px-lg-4">
              <div class="product-detai-imgs">
                <FadeInRight>
                  <div class="product-img py-4">
                    <img
                      style="min-width: 90%"
                      :src="diagrams[currentStep]"
                      :alt="`Step ${1}`"
                      class="img-fluid d-block"
                      :class="{ 'blur-image': drill.censor != CENSOR_STATUS_ENUMS.APPROVED }"
                    />
                  </div>

                  <div
                    v-if="diagrams.length > 1 && drill.censor == CENSOR_STATUS_ENUMS.APPROVED"
                    class="step-drill-section position-relative mt-5"
                  >
                    <h4>{{ $t('public.drills.form.fields.step') }}</h4>

                    <BaseCarousel :items="diagrams" :auto-height="false">
                      <template #default="{ item, index }">
                        <FadeInUp class="drill-list g-4">
                          <div
                            class="product-img diagram-hover-animate"
                            :class="{ 'diagram-animate-active': animatedIndex === diagrams.indexOf(item) }"
                          >
                            <div class="position-relative">
                              <img
                                :src="item"
                                class="img-fluid mx-auto d-block"
                                :class="{
                                  'blur-image': diagrams.indexOf(item) > 0 && !drill.isVisible
                                }"
                                @click="
                                  setCurrentStep(item);
                                  onToggleAnimation(diagrams.indexOf(item));
                                "
                              />
                              <div
                                v-if="diagrams.indexOf(item) > 0 && !drill.isVisible"
                                class="lock-overlay d-flex align-items-center justify-content-center"
                              >
                                <i class="mdi mdi-lock-outline text-white fa-3x"></i>
                              </div>
                            </div>
                          </div>

                          <h5 class="pt-2 mt-2" v-if="index === 0">
                            {{ $t('common.drill') }}
                          </h5>
                          <h5 v-else class="pt-2 mt-2">{{ $t('public.drills.form.fields.step') }} {{ index }}</h5>
                        </FadeInUp>
                      </template>
                    </BaseCarousel>
                  </div>
                </FadeInRight>
              </div>
            </BCol>

            <BCol lg="5" md="12" sm="12">
              <FadeInLeft>
                <div class="text-left px-lg-4 d-flex flex-column justify-content-lg-between" style="height: 100%">
                  <div
                    class="mb-3 mt-4"
                    :class="{
                      'creator-teacher': drill.ownerType === OWNER_TYPE_ENUMS.TEACHER,
                      'creator-admin': drill.ownerType === OWNER_TYPE_ENUMS.ADMIN
                    }"
                  >
                    <div v-if="drill.ownerType === OWNER_TYPE_ENUMS.ADMIN" class="d-flex align-items-center py-2">
                      <i class="bx bx-bot fs-2 me-3 admin-icon"></i>
                      <div class="d-flex flex-column">
                        <span class="fs-5 fw-semibold" style="color: #1e3a8a">{{
                          $t('public.drills.created_by_admin.label')
                        }}</span>
                        <span style="color: #325ddc">{{ $t('public.drills.created_by_admin.text') }}</span>
                      </div>
                    </div>
                    <div v-if="drill.ownerType === OWNER_TYPE_ENUMS.TEACHER" class="fs-5 py-2">
                      <div class="d-flex align-items-center">
                        <BAvatar
                          :src="drill.owner.imageUrl ? drill.owner.imageUrl : dummyAvatar"
                          :alt="drill.owner.name"
                          size="50"
                          class="avatar-image me-2"
                        />
                        <div class="d-flex flex-column">
                          <span class="fs-4 fw-semibold" style="color: #14532d">{{ drill.owner.name }} </span>
                          <span style="font-size: 12px; color: #15803d">{{ drill.owner.contactEmail }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <h3 class="mb-3">{{ drill.title }}</h3>

                  <h5 class="mb-3" v-if="drill.level">
                    <BadgeLevel :value="drill.levelI18n" :level="drill.level"></BadgeLevel>
                  </h5>

                  <PriceDisplay :price="drill.price" :salePrice="drill.salePrice" show-label></PriceDisplay>

                  <div class="my-2">
                    <span
                      v-for="item in drill.skills"
                      :key="item.id"
                      class="badge-soft-primary badge rounded-pill mx-2 my-1"
                    >
                      {{ item.nameI18n }}
                    </span>
                  </div>

                  <div v-if="drill.description && drill.censor == CENSOR_STATUS_ENUMS.APPROVED" class="pt-4 fs-4">
                    <pre class="text-muted mb-4 pre-content fs-6">{{ drill.description }}</pre>
                  </div>

                  <div v-if="currentStep > 0" class="fs-5 mt-5">
                    <h4 class="border-bottom pb-2">
                      {{ $t('teacher.drills.form.fields.step') }} <span>{{ currentStep }}</span>
                    </h4>

                    <div class="d-flex w-100 des">
                      <i class="bx bx-right-arrow-alt font-size-16 align-middle text-primary me-1 p-1"></i>
                      <pre v-if="drill.step && drill.step.length" class="m-0 text-break w-100 pre-content">{{
                        isExpanded ? drill.step[currentStep - 1].description : truncatedDescription
                      }}<span
                        v-if="drill.step[currentStep - 1]?.description?.length > MAX_LENGTH_255"
                        @click="toggleExpand"
                        class="text-primary cursor-pointer ms-1"
                      >{{ isExpanded ? $t('common.collapse') : $t('common.show_more') }}</span></pre>
                    </div>
                  </div>

                  <div class="d-flex justify-content-start gap-2">
                    <div class="mb-4" v-if="haveReadyVideo && drill.censor == CENSOR_STATUS_ENUMS.APPROVED">
                      <Button icon="bx-video" @click="showVideoModal = true" :disabled="!drill.isVisible">
                        {{ $t('public.drills.form.fields.instructional_video') }}
                      </Button>
                    </div>

                    <div class="mb-4" v-if="!drill.isVisible">
                      <Button icon="bx bx-money" @click="confirmBuyDrill" variant="success">
                        {{ $t('common.buy_now') }}
                      </Button>
                    </div>
                  </div>
                </div>
              </FadeInLeft>
            </BCol>
          </BRow>
        </BCardBody>
      </BCard>
    </BCol>
  </BRow>

  <BRow v-if="goListInstance?.items && goListInstance.items.length > 0">
    <BCol lg="12" xl="12" xxl="12">
      <FadeInUp>
        <RelatedDrillCard :relatedDrills="goListInstance.items" />
      </FadeInUp>
    </BCol>
  </BRow>

  <BModal v-if="showVideoModal" v-model="showVideoModal" size="lg" title="Video Hướng Dẫn" lazy no-footer>
    <div
      v-for="(video, idx) in listReadyVideo"
      :key="video.id"
      :class="[idx === listReadyVideo.length - 1 ? 'mb-0' : 'mb-5']"
    >
      <h5>{{ video.title }}</h5>
      <VideoPlayer :video-id="String(video.id)" :title="video.title" :thumbnail="video.thumbnailURL" />
    </div>
  </BModal>
</template>

<script lang="ts" setup>
  import { useGoList } from '@bachdx/b-vuse';

  import { useAuthPublicStore } from '@/store/public/auth';

  import useSwal from '@/composable/swal';

  import { buyDrill, showDrill as userShowDrill } from '@/services/user/repositories/drill';
  import { drillShow } from '@/services/public/repositories/drill';
  import { relatedDrills } from '@/services/public/repositories/drill';

  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';
  import BadgeLevel from '@/components/base/DrillBadgeLevel.vue';
  import BaseCarousel from '@/components/base/BaseCarousel.vue';
  import Button from '@/components/base/Button.vue';
  import PriceDisplay from '@/components/base/PriceDisplay.vue';
  import RelatedDrillCard from '@/components/public/drills/RelatedDrillCard.vue';
  import VideoPlayer from '@/components/base/VideoPlayer.vue';

  import { CENSOR_STATUS_ENUMS, MAX_LENGTH_255, OWNER_TYPE_ENUMS } from '@/utils/constant';
  import { DrillInterface } from '@/utils/interface/drill/drill';
  import { SwalIconOptions, SwalOptions } from '@/utils/swal-options';

  import { RelatedDrillListQueryFormModel } from '@/forms/public/drill';

  import i18n from '@/plugin/i18n';

  const route = useRoute();
  const router = useRouter();
  const { confirming } = useSwal();

  const authStore = useAuthPublicStore();
  const accessToken = authStore.accessToken;

  const animatedIndex = ref<number>();
  const currentStep = ref<number>(0);
  const drill = ref<DrillInterface>({} as DrillInterface);
  const goListInstance = ref<ReturnType<typeof useGoList> | null>(null);
  const isExpanded = ref<boolean>(false);
  const showVideoModal = ref<boolean>(false);

  const showDrillRejectAlert = computed(
    () => !!(accessToken && drill.value.censor && drill.value.censor != CENSOR_STATUS_ENUMS.APPROVED)
  );

  const drillSlug = computed(() => route.params.slug.toString());

  const truncatedDescription = computed(() => {
    if (!drill.value?.step?.[currentStep.value - 1]?.description) return '';
    const description = drill.value.step[currentStep.value - 1].description;
    if (description.length <= MAX_LENGTH_255) return description;
    return description.slice(0, MAX_LENGTH_255) + '...';
  });

  const haveReadyVideo = computed(() => {
    if (drill.value.videoCount > 0) return true;
    if (!drill.value.videos) return false;
    return drill.value.videos.some(video => video.isPlayable);
  });

  const listReadyVideo = computed(() => drill.value.videos?.filter(video => video.isPlayable));

  const diagrams = computed(() => {
    if (!drill.value) return [];

    const images = [];
    if (drill.value.diagrams?.[0]?.imageUrl) {
      images.push(drill.value.diagrams[0].imageUrl);
    }

    if (drill.value.step) {
      const stepImages = drill.value.step.map(step => step.diagramImage);
      images.push(...stepImages);
    } else if (!drill.value.step && drill.value.stepCount > 0) {
      images.push(...Array(drill.value.stepCount).fill('/pool-table.png'));
    }

    if (images.length === 0) return ['/pool-table.png'];

    return images;
  });

  function onToggleAnimation(index: number) {
    animatedIndex.value = animatedIndex.value === index ? undefined : index;
  }

  const toggleExpand = () => {
    isExpanded.value = !isExpanded.value;
  };

  const getDrillData = async (slug: string) => {
    if (accessToken) {
      const data = await userShowDrill(slug);

      drill.value = data.drill;
    } else {
      const { data } = await drillShow(slug);
      drill.value = data.drill;
    }
  };

  const fetchDrillData = async (slug: string) => {
    await getDrillData(slug);

    createGoList();
    if (!goListInstance.value) return;

    goListInstance.value.query.skillIDIn = drill.value.skills?.map(skill => skill.id);
    goListInstance.value.query.levelIn = [drill.value.level];

    goListInstance.value.parseQueryAndFetch(goListInstance.value.query);
  };

  const setCurrentStep = (item: string) => {
    if (!diagrams.value) return;
    const index = diagrams.value.indexOf(item);

    if (index !== -1) {
      currentStep.value = index;
    } else {
      console.warn('Item not found in diagrams');
    }
  };

  const createGoList = () => {
    goListInstance.value = useGoList({
      fetchListFnc: relatedDrills,
      fetchKey: 'relatedDrills',
      route: route,
      router: router,
      queryFormModels: RelatedDrillListQueryFormModel,
      perPage: 12,
      reflectUrl: false,
      extraParams: {
        drillSlug: drillSlug.value
      }
    });
  };

  const confirmBuyDrill = async () => {
    const message = i18n.global.t('public.drills.confirm_buy_drill.message');

    const confirmed = await confirming(
      new SwalOptions({
        title: i18n.global.t('public.drills.confirm_buy_drill.title'),
        html: message,
        icon: SwalIconOptions.Question,
        scrollbarPadding: false
      })
    );

    if (!confirmed) return;

    try {
      if (confirmed) {
        await buyDrill(String(drill.value.id));

        getDrillData(drill.value.slug);
      }
    } catch (e) {
      console.log('e', e);
    }
  };

  watch(
    () => route.params.slug,
    newSlug => {
      if (newSlug) {
        currentStep.value = 0;
        animatedIndex.value = undefined;
        fetchDrillData(newSlug.toString());
      }
    }
  );

  onMounted(() => {
    fetchDrillData(drillSlug.value);
  });
</script>

<style lang="scss" scoped>
  .description-text {
    text-wrap: inherit;
    text-align: left;
  }
  .des {
    padding: 15px;
    max-height: 35vh;
    overflow-y: auto;
  }

  .buttons {
    padding-bottom: 15px;
  }
  .diagram-animate-active {
    z-index: 2;
    transition:
      transform 0.35s cubic-bezier(0.22, 1, 0.36, 1),
      box-shadow 0.35s cubic-bezier(0.22, 1, 0.36, 1);
    border-radius: 10px;
    border: 4px solid #34c38f !important;
    box-sizing: border-box;
  }

  @media screen and (min-width: 990px) {
    .diagram-img-container {
      min-height: 320px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .diagram-hover-animate {
      transition:
        transform 0.35s cubic-bezier(0.22, 1, 0.36, 1),
        box-shadow 0.35s cubic-bezier(0.22, 1, 0.36, 1);
    }

    .diagram-hover-animate:hover {
      transform: scale(0.95);
      z-index: 2;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.18);
      transition:
        transform 0.35s cubic-bezier(0.22, 1, 0.36, 1),
        box-shadow 0.35s cubic-bezier(0.22, 1, 0.36, 1);
      border-radius: 10px;
    }
  }
  @media screen and (max-width: 500px) {
    .card-title {
      display: none;
    }
  }

  @media screen and (max-width: 990px) {
    .des {
      max-height: 25vh;
      max-width: 75vw;
      margin: auto;
    }
  }
  @media screen and (max-width: 768px) {
    .des {
      max-height: 25vh;
      max-width: 100vw;
      padding: 5px;
    }
  }

  .blur-image {
    filter: blur(4px);
    pointer-events: none;
  }

  .lock-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
  }

  .creator-teacher {
    background-color: #f0fdf4;
    padding-inline: 12px;
    padding-block: 5px;
    border: 1px solid #00e446;
    border-radius: 12px;
  }

  .creator-admin {
    background-color: #eff6ff;
    padding-inline: 12px;
    padding-block: 5px;
    border: 1px solid #2563eb;
    border-radius: 12px;
  }

  .admin-icon {
    color: #2563eb;
    background-color: #dbeafe;
    width: fit-content;
    padding: 10px;
    border-radius: 100%;
  }
</style>
