<template>
  <BRow class="justify-content-center" style="margin: 0 auto">
    <DrillListHeroSection
      v-model:selected-levels="selectedLevels"
      v-model:selected-skills="selectedSkills"
      v-model:teacher-name="teacherName"
      :drills="items"
      :level-options="selectOptions ? selectOptions.levelOptions : []"
      :skill-options="selectOptions ? selectOptions.skillOptions : []"
      @clear-level-selection="clearLevelSelection"
      @clear-skill-selection="clearSkillSelection"
      @toggle-level="toggleLevel"
      @toggle-search-teacher="toggleSearchTeacher"
      @toggle-skill="toggleSkill"
    ></DrillListHeroSection>

    <BContainer>
      <BCol lg="12" class="order-2 order-lg-2 px-0 px-lg-2">
        <div class="mb-3 text-center d-flex justify-content-between align-items-end">
          <div class="mb-0">
            <span class="text-warning fw-bold">{{ metadata.total || 0 }}</span>
            {{ $t('teacher.drills.search_form.result') }}
          </div>

          <FadeInUp>
            <div class="d-flex flex-wrap gap-2 align-items-center filter-bar">
              <div class="d-none d-md-flex align-items-center gap-1 me-3">
                <i class="mdi mdi-filter-outline font-size-16"></i>
                <span>{{ $t('public.drills.sort.label') }}:</span>
              </div>

              <BFormSelect
                v-model="orderBy"
                class="filter-item form-select"
                style="width: 120px"
                @change="handleSearch"
              >
                <option :value="DRILL_ORDER_BY.CREATED_AT_DESC">
                  {{ $t('teacher.drills.search_form.newest') }}
                </option>
                <option :value="DRILL_ORDER_BY.CREATED_AT_ASC">
                  {{ $t('teacher.drills.search_form.oldest') }}
                </option>
              </BFormSelect>

              <Button
                v-if="!hideResetButton"
                classes="filter-item d-flex align-items-center gap-1"
                icon="bx-reset"
                @click="resetFilters"
              >
                {{ $t('teacher.drills.search_form.reset') }}
              </Button>
            </div>
          </FadeInUp>
        </div>

        <hr />
        <FadeInUp>
          <DrillsList
            v-if="items.length > 0"
            :drills="items"
            :loading="isTableLoading"
            :metadata="metadata"
            @fetch-list="changePage"
            @on-skill-click="filterBySkill"
          />

          <DataEmpty v-else :message="$t('public.drills.data_empty')" height="150" />
        </FadeInUp>
      </BCol>
    </BContainer>
  </BRow>
</template>

<script lang="ts" setup>
  import { useGoList } from '@bachdx/b-vuse';

  import { useAuthPublicStore } from '@/store/public/auth';

  import useSelectOptions from '@/composable/useSelectOptions';

  import { drillList } from '@/services/public/repositories/drill';
  import { userFetchListDrill } from '@/services/user/repositories/drill';

  import { DrillListQueryFormModel } from '@/forms/public/drill';

  import { DRILL_ORDER_BY } from '@/utils/constant';

  import Button from '@/components/base/Button.vue';
  import DataEmpty from '@/components/utility/DataEmpty.vue';
  import DrillListHeroSection from '@/components/public/drills/drillList/DrillListHeroSection.vue';
  import DrillsList from '@/components/public/drills/DrillsList.vue';
  import FadeInUp from '@/components/base/animations/FadeInUp.vue';
  import { SkillInterface } from '@/utils/interface/skill';

  const authStore = useAuthPublicStore();
  const route = useRoute();
  const router = useRouter();
  const { fetchSelectOptions, selectOptions } = useSelectOptions();

  const accessToken = authStore.accessToken;

  const fetchListFnc = accessToken ? userFetchListDrill : drillList;

  const { items, metadata, search, changePage, query, parseQueryAndFetch, orderBy, reset } = useGoList({
    fetchListFnc: fetchListFnc,
    fetchKey: 'drills',
    route: route,
    queryFormModels: DrillListQueryFormModel,
    router: router,
    perPage: 9
  });

  const isTableLoading = ref<boolean>(false);
  const selectedLevels = ref<number[]>([]);
  const selectedSkills = ref<number[]>([]);
  const teacherName = ref<string>('');

  const handleSearch = async () => {
    isTableLoading.value = true;
    await search();
    isTableLoading.value = false;
  };

  const resetFilters = async () => {
    isTableLoading.value = true;
    selectedLevels.value = [];
    selectedSkills.value = [];
    teacherName.value = '';

    await reset({ defaultQuery: null, defaultOrder: DRILL_ORDER_BY.CREATED_AT_DESC });
    isTableLoading.value = false;
  };

  const toggleLevel = async (value: number) => {
    const index = selectedLevels.value.indexOf(value);

    if (index !== -1) {
      selectedLevels.value.splice(index, 1);
    } else {
      selectedLevels.value.push(value);
    }

    query.value.levelIn = [...selectedLevels.value];

    await handleSearch();
  };

  const clearLevelSelection = async () => {
    selectedLevels.value = [];
    query.value.levelIn = selectedLevels.value;
    await handleSearch();
  };

  const toggleSkill = async (value: number) => {
    const index = selectedSkills.value.indexOf(value);

    if (index !== -1) {
      selectedSkills.value.splice(index, 1);
    } else {
      selectedSkills.value.push(value);
    }

    query.value.skillIDIn = [...selectedSkills.value];

    await handleSearch();
  };

  const filterBySkill = (skill: SkillInterface) => {
    selectedSkills.value = [];
    selectedSkills.value = [Number(skill.id)];
    query.value.skillIDIn = selectedSkills.value;
    handleSearch();
  };

  const clearSkillSelection = async () => {
    selectedSkills.value = [];
    query.value.skillIDIn = selectedSkills.value;
    await handleSearch();
  };
  const hideResetButton = computed(() => {
    return selectedLevels.value.length === 0 && selectedSkills.value.length === 0 && teacherName.value == '';
  });

  const toggleSearchTeacher = async () => {
    query.value.ownerName = teacherName;
    await handleSearch();
  };

  onMounted(async () => {
    await fetchSelectOptions(['skillOptions', 'levelOptions']);
    orderBy.value = DRILL_ORDER_BY.CREATED_AT_DESC;
    isTableLoading.value = true;
    await parseQueryAndFetch();
    selectedLevels.value = query.value.levelIn ? [...query.value.levelIn] : [];
    selectedSkills.value = query.value.skillIDIn ? [...query.value.skillIDIn] : [];
    isTableLoading.value = false;
  });
</script>
