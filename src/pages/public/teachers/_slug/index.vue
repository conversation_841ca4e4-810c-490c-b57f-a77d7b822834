<template>
  <BContainer>
    <div class="teacher-profile mt-5">
      <HeroProfile v-if="teacher" :teacher="teacher" />

      <div class="main-content">
        <AboutSection v-if="teacher" :teacher="teacher" />

        <section class="content-section courses-section">
          <div class="section-header">
            <h2 class="section-title">{{ $t('public.course.title') }}</h2>
            <div class="section-divider"></div>
          </div>

          <BRow>
            <BCol md="6" lg="3" v-for="item in items" :key="item.id">
              <CourseCard :course="item" />
            </BCol>
          </BRow>

          <Pagination :metadata="metadata" @change="changePage" site="user" />
        </section>

        <ReviewList v-if="teacher" :teacher="teacher" />
      </div>
    </div>
  </BContainer>

  <BModal
    v-if="accessToken"
    v-model="showRatingModal"
    :title="$t('user.comment.form.rating_teacher')"
    size="md"
    centered
    no-footer
    fade
    lazy
  >
    <Transition name="fade-scale">
      <RatingTeacher
        v-if="teacher"
        :teacher="teacher"
        @refresh-course="reviewListRef?.fetchList()"
        :is-available-to-rate="teacher.availableRating"
      />
    </Transition>
  </BModal>
</template>

<script setup lang="ts">
  import { useGoList } from '@bachdx/b-vuse';

  import { useAuthPublicStore } from '@/store/public/auth';

  import { teacherDetail } from '@/services/public/repositories/teacher';
  import { courseList } from '@/services/public/repositories/course';
  import { teacherDetail as userTeacherDetail } from '@/services/user/repositories/teacher';

  import { CourseListQueryFormModel } from '@/forms/public/course';

  import Pagination from '@/components/base/Pagination.vue';
  import RatingTeacher from '@/components/user/course/RatingTeacher.vue';

  import { TeacherInterface } from '@/utils/interface/public/teacher';

  import HeroProfile from '@/components/public/teachers/profile/HeroProfile.vue';
  import AboutSection from '@/components/public/teachers/profile/AboutSection.vue';
  import CourseCard from '@/components/base/CourseCard.vue';
  import ReviewList from '@/components/public/teachers/profile/ReviewList.vue';

  const authStore = useAuthPublicStore();
  const accessToken = authStore.accessToken;

  const route = useRoute();
  const router = useRouter();

  const showRatingModal = ref(false);
  const reviewListRef = ref<InstanceType<typeof ReviewList> | null>(null);

  const { items, query, search, metadata, changePage } = useGoList({
    fetchListFnc: courseList,
    fetchKey: 'courses',
    route,
    router,
    queryFormModels: CourseListQueryFormModel,
    perPage: 12
  });

  const teacher = ref<TeacherInterface>();

  const fetchTeacher = accessToken ? userTeacherDetail : teacherDetail;

  onMounted(async () => {
    const slug = route.params.slug as string;
    const data = await fetchTeacher(slug);
    teacher.value = data.teacher;
    if (route.hash) {
      setTimeout(() => {
        showRatingModal.value = true;
      }, 300);

      const id = typeof route.hash === 'string' ? route.hash.replace('#', '') : '';
      setTimeout(() => {
        const el = document.getElementById(id);
        if (el) el.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }, 400);
    }

    if (teacher.value) {
      query.value = {
        ...query.value,
        teacherIdEq: teacher.value.id
      };
      search();
    }
  });
</script>

<style scoped lang="scss">
  $primary: #f1b44c;

  .primary-color {
    color: $primary;
  }

  .teacher-avatar {
    border: 4px solid white;
    object-fit: cover;
  }

  .about-badge {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .review-section {
    background-color: white;
    width: 100%;
    border-radius: 20px;
    padding: 20px;
    margin-top: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }

  .teacher-profile {
    min-height: 100vh;
    color: #495057;
    font-family: 'Poppins', sans-serif;

    @media (max-width: 992px) {
      margin-top: 110px !important;
    }
  }

  .main-content {
    padding: 60px 0px;

    @media (max-width: 768px) {
      padding: 40px 0px;
    }
  }

  .content-section {
    margin-bottom: 60px;

    @media (max-width: 768px) {
      margin-bottom: 40px;
    }
  }

  .section-header {
    margin-bottom: 30px;
    text-align: center;

    @media (max-width: 768px) {
      margin-bottom: 20px;
    }
  }

  .section-title {
    font-size: 28px;
    font-weight: 700;
    color: #343a40;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;

    @media (max-width: 768px) {
      font-size: 24px;
      margin-bottom: 10px;
    }
  }

  .section-divider {
    width: 100px;
    height: 2px;
    background-color: rgba($primary, 0.3);
    margin: 0 auto;
  }

  .courses-section {
    .courses-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 30px;
      margin-bottom: 40px;

      @media (max-width: 768px) {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }

      @media (max-width: 480px) {
        grid-template-columns: 1fr;
        gap: 15px;
      }
    }
  }

  @media (max-width: 768px) {
    .teacher-avatar {
      width: 150px !important;
      height: 150px !important;
    }
  }
</style>
