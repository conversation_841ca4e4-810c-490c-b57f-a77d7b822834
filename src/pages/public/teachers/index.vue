<template>
  <div class="teachers-page">
    <HeroSection :stats="stats">
      <template #search-form>
        <div class="search-container">
          <div class="search-input-wrapper">
            <i class="bx bx-search search-icon"></i>
            <BFormInput
              type="text"
              v-model="query.nameCont"
              :placeholder="$t('public.teacher.search_form.name.label')"
              class="search-input"
            />
          </div>

          <Button @click="handleSearch" class="search-button" :disabled="isTableLoading">
            <i class="bx bx-search"></i>
            <span>{{ $t('common.search_btn') }}</span>
          </Button>
        </div>
      </template>
    </HeroSection>

    <section class="content-section">
      <div class="container">
        <TeacherFilterCard
          :order-by="orderBy || TEACHER_ORDER_BY.STUDENTS_COUNT_DESC"
          :query="query"
          @update:orderBy="orderBy = $event"
          @search="handleSearch"
          @reset="handleReset"
          :metadata="metadata"
        />

        <div v-if="!isTableLoading && items.length > 0">
          <BRow>
            <BCol md="6" lg="4" v-for="teacher in items" :key="teacher.id" class="mb-4">
              <TeacherModernCard :teacher="teacher" />
            </BCol>
          </BRow>

          <Pagination :metadata="metadata" @change="changePage" site="user" />
        </div>

        <div class="loading-state" v-if="isTableLoading">
          <div class="spinner"></div>
          <p>{{ $t('public.teacher.loading') }}</p>
        </div>

        <div class="empty-state" v-if="!isTableLoading && items.length === 0">
          <DataEmpty :message="$t('public.teacher.data_empty')" height="150" />
        </div>
      </div>
    </section>

    <Testimonial :reviews="featureReviews" />

    <CtaCard />
  </div>
</template>

<script lang="ts" setup>
  import { useGoList } from '@bachdx/b-vuse';

  import { teacherList } from '@/services/public/repositories/teacher';
  import { featureReviewsList } from '@/services/public/repositories/comment';
  import { fetchTeacherOverviewStats } from '@/services/public/repositories/stats';

  import { TeacherQueryFormInput } from '@/forms/public/teacher';
  import { OverviewStatsInterface } from '@/utils/interface/public/stats';

  import DataEmpty from '@/components/utility/DataEmpty.vue';
  import Pagination from '@/components/base/Pagination.vue';
  import TeacherModernCard from '@/components/public/teachers/TeacherModernCard.vue';
  import CtaCard from '@/components/public/teachers/CtaCard.vue';
  import TeacherFilterCard from '@/components/public/teachers/TeacherFilterCard.vue';
  import Testimonial from '@/components/public/teachers/Testimonial.vue';
  import HeroSection from '@/components/public/teachers/HeroSection.vue';
  import Button from '@/components/base/Button.vue';

  import { COMMENT_TARGET_TYPE, TEACHER_ORDER_BY } from '@/utils/constant';

  const route = useRoute();
  const router = useRouter();

  const { items, metadata, changePage, search, reset, query, parseQueryAndFetch, orderBy } = useGoList({
    fetchListFnc: teacherList,
    fetchKey: 'teachers',
    route: route,
    queryFormModels: TeacherQueryFormInput,
    router: router,
    perPage: 9
  });

  const { items: featureReviews, parseQueryAndFetch: parseQueryAndFetchReviews } = useGoList({
    fetchListFnc: async (params: any) => {
      const res = await featureReviewsList(params);

      return res;
    },
    fetchKey: 'featuredReviews',
    route: route,
    router: router,
    perPage: 3,
    reflectUrl: false,
    extraParams: {
      targetType: COMMENT_TARGET_TYPE.TEACHER
    }
  });

  const stats = ref<OverviewStatsInterface>({
    totalUsers: 0,
    totalTeachers: 0,
    totalCourses: 0,
    satisfactionRate: 0,
    teacherAverageRating: 0
  });

  const isTableLoading = ref(true);

  const handleSearch = async () => {
    isTableLoading.value = true;
    await search();
    isTableLoading.value = false;
  };

  const handleReset = async () => {
    isTableLoading.value = true;
    await reset({});

    orderBy.value = TEACHER_ORDER_BY.STUDENTS_COUNT_DESC;
    isTableLoading.value = false;
  };

  onMounted(async () => {
    isTableLoading.value = true;
    await parseQueryAndFetch();
    await parseQueryAndFetchReviews({});

    const { data } = await fetchTeacherOverviewStats();
    if (data) {
      stats.value = data.overviewStats;
    }

    orderBy.value ||= TEACHER_ORDER_BY.STUDENTS_COUNT_DESC;
    isTableLoading.value = false;
  });
</script>

<style lang="scss" scoped>
  // Variables
  $primary-color: #f1b44c;
  $secondary-color: #2a3042;
  $text-color: #495057;
  $text-light: #74788d;
  $border-radius: 12px;
  $box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  $transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

  // Global styles
  .teachers-page {
    font-family: 'Inter', sans-serif;
    color: $text-color;
    background-color: #f9fafb;
    overflow-x: hidden;
  }

  // Search Form
  .search-container {
    display: flex;
    gap: 10px;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  }

  .search-input-wrapper {
    position: relative;
    flex: 1;
  }

  .search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: $text-light;
    font-size: 1.2rem;
  }

  .search-input {
    width: 100%;
    height: 50px;
    padding: 0 20px 0 45px;
    border-radius: 25px;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    font-size: 1rem;
    transition: $transition;

    &:focus {
      outline: none;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    &::placeholder {
      color: lighten($text-light, 15%);
    }
  }

  .search-button {
    height: 50px;
    border: none;
    border-radius: 25px;
    padding: 0 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: $transition;
    cursor: pointer;
    font-size: 0.95rem;
    background-color: $secondary-color;
    color: white;
    box-shadow: 0 4px 10px rgba($secondary-color, 0.3);

    &:hover:not(:disabled) {
      background-color: darken($secondary-color, 5%);
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba($secondary-color, 0.4);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    i {
      font-size: 1.1rem;
    }
  }

  // Content Section
  .content-section {
    padding: 20px 0 80px;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  // Teachers Grid
  .teachers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
  }

  // Loading State
  .loading-state {
    text-align: center;
    padding: 60px 20px;

    .spinner {
      width: 40px;
      height: 40px;
      margin: 0 auto 20px;
      border: 3px solid rgba($primary-color, 0.3);
      border-radius: 50%;
      border-top-color: $primary-color;
      animation: spin 1s linear infinite;
    }

    p {
      color: $text-light;
    }
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  // Empty State
  .empty-state {
    text-align: center;
    padding: 60px 20px;
    background-color: white;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
  }

  // Responsive
  @media (max-width: 768px) {
    .search-container {
      flex-direction: column;
    }

    .search-button {
      width: 100%;
      justify-content: center;
    }

    .teachers-grid {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
  }

  @media (max-width: 480px) {
    .teachers-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
