<template>
  <FadeInUp>
    <HeroSection> </HeroSection>

    <BCard no-body class="search-form container rounded-4">
      <BCardHeader class="text-center bg-white">
        <h2 class="search-form-title">{{ $t('user.user_course.search_form.title') }}</h2>
      </BCardHeader>
      <SearchForm
        v-model:query="query"
        search-btn-variant="warning"
        :loading="loading"
        :search-fields-list="searchFieldsList"
        @reset="handleReset"
        @search="search"
      />
    </BCard>

    <BContainer>
      <div class="level-filter-container">
        <div v-for="opt in levelOptions" :key="opt.value" class="mb-2 mb-md-0 px-1 level-btn-grid">
          <button
            :class="['level-btn', { active: activeLevel === opt.value }]"
            @click="onLevelClick(opt.value)"
            class="d-flex filter-btn justify-content-center align-items-center"
            type="button"
          >
            <span class="icon-wrap">
              <i :class="opt.icon"></i>
            </span>
            <span class="label">{{ $t(opt.label) }}</span>
          </button>
        </div>
      </div>

      <div class="d-flex align-items-center justify-content-between">
        <div class="mb-0">
          <span class="text-warning fw-bold font-size-20 me-1">
            {{ metadata.total || 0 }}
          </span>
          {{ $t('public.course.title') }}
        </div>
        <div class="d-flex align-items-center gap-3">
          <div class="d-none d-md-flex align-items-center gap-1">
            <i class="mdi mdi-filter-outline font-size-16"></i>
            <span>{{ $t('user.user_course.sort.label') }}:</span>
          </div>
          <BFormSelect v-model="orderBy" @change="handleSort" class="filter-item form-select w-auto min-w-150">
            <option :value="COURSE_ORDER_BY.CREATED_AT_DESC">
              {{ $t('user.user_course.sort.recent_uploaded') }}
            </option>
            <option :value="COURSE_ORDER_BY.STUDENTS_COUNT_DESC">
              {{ $t('user.user_course.sort.best_selling') }}
            </option>
            <option :value="COURSE_ORDER_BY.RATING_COUNT_DESC">
              {{ $t('user.user_course.sort.best_rated') }}
            </option>
            <option :value="COURSE_ORDER_BY.PRICE_ASC">
              {{ $t('user.user_course.sort.price_asc') }}
            </option>
            <option :value="COURSE_ORDER_BY.PRICE_DESC">
              {{ $t('user.user_course.sort.price_desc') }}
            </option>
          </BFormSelect>
        </div>
      </div>

      <BRow v-if="items.length">
        <BCol xxl="3" xl="4" md="6" v-for="course in items" :key="course.id" class="user-course-view mt-3 mt-lg-4">
          <CourseCard :course="course" />
        </BCol>

        <Pagination :metadata="metadata" @change="changePage" site="user"></Pagination>
      </BRow>
      <DataEmpty v-else :message="$t('user.user_course.data_empty')" height="150" class="mt-5" />
    </BContainer>

    <TestimonialsSection class="mt-5" />

    <CtaCard class="mt-5" />
  </FadeInUp>
</template>
<script lang="ts" setup>
  import i18n from '@/plugin/i18n';
  import { useGoList } from '@bachdx/b-vuse';

  import useDynamicSearch from '@/composable/dynamicSearch';

  import CourseCard from '@/components/base/CourseCard.vue';
  import DataEmpty from '@/components/utility/DataEmpty.vue';
  import SearchForm from '@/components/base/SearchForm.vue';
  import HeroSection from '@/components/public/course/HeroSection.vue';
  import TestimonialsSection from '@/components/public/course/TestimonialsSection.vue';
  import CtaCard from '@/components/public/course/CtaCard.vue';

  import { courseList } from '@/services/public/repositories/course';

  import { CourseListQueryFormModel } from '@/forms/public/course';

  import SearchField, { SearchFieldOptions } from '@/utils/search-fields';
  import { COURSE_ORDER_BY } from '@/utils/constant';

  const { searchFieldsList, searchComponents } = useDynamicSearch();

  const route = useRoute();
  const router = useRouter();
  const { items, metadata, changePage, parseQueryAndFetch, query, orderBy, search, reset } = useGoList({
    fetchListFnc: courseList,
    fetchKey: 'courses',
    route: route,
    router: router,
    queryFormModels: CourseListQueryFormModel,
    perPage: 12
  });

  const salePriceRangeOptions = [
    { label: i18n.global.t('user.user_course.search_form.field.price.price_range.free'), value: [0, 0] },
    { label: i18n.global.t('user.user_course.search_form.field.price.price_range.0_500k'), value: [0, 500000] },
    { label: i18n.global.t('user.user_course.search_form.field.price.price_range.500k_2m'), value: [500000, 2000000] },
    { label: i18n.global.t('user.user_course.search_form.field.price.price_range.2m_+'), value: [2000000, 0] }
  ];

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'mdi mdi-bullseye';
      case 'intermediate':
        return 'mdi mdi-trending-up';
      case 'advanced':
        return 'mdi mdi-lightning-bolt';
      case 'all':
        return 'mdi mdi-web';
      default:
        return '';
    }
  };

  const levelOptions = [
    { label: 'user.user_course.filters.levels.all', value: 'all', icon: getLevelIcon('all') },
    { label: 'user.user_course.filters.levels.beginner', value: 'beginner', icon: getLevelIcon('beginner') },
    {
      label: 'user.user_course.filters.levels.intermediate',
      value: 'intermediate',
      icon: getLevelIcon('intermediate')
    },
    { label: 'user.user_course.filters.levels.advanced', value: 'advanced', icon: getLevelIcon('advanced') }
  ];

  const loading = ref(true);

  searchFieldsList.value = [
    new SearchField(
      i18n.global.t('user.user_course.search_form.field.title'),
      'titleCont',
      'bx bx-search-alt',
      searchComponents.TextInputField,
      { lg: 4 }
    ),
    new SearchField(
      i18n.global.t('user.user_course.search_form.field.teacher.label'),
      'teacherNameCont',
      'bx bx-user',
      searchComponents.TextInputField,
      { lg: 4 }
    ),
    new SearchField(
      i18n.global.t('user.user_course.search_form.field.price.label'),
      'salePriceRange',
      'bx bx-money',
      searchComponents.SingleSelectField,
      { lg: 4 },
      new SearchFieldOptions({
        selectOptions: salePriceRangeOptions
      })
    )
  ];

  const activeLevel = computed(() => (route.query.levelEq as string) || 'all');

  const onLevelClick = async (level: string) => {
    if (activeLevel.value === level) return;
    loading.value = true;
    await router.replace({
      query: {
        ...route.query,
        levelEq: level === 'all' ? undefined : level
      }
    });

    query.value = {
      ...query.value,
      levelEq: level === 'all' ? undefined : level
    };
    await search();
    loading.value = false;
  };

  const handleSort = async () => {
    loading.value = true;

    await search();

    loading.value = false;
  };

  onMounted(async () => {
    loading.value = true;

    orderBy.value = COURSE_ORDER_BY.CREATED_AT_DESC;
    await parseQueryAndFetch();

    loading.value = false;
  });

  const handleReset = async () => {
    loading.value = true;

    await reset({ defaultOrder: COURSE_ORDER_BY.CREATED_AT_DESC });

    loading.value = false;
  };
</script>

<style lang="scss">
  .search-form {
    margin: auto;
    margin-top: -160px;
    padding: 20px;
  }

  .search-form-title {
    color: #2c3e50;
    position: relative;
    display: inline-block;
    margin-bottom: 1rem;
  }

  .search-form-title::after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -7.5px;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    border-radius: 2px;
    background: linear-gradient(to right, #f9a825, #f57c00);
  }

  .level-filter-container {
    width: 100%;
    margin-top: 1.2rem;
    margin-bottom: 1.2rem;
    display: grid;
    grid-template-columns: repeat(4, 130px);
    justify-content: center;
    gap: 10px;
    @media screen and (max-width: 768px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  .level-btn {
    background: #ffffff;
    border: none;
    border-radius: 1.25rem;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    padding: 0.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.15rem; // reduce gap between icon and label
    cursor: pointer;
    transition:
      transform 0.15s ease,
      box-shadow 0.15s ease;
    color: #555555;
    width: 80px;
    height: 80px;
    min-width: 0;
    min-height: 0;
  }

  .level-btn .icon-wrap {
    background: #f0f0f0;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 1.1rem;
  }

  .level-btn .label {
    font-size: 0.85rem;
    margin-top: 0.2rem;
    text-align: center;
    line-height: 1.1;
  }

  .level-btn.active {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: #ffffff;
  }

  .level-btn.active .icon-wrap {
    background: rgba(255, 255, 255, 0.3);
  }

  .filter-btn {
    width: 120px;
    height: 100px;
  }

  @media (max-width: 768px) {
    .level-btn {
      align-items: center;
      justify-content: center;
      width: 100% !important;
      height: 60px;
      width: fit-content;
      border-radius: 0.75rem;
    }
    .level-btn .icon-wrap {
      background: #f0f0f0;
      width: 1rem;
      height: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .level-btn .label {
      font-size: 0.75rem;
    }
  }
</style>
