<template>
  <div v-if="course" class="course-detail">
    <div class="banner text-white py-2 d-none d-lg-block">
      <BContainer fluid class="h-100">
        <BRow class="justify-content-center h-100 m-0">
          <BCol lg="11" xl="11" xxl="9" class="position-relative h-100">
            <CourseDetailBanner :course="course" />
          </BCol>
        </BRow>
      </BContainer>
      <img src="@/assets/images/vector-billiard-1.png" alt="vector billiard" class="vector-billiard left" />
      <img src="@/assets/images/vector-billiard-2.png" alt="vector billiard" class="vector-billiard right" />
      <img src="@/assets/images/vector-billiard-3.png" alt="vector billiard" class="vector-billiard right sp" />
    </div>

    <BContainer fluid class="mt-4">
      <BRow class="justify-content-center m-0">
        <BCol lg="7" xl="7" xxl="6" class="order-2 order-lg-1 px-0 px-lg-2">
          <FadeInRight>
            <CourseIntroContent
              :course="course"
              :reviews="reviews"
              :stats="statsReviews"
              :metadata="reviewMeta"
              @change-page="fetchCourseReviews"
            />
          </FadeInRight>
        </BCol>
        <BCol lg="4" xl="4" xxl="3" class="order-1 order-lg-2 px-0 px-lg-2">
          <FadeInLeft>
            <CourseIntroSidebar
              :course="course"
              :joined="joined"
              :isInvited="isInvited"
              @join-course="isInvited = false"
              @verify-code="handleVerifyCode"
            />
          </FadeInLeft>
        </BCol>
      </BRow>

      <BRow class="justify-content-center my-4 mx-0">
        <BCol lg="11" xl="11" xxl="9">
          <RelatedCourseCard />
        </BCol>
      </BRow>
    </BContainer>
  </div>
</template>

<script lang="ts" setup>
  import CourseIntroSidebar from '@/components/public/course/CourseIntroSidebar.vue';
  import CourseIntroContent from '@/components/public/course/CourseIntroContent.vue';
  import CourseDetailBanner from '@/components/public/course/CourseDetailBanner.vue';
  import RelatedCourseCard from '@/components/user/course/RelatedCourseCard.vue';

  import { joinCourse, showCourse } from '@/services/public/repositories/course';
  import { UserCourseInterface, UserJoinCourseParams } from '@/utils/interface/user/userCourse';
  import { useAuthPublicStore } from '@/store/public/auth';
  import { showUserCourse } from '@/services/user';
  import { reviewsList } from '@/services/public/repositories/comment';
  import { ReviewInterface } from '@/utils/interface/user/comment';
  import { MetaDataInterface } from '@/utils/interface/common';
  import { useUserAuthStore } from '@/store/user/auth';

  const route = useRoute();
  const router = useRouter();

  const authStore = useAuthPublicStore();
  const accessToken = authStore.accessToken;
  const userAuthStore = useUserAuthStore();

  const course = ref<UserCourseInterface>();
  const reviews = ref<ReviewInterface[]>([]);
  const statsReviews = ref({});
  const isInvited = ref<boolean>(false);
  const joined = ref<boolean>(false);
  const reviewPage = ref(1);
  const reviewMeta = ref<MetaDataInterface>({
    page: 1,
    perPage: 10,
    total: 0,
    pages: 1,
    count: 0,
    from: 0,
    to: 0,
    next: -1,
    prev: -1
  });

  const courseSlug = computed(() => route.params.slug.toString());
  const fromInvitedLink = computed(() => route.query.invited);

  const fetchCourseData = async (slug: string) => {
    if (accessToken) {
      const res = await showUserCourse(slug);
      course.value = res.userCourse;
    } else {
      try {
        if (fromInvitedLink.value) {
          const pendingAccess = {
            type: 'course_invitation',
            returnUrl: route.fullPath,
            timestamp: Date.now()
          };

          sessionStorage.setItem('pendingAccess', JSON.stringify(pendingAccess));
        }

        const res = await showCourse(slug);
        course.value = res.course;
      } catch (e: any) {
        if (fromInvitedLink.value) {
          router.push({
            path: '/login',
            query: { redirect: 'course_invitation' }
          });
        } else {
          console.error(e);
        }
      }
    }

    if (course.value?.currentUserJoining?.status === 'invited') {
      isInvited.value = true;
    }
  };

  const handleVerifyCode = async (code: string) => {
    if (!course.value) return;
    try {
      const joinParams: UserJoinCourseParams = {
        id: String(course.value.id),
        verifyCode: code
      };

      await joinCourse(joinParams);
      isInvited.value = false;
      joined.value = true;
      await userAuthStore.getProfile();
    } catch (e) {
      console.error('Error verifying code:', e);
    }
  };

  const fetchCourseReviews = async (page = 1) => {
    if (course.value) {
      const res = await reviewsList({
        targetID: String(course.value.id),
        targetType: 'Course',
        input: {
          page,
          perPage: reviewMeta.value.perPage
        }
      });
      reviews.value = res.reviews.collection;
      statsReviews.value = res.reviews.stats;
      reviewMeta.value = res.reviews.metadata;
      reviewPage.value = page;
    }
  };

  onMounted(async () => {
    await fetchCourseData(courseSlug.value);
    await fetchCourseReviews();
  });

  watch(
    () => route.params.slug,
    async newSlug => {
      if (newSlug) {
        await fetchCourseData(newSlug.toString());
        fetchCourseReviews();
      }
    }
  );
</script>
