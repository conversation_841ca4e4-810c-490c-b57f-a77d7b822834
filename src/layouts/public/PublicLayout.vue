<template>
  <div>
    <div id="layout-wrapper">
      <NavBar @toggle-menu="toggleMenu" />
      <HeroNotify v-if="userProfile && userProfile.invitedCourseUsers?.length" :profile="userProfile" />

      <div class="main-content custom-layout">
        <div class="page-content custom-flex-shrink">
          <slot />
        </div>

        <PublicFooter />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import PublicFooter from '@/components/layout/PublicFooter.vue';
  import NavBar from '@/layouts/public/NavBar.vue';
  import HeroNotify from '@/layouts/user/HeroNotify.vue';

  import { useUserAuthStore } from '@/store/user/auth';

  const authUserStore = useUserAuthStore();

  const { userProfile } = storeToRefs(authUserStore);

  const toggleMenu = () => {
    let element = document.getElementById('topnav-menu-content');
    element?.classList.toggle('show');
  };

  onMounted(() => {
    document.body.setAttribute('data-bs-theme', 'light');
    document.body.setAttribute('data-layout-mode', 'fluid');
    document.body.setAttribute('data-layout', 'horizontal');
    document.body.setAttribute('data-topbar', 'colored');
    document.body.removeAttribute('data-layout-scrollable');
    document.body.removeAttribute('data-layout-size');
    document.body.removeAttribute('data-sidebar');
    document.body.classList.remove('auth-body-bg');
  });
</script>

<style lang="scss" scoped>
  #page-topbar {
    background-color: white !important;
  }

  .page-content {
    &:has(.hero-section) {
      padding-top: 0 !important;
    }

    @media (max-width: 992px) {
      margin-top: 70px !important;
    }
  }
</style>
