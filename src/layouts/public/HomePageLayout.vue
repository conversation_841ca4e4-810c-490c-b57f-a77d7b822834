<template>
  <div>
    <div id="layout-wrapper">
      <NavBar @toggle-menu="toggleMenu" />
      <PublicMenuSp />

      <div class="main-content custom-layout">
        <div class="custom-flex-shrink">
          <slot />
        </div>

        <PublicFooter />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import NavBar from '@/layouts/public/NavBar.vue';
  import PublicFooter from '@/components/layout/PublicFooter.vue';
  import PublicMenuSp from '@/components/layout/PublicMenuSp.vue';

  const toggleMenu = () => {
    let element = document.getElementById('topnav-menu-content');
    element?.classList.toggle('show');
  };

  onMounted(() => {
    document.body.setAttribute('data-bs-theme', 'light');
    document.body.setAttribute('data-layout-mode', 'fluid');
    document.body.setAttribute('data-layout', 'horizontal');
    document.body.setAttribute('data-topbar', 'colored');
    document.body.removeAttribute('data-layout-scrollable');
    document.body.removeAttribute('data-layout-size');
    document.body.removeAttribute('data-sidebar');
    document.body.classList.remove('auth-body-bg');
  });
</script>
