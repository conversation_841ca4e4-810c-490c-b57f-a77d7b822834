<template>
  <header id="page-topbar" class="shadow">
    <div class="navbar-header public-navbar">
      <div class="d-flex align-items-center gap-0 gap-lg-3 gap-xxl-4 h-100">
        <div class="navbar-brand-box h-100">
          <router-link to="/" class="logo logo-dark">
            <span class="logo-sm">
              <img src="@/assets/images/logo-sm.png" height="22" />
            </span>
            <span class="logo-lg">
              <img src="@/assets/images/logo-dark.png" height="17" />
            </span>
          </router-link>

          <router-link to="/" class="logo logo-light">
            <span class="logo-sm">
              <img src="@/assets/images/logo-sm.png" height="22" />
            </span>
            <span class="logo-lg">
              <img src="@/assets/images/logo-dark.png" height="60" />
            </span>
          </router-link>
        </div>

        <BButton
          variant="white"
          id="vertical-menu-btn"
          type="button"
          class="btn btn-sm px-3 font-size-16 d-lg-none header-item"
          @click="toggleMenu"
        >
          <i class="fa fa-fw fa-bars"></i>
        </BButton>

        <div v-if="isLoggedIn">
          <div class="user-site-badge badge rounded-pill d-flex align-items-center gap-1 font-size-12">
            <i class="mdi mdi-account-outline"></i>
            <b>{{ $t('public.top_menu.user_site') }}</b>
          </div>
        </div>
      </div>

      <div class="d-none d-lg-flex">
        <Menu :is-logged-in="isLoggedIn" />
      </div>

      <div class="d-flex gap-2 gap-xxl-3">
        <Notification :is-logged-in="isLoggedIn" :site="SITES.USER" />

        <BDropdown
          v-if="isLoggedIn"
          right
          toggle-class="m-0 p-0 bg-transparent border-0 custom-dropdown"
          menu-class="public-menu"
        >
          <template #button-content>
            <BAvatar
              variant="warning"
              :src="userProfile?.imageUrl ? userProfile.imageUrl : dummyAvatar"
              size="36"
            ></BAvatar>
          </template>

          <BDropdownItem @click="router.push('/teacher')" class="teacher-site-item">
            <div class="fb-icon-wrapper">
              <i class="mdi mdi-rotate-3d-variant"></i>
            </div>
            {{ $t('public.top_menu.teacher_site') }}
          </BDropdownItem>

          <BDropdownDivider />

          <div v-if="!blockExperimentFeatures">
            <BDropdownItem
              v-for="item in Object.values(langOptions)"
              :key="item.value"
              @click="selectLanguage(item.value)"
              class="fb-dropdown-item language-item"
              :class="{ active: item.value === language }"
            >
              <div class="d-flex align-content-center gap-2">
                <i :class="`fi fi-${item.icon}`"></i>
                <span>{{ item.label }}</span>
              </div>

              <i v-if="item.value === language" class="mdi mdi-check fb-check-icon"></i>
            </BDropdownItem>
          </div>

          <BDropdownDivider />

          <BDropdownItem @click="router.push('/profile')">
            <div class="fb-icon-wrapper">
              <i class="bx bx-user"></i>
            </div>
            {{ $t('public.top_menu.profile') }}
          </BDropdownItem>

          <BDropdownDivider />

          <BDropdownItem @click="handleLogout" class="logout-item">
            <div class="fb-icon-wrapper">
              <i class="bx bx-power-off"></i>
            </div>
            {{ $t('public.top_menu.logout') }}
          </BDropdownItem>
        </BDropdown>

        <div v-else class="auth-btn d-flex gap-2">
          <router-link to="/login" class="login-btn">
            {{ $t('public.top_menu.login') }}
          </router-link>

          <router-link to="/register" class="register-btn">
            {{ $t('public.top_menu.register') }}
          </router-link>
        </div>
      </div>
    </div>

    <div class="mobile-menu-overlay" :class="{ active: isMobileMenuOpen }" @click="isMobileMenuOpen = false"></div>

    <div class="mobile-menu" :class="{ active: isMobileMenuOpen }">
      <div class="mobile-menu-header d-flex align-items-center justify-content-between">
        <router-link to="/" @click="isMobileMenuOpen = false">
          <img src="@/assets/images/logo-dark.png" alt="Vibico Logo" class="mobile-logo" />
        </router-link>
        <BButton
          variant="light"
          class="mobile-menu-close d-flex align-items-center justify-content-center border-0 shadow-none font-size-20"
          @click="isMobileMenuOpen = false"
        >
          <i class="mdi mdi-close"></i>
        </BButton>
      </div>
      <div class="mobile-menu-content">
        <Menu :is-logged-in="isLoggedIn" @mobileMenuClick="isMobileMenuOpen = false" />
      </div>
    </div>
  </header>
</template>

<script lang="ts" setup>
  import { useAuthPublicStore } from '@/store/public/auth';
  import { useUserAuthStore } from '@/store/user/auth';
  import { useGlobalStore } from '@/store/global';
  import { langOptions } from '@/utils/language';

  import Menu from '@/components/layout/Menu.vue';
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';
  import Notification from '@/components/base/Notification.vue';
  import { SITES } from '@/utils/constant';

  const globalStore = useGlobalStore();
  const { language } = storeToRefs(globalStore);

  const emit = defineEmits(['toggle-menu']);

  defineProps({
    isBracketLayout: {
      type: Boolean,
      default: false
    }
  });

  const authPublicStore = useAuthPublicStore();
  const authUserStore = useUserAuthStore();
  const router = useRouter();

  const { userProfile } = storeToRefs(authUserStore);
  const blockExperimentFeatures = import.meta.env.VITE_APP_BLOCK_EXPERIMENTAL_FEATURES.toLowerCase() === 'true';

  const isMobileMenuOpen = ref(false);

  const isLoggedIn = computed(() => !!authPublicStore.accessToken);

  const selectLanguage = (value: string) => {
    globalStore.setLanguage(value);
  };

  const toggleMenu = () => {
    isMobileMenuOpen.value = !isMobileMenuOpen.value;
    emit('toggle-menu');
  };

  const handleLogout = () => {
    authPublicStore.logout();
    isMobileMenuOpen.value = false;
  };
</script>

<style lang="scss" scoped>
  #page-topbar {
    background-color: white !important;
  }

  .navbar-header {
    padding: 0 15px;
    .navbar-brand-box {
      .logo {
        .logo-lg {
          display: flex;
          img {
            width: 130px;
            object-fit: contain;
          }
          @media (max-width: 992px) {
            display: none;
          }
        }
      }
    }

    @media (max-width: 992px) {
      padding: 0 15px 0 0;
    }
  }

  .fb-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #fef9e7;
    margin-right: 10px;

    i {
      font-size: 16px;
      color: #ecac24;
    }
  }

  .teacher-site-item {
    .fb-icon-wrapper {
      background-color: #e8f5e9;

      i {
        color: #2e7d32;
      }
    }
  }

  .logout-item {
    .fb-icon-wrapper {
      background-color: #ffebe9;

      i {
        color: #e41e3f;
      }
    }
  }

  .language-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border-radius: 6px;
    transition: all 0.2s ease;

    i {
      font-size: 18px;
      &.fb-check-icon {
        position: absolute;
        right: 16px;
        color: #ecac24;
        font-size: 18px;
      }
    }

    &:hover,
    &.active {
      background-color: #f8f9fa;
    }

    &.active {
      color: #ecac24;
      font-weight: 600;
    }
  }

  .mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;

    &.active {
      opacity: 1;
      visibility: visible;
    }
  }

  .mobile-menu {
    position: fixed;
    top: 0;
    left: -280px;
    width: 280px;
    height: 100%;
    background-color: white;
    z-index: 1002;
    transition: all 0.3s ease;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    overflow-y: auto;

    &.active {
      left: 0;
    }

    .mobile-menu-header {
      padding: 10px 0 10px 10px;
      border-bottom: 1px solid #f0f0f0;

      .mobile-logo {
        height: 30px;
      }

      .mobile-menu-close {
        width: 36px;
        height: 36px;
        background-color: transparent;
        cursor: pointer;
        transition: all 0.2s ease;
      }
    }

    .mobile-menu-content {
      padding: 20px;

      :deep(.menu-list) {
        .menu-link {
          border-bottom: 1px solid #f0f0f0;
          a {
            display: inline-block;
            color: black;
            padding-bottom: 15px;
            font-weight: 500;

            &.active {
              color: #ecac24;
            }
          }
        }
      }
    }
  }
</style>
