<template>
  <header id="page-topbar">
    <div class="navbar-header">
      <div class="d-flex">
        <div class="navbar-brand-box">
          <router-link to="/teacher" class="logo logo-dark">
            <span class="logo-sm">
              <img src="@/assets/images/logo.png" height="22" />
            </span>
            <span class="logo-lg">
              <img src="@/assets/images/logo-dark.png" height="17" />
            </span>
          </router-link>

          <router-link to="/teacher" class="logo logo-light">
            <span class="logo-sm">
              <img src="@/assets/images/logo.png" height="20" />
            </span>
            <span class="logo-lg">
              <img src="@/assets/images/logo-light.png" height="60" />
            </span>
          </router-link>
        </div>

        <BButton
          variant="white"
          id="vertical-menu-btn"
          type="button"
          class="btn btn-sm px-3 font-size-16 d-lg-none header-item"
          @click="toggleMenu"
        >
          <i class="fa fa-fw fa-bars"></i>
        </BButton>
      </div>

      <div class="d-flex">
        <Notification :is-logged-in="!!accessToken" :site="SITES.TEACHER" />

        <BDropdown right variant="black" toggle-class="header-item" menu-class="dropdown-menu-end">
          <template v-slot:button-content v-if="teacherProfile">
            <img
              class="rounded-circle header-profile-user"
              :src="teacherProfile.imageUrl ? teacherProfile.imageUrl : dummyAvatar"
              alt="Header Avatar"
            />
            <span class="d-none d-xl-inline-block ms-1">
              <div>Profile</div>
            </span>
            <i class="mdi mdi-chevron-down d-none d-xl-inline-block"></i>
          </template>
          <BDropdownItem @click="router.push('/teacher/profile/edit')">
            <span class="text-body">
              {{ $t('teacher.nav_bar.account.sub_menus.profile') }}
            </span>
          </BDropdownItem>
          <BDropdownItem @click="router.push('/')">
            <span class="text-body">
              {{ $t('teacher.nav_bar.account.sub_menus.student_page') }}
            </span>
          </BDropdownItem>
          <BDropdownDivider></BDropdownDivider>
          <a href="#" class="dropdown-item text-danger" @click="handleLogout">
            <i class="bx bx-power-off font-size-16 align-middle me-1 text-danger"></i>
            {{ $t('teacher.nav_bar.account.sub_menus.logout') }}
          </a>
        </BDropdown>
      </div>
    </div>
  </header>
</template>

<script lang="ts" setup>
  import dummyAvatar from '@/assets/images/users/user-dummy-img.png';
  import Notification from '@/components/base/Notification.vue';

  import { useAuthPublicStore } from '@/store/public/auth';
  import { useTeacherAuthStore } from '@/store/teacher/auth';
  import { SITES } from '@/utils/constant';

  const router = useRouter();

  const emit = defineEmits(['toggle-menu']);

  defineProps({
    isBracketLayout: {
      type: Boolean,
      default: false
    }
  });

  const teacherAuthStore = useTeacherAuthStore();
  const publicAuthStore = useAuthPublicStore();
  const { accessToken } = useAuthPublicStore();
  const { teacherProfile } = storeToRefs(teacherAuthStore);

  const toggleMenu = () => {
    emit('toggle-menu');
  };

  const handleLogout = () => {
    publicAuthStore.logout();
  };
</script>
