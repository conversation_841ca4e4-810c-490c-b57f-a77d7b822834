<template>
  <header id="page-topbar">
    <div class="navbar-header">
      <div class="d-flex">
        <div class="navbar-brand-box">
          <router-link to="/admin" class="logo logo-dark">
            <span class="logo-sm">
              <img src="@/assets/images/logo.png" height="22" />
            </span>
            <span class="logo-lg">
              <img src="@/assets/images/logo-dark.png" height="17" />
            </span>
          </router-link>

          <router-link to="/admin" class="logo logo-light">
            <span class="logo-sm">
              <img src="@/assets/images/logo.png" height="20" />
            </span>
            <span class="logo-lg">
              <img src="@/assets/images/logo-light.png" height="60" />
            </span>
          </router-link>
        </div>

        <BButton
          variant="white"
          id="vertical-menu-btn"
          type="button"
          class="btn btn-sm px-3 font-size-16 d-lg-none header-item"
          @click="toggleMenu"
        >
          <i class="fa fa-fw fa-bars"></i>
        </BButton>
      </div>

      <div class="d-flex">
        <Notification :is-logged-in="!!adminAuthStore.adminAccessToken" :site="SITES.ADMIN" />

        <BDropdown right variant="black" toggle-class="header-item" menu-class="dropdown-menu-end">
          <template v-slot:button-content>
            <img class="rounded-circle header-profile-user" :src="avatar1" alt="Header Avatar" />
          </template>
          <a href="#" class="dropdown-item text-danger" @click="handleLogout">
            <i class="bx bx-power-off font-size-16 align-middle me-1 text-danger"></i>
            {{ $t('teacher.nav_bar.account.sub_menus.logout') }}
          </a>
        </BDropdown>
      </div>
    </div>
  </header>
</template>

<script lang="ts" setup>
  import avatar1 from '@/assets/images/users/avatar-1.jpg';
  import Notification from '@/components/base/Notification.vue';
  import { useAuthAdminStore } from '@/store/admin/auth';
  import { SITES } from '@/utils/constant';

  const emit = defineEmits(['toggle-menu']);

  defineProps({
    isBracketLayout: {
      type: Boolean,
      default: false
    }
  });

  const adminAuthStore = useAuthAdminStore();

  const toggleMenu = () => {
    emit('toggle-menu');
  };

  const handleLogout = () => {
    adminAuthStore.logout();
  };
</script>
