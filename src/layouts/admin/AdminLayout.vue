<template>
  <div>
    <div id="layout-wrapper">
      <NavBar @toggle-menu="toggleMenu" />
      <TopNav />

      <div class="main-content">
        <div class="page-content">
          <BContainer fluid>
            <PageHeader />
            <slot />
          </BContainer>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import NavBar from '@/layouts/admin/NavBar.vue';
  import PageHeader from '@/components/layout/PageHeader.vue';
  import TopNav from '@/components/layout/TopNav.vue';

  const toggleMenu = () => {
    let element = document.getElementById('topnav-menu-content');
    element?.classList.toggle('show');
  };

  onMounted(() => {
    document.body.setAttribute('data-bs-theme', 'light');
    document.body.setAttribute('data-layout-mode', 'fluid');
    document.body.setAttribute('data-layout', 'horizontal');
    document.body.setAttribute('data-topbar', 'colored');
    document.body.removeAttribute('data-layout-scrollable');
    document.body.removeAttribute('data-layout-size');
    document.body.removeAttribute('data-sidebar');
    document.body.classList.remove('auth-body-bg');
  });
</script>
