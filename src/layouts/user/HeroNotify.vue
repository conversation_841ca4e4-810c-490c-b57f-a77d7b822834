<template>
  <div class="hero-notify border-top p-0">
    <div class="navbar-header py-3">
      <div
        v-if="profile.invitedCourseUsers?.length == 1"
        class="w-100 d-flex justify-content-between align-items-center gap-2 gap-lg-3"
      >
        <div class="d-flex align-items-center gap-2 gap-lg-3">
          <div class="notify-icon blinking d-flex align-items-center justify-content-center">
            <i class="bx bx-bell"></i>
          </div>
          <div class="notify-title d-flex align-items-center flex-wrap gap-1">
            {{ $t('public.course.notification.one_course.title') }}:
            <span class="fw-bold">{{ profile.invitedCourseUsers?.[0]?.course?.title }}</span>
            {{ $t('public.course.notification.one_course.by') }}
            <span class="fw-bold">{{ profile.invitedCourseUsers?.[0]?.course?.teacher?.name }}</span>
          </div>
        </div>

        <BButton
          variant="outline-primary"
          class="d-none d-lg-inline-flex align-items-center gap-1 rounded-3 px-3 py-2 text-sm border-1 bg-white text-primary font-size-14"
          @click="router.push(`/courses/${profile.invitedCourseUsers?.[0]?.course?.slug}`)"
        >
          {{ $t('public.course.notification.one_course.btn_show') }}
          <i class="bx bx-link-external"></i>
        </BButton>
        <i
          class="d-block d-lg-none bx bx-link-external font-size-20 lh-1 text-primary"
          @click="router.push(`/courses/${profile.invitedCourseUsers?.[0]?.course?.slug}`)"
        >
        </i>
      </div>

      <div v-else class="position-relative w-100 d-flex justify-content-between align-items-center gap-2 gap-lg-3">
        <div class="d-flex align-items-center gap-2 gap-lg-3">
          <div class="notify-icon blinking d-flex align-items-center justify-content-center">
            <i class="bx bx-bell"></i>
          </div>
          <div class="notify-title d-flex align-items-center flex-wrap gap-1">
            {{ $t('public.course.notification.many_courses.title_1') }}
            <span>{{ profile.invitedCourseUsers?.length }}</span>
            {{ $t('public.course.notification.many_courses.title_2') }}
          </div>
        </div>
        <BButton
          variant="outline-primary"
          class="d-none d-lg-flex align-items-center gap-1 bg-white text-primary font-size-14"
          @click="showNotification = !showNotification"
        >
          <span>{{ $t('public.course.notification.many_courses.btn_show') }}</span>
          <i class="bx font-size-20 ms-1" :class="showNotification ? 'bx-chevron-up' : 'bx-chevron-down'"></i>
        </BButton>
        <i
          class="d-block d-lg-none mdi mdi-dots-horizontal-circle-outline font-size-18 lh-1 text-primary"
          @click="showNotification = !showNotification"
        >
        </i>

        <BCard no-body v-if="showNotification" class="notification-section py-3" v-click-outside="hiddenNotification">
          <div class="d-flex justify-content-between align-items-center px-3">
            <h6 class="mb-0">{{ $t('public.course.notification.invitation.title') }}</h6>
            <i class="mdi mdi-close font-size-18 lh-1 cursor-pointer" @click="hiddenNotification"></i>
          </div>
          <hr />
          <div class="px-3">
            <CourseNotification v-for="item in profile.invitedCourseUsers" :key="item.id" :invitedCourse="item" />
          </div>
        </BCard>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import CourseNotification from '@/components/user/course/CourseNotification.vue';

  import { UserInterface } from '@/utils/interface/user/user';

  defineProps({
    profile: {
      type: Object as PropType<UserInterface>,
      required: true
    }
  });

  const router = useRouter();

  const showNotification = ref<boolean>(false);

  const hiddenNotification = () => {
    showNotification.value = false;
  };
</script>

<style lang="scss" scoped>
  .hero-notify {
    position: sticky;
    top: 70px;
    left: 0;
    z-index: 9;

    font-size: 16px;
    background: #e1ecfc;
    box-shadow: 0 3px 6px hsla(0, 0%, 43.9%, 0.2);

    .navbar-header {
      height: auto;
    }

    .notify-icon {
      width: 30px;
      height: 30px;
      background-color: white;
      border-radius: 50%;
      color: #556ee6;
      font-size: 18px;
      &.blinking {
        animation: blink 1s infinite;
      }
    }

    .notification-section {
      position: absolute;
      top: 100%;
      right: 0;
      width: 500px;
      max-height: 560px;
      overflow-y: auto;
      background-color: white;
      border: 1px solid rgb(231, 231, 231);
      border-radius: 8px;
    }

    @media (max-width: 1200px) {
      .navbar-header {
        padding-inline: 15px;
      }
    }

    @media (max-width: 992px) {
      .notification-section {
        width: 100%;
      }

      .navbar-header {
        padding-inline: 10px;
      }

      .notify-title {
        font-size: 14px;
      }
    }

    @keyframes blink {
      0% {
        opacity: 1;
        transform: scale(1);
      }
      50% {
        opacity: 0.3;
        transform: scale(1.2);
      }
      100% {
        opacity: 1;
        transform: scale(1);
      }
    }
  }
</style>
