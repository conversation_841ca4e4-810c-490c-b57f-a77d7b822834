<template>
  <div>
    <div id="layout-wrapper">
      <div class="main-content">
        <div class="page-content mb-5 p-0">
          <BContainer fluid>
            <slot />
          </BContainer>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  onMounted(() => {
    document.body.setAttribute('data-bs-theme', 'light');
    document.body.setAttribute('data-layout-mode', 'fluid');
    document.body.setAttribute('data-layout', 'horizontal');
    document.body.setAttribute('data-topbar', 'colored');
    document.body.removeAttribute('data-layout-scrollable');
    document.body.removeAttribute('data-layout-size');
    document.body.removeAttribute('data-sidebar');
    document.body.classList.remove('auth-body-bg');
  });
</script>

<style lang="scss" scoped>
  .page-content {
    margin-top: 76px !important;
  }
</style>
