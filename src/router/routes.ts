import { RouteRecordRaw } from 'vue-router';

import adminRoute from '@/router/admin';
import homeRoute from '@/router/home';
import teacherRoute from '@/router/teacher';

import DefaultLayout from '@/layouts/DefaultLayout.vue';

import Page404 from '@/components/utility/Page404.vue';

const routes: Readonly<RouteRecordRaw[]> = [
  {
    path: '/',
    children: [
      {
        path: 'admin',
        children: adminRoute
      },
      {
        path: 'teacher',
        children: teacherRoute
      }
    ]
  },
  ...homeRoute,
  {
    path: '/404',
    name: 'Error-404',
    component: Page404,
    meta: {
      title: '404 Error Page',
      layout: DefaultLayout
    }
  },
  {
    path: '/:pathMatch(.*)*',
    component: Page404,
    meta: {
      title: '404 Error Page',
      layout: DefaultLayout
    }
  }
];

export default routes;
