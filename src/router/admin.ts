import { RouteRecordRaw } from 'vue-router';

import { GUARD } from '@/utils/constant';

import AdminLayout from '@/layouts/admin/AdminLayout.vue';
import AuthLayout from '@/layouts/AuthLayout.vue';
import EditorLayout from '@/layouts/EditorLayout.vue';

import CourseDetail from '@/pages/admin/courses/_id/index.vue';
import CourseView from '@/pages/admin/courses/index.vue';
import DrillsView from '@/pages/admin/drills/index.vue';
import DrillView from '@/pages/admin/drills/_id/index.vue';
import EditorDrillEditView from '@/pages/admin/editor/drill/_id/index.vue';
import EditorDrillInitView from '@/pages/admin/editor/drill/new.vue';
import LoginView from '@/pages/admin/auth/LoginView.vue';
import TeacherView from '@/pages/admin/teachers/index.vue';
import UserView from '@/pages/admin/users/index.vue';

const adminRoute: RouteRecordRaw[] = [
  {
    path: '/admin',
    redirect: '/admin/teachers'
  },
  {
    path: 'login',
    component: LoginView,
    meta: {
      title: 'Login',
      layout: AuthLayout
    }
  },
  {
    path: 'drills',
    component: DrillsView,
    meta: {
      title: 'Diagrams',
      guard: GUARD.ADMIN,
      authRequired: true,
      layout: AdminLayout
    }
  },
  {
    path: 'drills/:id',
    component: DrillView,
    meta: {
      title: 'Diagram Detail',
      guard: GUARD.ADMIN,
      authRequired: true,
      layout: AdminLayout
    }
  },
  {
    path: 'editor/drill/new',
    component: EditorDrillInitView,
    meta: {
      title: 'Editor Drill Init',
      guard: GUARD.ADMIN,
      authRequired: true,
      layout: EditorLayout
    }
  },
  {
    path: 'editor/drill/:id',
    component: EditorDrillEditView,
    meta: {
      title: 'Editor Drill Edit',
      guard: GUARD.ADMIN,
      authRequired: true,
      layout: EditorLayout
    }
  },
  {
    path: 'courses',
    component: CourseView,
    meta: {
      title: 'Courses',
      guard: GUARD.ADMIN,
      authRequired: true,
      layout: AdminLayout
    }
  },
  {
    path: 'courses/:id',
    component: CourseDetail,
    meta: {
      title: 'Course',
      guard: GUARD.ADMIN,
      authRequired: true,
      layout: AdminLayout
    }
  },
  {
    path: 'teachers',
    component: TeacherView,
    meta: {
      title: 'Teachers',
      guard: GUARD.ADMIN,
      authRequired: true,
      layout: AdminLayout
    }
  },
  {
    path: 'users',
    component: UserView,
    meta: {
      title: 'Users',
      guard: GUARD.ADMIN,
      authRequired: true,
      layout: AdminLayout
    }
  }
];

export default adminRoute;
