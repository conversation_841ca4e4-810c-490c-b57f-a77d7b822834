import { RouteRecordRaw } from 'vue-router';

import { GUARD } from '@/utils/constant';

import EditorLayout from '@/layouts/EditorLayout.vue';
import TeacherLayout from '@/layouts/teacher/TeacherLayout.vue';

import CourseBaseInfo from '@/components/teacher/courses/details/CourseBaseInfo.vue';
import CourseDetail from '@/pages/teacher/courses/_slug/index.vue';
import CoursePracticeSubmissionList from '@/components/teacher/courses/details/CoursePracticeSubmissionList.vue';
import CourseUserList from '@/components/teacher/courses/details/CourseUserList.vue';
import CourseView from '@/pages/teacher/courses/index.vue';
import DrillsView from '@/pages/teacher/drills/index.vue';
import DrillView from '@/pages/teacher/drills/_slug/index.vue';
import EditorCourseEditView from '@/pages/teacher/editor/course/_slug/index.vue';
import EditorCourseInitView from '@/pages/teacher/editor/course/new.vue';
import EditorDrillEditView from '@/pages/teacher/editor/drill/_slug/index.vue';
import EditorDrillInitView from '@/pages/teacher/editor/drill/new.vue';
import EditorInitView from '@/pages/teacher/editor/index.vue';
import PracticeSubmissionsView from '@/pages/teacher/practiceSubmissions/index.vue';
import Profile from '@/pages/teacher/profile/profile.vue';
import ProfileEdit from '@/pages/teacher/profile/edit.vue';
import SetupView from '@/pages/teacher/setup/setupView.vue';
import UsersView from '@/pages/teacher/users/index.vue';
import HighlightView from '@/pages/teacher/highlights/index.vue';
const teacherRoute: RouteRecordRaw[] = [
  {
    path: '/teacher',
    redirect: '/teacher/users'
  },
  {
    path: 'setup',
    component: SetupView,
    meta: {
      title: 'Setup',
      guard: GUARD.TEACHER,
      authRequired: false,
      layout: TeacherLayout
    }
  },
  {
    path: 'profile',
    component: Profile,
    meta: {
      title: 'Profile',
      guard: GUARD.TEACHER,
      authRequired: true,
      layout: TeacherLayout
    }
  },
  {
    path: 'profile/edit',
    component: ProfileEdit,
    meta: {
      title: 'Profile Edit',
      guard: GUARD.TEACHER,
      authRequired: true,
      layout: TeacherLayout
    }
  },
  {
    path: 'users',
    component: UsersView,
    meta: {
      title: 'Users',
      guard: GUARD.TEACHER,
      authRequired: true,
      layout: TeacherLayout
    }
  },
  {
    path: 'drills',
    component: DrillsView,
    meta: {
      title: 'Diagrams',
      guard: GUARD.TEACHER,
      authRequired: true,
      layout: TeacherLayout
    }
  },
  {
    name: 'DrillView',
    path: 'drills/:slug',
    component: DrillView,
    meta: {
      title: 'Diagram Detail',
      guard: GUARD.TEACHER,
      authRequired: true,
      layout: TeacherLayout
    }
  },
  {
    path: 'courses',
    name: 'courses',
    component: CourseView,
    meta: {
      title: 'Courses',
      guard: GUARD.TEACHER,
      authRequired: true,
      layout: TeacherLayout
    }
  },
  {
    path: 'courses/:slug/detail',
    name: 'CourseDetail',
    component: CourseDetail,
    meta: {
      title: 'Course Detail',
      guard: GUARD.TEACHER,
      authRequired: true,
      layout: TeacherLayout
    },
    children: [
      {
        path: '',
        name: 'CourseBaseInfo',
        component: CourseBaseInfo
      },
      {
        path: 'students',
        name: 'CourseUserList',
        component: CourseUserList
      },
      {
        path: 'practice-submissions',
        name: 'CoursePracticeSubmissionList',
        component: CoursePracticeSubmissionList
      }
    ]
  },
  {
    path: 'editor',
    component: EditorInitView,
    meta: {
      title: 'Editor Init',
      guard: GUARD.TEACHER,
      authRequired: true,
      layout: TeacherLayout
    }
  },
  {
    path: 'editor/drill/new',
    component: EditorDrillInitView,
    meta: {
      title: 'Editor Drill Init',
      guard: GUARD.TEACHER,
      authRequired: true,
      layout: EditorLayout
    }
  },
  {
    path: 'editor/drill/:slug',
    component: EditorDrillEditView,
    meta: {
      title: 'Editor Drill Edit',
      guard: GUARD.TEACHER,
      authRequired: true,
      layout: EditorLayout
    }
  },
  {
    path: 'editor/course/new',
    component: EditorCourseInitView,
    meta: {
      title: 'Editor Course Init',
      guard: GUARD.TEACHER,
      authRequired: true,
      layout: TeacherLayout
    }
  },
  {
    path: 'editor/course/:slug',
    component: EditorCourseEditView,
    meta: {
      title: 'Editor Course Edit',
      guard: GUARD.TEACHER,
      authRequired: true,
      layout: EditorLayout
    }
  },
  {
    path: 'practice-submissions',
    component: PracticeSubmissionsView,
    meta: {
      title: 'Practice Submissions',
      guard: GUARD.TEACHER,
      authRequired: true,
      layout: TeacherLayout
    }
  },
  {
    path: 'highlights',
    component: HighlightView,
    meta: {
      title: 'Highlights',
      guard: GUARD.TEACHER,
      authRequired: true,
      layout: TeacherLayout
    }
  }
];

export default teacherRoute;
