import { createRouter, createWebHistory, RouterScrollBehavior } from 'vue-router';
import { useBreadcrumb } from '@bachdx/b-vuse';
const { setBreadcrumb } = useBreadcrumb({});
import { useAuthPublicStore } from '@/store/public/auth';
import { ROUTE_PATH } from '@/utils/constant';

import routes from '@/router/routes';
import useRouterHandler from '@/composable/useRouterHandler';

const scrollBehavior: RouterScrollBehavior = (to, from, savedPosition) => {
  if (savedPosition) {
    return savedPosition;
  }

  if (to.path === from.path) {
    return false;
  }

  return { left: 0, top: 0, behavior: 'smooth' };
};

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: routes,
  scrollBehavior
});

router.beforeEach(async (to, from, next) => {
  if ([ROUTE_PATH.HOMEPAGE, ROUTE_PATH.USER_DASHBOARD].includes(to.path)) {
    const authPublicStore = useAuthPublicStore();
    const isLoggedIn = !!authPublicStore.accessToken;

    if (to.path === ROUTE_PATH.HOMEPAGE && isLoggedIn) {
      return next(ROUTE_PATH.USER_DASHBOARD);
    }

    if (to.path === ROUTE_PATH.USER_DASHBOARD && !isLoggedIn) {
      return next(ROUTE_PATH.HOMEPAGE);
    }
  }

  if (to.path !== from.path) {
    setBreadcrumb({});
    const guard = to.meta.guard as string;
    const { handleRoute } = useRouterHandler({ guard, to, from });

    const route = await handleRoute();
    if (route) {
      return next(route);
    }
  }

  next();
});

export default router;
