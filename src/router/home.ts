import { RouteRecordRaw } from 'vue-router';

import { GUARD } from '@/utils/constant';

import HomePageLayout from '@/layouts/public/HomePageLayout.vue';
import PublicLayout from '@/layouts/public/PublicLayout.vue';

import ChangePassword from '@/pages/public/auth/ChangePassword.vue';
import CourseDetail from '@/pages/public/course/_slug/CourseDetail.vue';
import HomePageView from '@/pages/public/index.vue';
import LoggedHomePageView from '@/pages/user/index.vue';
import LoginView from '@/pages/public/auth/LoginView.vue';
import PublicCourseList from '@/pages/public/course/index.vue';
import PublicDrillDetailView from '@/pages/public/drill/_id/index.vue';
import PublicDrillView from '@/pages/public/drill/index.vue';
import RecoverPassword from '@/pages/public/auth/RecoverPassword.vue';
import RecoverPasswordVerify from '@/pages/public/auth/RecoverPasswordVerify.vue';
import RegisterVerifyView from '@/pages/public/auth/RegisterVerify.vue';
import RegisterView from '@/pages/public/auth/RegisterView.vue';
import ResetPassword from '@/pages/public/auth/ResetPassword.vue';
import TeacherDetailView from '@/pages/public/teachers/_slug/index.vue';
import TeacherView from '@/pages/public/teachers/index.vue';
import TermsAndConditions from '@/pages/public/termsAndConditions/index.vue';
import UserCourseDetail from '@/pages/user/course/_id/UserCourseDetail.vue';
import UserCoursePreview from '@/pages/user/course/_id/Preview.vue';
import UserCourseView from '@/pages/user/course/index.vue';
import UserProfileView from '@/pages/user/profile/ProfileView.vue';

const homeRoute: RouteRecordRaw[] = [
  {
    path: '/',
    component: HomePageView,
    meta: {
      title: 'Homepage',
      layout: HomePageLayout
    }
  },
  {
    path: '/home',
    component: LoggedHomePageView,
    meta: {
      title: 'Homepage',
      layout: PublicLayout,
      guard: GUARD.USER,
      authRequired: true
    }
  },
  {
    path: '/login',
    component: LoginView,
    meta: {
      title: 'Login',
      layout: PublicLayout
    }
  },
  {
    path: '/register',
    component: RegisterView,
    meta: {
      title: 'Register',
      layout: PublicLayout
    }
  },
  {
    path: '/register-verify',
    component: RegisterVerifyView,
    meta: {
      title: 'Register Verify',
      layout: PublicLayout
    }
  },
  {
    path: '/recover-password',
    component: RecoverPassword,
    meta: {
      title: 'Recover password',
      layout: PublicLayout
    }
  },
  {
    path: '/recover-password-verify',
    component: RecoverPasswordVerify,
    meta: {
      title: 'Recover password verify',
      layout: PublicLayout
    }
  },
  {
    path: '/reset_password',
    component: ResetPassword,
    meta: {
      title: 'Reset password',
      layout: PublicLayout
    }
  },
  {
    path: '/my-courses',
    component: UserCourseView,
    meta: {
      title: 'My Courses',
      layout: PublicLayout,
      guard: GUARD.USER,
      authRequired: true
    }
  },
  {
    path: '/my-courses/:id',
    component: UserCourseDetail,
    meta: {
      title: 'My Courses Detail',
      layout: HomePageLayout,
      guard: GUARD.USER,
      authRequired: true
    }
  },
  {
    path: '/preview-courses/:id',
    component: UserCoursePreview,
    meta: {
      title: 'Preview Courses Detail',
      layout: HomePageLayout,
      guard: GUARD.USER,
      authRequired: true
    }
  },
  {
    path: '/courses',
    component: PublicCourseList,
    meta: {
      title: 'Courses',
      layout: PublicLayout
    }
  },
  {
    path: '/courses/:slug',
    component: CourseDetail,
    meta: {
      title: 'Courses Detail',
      layout: HomePageLayout
    }
  },
  {
    path: '/drills',
    component: PublicDrillView,
    meta: {
      title: 'Drills',
      layout: PublicLayout
    }
  },
  {
    path: '/drills/:slug',
    component: PublicDrillDetailView,
    meta: {
      title: 'Drills',
      layout: PublicLayout
    }
  },
  {
    path: '/profile',
    component: UserProfileView,
    meta: {
      title: 'Profile',
      layout: PublicLayout,
      guard: GUARD.USER,
      authRequired: true
    }
  },
  {
    path: '/teachers',
    component: TeacherView,
    meta: {
      title: 'Teachers',
      layout: PublicLayout,
      authRequired: false
    }
  },
  {
    path: '/teachers/:slug',
    component: TeacherDetailView,
    meta: {
      title: 'Teacher',
      layout: PublicLayout
    }
  },
  {
    path: '/change-password',
    component: ChangePassword,
    meta: {
      title: 'Teacher',
      layout: PublicLayout
    }
  },
  {
    path: '/terms-and-conditions',
    component: TermsAndConditions,
    meta: {
      title: 'Terms and Conditions',
      layout: PublicLayout
    }
  }
];

export default homeRoute;
