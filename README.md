# vibico-education-web

## Project setup

```bash
yarn install
```

### Compiles and hot-reloads for development

```bash
yarn dev
```

### Compiles and minifies for production

```bash
yarn build
```

### Lints and fixes files

```bash
# eslint
yarn lint

# prettier
yarn format
# or
yarn format-fix
```

### Run type check

```bash
yarn type-check
# or
yarn type-watch
```

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).
